Gapp/src/main/kotlin/com/example/kpitrackerapp/AdminDashboardActivity.ktFapp/src/main/kotlin/com/example/kpitrackerapp/KpiTrackerApplication.kt=app/src/main/kotlin/com/example/kpitrackerapp/MainActivity.ktOapp/src/main/kotlin/com/example/kpitrackerapp/adapters/AdminDashboardAdapter.ktLapp/src/main/kotlin/com/example/kpitrackerapp/adapters/ChatMessageAdapter.ktMapp/src/main/kotlin/com/example/kpitrackerapp/adapters/ConversationAdapter.ktMapp/src/main/kotlin/com/example/kpitrackerapp/adapters/NotificationAdapter.ktKapp/src/main/kotlin/com/example/kpitrackerapp/adapters/UserFilterAdapter.ktIapp/src/main/kotlin/com/example/kpitrackerapp/adapters/UserListAdapter.ktJapp/src/main/kotlin/com/example/kpitrackerapp/fragments/AccountFragment.ktLapp/src/main/kotlin/com/example/kpitrackerapp/fragments/DashboardFragment.ktPapp/src/main/kotlin/com/example/kpitrackerapp/fragments/MainDashboardFragment.ktKapp/src/main/kotlin/com/example/kpitrackerapp/fragments/MessagesFragment.ktNapp/src/main/kotlin/com/example/kpitrackerapp/fragments/PerformanceFragment.ktJapp/src/main/kotlin/com/example/kpitrackerapp/models/AdminDashboardData.ktJapp/src/main/kotlin/com/example/kpitrackerapp/models/AdminDashboardItem.ktBapp/src/main/kotlin/com/example/kpitrackerapp/models/ChatModels.kt;app/src/main/kotlin/com/example/kpitrackerapp/models/Kpi.ktHapp/src/main/kotlin/com/example/kpitrackerapp/models/KpiProgressEntry.ktEapp/src/main/kotlin/com/example/kpitrackerapp/models/OcrResultItem.ktEapp/src/main/kotlin/com/example/kpitrackerapp/models/SmartListItem.kt?app/src/main/kotlin/com/example/kpitrackerapp/models/Subtask.kt<app/src/main/kotlin/com/example/kpitrackerapp/models/Task.ktEapp/src/main/kotlin/com/example/kpitrackerapp/models/TaskAnalytics.ktDapp/src/main/kotlin/com/example/kpitrackerapp/models/TaskCategory.kt<app/src/main/kotlin/com/example/kpitrackerapp/models/User.ktFapp/src/main/kotlin/com/example/kpitrackerapp/models/UserFilterItem.ktIapp/src/main/kotlin/com/example/kpitrackerapp/models/UserKpiAssignment.ktHapp/src/main/kotlin/com/example/kpitrackerapp/persistence/AppDatabase.ktDapp/src/main/kotlin/com/example/kpitrackerapp/persistence/ChatDao.ktGapp/src/main/kotlin/com/example/kpitrackerapp/persistence/Converters.ktCapp/src/main/kotlin/com/example/kpitrackerapp/persistence/KpiDao.ktPapp/src/main/kotlin/com/example/kpitrackerapp/persistence/KpiProgressEntryDao.ktGapp/src/main/kotlin/com/example/kpitrackerapp/persistence/SubtaskDao.ktLapp/src/main/kotlin/com/example/kpitrackerapp/persistence/TaskCategoryDao.ktDapp/src/main/kotlin/com/example/kpitrackerapp/persistence/TaskDao.ktDapp/src/main/kotlin/com/example/kpitrackerapp/persistence/UserDao.ktQapp/src/main/kotlin/com/example/kpitrackerapp/persistence/UserKpiAssignmentDao.ktLapp/src/main/kotlin/com/example/kpitrackerapp/repositories/ChatRepository.ktRapp/src/main/kotlin/com/example/kpitrackerapp/services/FirebaseMessagingService.ktOapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditAdvancedTaskActivity.ktFapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditKpiActivity.ktNapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditKpiOriginalActivity.ktQapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditProgressDialogFragment.ktLapp/src/main/kotlin/com/example/kpitrackerapp/ui/AutoSendSettingsActivity.ktCapp/src/main/kotlin/com/example/kpitrackerapp/ui/ChartMarkerView.kt@app/src/main/kotlin/com/example/kpitrackerapp/ui/ChatActivity.ktDapp/src/main/kotlin/com/example/kpitrackerapp/ui/ChatListActivity.ktHapp/src/main/kotlin/com/example/kpitrackerapp/ui/ColoredReportAdapter.ktHapp/src/main/kotlin/com/example/kpitrackerapp/ui/CompactReportAdapter.ktFapp/src/main/kotlin/com/example/kpitrackerapp/ui/CreateUserActivity.ktGapp/src/main/kotlin/com/example/kpitrackerapp/ui/EnhancedTaskAdapter.ktGapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExcelImportActivity.ktGapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExcelReviewActivity.ktFapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExcelReviewAdapter.ktLapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExpireManagementActivity.ktEapp/src/main/kotlin/com/example/kpitrackerapp/ui/KpiDetailActivity.ktBapp/src/main/kotlin/com/example/kpitrackerapp/ui/KpiListAdapter.ktKapp/src/main/kotlin/com/example/kpitrackerapp/ui/KpiProgressEntryAdapter.ktAapp/src/main/kotlin/com/example/kpitrackerapp/ui/LoginActivity.ktHapp/src/main/kotlin/com/example/kpitrackerapp/ui/ModernReportActivity.ktIapp/src/main/kotlin/com/example/kpitrackerapp/ui/NotificationsActivity.kt?app/src/main/kotlin/com/example/kpitrackerapp/ui/OcrActivity.ktEapp/src/main/kotlin/com/example/kpitrackerapp/ui/OcrReviewActivity.ktDapp/src/main/kotlin/com/example/kpitrackerapp/ui/OcrReviewAdapter.ktFapp/src/main/kotlin/com/example/kpitrackerapp/ui/RecentUsersAdapter.ktBapp/src/main/kotlin/com/example/kpitrackerapp/ui/ReportActivity.ktAapp/src/main/kotlin/com/example/kpitrackerapp/ui/ReportAdapter.ktNapp/src/main/kotlin/com/example/kpitrackerapp/ui/SearchEditProgressActivity.kt?app/src/main/kotlin/com/example/kpitrackerapp/ui/TaskAdapter.ktJapp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskManagementActivity.ktPapp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskReminderSettingsActivity.ktFapp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskReportActivity.ktEapp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskReportAdapter.ktHapp/src/main/kotlin/com/example/kpitrackerapp/ui/UnifiedReportAdapter.ktDapp/src/main/kotlin/com/example/kpitrackerapp/ui/UserFilterDialog.ktGapp/src/main/kotlin/com/example/kpitrackerapp/ui/UserKpiListActivity.ktFapp/src/main/kotlin/com/example/kpitrackerapp/ui/UserSummaryAdapter.ktCapp/src/main/kotlin/com/example/kpitrackerapp/ui/UserSummaryItem.ktDapp/src/main/kotlin/com/example/kpitrackerapp/util/ExcelDataCache.ktHapp/src/main/kotlin/com/example/kpitrackerapp/util/NotificationHelper.ktIapp/src/main/kotlin/com/example/kpitrackerapp/utils/AppDetectionHelper.ktHapp/src/main/kotlin/com/example/kpitrackerapp/utils/AutoSendScheduler.ktJapp/src/main/kotlin/com/example/kpitrackerapp/utils/CardAnimationHelper.ktHapp/src/main/kotlin/com/example/kpitrackerapp/utils/CardGestureHelper.ktEapp/src/main/kotlin/com/example/kpitrackerapp/utils/DragDropHelper.ktMapp/src/main/kotlin/com/example/kpitrackerapp/utils/EisenhowerMatrixHelper.ktMapp/src/main/kotlin/com/example/kpitrackerapp/utils/FirebaseMessageManager.ktFapp/src/main/kotlin/com/example/kpitrackerapp/utils/LanguageManager.ktJapp/src/main/kotlin/com/example/kpitrackerapp/utils/NotificationManager.ktKapp/src/main/kotlin/com/example/kpitrackerapp/utils/RecurringTaskManager.ktEapp/src/main/kotlin/com/example/kpitrackerapp/utils/SessionManager.ktCapp/src/main/kotlin/com/example/kpitrackerapp/utils/ThemeManager.ktIapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/ChatViewModel.ktPapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/ChatViewModelFactory.ktHapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/KpiViewModel.ktOapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/KpiViewModelFactory.ktKapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/KpiWithProgress.ktIapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/TaskViewModel.ktPapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/TaskViewModelFactory.ktOapp/src/main/kotlin/com/example/kpitrackerapp/workers/AutoReportSenderWorker.ktQapp/src/main/kotlin/com/example/kpitrackerapp/workers/ExpiryNotificationWorker.ktLapp/src/main/kotlin/com/example/kpitrackerapp/workers/RecurringTaskWorker.ktKapp/src/main/kotlin/com/example/kpitrackerapp/workers/TaskReminderWorker.ktNapp/src/main/kotlin/com/example/kpitrackerapp/adapters/SearchResultsAdapter.ktIapp/src/main/kotlin/com/example/kpitrackerapp/ui/DateConverterActivity.ktIapp/src/main/kotlin/com/example/kpitrackerapp/ui/ModernAddTaskActivity.ktIapp/src/main/kotlin/com/example/kpitrackerapp/ui/PomodoroTimerActivity.ktHapp/src/main/kotlin/com/example/kpitrackerapp/utils/AlarmClockManager.ktDapp/src/main/kotlin/com/example/kpitrackerapp/utils/HijriCalendar.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      