<dependencies>
  <compile
      roots=":@@:app::debug,androidx.test.ext:junit:1.1.5@aar,androidx.test.espresso:espresso-core:3.5.1@aar,androidx.databinding:viewbinding:8.9.1@aar,org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar,com.google.android.material:material:1.11.0@aar,androidx.appcompat:appcompat:1.6.1@aar,com.github.bumptech.glide:glide:4.16.0@aar,androidx.viewpager2:viewpager2:1.0.0@aar,com.google.android.gms:play-services-mlkit-text-recognition:19.0.0@aar,com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0@aar,com.google.mlkit:vision-common:17.3.0@aar,com.google.mlkit:common:18.8.0@aar,com.google.firebase:firebase-database-ktx:20.3.0@aar,com.google.firebase:firebase-database:20.3.0@aar,com.google.firebase:firebase-messaging-ktx:23.4.0@aar,com.google.firebase:firebase-messaging:23.4.0@aar,com.google.firebase:firebase-appcheck-interop:17.1.0@aar,com.google.firebase:firebase-database-collection:18.0.1@aar,com.google.android.gms:play-services-base:18.1.0@aar,com.google.firebase:firebase-analytics-ktx:21.5.0@aar,com.google.firebase:firebase-analytics:21.5.0@aar,com.google.android.gms:play-services-measurement-api:21.5.0@aar,com.google.firebase:firebase-installations:17.2.0@aar,com.google.firebase:firebase-common-ktx:20.4.2@aar,com.google.firebase:firebase-common:20.4.2@aar,com.google.mlkit:vision-interfaces:16.2.0@aar,com.google.firebase:firebase-auth-interop:20.0.0@aar,com.google.firebase:firebase-iid-interop:17.1.0@aar,com.google.android.gms:play-services-cloud-messaging:17.1.0@aar,androidx.room:room-common:2.6.1@jar,androidx.room:room-runtime:2.6.1@aar,androidx.room:room-ktx:2.6.1@aar,androidx.recyclerview:recyclerview:1.3.2@aar,androidx.recyclerview:recyclerview:1.3.2@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.transition:transition:1.2.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.activity:activity:1.8.2@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,com.google.android.gms:play-services-measurement:21.5.0@aar,com.google.android.gms:play-services-measurement-sdk:21.5.0@aar,com.google.android.gms:play-services-measurement-impl:21.5.0@aar,com.google.android.gms:play-services-stats:17.0.2@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.core:core:1.12.0@aar,androidx.core:core:1.12.0@aar,androidx.test:core:1.5.0@aar,androidx.work:work-runtime-ktx:2.9.0@aar,androidx.work:work-runtime:2.9.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar,androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.1@jar,com.google.firebase:firebase-installations-interop:17.1.1@aar,com.google.android.gms:play-services-tasks:18.0.2@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.android.gms:play-services-ads-identifier:18.0.0@aar,com.google.android.gms:play-services-measurement-sdk-api:21.5.0@aar,com.google.android.gms:play-services-measurement-base:21.5.0@aar,com.google.android.gms:play-services-basement:18.1.0@aar,androidx.fragment:fragment:1.6.2@aar,androidx.fragment:fragment:1.6.2@aar,androidx.fragment:fragment-ktx:1.6.2@aar,androidx.activity:activity-ktx:1.8.2@aar,androidx.core:core-ktx:1.12.0@aar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar,androidx.test:runner:1.5.2@aar,androidx.test.services:storage:1.4.2@aar,androidx.test:monitor:1.6.1@aar,androidx.test:annotation:1.0.1@aar,androidx.annotation:annotation-experimental:1.3.0@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.collection:collection-ktx:1.1.0@jar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.cardview:cardview:1.0.0@aar,androidx.sqlite:sqlite-framework:2.4.0@aar,androidx.sqlite:sqlite:2.4.0@aar,com.github.bumptech.glide:gifdecoder:4.16.0@aar,com.google.firebase:firebase-components:17.1.5@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,com.google.firebase:firebase-datatransport:18.1.7@aar,com.google.android.datatransport:transport-backend-cct:3.1.8@aar,com.google.android.datatransport:transport-runtime:3.1.8@aar,com.google.firebase:firebase-encoders-json:18.0.0@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.firebase:firebase-encoders:17.0.0@jar,androidx.interpolator:interpolator:1.0.0@aar,com.google.android.datatransport:transport-api:3.1.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation-jvm:1.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.22@jar,androidx.constraintlayout:constraintlayout:2.1.4@aar,com.github.PhilJay:MPAndroidChart:v3.1.0@aar,com.google.code.gson:gson:2.10.1@jar,com.github.dhaval2404:colorpicker:2.3@aar,org.jsoup:jsoup:1.17.2@jar,org.apache.poi:poi-ooxml:5.2.5@jar,org.apache.poi:poi:5.2.5@jar,org.apache.poi:poi-ooxml-lite:5.2.5@jar,org.apache.xmlbeans:xmlbeans:5.2.0@jar,org.apache.commons:commons-compress:1.26.0@jar,org.apache.commons:commons-collections4:4.4@jar,junit:junit:4.13.2@jar,org.hamcrest:hamcrest-integration:1.3@jar,org.hamcrest:hamcrest-library:1.3@jar,org.hamcrest:hamcrest-core:1.3@jar,org.jetbrains:annotations:23.0.0@jar,androidx.tracing:tracing:1.0.0@aar,com.google.guava:guava:31.1-android@jar,com.google.code.findbugs:jsr305:3.0.2@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,androidx.test.espresso:espresso-idling-resource:3.5.1@aar,com.squareup:javawriter:2.1.1@jar,com.google.firebase:firebase-annotations:16.2.0@jar,javax.inject:javax.inject:1@jar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,commons-codec:commons-codec:1.16.0@jar,org.apache.commons:commons-math3:3.6.1@jar,commons-io:commons-io:2.15.1@jar,com.zaxxer:SparseBitSet:1.3@jar,org.apache.logging.log4j:log4j-api:2.21.1@jar,com.github.virtuald:curvesapi:1.08@jar,org.apache.commons:commons-lang3:3.14.0@jar,com.github.bumptech.glide:disklrucache:4.16.0@jar,com.github.bumptech.glide:annotations:4.16.0@jar,androidx.exifinterface:exifinterface:1.3.6@aar,androidx.exifinterface:exifinterface:1.3.6@aar,androidx.startup:startup-runtime:1.1.1@aar,com.google.android.odml:image:1.0.0-beta1@aar,com.google.errorprone:error_prone_annotations:2.11.0@jar,com.google.guava:failureaccess:1.0.1@jar,org.checkerframework:checker-qual:3.12.0@jar,com.google.j2objc:j2objc-annotations:1.3@jar">
    <dependency
        name=":@@:app::debug"
        simpleName="artifacts::app"/>
    <dependency
        name="androidx.test.ext:junit:1.1.5@aar"
        simpleName="androidx.test.ext:junit"/>
    <dependency
        name="androidx.test.espresso:espresso-core:3.5.1@aar"
        simpleName="androidx.test.espresso:espresso-core"/>
    <dependency
        name="androidx.databinding:viewbinding:8.9.1@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="com.google.android.material:material:1.11.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.16.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="com.google.android.gms:play-services-mlkit-text-recognition:19.0.0@aar"
        simpleName="com.google.android.gms:play-services-mlkit-text-recognition"/>
    <dependency
        name="com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0@aar"
        simpleName="com.google.android.gms:play-services-mlkit-text-recognition-common"/>
    <dependency
        name="com.google.mlkit:vision-common:17.3.0@aar"
        simpleName="com.google.mlkit:vision-common"/>
    <dependency
        name="com.google.mlkit:common:18.8.0@aar"
        simpleName="com.google.mlkit:common"/>
    <dependency
        name="com.google.firebase:firebase-database-ktx:20.3.0@aar"
        simpleName="com.google.firebase:firebase-database-ktx"/>
    <dependency
        name="com.google.firebase:firebase-database:20.3.0@aar"
        simpleName="com.google.firebase:firebase-database"/>
    <dependency
        name="com.google.firebase:firebase-messaging-ktx:23.4.0@aar"
        simpleName="com.google.firebase:firebase-messaging-ktx"/>
    <dependency
        name="com.google.firebase:firebase-messaging:23.4.0@aar"
        simpleName="com.google.firebase:firebase-messaging"/>
    <dependency
        name="com.google.firebase:firebase-appcheck-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-appcheck-interop"/>
    <dependency
        name="com.google.firebase:firebase-database-collection:18.0.1@aar"
        simpleName="com.google.firebase:firebase-database-collection"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.firebase:firebase-analytics-ktx:21.5.0@aar"
        simpleName="com.google.firebase:firebase-analytics-ktx"/>
    <dependency
        name="com.google.firebase:firebase-analytics:21.5.0@aar"
        simpleName="com.google.firebase:firebase-analytics"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-api:21.5.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-api"/>
    <dependency
        name="com.google.firebase:firebase-installations:17.2.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:20.4.2@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-common:20.4.2@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="com.google.mlkit:vision-interfaces:16.2.0@aar"
        simpleName="com.google.mlkit:vision-interfaces"/>
    <dependency
        name="com.google.firebase:firebase-auth-interop:20.0.0@aar"
        simpleName="com.google.firebase:firebase-auth-interop"/>
    <dependency
        name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-iid-interop"/>
    <dependency
        name="com.google.android.gms:play-services-cloud-messaging:17.1.0@aar"
        simpleName="com.google.android.gms:play-services-cloud-messaging"/>
    <dependency
        name="androidx.room:room-common:2.6.1@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.room:room-runtime:2.6.1@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.room:room-ktx:2.6.1@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.3.2@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.transition:transition:1.2.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.activity:activity:1.8.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="com.google.android.gms:play-services-measurement:21.5.0@aar"
        simpleName="com.google.android.gms:play-services-measurement"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk:21.5.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-impl:21.5.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-impl"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.core:core:1.12.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.test:core:1.5.0@aar"
        simpleName="androidx.test:core"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.9.0@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="androidx.work:work-runtime:2.9.0@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:21.5.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:21.5.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.6.2@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.6.2@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.2@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.core:core-ktx:1.12.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="androidx.test:runner:1.5.2@aar"
        simpleName="androidx.test:runner"/>
    <dependency
        name="androidx.test.services:storage:1.4.2@aar"
        simpleName="androidx.test.services:storage"/>
    <dependency
        name="androidx.test:monitor:1.6.1@aar"
        simpleName="androidx.test:monitor"/>
    <dependency
        name="androidx.test:annotation:1.0.1@aar"
        simpleName="androidx.test:annotation"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.collection:collection-ktx:1.1.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="com.google.firebase:firebase-components:17.1.5@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="com.google.firebase:firebase-datatransport:18.1.7@aar"
        simpleName="com.google.firebase:firebase-datatransport"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.1.8@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.1.8@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.1.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.6.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="com.github.PhilJay:MPAndroidChart:v3.1.0@aar"
        simpleName="com.github.PhilJay:MPAndroidChart"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.github.dhaval2404:colorpicker:2.3@aar"
        simpleName="com.github.dhaval2404:colorpicker"/>
    <dependency
        name="org.jsoup:jsoup:1.17.2@jar"
        simpleName="org.jsoup:jsoup"/>
    <dependency
        name="org.apache.poi:poi-ooxml:5.2.5@jar"
        simpleName="org.apache.poi:poi-ooxml"/>
    <dependency
        name="org.apache.poi:poi:5.2.5@jar"
        simpleName="org.apache.poi:poi"/>
    <dependency
        name="org.apache.poi:poi-ooxml-lite:5.2.5@jar"
        simpleName="org.apache.poi:poi-ooxml-lite"/>
    <dependency
        name="org.apache.xmlbeans:xmlbeans:5.2.0@jar"
        simpleName="org.apache.xmlbeans:xmlbeans"/>
    <dependency
        name="org.apache.commons:commons-compress:1.26.0@jar"
        simpleName="org.apache.commons:commons-compress"/>
    <dependency
        name="org.apache.commons:commons-collections4:4.4@jar"
        simpleName="org.apache.commons:commons-collections4"/>
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="org.hamcrest:hamcrest-integration:1.3@jar"
        simpleName="org.hamcrest:hamcrest-integration"/>
    <dependency
        name="org.hamcrest:hamcrest-library:1.3@jar"
        simpleName="org.hamcrest:hamcrest-library"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="com.google.guava:guava:31.1-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.test.espresso:espresso-idling-resource:3.5.1@aar"
        simpleName="androidx.test.espresso:espresso-idling-resource"/>
    <dependency
        name="com.squareup:javawriter:2.1.1@jar"
        simpleName="com.squareup:javawriter"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="commons-codec:commons-codec:1.16.0@jar"
        simpleName="commons-codec:commons-codec"/>
    <dependency
        name="org.apache.commons:commons-math3:3.6.1@jar"
        simpleName="org.apache.commons:commons-math3"/>
    <dependency
        name="commons-io:commons-io:2.15.1@jar"
        simpleName="commons-io:commons-io"/>
    <dependency
        name="com.zaxxer:SparseBitSet:1.3@jar"
        simpleName="com.zaxxer:SparseBitSet"/>
    <dependency
        name="org.apache.logging.log4j:log4j-api:2.21.1@jar"
        simpleName="org.apache.logging.log4j:log4j-api"/>
    <dependency
        name="com.github.virtuald:curvesapi:1.08@jar"
        simpleName="com.github.virtuald:curvesapi"/>
    <dependency
        name="org.apache.commons:commons-lang3:3.14.0@jar"
        simpleName="org.apache.commons:commons-lang3"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.16.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.6@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="com.google.android.odml:image:1.0.0-beta1@aar"
        simpleName="com.google.android.odml:image"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.11.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="org.checkerframework:checker-qual:3.12.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:1.3@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
  </compile>
  <package
      roots="androidx.test.ext:junit:1.1.5@aar,androidx.test.espresso:espresso-core:3.5.1@aar,org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar,androidx.test:core:1.5.0@aar,androidx.test:runner:1.5.2@aar,androidx.test.services:storage:1.4.2@aar,androidx.test:monitor:1.6.1@aar,androidx.test:annotation:1.0.1@aar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.tracing:tracing:1.0.0@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.annotation:annotation-jvm:1.6.0@jar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar,androidx.annotation:annotation-experimental:1.3.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.22@jar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,javax.inject:javax.inject:1@jar,com.google.code.findbugs:jsr305:3.0.2@jar,junit:junit:4.13.2@jar,androidx.test.espresso:espresso-idling-resource:3.5.1@aar,com.squareup:javawriter:2.1.1@jar,org.hamcrest:hamcrest-integration:1.3@jar,org.hamcrest:hamcrest-library:1.3@jar,org.hamcrest:hamcrest-core:1.3@jar">
    <dependency
        name="androidx.test.ext:junit:1.1.5@aar"
        simpleName="androidx.test.ext:junit"/>
    <dependency
        name="androidx.test.espresso:espresso-core:3.5.1@aar"
        simpleName="androidx.test.espresso:espresso-core"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="androidx.test:core:1.5.0@aar"
        simpleName="androidx.test:core"/>
    <dependency
        name="androidx.test:runner:1.5.2@aar"
        simpleName="androidx.test:runner"/>
    <dependency
        name="androidx.test.services:storage:1.4.2@aar"
        simpleName="androidx.test.services:storage"/>
    <dependency
        name="androidx.test:monitor:1.6.1@aar"
        simpleName="androidx.test:monitor"/>
    <dependency
        name="androidx.test:annotation:1.0.1@aar"
        simpleName="androidx.test:annotation"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.6.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="androidx.test.espresso:espresso-idling-resource:3.5.1@aar"
        simpleName="androidx.test.espresso:espresso-idling-resource"/>
    <dependency
        name="com.squareup:javawriter:2.1.1@jar"
        simpleName="com.squareup:javawriter"/>
    <dependency
        name="org.hamcrest:hamcrest-integration:1.3@jar"
        simpleName="org.hamcrest:hamcrest-integration"/>
    <dependency
        name="org.hamcrest:hamcrest-library:1.3@jar"
        simpleName="org.hamcrest:hamcrest-library"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
  </package>
</dependencies>
