package com.example.kpitrackerapp.viewmodels

import com.example.kpitrackerapp.models.Kpi
import com.example.kpitrackerapp.models.KpiProgressEntry // Import KpiProgressEntry

// Data class to hold a KPI along with calculated progress values for different display needs
data class KpiWithProgress(
    val kpi: Kpi,
    val userId: String?, // Add the userId associated with this progress calculation, nullable for aggregated views
    val currentProgressValue: Double, // Overall total progress (used in Detail screen)
    val monthlyTargetValue: Double?,  // Monthly target (used in List screen)
    val currentMonthProgressValue: Double, // Progress sum for the *selected filter month* (used for monthly % indicator)
    val actualCurrentMonthProgressValue: Double, // Progress sum for the *actual current calendar month* (for card display)
    val monthlyPercentage: Int?,      // Calculated monthly percentage (based on selected filter month)
    val progressEntries: List<KpiProgressEntry> = emptyList(), // Add list of entries for trend calculation
    // --- New fields for additional analysis (based on actual current month) ---
    val remainingMonthlyValue: Double?,
    val remainingMonthlyPercentage: Int?,
    val remainingDaysInMonth: Int?,
    val requiredDailyRate: Double?,
    // Removed projectedMonthEndValue
    val daysSinceLastUpdate: Long?,      // Added for last update info
    // --- New fields for Current Period Target and Achievement ---
    val currentPeriodTarget: Double?,    // Target that should be achieved by current date
    val currentPeriodAchievement: Int?   // Percentage of current period target achieved
) {
    // Helper property to calculate ANNUAL percentage (used in Detail screen)
    val annualProgressPercentage: Int
        get() = if (kpi.annualTarget > 0) {
            // Use coerceIn(0, 1000) to allow >100% visually, but cap progress bar at 100
            ((currentProgressValue / kpi.annualTarget) * 100).toInt().coerceIn(0, 1000)
        } else {
            0
        }
}
