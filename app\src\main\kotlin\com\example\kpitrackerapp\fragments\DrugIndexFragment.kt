package com.example.kpitrackerapp.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.TextView
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.Fragment
import com.example.kpitrackerapp.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.net.HttpURLConnection
import java.net.URL

class DrugIndexFragment : Fragment() {

    private lateinit var searchEditText: EditText
    private lateinit var drugNameTextView: TextView
    private lateinit var usesTextView: TextView
    private lateinit var interactionsTextView: TextView
    private lateinit var sideEffectsTextView: TextView
    private lateinit var usageInstructionsTextView: TextView

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_drug_index, container, false)

        searchEditText = view.findViewById(R.id.searchEditText)
        drugNameTextView = view.findViewById(R.id.drugNameTextView)
        usesTextView = view.findViewById(R.id.usesTextView)
        interactionsTextView = view.findViewById(R.id.interactionsTextView)
        sideEffectsTextView = view.findViewById(R.id.sideEffectsTextView)
        usageInstructionsTextView = view.findViewById(R.id.usageInstructionsTextView)

        searchEditText.addTextChangedListener {
            val query = it.toString().trim()
            if (query.isNotEmpty()) {
                searchDrug(query)
            }
        }

        return view
    }

    private fun searchDrug(query: String) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val url = URL("https://api.fda.gov/drug/label.json?search=openfda.brand_name:\"$query\"+OR+openfda.generic_name:\"$query\"&limit=1")
                val urlConnection = url.openConnection() as HttpURLConnection
                val inputStream = urlConnection.inputStream
                val response = inputStream.bufferedReader().use { it.readText() }

                val jsonObject = JSONObject(response)
                val results = jsonObject.getJSONArray("results")

                if (results.length() > 0) {
                    val drugInfo = results.getJSONObject(0)
                    val openfda = drugInfo.optJSONObject("openfda")
                    val brandName = openfda?.optJSONArray("brand_name")?.optString(0) ?: "N/A"
                    val genericName = openfda?.optJSONArray("generic_name")?.optString(0) ?: "N/A"

                    val uses = drugInfo.optJSONArray("indications_and_usage")?.optString(0) ?: "N/A"
                    val interactions = drugInfo.optJSONArray("drug_interactions")?.optString(0) ?: "N/A"
                    val sideEffects = drugInfo.optJSONArray("adverse_reactions")?.optString(0) ?: "N/A"
                    val usageInstructions = drugInfo.optJSONArray("dosage_and_administration")?.optString(0) ?: "N/A"

                    withContext(Dispatchers.Main) {
                        drugNameTextView.text = "$brandName ($genericName)"
                        usesTextView.text = formatTextAsNumberedList(uses)
                        interactionsTextView.text = formatTextAsNumberedList(interactions)
                        sideEffectsTextView.text = formatTextAsNumberedList(sideEffects)
                        usageInstructionsTextView.text = formatTextAsNumberedList(usageInstructions)
                        drugNameTextView.visibility = View.VISIBLE
                    }
                } else {
                    withContext(Dispatchers.Main) {
                        drugNameTextView.visibility = View.GONE
                        usesTextView.text = "No information found."
                        interactionsTextView.text = "-"
                        sideEffectsTextView.text = "-"
                        usageInstructionsTextView.text = "-"
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                withContext(Dispatchers.Main) {
                    drugNameTextView.visibility = View.GONE
                    usesTextView.text = "Error fetching data."
                    interactionsTextView.text = "-"
                    sideEffectsTextView.text = "-"
                    usageInstructionsTextView.text = "-"
                }
            }
        }
    }

    private fun formatTextAsNumberedList(text: String): String {
        if (text == "N/A" || text.isBlank()) return text

        // Clean and prepare text
        val cleanText = text.replace(Regex("\\s+"), " ").trim()

        // Extract key information based on content type
        val keyPoints = mutableListOf<String>()

        // For drug interactions - extract specific drug names and effects
        if (cleanText.contains("drug interactions", ignoreCase = true)) {
            val drugNames = extractDrugNames(cleanText)
            val effects = extractEffects(cleanText)

            drugNames.forEach { drug ->
                keyPoints.add("Interacts with $drug")
            }
            effects.forEach { effect ->
                keyPoints.add(effect)
            }
        }
        // For indications/uses - extract conditions
        else if (cleanText.contains("indicated", ignoreCase = true) || cleanText.contains("treatment", ignoreCase = true)) {
            val conditions = extractConditions(cleanText)
            conditions.forEach { condition ->
                keyPoints.add("Treats $condition")
            }
        }
        // For side effects - extract specific effects
        else if (cleanText.contains("adverse", ignoreCase = true) || cleanText.contains("side effects", ignoreCase = true)) {
            val sideEffects = extractSideEffects(cleanText)
            sideEffects.forEach { effect ->
                keyPoints.add("May cause $effect")
            }
        }
        // For dosage - extract key dosing info
        else if (cleanText.contains("dosage", ignoreCase = true) || cleanText.contains("administration", ignoreCase = true)) {
            val dosageInfo = extractDosageInfo(cleanText)
            dosageInfo.forEach { info ->
                keyPoints.add(info)
            }
        }

        // If no specific extraction worked, use general approach
        if (keyPoints.isEmpty()) {
            val sentences = cleanText.split(Regex("[.!?]"))
                .map { it.trim() }
                .filter { it.length in 10..80 } // Short, meaningful sentences
                .take(4)

            keyPoints.addAll(sentences)
        }

        if (keyPoints.isEmpty()) return "📝 No specific information available"

        // Format as concise numbered list
        return keyPoints.take(4).mapIndexed { index, point ->
            val cleanPoint = point.trim()
                .replaceFirstChar { if (it.isLowerCase()) it.titlecase() else it.toString() }
                .let { if (!it.endsWith(".")) "$it." else it }

            "${index + 1}. $cleanPoint"
        }.joinToString("\n")
    }

    private fun extractDrugNames(text: String): List<String> {
        val drugPattern = Regex("\\b[A-Z][a-z]+(?:ine|ol|ide|ate|cin|zole|pril|sartan|statin)\\b")
        return drugPattern.findAll(text).map { it.value }.distinct().take(3).toList()
    }

    private fun extractConditions(text: String): List<String> {
        val conditions = listOf(
            "GERD", "gastroesophageal reflux", "erosive esophagitis", "ulcer",
            "hypertension", "diabetes", "infection", "pain", "inflammation"
        )
        return conditions.filter { text.contains(it, ignoreCase = true) }.take(3)
    }

    private fun extractEffects(text: String): List<String> {
        val effects = listOf(
            "increased toxicity", "reduced effectiveness", "drug resistance",
            "contraindicated", "monitor closely", "avoid concomitant use"
        )
        return effects.filter { text.contains(it, ignoreCase = true) }.take(2)
    }

    private fun extractSideEffects(text: String): List<String> {
        val sideEffects = listOf(
            "nausea", "headache", "diarrhea", "dizziness", "rash",
            "fatigue", "abdominal pain", "vomiting", "constipation"
        )
        return sideEffects.filter { text.contains(it, ignoreCase = true) }.take(3)
    }

    private fun extractDosageInfo(text: String): List<String> {
        val dosagePattern = Regex("\\d+\\s*mg|\\d+\\s*times|once daily|twice daily|with food|without food")
        return dosagePattern.findAll(text).map { it.value }.distinct().take(3).toList()
    }
}
