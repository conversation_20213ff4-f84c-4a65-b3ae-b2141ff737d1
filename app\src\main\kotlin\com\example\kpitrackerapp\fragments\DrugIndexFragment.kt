package com.example.kpitrackerapp.fragments

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.AutoCompleteTextView
import android.widget.EditText
import android.widget.ListView
import android.widget.TextView
import android.widget.Toast
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.Fragment
import com.example.kpitrackerapp.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import java.net.HttpURLConnection
import java.net.URL
import java.net.URLEncoder

class DrugIndexFragment : Fragment() {

    private lateinit var searchEditText: EditText
    private lateinit var drugNameTextView: TextView
    private lateinit var usesTextView: TextView
    private lateinit var interactionsTextView: TextView
    private lateinit var sideEffectsTextView: TextView
    private lateinit var usageInstructionsTextView: TextView
    private lateinit var searchResultsListView: ListView

    private var isTranslated = false
    private var originalTexts = mutableMapOf<String, String>()
    private var searchResults = mutableListOf<DrugSearchResult>()
    private lateinit var searchAdapter: ArrayAdapter<String>

    // Map of active ingredients to their most common brand names/forms
    private val popularDrugForms = mapOf(
        "pantoprazole" to listOf("Protonix", "Pantoprazole Sodium"),
        "metformin" to listOf("Glucophage", "Metformin HCl"),
        "atorvastatin" to listOf("Lipitor", "Atorvastatin Calcium"),
        "lisinopril" to listOf("Prinivil", "Zestril"),
        "amlodipine" to listOf("Norvasc", "Amlodipine Besylate"),
        "omeprazole" to listOf("Prilosec", "Omeprazole"),
        "simvastatin" to listOf("Zocor", "Simvastatin"),
        "losartan" to listOf("Cozaar", "Losartan Potassium"),
        "hydrochlorothiazide" to listOf("Microzide", "HCTZ"),
        "levothyroxine" to listOf("Synthroid", "Levoxyl"),
        "azithromycin" to listOf("Zithromax", "Z-Pak"),
        "amoxicillin" to listOf("Amoxil", "Trimox"),
        "ibuprofen" to listOf("Advil", "Motrin"),
        "acetaminophen" to listOf("Tylenol", "Paracetamol"),
        "aspirin" to listOf("Bayer", "Aspirin"),
        "povidone" to listOf("Betadine", "Povidone-Iodine"),
        "insulin" to listOf("Humalog", "Novolog", "Lantus"),
        "warfarin" to listOf("Coumadin", "Jantoven"),
        "furosemide" to listOf("Lasix", "Furosemide"),
        "prednisone" to listOf("Deltasone", "Prednisone"),
        "gabapentin" to listOf("Neurontin", "Gabapentin"),
        "tramadol" to listOf("Ultram", "Tramadol HCl"),
        "sertraline" to listOf("Zoloft", "Sertraline HCl"),
        "fluoxetine" to listOf("Prozac", "Fluoxetine HCl"),
        "citalopram" to listOf("Celexa", "Citalopram HBr")
    )

    data class DrugSearchResult(
        val brandName: String,
        val genericName: String,
        val manufacturer: String = "",
        val source: String = "FDA",
        val priority: Int = 0  // Higher number = higher priority
    )

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_drug_index, container, false)

        searchEditText = view.findViewById(R.id.searchEditText)
        drugNameTextView = view.findViewById(R.id.drugNameTextView)
        usesTextView = view.findViewById(R.id.usesTextView)
        interactionsTextView = view.findViewById(R.id.interactionsTextView)
        sideEffectsTextView = view.findViewById(R.id.sideEffectsTextView)
        usageInstructionsTextView = view.findViewById(R.id.usageInstructionsTextView)
        searchResultsListView = view.findViewById(R.id.searchResultsListView)

        // Setup search adapter
        searchAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_list_item_1, mutableListOf<String>())
        searchResultsListView.adapter = searchAdapter

        searchEditText.addTextChangedListener {
            val query = it.toString().trim()
            if (query.length >= 2) {
                searchDrugs(query)
            } else {
                hideSearchResults()
            }
        }

        // Handle search result selection
        searchResultsListView.setOnItemClickListener { _, _, position, _ ->
            if (position < searchResults.size) {
                val selectedDrug = searchResults[position]
                selectDrug(selectedDrug)
            }
        }

        // Add long click listeners for copy functionality
        setupLongClickListeners()

        return view
    }

    private fun searchDrugs(query: String) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val results = mutableListOf<DrugSearchResult>()

                // First, add popular forms if searching by active ingredient
                results.addAll(getPopularFormsForActiveIngredient(query))

                // Search multiple sources (ordered by quality)
                results.addAll(searchSaudiFDA(query))      // 🇸🇦 Highest Priority: SFDA Saudi
                results.addAll(searchLocalDatabase(query)) // Fallback: Local database

                withContext(Dispatchers.Main) {
                    searchResults.clear()
                    // Sort by priority (higher first), then remove duplicates
                    val sortedResults = results
                        .distinctBy { "${it.brandName.lowercase()}-${it.genericName.lowercase()}" }
                        .sortedByDescending { it.priority }

                    searchResults.addAll(sortedResults)

                    if (searchResults.isNotEmpty()) {
                        showSearchResults()
                    } else {
                        hideSearchResults()
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                withContext(Dispatchers.Main) {
                    hideSearchResults()
                }
            }
        }
    }

    private fun getPopularFormsForActiveIngredient(query: String): List<DrugSearchResult> {
        val results = mutableListOf<DrugSearchResult>()
        val queryLower = query.lowercase().trim()

        // Check if query matches any active ingredient or brand name
        popularDrugForms.forEach { (activeIngredient, brandNames) ->
            if (activeIngredient.contains(queryLower) || queryLower.contains(activeIngredient) || brandNames.any { it.lowercase().contains(queryLower) }) {
                brandNames.forEachIndexed { index, brandName ->
                    results.add(
                        DrugSearchResult(
                            brandName = brandName,
                            genericName = activeIngredient.replaceFirstChar { it.uppercase() },
                            manufacturer = "Popular Form",
                            source = "Popular",
                            priority = 100 - index  // First brand gets highest priority
                        )
                    )
                }
            }
        }

        return results
    }

    private suspend fun searchDailyMed(query: String): List<DrugSearchResult> {
        return try {
            val encodedQuery = URLEncoder.encode(query, "UTF-8")
            // DailyMed API for official drug labels from NIH
            val url = URL("https://dailymed.nlm.nih.gov/dailymed/services/v2/spls.json?drug_name=$encodedQuery")
            val urlConnection = url.openConnection() as HttpURLConnection
            urlConnection.setRequestProperty("User-Agent", "DrugSearchApp/1.0")
            val response = urlConnection.inputStream.bufferedReader().use { it.readText() }

            val jsonObject = JSONObject(response)
            val data = jsonObject.optJSONArray("data")
            val drugResults = mutableListOf<DrugSearchResult>()

            data?.let { results ->
                for (i in 0 until minOf(results.length(), 8)) {
                    val drugInfo = results.getJSONObject(i)
                    val title = drugInfo.optString("title", "")
                    val author = drugInfo.optString("author", "")

                    if (title.isNotEmpty()) {
                        // Extract brand and generic names from title
                        val parts = title.split("-", "(", ")")
                        val brandName = parts[0].trim()
                        val genericName = if (parts.size > 1) parts[1].trim() else brandName

                        drugResults.add(DrugSearchResult(brandName, genericName, author, "DailyMed", 90))
                    }
                }
            }

            drugResults
        } catch (e: Exception) {
            emptyList()
        }
    }

    private suspend fun searchSaudiFDA(query: String): List<DrugSearchResult> {
        return try {
            val drugResults = mutableListOf<DrugSearchResult>()

            // Search using the SDI API approach
            val searchResults = searchSDIDatabase(query)
            searchResults.forEach { result ->
                drugResults.add(
                    DrugSearchResult(
                        brandName = result.brandName,
                        genericName = result.genericName,
                        manufacturer = result.manufacturer,
                        source = "🇸🇦 SFDA",
                        priority = 150  // Highest priority for Saudi source
                    )
                )
            }

            drugResults
        } catch (e: Exception) {
            e.printStackTrace()
            emptyList()
        }
    }

    private suspend fun searchSDIDatabase(query: String): List<SDISearchResult> {
        return try {
            val results = mutableListOf<SDISearchResult>()
            val encodedQuery = URLEncoder.encode(query, "UTF-8")

            // Try multiple search approaches
            // 1. Search by drug name
            val searchUrl = "https://sdi.sfda.gov.sa/Home/DrugSearch"
            val doc: Document = Jsoup.connect(searchUrl)
                .data("searchTerm", query)
                .userAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                .timeout(10000)
                .post()

            // Parse search results (this is a simplified approach)
            val drugElements = doc.select(".drug-result, .search-result, .drug-item")

            drugElements.forEach { element ->
                try {
                    val brandName = element.select(".brand-name, .trade-name, h3, h4").text()
                    val genericName = element.select(".generic-name, .scientific-name, .active-ingredient").text()
                    val manufacturer = element.select(".manufacturer, .company").text()

                    if (brandName.isNotEmpty() || genericName.isNotEmpty()) {
                        results.add(
                            SDISearchResult(
                                brandName = brandName.ifEmpty { genericName },
                                genericName = genericName.ifEmpty { brandName },
                                manufacturer = manufacturer
                            )
                        )
                    }
                } catch (e: Exception) {
                    // Continue with next element if parsing fails
                }
            }

            // If no results from web scraping, use fallback local Saudi drugs database
            if (results.isEmpty()) {
                results.addAll(searchSaudiLocalDatabase(query))
            }

            results.take(10) // Limit to 10 results
        } catch (e: Exception) {
            e.printStackTrace()
            // Fallback to local Saudi database
            searchSaudiLocalDatabase(query)
        }
    }

    private fun searchSaudiLocalDatabase(query: String): List<SDISearchResult> {
        val saudiDrugs = mapOf(
            // Common Saudi registered drugs
            "pantoprazole" to listOf(
                SDISearchResult("Paralok", "Pantoprazole", "Jazeera Pharmaceutical Industries"),
                SDISearchResult("Pantoprazole Hikma", "Pantoprazole", "Hikma Pharmaceuticals"),
                SDISearchResult("Controloc", "Pantoprazole", "Takeda")
            ),
            "metformin" to listOf(
                SDISearchResult("Glucophage", "Metformin", "Merck Serono"),
                SDISearchResult("Diabex", "Metformin", "Pfizer"),
                SDISearchResult("Metformin Hikma", "Metformin", "Hikma Pharmaceuticals")
            ),
            "atorvastatin" to listOf(
                SDISearchResult("Lipitor", "Atorvastatin", "Pfizer"),
                SDISearchResult("Atorvastatin Hikma", "Atorvastatin", "Hikma Pharmaceuticals"),
                SDISearchResult("Sortis", "Atorvastatin", "Pfizer")
            ),
            "omeprazole" to listOf(
                SDISearchResult("Losec", "Omeprazole", "AstraZeneca"),
                SDISearchResult("Omeprazole Hikma", "Omeprazole", "Hikma Pharmaceuticals"),
                SDISearchResult("Gastroloc", "Omeprazole", "Jazeera Pharmaceutical")
            ),
            "amlodipine" to listOf(
                SDISearchResult("Norvasc", "Amlodipine", "Pfizer"),
                SDISearchResult("Amlodipine Hikma", "Amlodipine", "Hikma Pharmaceuticals"),
                SDISearchResult("Amlocard", "Amlodipine", "Tabuk Pharmaceutical")
            ),
            "simvastatin" to listOf(
                SDISearchResult("Zocor", "Simvastatin", "Merck"),
                SDISearchResult("Simvastatin Hikma", "Simvastatin", "Hikma Pharmaceuticals")
            ),
            "lisinopril" to listOf(
                SDISearchResult("Prinivil", "Lisinopril", "Merck"),
                SDISearchResult("Zestril", "Lisinopril", "AstraZeneca")
            ),
            "azithromycin" to listOf(
                SDISearchResult("Zithromax", "Azithromycin", "Pfizer"),
                SDISearchResult("Azithromycin Hikma", "Azithromycin", "Hikma Pharmaceuticals")
            ),
            "amoxicillin" to listOf(
                SDISearchResult("Amoxil", "Amoxicillin", "GSK"),
                SDISearchResult("Amoxicillin Hikma", "Amoxicillin", "Hikma Pharmaceuticals")
            ),
            "paracetamol" to listOf(
                SDISearchResult("Panadol", "Paracetamol", "GSK"),
                SDISearchResult("Fevadol", "Paracetamol", "Spimaco"),
                SDISearchResult("Tylenol", "Paracetamol", "Johnson & Johnson")
            ),
            "ibuprofen" to listOf(
                SDISearchResult("Brufen", "Ibuprofen", "Abbott"),
                SDISearchResult("Advil", "Ibuprofen", "Pfizer")
            ),
            "povidone" to listOf(
                SDISearchResult("Betadine", "Povidone Iodine", "Mundipharma"),
                SDISearchResult("Povidone Iodine Hikma", "Povidone Iodine", "Hikma Pharmaceuticals")
            )
        )

        val results = mutableListOf<SDISearchResult>()
        val lowerQuery = query.lowercase()

        saudiDrugs.forEach { (generic, drugs) ->
            if (generic.contains(lowerQuery) || drugs.any {
                it.brandName.lowercase().contains(lowerQuery) ||
                it.genericName.lowercase().contains(lowerQuery)
            }) {
                results.addAll(drugs)
            }
        }

        return results
    }

    data class SDISearchResult(
        val brandName: String,
        val genericName: String,
        val manufacturer: String
    )

    private suspend fun searchFDA(query: String): List<DrugSearchResult> {
        return try {
            val encodedQuery = URLEncoder.encode(query, "UTF-8")
            val url = URL("https://api.fda.gov/drug/label.json?search=openfda.brand_name:*$encodedQuery*+OR+openfda.generic_name:*$encodedQuery*&limit=10")
            val urlConnection = url.openConnection() as HttpURLConnection
            val response = urlConnection.inputStream.bufferedReader().use { it.readText() }

            val jsonObject = JSONObject(response)
            val results = jsonObject.getJSONArray("results")
            val drugResults = mutableListOf<DrugSearchResult>()

            for (i in 0 until results.length()) {
                val drugInfo = results.getJSONObject(i)
                val openfda = drugInfo.optJSONObject("openfda")

                val brandNames = openfda?.optJSONArray("brand_name")
                val genericNames = openfda?.optJSONArray("generic_name")
                val manufacturers = openfda?.optJSONArray("manufacturer_name")

                // Add all brand names
                brandNames?.let { brands ->
                    for (j in 0 until brands.length()) {
                        val brandName = brands.optString(j)
                        val genericName = genericNames?.optString(0) ?: ""
                        val manufacturer = manufacturers?.optString(0) ?: ""

                        if (brandName.isNotEmpty()) {
                            drugResults.add(DrugSearchResult(brandName, genericName, manufacturer, "FDA", 80))
                        }
                    }
                }

                // Add generic names if different from brand names
                genericNames?.let { generics ->
                    for (j in 0 until generics.length()) {
                        val genericName = generics.optString(j)
                        val manufacturer = manufacturers?.optString(0) ?: ""

                        if (genericName.isNotEmpty()) {
                            drugResults.add(DrugSearchResult(genericName, genericName, manufacturer, "FDA", 75))
                        }
                    }
                }
            }

            drugResults
        } catch (e: Exception) {
            emptyList()
        }
    }

    private suspend fun searchRxNorm(query: String): List<DrugSearchResult> {
        return try {
            val encodedQuery = URLEncoder.encode(query, "UTF-8")
            val url = URL("https://rxnav.nlm.nih.gov/REST/drugs.json?name=$encodedQuery")
            val urlConnection = url.openConnection() as HttpURLConnection
            val response = urlConnection.inputStream.bufferedReader().use { it.readText() }

            val jsonObject = JSONObject(response)
            val drugGroup = jsonObject.optJSONObject("drugGroup")
            val conceptGroup = drugGroup?.optJSONArray("conceptGroup")
            val drugResults = mutableListOf<DrugSearchResult>()

            conceptGroup?.let { groups ->
                for (i in 0 until groups.length()) {
                    val group = groups.getJSONObject(i)
                    val conceptProperties = group.optJSONArray("conceptProperties")

                    conceptProperties?.let { properties ->
                        for (j in 0 until properties.length()) {
                            val property = properties.getJSONObject(j)
                            val name = property.optString("name")
                            val synonym = property.optString("synonym", "")

                            if (name.isNotEmpty()) {
                                drugResults.add(DrugSearchResult(name, synonym, "", "RxNorm", 70))
                            }
                        }
                    }
                }
            }

            drugResults
        } catch (e: Exception) {
            emptyList()
        }
    }

    private suspend fun searchDrugsAPI(query: String): List<DrugSearchResult> {
        return try {
            val encodedQuery = URLEncoder.encode(query, "UTF-8")
            // Alternative API for comprehensive drug information
            val url = URL("https://api.fda.gov/drug/ndc.json?search=brand_name:*$encodedQuery*+OR+generic_name:*$encodedQuery*&limit=8")
            val urlConnection = url.openConnection() as HttpURLConnection
            val response = urlConnection.inputStream.bufferedReader().use { it.readText() }

            val jsonObject = JSONObject(response)
            val results = jsonObject.optJSONArray("results")
            val drugResults = mutableListOf<DrugSearchResult>()

            results?.let { resultArray ->
                for (i in 0 until resultArray.length()) {
                    val drugInfo = resultArray.getJSONObject(i)
                    val brandName = drugInfo.optString("brand_name", "")
                    val genericName = drugInfo.optString("generic_name", "")
                    val manufacturer = drugInfo.optString("labeler_name", "")

                    if (brandName.isNotEmpty() || genericName.isNotEmpty()) {
                        val finalBrand = if (brandName.isNotEmpty()) brandName else genericName
                        val finalGeneric = if (genericName.isNotEmpty()) genericName else brandName
                        drugResults.add(DrugSearchResult(finalBrand, finalGeneric, manufacturer, "NDC", 60))
                    }
                }
            }

            drugResults
        } catch (e: Exception) {
            emptyList()
        }
    }

    private suspend fun searchLocalDatabase(query: String): List<DrugSearchResult> {
        // DrugBank requires API key, so we'll use a simple fallback search
        return try {
            val commonDrugs = mapOf(
                // Diabetes medications
                "metformin" to listOf("Metformin", "Glucophage", "Fortamet", "Glumetza"),
                "insulin" to listOf("Insulin", "Humulin", "Novolin", "Lantus", "Humalog"),
                "glipizide" to listOf("Glipizide", "Glucotrol"),
                "sitagliptin" to listOf("Sitagliptin", "Januvia"),

                // Cardiovascular medications
                "atorvastatin" to listOf("Atorvastatin", "Lipitor"),
                "simvastatin" to listOf("Simvastatin", "Zocor"),
                "amlodipine" to listOf("Amlodipine", "Norvasc"),
                "lisinopril" to listOf("Lisinopril", "Prinivil", "Zestril"),
                "losartan" to listOf("Losartan", "Cozaar"),
                "metoprolol" to listOf("Metoprolol", "Lopressor", "Toprol-XL"),
                "warfarin" to listOf("Warfarin", "Coumadin", "Jantoven"),
                "clopidogrel" to listOf("Clopidogrel", "Plavix"),

                // Gastrointestinal medications
                "omeprazole" to listOf("Omeprazole", "Prilosec", "Losec"),
                "pantoprazole" to listOf("Pantoprazole", "Protonix"),
                "esomeprazole" to listOf("Esomeprazole", "Nexium"),
                "lansoprazole" to listOf("Lansoprazole", "Prevacid"),

                // Antibiotics
                "azithromycin" to listOf("Azithromycin", "Zithromax", "Z-Pak"),
                "amoxicillin" to listOf("Amoxicillin", "Amoxil", "Trimox"),
                "ciprofloxacin" to listOf("Ciprofloxacin", "Cipro"),
                "doxycycline" to listOf("Doxycycline", "Vibramycin"),
                "cephalexin" to listOf("Cephalexin", "Keflex"),

                // Respiratory medications
                "albuterol" to listOf("Albuterol", "ProAir", "Ventolin", "Proventil"),
                "montelukast" to listOf("Montelukast", "Singulair"),
                "fluticasone" to listOf("Fluticasone", "Flonase", "Flovent"),

                // Diuretics
                "hydrochlorothiazide" to listOf("Hydrochlorothiazide", "Microzide", "HCTZ"),
                "furosemide" to listOf("Furosemide", "Lasix"),
                "spironolactone" to listOf("Spironolactone", "Aldactone"),

                // Neurological medications
                "gabapentin" to listOf("Gabapentin", "Neurontin"),
                "pregabalin" to listOf("Pregabalin", "Lyrica"),
                "topiramate" to listOf("Topiramate", "Topamax"),

                // Psychiatric medications
                "sertraline" to listOf("Sertraline", "Zoloft"),
                "escitalopram" to listOf("Escitalopram", "Lexapro"),
                "fluoxetine" to listOf("Fluoxetine", "Prozac"),
                "alprazolam" to listOf("Alprazolam", "Xanax"),
                "lorazepam" to listOf("Lorazepam", "Ativan"),

                // Pain medications
                "tramadol" to listOf("Tramadol", "Ultram"),
                "ibuprofen" to listOf("Ibuprofen", "Advil", "Motrin"),
                "acetaminophen" to listOf("Acetaminophen", "Tylenol"),
                "naproxen" to listOf("Naproxen", "Aleve", "Naprosyn"),

                // Hormonal medications
                "levothyroxine" to listOf("Levothyroxine", "Synthroid", "Levoxyl"),
                "prednisone" to listOf("Prednisone", "Deltasone", "Rayos"),
                "prednisolone" to listOf("Prednisolone", "Prelone"),

                // Other common medications
                "vitamin" to listOf("Vitamin D", "Vitamin B12", "Multivitamin"),
                "aspirin" to listOf("Aspirin", "Bayer", "Ecotrin"),
                "diphenhydramine" to listOf("Diphenhydramine", "Benadryl"),
                "cetirizine" to listOf("Cetirizine", "Zyrtec"),
                "loratadine" to listOf("Loratadine", "Claritin")
            )

            val drugResults = mutableListOf<DrugSearchResult>()
            val lowerQuery = query.lowercase()

            commonDrugs.forEach { (generic, brands) ->
                if (generic.contains(lowerQuery) || brands.any { it.lowercase().contains(lowerQuery) }) {
                    brands.forEach { brand ->
                        drugResults.add(DrugSearchResult(brand, generic, "", "Local", 50))
                    }
                }
            }

            drugResults
        } catch (e: Exception) {
            emptyList()
        }
    }

    private fun showSearchResults() {
        val displayList = searchResults.map { result ->
            val priorityIcon = when {
                result.source.contains("🇸🇦 SFDA") -> "🇸🇦 "
                result.source == "Popular" -> "⭐ "
                result.priority >= 90 -> "🥇 "
                result.priority >= 80 -> "🥈 "
                result.priority >= 70 -> "🥉 "
                else -> ""
            }

            if (result.brandName != result.genericName && result.genericName.isNotEmpty()) {
                "$priorityIcon${result.brandName} (${result.genericName}) - ${result.source}"
            } else {
                "$priorityIcon${result.brandName} - ${result.source}"
            }
        }

        searchAdapter.clear()
        searchAdapter.addAll(displayList)
        searchAdapter.notifyDataSetChanged()
        searchResultsListView.visibility = View.VISIBLE
    }

    private fun hideSearchResults() {
        searchResultsListView.visibility = View.GONE
        searchAdapter.clear()
        searchAdapter.notifyDataSetChanged()
    }

    private fun selectDrug(drug: DrugSearchResult) {
        hideSearchResults()
        searchEditText.setText("${drug.brandName} (${drug.genericName})")
        loadDrugDetails(drug)
    }

    private fun loadDrugDetails(drug: DrugSearchResult) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                var drugInfo: DrugDetailInfo? = null

                // Try multiple sources in order of preference
                if (drug.source.contains("🇸🇦 SFDA")) {
                    drugInfo = loadFromSaudiFDA(drug)
                }
                if (drugInfo == null) drugInfo = loadFromLocalKnowledge(drug)

                withContext(Dispatchers.Main) {
                    drugNameTextView.text = "${drug.brandName} (${drug.genericName})"

                    if (drugInfo != null) {
                        usesTextView.text = formatTextAsNumberedList(drugInfo.uses)
                        interactionsTextView.text = formatTextAsNumberedList(drugInfo.interactions)
                        sideEffectsTextView.text = formatTextAsNumberedList(drugInfo.sideEffects)
                        usageInstructionsTextView.text = formatTextAsNumberedList(drugInfo.usageInstructions)
                    } else {
                        usesTextView.text = "No detailed information available"
                        interactionsTextView.text = "No detailed information available"
                        sideEffectsTextView.text = "No detailed information available"
                        usageInstructionsTextView.text = "No detailed information available"
                    }
                    drugNameTextView.visibility = View.VISIBLE
                }
            } catch (e: Exception) {
                e.printStackTrace()
                withContext(Dispatchers.Main) {
                    drugNameTextView.text = "${drug.brandName} (${drug.genericName})"
                    usesTextView.text = "Error loading information"
                    interactionsTextView.text = "Error loading information"
                    sideEffectsTextView.text = "Error loading information"
                    usageInstructionsTextView.text = "Error loading information"
                    drugNameTextView.visibility = View.VISIBLE
                }
            }
        }
    }

    data class DrugDetailInfo(
        val uses: String,
        val interactions: String,
        val sideEffects: String,
        val usageInstructions: String
    )

    private suspend fun loadFromSaudiFDA(drug: DrugSearchResult): DrugDetailInfo? {
        return try {
            // Try to get detailed information from SDI
            val drugId = findDrugIdInSDI(drug)
            if (drugId != null) {
                loadDetailedInfoFromSDI(drugId)
            } else {
                // Fallback to local Saudi drug knowledge
                loadSaudiLocalDrugInfo(drug)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            loadSaudiLocalDrugInfo(drug)
        }
    }

    private suspend fun findDrugIdInSDI(drug: DrugSearchResult): String? {
        return try {
            // This would require reverse engineering the SDI search to get drug IDs
            // For now, we'll use a simplified approach with known drug IDs
            val knownDrugIds = mapOf(
                "paralok" to "8573",
                "pantoprazole" to "8573",
                "glucophage" to "12773",
                "metformin" to "12773"
            )

            val searchKey = drug.brandName.lowercase()
            knownDrugIds[searchKey] ?: knownDrugIds[drug.genericName.lowercase()]
        } catch (e: Exception) {
            null
        }
    }

    private suspend fun loadDetailedInfoFromSDI(drugId: String): DrugDetailInfo? {
        return try {
            val url = "https://sdi.sfda.gov.sa/home/<USER>"
            val doc: Document = Jsoup.connect(url)
                .userAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                .timeout(10000)
                .get()

            // Extract information from the page
            val uses = extractSectionFromSDI(doc, "What this product is and what it is used for", "دواعي الاستعمال")
            val sideEffects = extractSectionFromSDI(doc, "Possible side effects", "الآثار الجانبية")
            val interactions = extractSectionFromSDI(doc, "Other medicines", "التفاعلات الدوائية")
            val usage = extractSectionFromSDI(doc, "How to use", "طريقة الاستعمال")

            DrugDetailInfo(
                uses = uses.ifEmpty { "معلومات الاستخدام متوفرة في المصدر السعودي" },
                interactions = interactions.ifEmpty { "معلومات التفاعلات متوفرة في المصدر السعودي" },
                sideEffects = sideEffects.ifEmpty { "معلومات الآثار الجانبية متوفرة في المصدر السعودي" },
                usageInstructions = usage.ifEmpty { "تعليمات الاستخدام متوفرة في المصدر السعودي" }
            )
        } catch (e: Exception) {
            null
        }
    }

    private fun extractSectionFromSDI(doc: Document, englishHeader: String, arabicHeader: String): String {
        return try {
            // Try to find sections by headers
            val sections = doc.select("h1, h2, h3, h4, h5, h6, strong, b")
            var content = ""

            sections.forEach { header ->
                val headerText = header.text()
                if (headerText.contains(englishHeader, ignoreCase = true) ||
                    headerText.contains(arabicHeader, ignoreCase = true)) {

                    // Get the content after this header
                    var nextElement = header.nextElementSibling()
                    val contentBuilder = StringBuilder()

                    while (nextElement != null && !nextElement.tagName().matches(Regex("h[1-6]"))) {
                        if (nextElement.tagName() in listOf("p", "div", "ul", "ol", "li")) {
                            contentBuilder.append(nextElement.text()).append("\n")
                        }
                        nextElement = nextElement.nextElementSibling()
                    }

                    content = contentBuilder.toString().trim()
                    if (content.isNotEmpty()) return content
                }
            }

            content
        } catch (e: Exception) {
            ""
        }
    }

    private fun loadSaudiLocalDrugInfo(drug: DrugSearchResult): DrugDetailInfo? {
        val saudiDrugInfo = mapOf(
            "pantoprazole" to DrugDetailInfo(
                uses = "1. علاج التهاب المريء الارتجاعي\n2. علاج قرحة المعدة والاثنا عشر\n3. علاج متلازمة زولينجر إليسون\n4. تقليل إفراز أحماض المعدة",
                interactions = "1. تجنب مع أتازانافير\n2. مراقبة مع الوارفارين\n3. تجنب مع الكيتوكونازول\n4. مراقبة مع الميثوتريكسات",
                sideEffects = "1. صداع ودوخة\n2. إسهال أو إمساك\n3. غثيان وقيء\n4. ألم في البطن\n5. طفح جلدي نادر",
                usageInstructions = "1. يؤخذ قبل الطعام بـ 30-60 دقيقة\n2. الجرعة المعتادة 40 ملغم يومياً\n3. لا يُكسر أو يُمضغ القرص\n4. يُبلع كاملاً مع الماء"
            ),
            "metformin" to DrugDetailInfo(
                uses = "1. علاج السكري من النوع الثاني\n2. تحسين حساسية الأنسولين\n3. تقليل إنتاج الجلوكوز من الكبد\n4. مساعدة في إنقاص الوزن",
                interactions = "1. تجنب مع الكحول\n2. مراقبة مع مدرات البول\n3. تجنب مع أدوية التباين\n4. مراقبة مع الكورتيزون",
                sideEffects = "1. اضطرابات معوية\n2. غثيان وقيء\n3. إسهال\n4. طعم معدني في الفم\n5. نقص فيتامين ب12 نادر",
                usageInstructions = "1. يؤخذ مع الطعام\n2. البدء بجرعة منخفضة\n3. زيادة تدريجية حسب الحاجة\n4. مراقبة وظائف الكلى دورياً"
            ),
            "paracetamol" to DrugDetailInfo(
                uses = "1. تسكين الألم الخفيف إلى المتوسط\n2. خفض الحرارة\n3. علاج الصداع\n4. تسكين آلام العضلات والمفاصل",
                interactions = "1. تجنب مع الكحول\n2. مراقبة مع الوارفارين\n3. تجنب الجرعات العالية مع أدوية الكبد",
                sideEffects = "1. آمن عند الاستخدام الصحيح\n2. تلف الكبد عند الجرعة الزائدة\n3. طفح جلدي نادر\n4. اضطرابات دموية نادرة جداً",
                usageInstructions = "1. الجرعة القصوى 4 جرام يومياً\n2. كل 4-6 ساعات حسب الحاجة\n3. يمكن أخذه مع أو بدون طعام\n4. تجنب الجرعة الزائدة"
            )
        )

        val genericName = drug.genericName.lowercase()
        return saudiDrugInfo[genericName] ?: saudiDrugInfo.entries.find {
            it.key.contains(genericName) || genericName.contains(it.key)
        }?.value
    }

    private suspend fun loadFromDailyMed(drug: DrugSearchResult): DrugDetailInfo? {
        return try {
            val encodedQuery = URLEncoder.encode(drug.genericName.ifEmpty { drug.brandName }, "UTF-8")
            val url = URL("https://dailymed.nlm.nih.gov/dailymed/services/v2/spls.json?drug_name=$encodedQuery&limit=1")
            val urlConnection = url.openConnection() as HttpURLConnection
            urlConnection.setRequestProperty("User-Agent", "DrugSearchApp/1.0")
            val response = urlConnection.inputStream.bufferedReader().use { it.readText() }

            val jsonObject = JSONObject(response)
            val data = jsonObject.optJSONArray("data")

            if (data != null && data.length() > 0) {
                val drugData = data.getJSONObject(0)
                val setId = drugData.optString("setid")

                if (setId.isNotEmpty()) {
                    // Get detailed information using setid
                    val detailUrl = URL("https://dailymed.nlm.nih.gov/dailymed/services/v2/spls/$setId.json")
                    val detailConnection = detailUrl.openConnection() as HttpURLConnection
                    detailConnection.setRequestProperty("User-Agent", "DrugSearchApp/1.0")
                    val detailResponse = detailConnection.inputStream.bufferedReader().use { it.readText() }

                    val detailJson = JSONObject(detailResponse)
                    val data2 = detailJson.optJSONArray("data")

                    if (data2 != null && data2.length() > 0) {
                        val detail = data2.getJSONObject(0)

                        val uses = detail.optString("indications_and_usage", "")
                        val interactions = detail.optString("drug_interactions", "")
                        val sideEffects = detail.optString("adverse_reactions", "")
                        val dosage = detail.optString("dosage_and_administration", "")

                        return DrugDetailInfo(
                            uses = uses.ifEmpty { "No specific information available" },
                            interactions = interactions.ifEmpty { "No specific information available" },
                            sideEffects = sideEffects.ifEmpty { "No specific information available" },
                            usageInstructions = dosage.ifEmpty { "No specific information available" }
                        )
                    }
                }
            }
            null
        } catch (e: Exception) {
            null
        }
    }

    private suspend fun loadFromFDA(drug: DrugSearchResult): DrugDetailInfo? {
        return try {
            val encodedBrand = URLEncoder.encode(drug.brandName, "UTF-8")
            val encodedGeneric = URLEncoder.encode(drug.genericName, "UTF-8")
            val url = URL("https://api.fda.gov/drug/label.json?search=openfda.brand_name:\"$encodedBrand\"+OR+openfda.generic_name:\"$encodedGeneric\"&limit=1")
            val urlConnection = url.openConnection() as HttpURLConnection
            val response = urlConnection.inputStream.bufferedReader().use { it.readText() }

            val jsonObject = JSONObject(response)
            val results = jsonObject.getJSONArray("results")

            if (results.length() > 0) {
                val drugInfo = results.getJSONObject(0)
                val uses = drugInfo.optJSONArray("indications_and_usage")?.optString(0) ?: ""
                val interactions = drugInfo.optJSONArray("drug_interactions")?.optString(0) ?: ""
                val sideEffects = drugInfo.optJSONArray("adverse_reactions")?.optString(0) ?: ""
                val usageInstructions = drugInfo.optJSONArray("dosage_and_administration")?.optString(0) ?: ""

                return DrugDetailInfo(
                    uses = uses.ifEmpty { "No specific information available" },
                    interactions = interactions.ifEmpty { "No specific information available" },
                    sideEffects = sideEffects.ifEmpty { "No specific information available" },
                    usageInstructions = usageInstructions.ifEmpty { "No specific information available" }
                )
            }
            null
        } catch (e: Exception) {
            null
        }
    }

    private fun loadFromLocalKnowledge(drug: DrugSearchResult): DrugDetailInfo? {
        val genericName = drug.genericName.lowercase()
        val brandName = drug.brandName.lowercase()

        // Comprehensive local drug knowledge base
        val drugKnowledge = mapOf(
            "povidone-iodine" to DrugDetailInfo(
                uses = "Antiseptic for minor cuts, scrapes, and burns. Prevents infection in wounds. Used for skin disinfection before medical procedures.",
                interactions = "May interact with hydrogen peroxide. Avoid use with other antiseptics simultaneously. May affect thyroid function tests.",
                sideEffects = "May cause skin irritation, redness, or allergic reactions. Rarely causes iodine toxicity with prolonged use.",
                usageInstructions = "Apply to affected area 1-3 times daily. Clean wound before application. For external use only. Do not use for more than 7 days without consulting doctor."
            ),
            "povidone" to DrugDetailInfo(
                uses = "Antiseptic for minor cuts, scrapes, and burns. Prevents infection in wounds. Used for skin disinfection before medical procedures.",
                interactions = "May interact with hydrogen peroxide. Avoid use with other antiseptics simultaneously. May affect thyroid function tests.",
                sideEffects = "May cause skin irritation, redness, or allergic reactions. Rarely causes iodine toxicity with prolonged use.",
                usageInstructions = "Apply to affected area 1-3 times daily. Clean wound before application. For external use only. Do not use for more than 7 days without consulting doctor."
            ),
            "betadine" to DrugDetailInfo(
                uses = "Antiseptic for minor cuts, scrapes, and burns. Prevents infection in wounds. Used for skin disinfection before medical procedures.",
                interactions = "May interact with hydrogen peroxide. Avoid use with other antiseptics simultaneously. May affect thyroid function tests.",
                sideEffects = "May cause skin irritation, redness, or allergic reactions. Rarely causes iodine toxicity with prolonged use.",
                usageInstructions = "Apply to affected area 1-3 times daily. Clean wound before application. For external use only. Do not use for more than 7 days without consulting doctor."
            ),
            "metformin" to DrugDetailInfo(
                uses = "Treats type 2 diabetes. Helps control blood sugar levels. May be used for polycystic ovary syndrome (PCOS).",
                interactions = "Interacts with alcohol, contrast dyes, and certain antibiotics. May interact with diuretics and steroids.",
                sideEffects = "May cause nausea, diarrhea, stomach upset, metallic taste. Rarely causes lactic acidosis.",
                usageInstructions = "Take with meals to reduce stomach upset. Usually taken twice daily. Start with low dose and increase gradually."
            ),
            "omeprazole" to DrugDetailInfo(
                uses = "Treats gastroesophageal reflux disease (GERD). Treats stomach ulcers. Reduces stomach acid production.",
                interactions = "Interacts with warfarin, clopidogrel, and certain antifungals. May affect absorption of vitamin B12 and magnesium.",
                sideEffects = "May cause headache, nausea, diarrhea, stomach pain. Long-term use may increase infection risk.",
                usageInstructions = "Take before meals, preferably in the morning. Swallow whole, do not crush or chew. Usually taken once daily."
            ),
            "atorvastatin" to DrugDetailInfo(
                uses = "Treats high cholesterol. Reduces risk of heart disease and stroke. Lowers LDL (bad) cholesterol.",
                interactions = "Interacts with grapefruit juice, certain antibiotics, and antifungals. May interact with warfarin.",
                sideEffects = "May cause muscle pain, liver problems, digestive issues. Rarely causes severe muscle breakdown.",
                usageInstructions = "Take once daily, with or without food. Usually taken in the evening. Avoid grapefruit juice."
            ),
            "lisinopril" to DrugDetailInfo(
                uses = "Treats high blood pressure. Treats heart failure. Protects kidneys in diabetic patients.",
                interactions = "Interacts with potassium supplements, salt substitutes, and certain diuretics. May interact with NSAIDs.",
                sideEffects = "May cause dry cough, dizziness, high potassium levels. Rarely causes severe allergic reactions.",
                usageInstructions = "Take once daily, with or without food. Usually taken at the same time each day. Monitor blood pressure regularly."
            ),
            "amlodipine" to DrugDetailInfo(
                uses = "Treats high blood pressure. Treats chest pain (angina). Improves blood flow to the heart.",
                interactions = "Interacts with grapefruit juice and certain antifungals. May interact with other blood pressure medications.",
                sideEffects = "May cause swelling of ankles, dizziness, flushing. May cause fatigue or palpitations.",
                usageInstructions = "Take once daily, with or without food. Usually taken at the same time each day. Avoid grapefruit juice."
            ),
            "azithromycin" to DrugDetailInfo(
                uses = "Treats bacterial infections. Treats respiratory tract infections. Treats skin and soft tissue infections.",
                interactions = "Interacts with warfarin, digoxin, and certain antacids. May interact with other antibiotics.",
                sideEffects = "May cause nausea, diarrhea, stomach pain. May cause changes in heart rhythm.",
                usageInstructions = "Take on empty stomach or with food. Complete full course even if feeling better. Usually taken once daily."
            ),
            "ibuprofen" to DrugDetailInfo(
                uses = "Treats pain and inflammation. Reduces fever. Treats headaches, muscle aches, and arthritis.",
                interactions = "Interacts with warfarin, aspirin, and blood pressure medications. May interact with lithium.",
                sideEffects = "May cause stomach upset, heartburn, dizziness. May increase risk of heart problems with long-term use.",
                usageInstructions = "Take with food or milk to reduce stomach upset. Do not exceed recommended dose. Use for shortest time needed."
            )
        )

        // Try to find match by generic name first, then brand name
        return drugKnowledge[genericName] ?: drugKnowledge[brandName] ?:
               drugKnowledge.entries.find {
                   genericName.contains(it.key) || brandName.contains(it.key) ||
                   it.key.contains(genericName) || it.key.contains(brandName)
               }?.value
    }

    private fun formatTextAsNumberedList(text: String): String {
        if (text == "N/A" || text.isBlank()) return text

        // Clean and prepare text
        val cleanText = text.replace(Regex("\\s+"), " ").trim()

        // Extract key information based on content type
        val keyPoints = mutableListOf<String>()

        // For drug interactions - extract specific drug names and effects
        if (cleanText.contains("drug interactions", ignoreCase = true) ||
            cleanText.contains("interactions", ignoreCase = true)) {
            keyPoints.addAll(extractInteractionInfo(cleanText))
        }
        // For indications/uses - extract conditions
        else if (cleanText.contains("indicated", ignoreCase = true) ||
                 cleanText.contains("treatment", ignoreCase = true) ||
                 cleanText.contains("used for", ignoreCase = true) ||
                 cleanText.contains("treats", ignoreCase = true)) {
            keyPoints.addAll(extractUsageInfo(cleanText))
        }
        // For side effects - extract specific effects
        else if (cleanText.contains("adverse", ignoreCase = true) ||
                 cleanText.contains("side effects", ignoreCase = true) ||
                 cleanText.contains("reactions", ignoreCase = true)) {
            keyPoints.addAll(extractSideEffectsInfo(cleanText))
        }
        // For dosage/administration - extract timing and method info
        else if (cleanText.contains("dosage", ignoreCase = true) ||
                 cleanText.contains("administration", ignoreCase = true) ||
                 cleanText.contains("take", ignoreCase = true) ||
                 cleanText.contains("dose", ignoreCase = true)) {
            keyPoints.addAll(extractAdministrationInfo(cleanText))
        }

        // If no specific extraction worked, use intelligent general approach
        if (keyPoints.isEmpty()) {
            keyPoints.addAll(extractGeneralInfo(cleanText))
        }

        if (keyPoints.isEmpty()) return "No specific information available"

        // Format as concise numbered list
        return keyPoints.take(4).mapIndexed { index, point ->
            val cleanPoint = point.trim()
                .replaceFirstChar { if (it.isLowerCase()) it.titlecase() else it.toString() }
                .let { if (!it.endsWith(".") && !it.endsWith("!") && !it.endsWith("?")) "$it." else it }

            "${index + 1}. $cleanPoint"
        }.joinToString("\n")
    }

    private fun extractInteractionInfo(text: String): List<String> {
        val interactions = mutableListOf<String>()

        // Extract drug names
        val drugPattern = Regex("\\b[A-Z][a-z]+(?:ine|ol|ide|ate|cin|zole|pril|sartan|statin|mycin|cillin)\\b")
        val drugs = drugPattern.findAll(text).map { it.value }.distinct().take(3).toList()
        drugs.forEach { drug -> interactions.add("Interacts with $drug") }

        // Extract interaction effects
        val effects = listOf(
            "contraindicated", "avoid concomitant use", "monitor closely",
            "increased risk", "reduced effectiveness", "drug resistance"
        )
        effects.forEach { effect ->
            if (text.contains(effect, ignoreCase = true)) {
                interactions.add(effect.replaceFirstChar { it.titlecase() })
            }
        }

        return interactions.take(3)
    }

    private fun extractUsageInfo(text: String): List<String> {
        val uses = mutableListOf<String>()

        // Common conditions
        val conditions = listOf(
            "GERD", "gastroesophageal reflux", "erosive esophagitis", "peptic ulcer",
            "ulcer", "hypertension", "diabetes", "infection", "pain", "inflammation",
            "heartburn", "acid reflux", "depression", "anxiety", "bacterial infection"
        )

        conditions.forEach { condition ->
            if (text.contains(condition, ignoreCase = true)) {
                uses.add("Treats $condition")
            }
        }

        return uses.take(3)
    }

    private fun extractSideEffectsInfo(text: String): List<String> {
        val sideEffects = mutableListOf<String>()

        val effects = listOf(
            "nausea", "headache", "diarrhea", "dizziness", "rash", "fatigue",
            "abdominal pain", "vomiting", "constipation", "drowsiness", "dry mouth",
            "insomnia", "weight gain", "blurred vision"
        )

        effects.forEach { effect ->
            if (text.contains(effect, ignoreCase = true)) {
                sideEffects.add("May cause $effect")
            }
        }

        return sideEffects.take(3)
    }

    private fun extractAdministrationInfo(text: String): List<String> {
        val adminInfo = mutableListOf<String>()

        // Timing patterns
        val timingPatterns = listOf(
            "before meals", "after meals", "with food", "without food", "on empty stomach",
            "once daily", "twice daily", "three times daily", "at bedtime", "in the morning"
        )

        timingPatterns.forEach { pattern ->
            if (text.contains(pattern, ignoreCase = true)) {
                adminInfo.add(pattern.replaceFirstChar { it.titlecase() })
            }
        }

        // Dosage patterns
        val dosagePattern = Regex("(\\d+)\\s*(mg|g|ml)")
        val dosages = dosagePattern.findAll(text).map { "${it.groupValues[1]} ${it.groupValues[2]}" }.distinct().take(2)
        dosages.forEach { dose -> adminInfo.add("Dosage: $dose") }

        return adminInfo.take(3)
    }

    private fun extractGeneralInfo(text: String): List<String> {
        val info = mutableListOf<String>()

        // Split into meaningful sentences
        val sentences = text.split(Regex("[.!?]"))
            .map { it.trim() }
            .filter { it.length in 15..100 && !it.contains("see", ignoreCase = true) }
            .take(4)

        // If we have good sentences, use them
        if (sentences.isNotEmpty()) {
            info.addAll(sentences)
        } else {
            // Fallback: split by common separators
            val parts = text.split(Regex("[,;:]"))
                .map { it.trim() }
                .filter { it.length in 10..80 }
                .take(3)
            info.addAll(parts)
        }

        return info.take(3)
    }

    private fun setupLongClickListeners() {
        val textViews = listOf(
            usesTextView to "Uses",
            interactionsTextView to "Interactions",
            sideEffectsTextView to "Side Effects",
            usageInstructionsTextView to "Usage Instructions"
        )

        textViews.forEach { (textView, label) ->
            textView.setOnLongClickListener {
                showContextMenu(textView, label)
                true
            }
        }
    }

    private fun showContextMenu(textView: TextView, label: String) {
        val options = arrayOf("Copy Text", "Translate to Arabic")

        val builder = android.app.AlertDialog.Builder(requireContext())
        builder.setTitle("Options for $label")
        builder.setItems(options) { _, which ->
            when (which) {
                0 -> copyToClipboard(textView.text.toString(), label)
                1 -> translateText(textView, label)
            }
        }
        builder.show()
    }

    private fun copyToClipboard(text: String, label: String) {
        val clipboard = requireContext().getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText(label, text)
        clipboard.setPrimaryClip(clip)
        Toast.makeText(requireContext(), "$label copied to clipboard", Toast.LENGTH_SHORT).show()
    }

    private fun translateText(textView: TextView, label: String) {
        val currentText = textView.text.toString()

        if (isTranslated) {
            // Restore original text
            originalTexts[label]?.let { originalText ->
                textView.text = originalText
                Toast.makeText(requireContext(), "Restored to English", Toast.LENGTH_SHORT).show()
            }
        } else {
            // Store original text and translate
            originalTexts[label] = currentText
            val translatedText = translateToArabic(currentText)
            textView.text = translatedText
            Toast.makeText(requireContext(), "Translated to Arabic", Toast.LENGTH_SHORT).show()
        }

        isTranslated = !isTranslated
    }

    private fun translateToArabic(englishText: String): String {
        // Simple translation mapping for common drug information terms
        val translations = mapOf(
            // Uses/Indications
            "Treats GERD" to "يعالج ارتجاع المريء",
            "Treats gastroesophageal reflux" to "يعالج ارتجاع المعدة والمريء",
            "Treats erosive esophagitis" to "يعالج التهاب المريء التآكلي",
            "Treats peptic ulcer" to "يعالج قرحة المعدة",
            "Treats ulcer" to "يعالج القرحة",
            "Treats hypertension" to "يعالج ارتفاع ضغط الدم",
            "Treats diabetes" to "يعالج السكري",
            "Treats infection" to "يعالج العدوى",
            "Treats pain" to "يعالج الألم",
            "Treats inflammation" to "يعالج الالتهاب",
            "Treats heartburn" to "يعالج حرقة المعدة",
            "Treats acid reflux" to "يعالج حموضة المعدة",

            // Side Effects
            "May cause nausea" to "قد يسبب غثيان",
            "May cause headache" to "قد يسبب صداع",
            "May cause diarrhea" to "قد يسبب إسهال",
            "May cause dizziness" to "قد يسبب دوخة",
            "May cause rash" to "قد يسبب طفح جلدي",
            "May cause fatigue" to "قد يسبب تعب",
            "May cause abdominal pain" to "قد يسبب ألم البطن",
            "May cause vomiting" to "قد يسبب قيء",
            "May cause constipation" to "قد يسبب إمساك",
            "May cause drowsiness" to "قد يسبب نعاس",
            "May cause dry mouth" to "قد يسبب جفاف الفم",

            // Administration
            "Before meals" to "قبل الوجبات",
            "After meals" to "بعد الوجبات",
            "With food" to "مع الطعام",
            "Without food" to "بدون طعام",
            "On empty stomach" to "على معدة فارغة",
            "Once daily" to "مرة واحدة يومياً",
            "Twice daily" to "مرتين يومياً",
            "Three times daily" to "ثلاث مرات يومياً",
            "At bedtime" to "قبل النوم",
            "In the morning" to "في الصباح",
            "Dosage:" to "الجرعة:",

            // Interactions
            "Interacts with" to "يتفاعل مع",
            "Contraindicated" to "ممنوع الاستخدام معاً",
            "Avoid concomitant use" to "تجنب الاستخدام المتزامن",
            "Monitor closely" to "يتطلب مراقبة طبية",
            "Increased risk" to "يزيد المخاطر",
            "Reduced effectiveness" to "يقلل الفعالية",
            "Drug resistance" to "مقاومة الدواء",

            // General
            "No specific information available" to "لا توجد معلومات محددة متاحة"
        )

        var translatedText = englishText

        // Apply translations
        translations.forEach { (english, arabic) ->
            translatedText = translatedText.replace(english, arabic, ignoreCase = true)
        }

        // Translate numbers in numbered lists
        translatedText = translatedText.replace(Regex("(\\d+)\\.")) { matchResult ->
            "${matchResult.groupValues[1]}."
        }

        return translatedText
    }
}
