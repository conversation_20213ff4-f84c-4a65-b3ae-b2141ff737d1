package com.example.kpitrackerapp.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.TextView
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.Fragment
import com.example.kpitrackerapp.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.net.HttpURLConnection
import java.net.URL

class DrugIndexFragment : Fragment() {

    private lateinit var searchEditText: EditText
    private lateinit var drugNameTextView: TextView
    private lateinit var usesTextView: TextView
    private lateinit var interactionsTextView: TextView
    private lateinit var sideEffectsTextView: TextView
    private lateinit var usageInstructionsTextView: TextView

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_drug_index, container, false)

        searchEditText = view.findViewById(R.id.searchEditText)
        drugNameTextView = view.findViewById(R.id.drugNameTextView)
        usesTextView = view.findViewById(R.id.usesTextView)
        interactionsTextView = view.findViewById(R.id.interactionsTextView)
        sideEffectsTextView = view.findViewById(R.id.sideEffectsTextView)
        usageInstructionsTextView = view.findViewById(R.id.usageInstructionsTextView)

        searchEditText.addTextChangedListener {
            val query = it.toString().trim()
            if (query.isNotEmpty()) {
                searchDrug(query)
            }
        }

        return view
    }

    private fun searchDrug(query: String) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val url = URL("https://api.fda.gov/drug/label.json?search=openfda.brand_name:\"$query\"+OR+openfda.generic_name:\"$query\"&limit=1")
                val urlConnection = url.openConnection() as HttpURLConnection
                val inputStream = urlConnection.inputStream
                val response = inputStream.bufferedReader().use { it.readText() }

                val jsonObject = JSONObject(response)
                val results = jsonObject.getJSONArray("results")

                if (results.length() > 0) {
                    val drugInfo = results.getJSONObject(0)
                    val openfda = drugInfo.optJSONObject("openfda")
                    val brandName = openfda?.optJSONArray("brand_name")?.optString(0) ?: "N/A"
                    val genericName = openfda?.optJSONArray("generic_name")?.optString(0) ?: "N/A"

                    val uses = drugInfo.optJSONArray("indications_and_usage")?.optString(0) ?: "N/A"
                    val interactions = drugInfo.optJSONArray("drug_interactions")?.optString(0) ?: "N/A"
                    val sideEffects = drugInfo.optJSONArray("adverse_reactions")?.optString(0) ?: "N/A"
                    val usageInstructions = drugInfo.optJSONArray("dosage_and_administration")?.optString(0) ?: "N/A"

                    withContext(Dispatchers.Main) {
                        drugNameTextView.text = "$brandName ($genericName)"
                        usesTextView.text = uses
                        interactionsTextView.text = interactions
                        sideEffectsTextView.text = sideEffects
                        usageInstructionsTextView.text = usageInstructions
                        drugNameTextView.visibility = View.VISIBLE
                    }
                } else {
                    withContext(Dispatchers.Main) {
                        drugNameTextView.visibility = View.GONE
                        usesTextView.text = "No information found."
                        interactionsTextView.text = "-"
                        sideEffectsTextView.text = "-"
                        usageInstructionsTextView.text = "-"
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                withContext(Dispatchers.Main) {
                    drugNameTextView.visibility = View.GONE
                    usesTextView.text = "Error fetching data."
                    interactionsTextView.text = "-"
                    sideEffectsTextView.text = "-"
                    usageInstructionsTextView.text = "-"
                }
            }
        }
    }
}
