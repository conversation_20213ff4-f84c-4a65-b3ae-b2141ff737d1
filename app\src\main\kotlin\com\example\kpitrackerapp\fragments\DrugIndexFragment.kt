package com.example.kpitrackerapp.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.TextView
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.Fragment
import com.example.kpitrackerapp.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.net.HttpURLConnection
import java.net.URL

class DrugIndexFragment : Fragment() {

    private lateinit var searchEditText: EditText
    private lateinit var drugNameTextView: TextView
    private lateinit var usesTextView: TextView
    private lateinit var interactionsTextView: TextView
    private lateinit var sideEffectsTextView: TextView
    private lateinit var usageInstructionsTextView: TextView

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_drug_index, container, false)

        searchEditText = view.findViewById(R.id.searchEditText)
        drugNameTextView = view.findViewById(R.id.drugNameTextView)
        usesTextView = view.findViewById(R.id.usesTextView)
        interactionsTextView = view.findViewById(R.id.interactionsTextView)
        sideEffectsTextView = view.findViewById(R.id.sideEffectsTextView)
        usageInstructionsTextView = view.findViewById(R.id.usageInstructionsTextView)

        searchEditText.addTextChangedListener {
            val query = it.toString().trim()
            if (query.isNotEmpty()) {
                searchDrug(query)
            }
        }

        return view
    }

    private fun searchDrug(query: String) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val url = URL("https://api.fda.gov/drug/label.json?search=openfda.brand_name:\"$query\"+OR+openfda.generic_name:\"$query\"&limit=1")
                val urlConnection = url.openConnection() as HttpURLConnection
                val inputStream = urlConnection.inputStream
                val response = inputStream.bufferedReader().use { it.readText() }

                val jsonObject = JSONObject(response)
                val results = jsonObject.getJSONArray("results")

                if (results.length() > 0) {
                    val drugInfo = results.getJSONObject(0)
                    val openfda = drugInfo.optJSONObject("openfda")
                    val brandName = openfda?.optJSONArray("brand_name")?.optString(0) ?: "N/A"
                    val genericName = openfda?.optJSONArray("generic_name")?.optString(0) ?: "N/A"

                    val uses = drugInfo.optJSONArray("indications_and_usage")?.optString(0) ?: "N/A"
                    val interactions = drugInfo.optJSONArray("drug_interactions")?.optString(0) ?: "N/A"
                    val sideEffects = drugInfo.optJSONArray("adverse_reactions")?.optString(0) ?: "N/A"
                    val usageInstructions = drugInfo.optJSONArray("dosage_and_administration")?.optString(0) ?: "N/A"

                    withContext(Dispatchers.Main) {
                        drugNameTextView.text = "$brandName ($genericName)"
                        usesTextView.text = formatTextAsNumberedList(uses)
                        interactionsTextView.text = formatTextAsNumberedList(interactions)
                        sideEffectsTextView.text = formatTextAsNumberedList(sideEffects)
                        usageInstructionsTextView.text = formatTextAsNumberedList(usageInstructions)
                        drugNameTextView.visibility = View.VISIBLE
                    }
                } else {
                    withContext(Dispatchers.Main) {
                        drugNameTextView.visibility = View.GONE
                        usesTextView.text = "No information found."
                        interactionsTextView.text = "-"
                        sideEffectsTextView.text = "-"
                        usageInstructionsTextView.text = "-"
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                withContext(Dispatchers.Main) {
                    drugNameTextView.visibility = View.GONE
                    usesTextView.text = "Error fetching data."
                    interactionsTextView.text = "-"
                    sideEffectsTextView.text = "-"
                    usageInstructionsTextView.text = "-"
                }
            }
        }
    }

    private fun formatTextAsNumberedList(text: String): String {
        if (text == "N/A" || text.isBlank()) return text

        // Clean and prepare text
        val cleanText = text.replace(Regex("\\s+"), " ").trim()

        // Extract key information based on content type
        val keyPoints = mutableListOf<String>()

        // For drug interactions - extract specific drug names and effects
        if (cleanText.contains("drug interactions", ignoreCase = true) ||
            cleanText.contains("interactions", ignoreCase = true)) {
            keyPoints.addAll(extractInteractionInfo(cleanText))
        }
        // For indications/uses - extract conditions
        else if (cleanText.contains("indicated", ignoreCase = true) ||
                 cleanText.contains("treatment", ignoreCase = true) ||
                 cleanText.contains("used for", ignoreCase = true) ||
                 cleanText.contains("treats", ignoreCase = true)) {
            keyPoints.addAll(extractUsageInfo(cleanText))
        }
        // For side effects - extract specific effects
        else if (cleanText.contains("adverse", ignoreCase = true) ||
                 cleanText.contains("side effects", ignoreCase = true) ||
                 cleanText.contains("reactions", ignoreCase = true)) {
            keyPoints.addAll(extractSideEffectsInfo(cleanText))
        }
        // For dosage/administration - extract timing and method info
        else if (cleanText.contains("dosage", ignoreCase = true) ||
                 cleanText.contains("administration", ignoreCase = true) ||
                 cleanText.contains("take", ignoreCase = true) ||
                 cleanText.contains("dose", ignoreCase = true)) {
            keyPoints.addAll(extractAdministrationInfo(cleanText))
        }

        // If no specific extraction worked, use intelligent general approach
        if (keyPoints.isEmpty()) {
            keyPoints.addAll(extractGeneralInfo(cleanText))
        }

        if (keyPoints.isEmpty()) return "لا توجد معلومات محددة متاحة"

        // Format as concise numbered list
        return keyPoints.take(4).mapIndexed { index, point ->
            val cleanPoint = point.trim()
                .replaceFirstChar { if (it.isLowerCase()) it.titlecase() else it.toString() }
                .let { if (!it.endsWith(".") && !it.endsWith("!") && !it.endsWith("?")) "$it." else it }

            "${index + 1}. $cleanPoint"
        }.joinToString("\n")
    }

    private fun extractInteractionInfo(text: String): List<String> {
        val interactions = mutableListOf<String>()

        // Extract drug names
        val drugPattern = Regex("\\b[A-Z][a-z]+(?:ine|ol|ide|ate|cin|zole|pril|sartan|statin|mycin|cillin)\\b")
        val drugs = drugPattern.findAll(text).map { it.value }.distinct().take(3).toList()
        drugs.forEach { drug -> interactions.add("يتفاعل مع $drug") }

        // Extract interaction effects
        val effects = listOf(
            "contraindicated" to "ممنوع الاستخدام معاً",
            "avoid" to "تجنب الاستخدام المتزامن",
            "monitor" to "يتطلب مراقبة طبية",
            "increased risk" to "يزيد المخاطر",
            "reduced effectiveness" to "يقلل الفعالية"
        )
        effects.forEach { (english, arabic) ->
            if (text.contains(english, ignoreCase = true)) {
                interactions.add(arabic)
            }
        }

        return interactions.take(3)
    }

    private fun extractUsageInfo(text: String): List<String> {
        val uses = mutableListOf<String>()

        // Common conditions
        val conditions = mapOf(
            "GERD" to "ارتجاع المريء",
            "gastroesophageal reflux" to "ارتجاع المعدة والمريء",
            "erosive esophagitis" to "التهاب المريء التآكلي",
            "peptic ulcer" to "قرحة المعدة",
            "ulcer" to "القرحة",
            "hypertension" to "ارتفاع ضغط الدم",
            "diabetes" to "السكري",
            "infection" to "العدوى",
            "pain" to "الألم",
            "inflammation" to "الالتهاب",
            "heartburn" to "حرقة المعدة",
            "acid reflux" to "حموضة المعدة"
        )

        conditions.forEach { (english, arabic) ->
            if (text.contains(english, ignoreCase = true)) {
                uses.add("يعالج $arabic")
            }
        }

        return uses.take(3)
    }

    private fun extractSideEffectsInfo(text: String): List<String> {
        val sideEffects = mutableListOf<String>()

        val effects = mapOf(
            "nausea" to "غثيان",
            "headache" to "صداع",
            "diarrhea" to "إسهال",
            "dizziness" to "دوخة",
            "rash" to "طفح جلدي",
            "fatigue" to "تعب",
            "abdominal pain" to "ألم البطن",
            "vomiting" to "قيء",
            "constipation" to "إمساك",
            "drowsiness" to "نعاس",
            "dry mouth" to "جفاف الفم"
        )

        effects.forEach { (english, arabic) ->
            if (text.contains(english, ignoreCase = true)) {
                sideEffects.add("قد يسبب $arabic")
            }
        }

        return sideEffects.take(3)
    }

    private fun extractAdministrationInfo(text: String): List<String> {
        val adminInfo = mutableListOf<String>()

        // Timing patterns
        val timingPatterns = mapOf(
            "before meals?" to "قبل الوجبات",
            "after meals?" to "بعد الوجبات",
            "with food" to "مع الطعام",
            "without food" to "بدون طعام",
            "on empty stomach" to "على معدة فارغة",
            "once daily" to "مرة واحدة يومياً",
            "twice daily" to "مرتين يومياً",
            "three times daily" to "ثلاث مرات يومياً",
            "at bedtime" to "قبل النوم",
            "in the morning" to "في الصباح"
        )

        timingPatterns.forEach { (pattern, arabic) ->
            if (text.contains(pattern, ignoreCase = true)) {
                adminInfo.add(arabic)
            }
        }

        // Dosage patterns
        val dosagePattern = Regex("(\\d+)\\s*(mg|g|ml)")
        val dosages = dosagePattern.findAll(text).map { "${it.groupValues[1]} ${it.groupValues[2]}" }.distinct().take(2)
        dosages.forEach { dose -> adminInfo.add("الجرعة: $dose") }

        return adminInfo.take(3)
    }

    private fun extractGeneralInfo(text: String): List<String> {
        val info = mutableListOf<String>()

        // Split into meaningful sentences
        val sentences = text.split(Regex("[.!?]"))
            .map { it.trim() }
            .filter { it.length in 15..100 && !it.contains("see", ignoreCase = true) }
            .take(4)

        // If we have good sentences, use them
        if (sentences.isNotEmpty()) {
            info.addAll(sentences)
        } else {
            // Fallback: split by common separators
            val parts = text.split(Regex("[,;:]"))
                .map { it.trim() }
                .filter { it.length in 10..80 }
                .take(3)
            info.addAll(parts)
        }

        return info.take(3)
    }
}
