package com.example.kpitrackerapp.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.TextView
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.Fragment
import com.example.kpitrackerapp.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.net.HttpURLConnection
import java.net.URL

class DrugIndexFragment : Fragment() {

    private lateinit var searchEditText: EditText
    private lateinit var drugNameTextView: TextView
    private lateinit var usesTextView: TextView
    private lateinit var interactionsTextView: TextView
    private lateinit var sideEffectsTextView: TextView
    private lateinit var usageInstructionsTextView: TextView

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_drug_index, container, false)

        searchEditText = view.findViewById(R.id.searchEditText)
        drugNameTextView = view.findViewById(R.id.drugNameTextView)
        usesTextView = view.findViewById(R.id.usesTextView)
        interactionsTextView = view.findViewById(R.id.interactionsTextView)
        sideEffectsTextView = view.findViewById(R.id.sideEffectsTextView)
        usageInstructionsTextView = view.findViewById(R.id.usageInstructionsTextView)

        searchEditText.addTextChangedListener {
            val query = it.toString().trim()
            if (query.isNotEmpty()) {
                searchDrug(query)
            }
        }

        return view
    }

    private fun searchDrug(query: String) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val url = URL("https://api.fda.gov/drug/label.json?search=openfda.brand_name:\"$query\"+OR+openfda.generic_name:\"$query\"&limit=1")
                val urlConnection = url.openConnection() as HttpURLConnection
                val inputStream = urlConnection.inputStream
                val response = inputStream.bufferedReader().use { it.readText() }

                val jsonObject = JSONObject(response)
                val results = jsonObject.getJSONArray("results")

                if (results.length() > 0) {
                    val drugInfo = results.getJSONObject(0)
                    val openfda = drugInfo.optJSONObject("openfda")
                    val brandName = openfda?.optJSONArray("brand_name")?.optString(0) ?: "N/A"
                    val genericName = openfda?.optJSONArray("generic_name")?.optString(0) ?: "N/A"

                    val uses = drugInfo.optJSONArray("indications_and_usage")?.optString(0) ?: "N/A"
                    val interactions = drugInfo.optJSONArray("drug_interactions")?.optString(0) ?: "N/A"
                    val sideEffects = drugInfo.optJSONArray("adverse_reactions")?.optString(0) ?: "N/A"
                    val usageInstructions = drugInfo.optJSONArray("dosage_and_administration")?.optString(0) ?: "N/A"

                    withContext(Dispatchers.Main) {
                        drugNameTextView.text = "$brandName ($genericName)"
                        usesTextView.text = formatTextAsNumberedList(uses)
                        interactionsTextView.text = formatTextAsNumberedList(interactions)
                        sideEffectsTextView.text = formatTextAsNumberedList(sideEffects)
                        usageInstructionsTextView.text = formatTextAsNumberedList(usageInstructions)
                        drugNameTextView.visibility = View.VISIBLE
                    }
                } else {
                    withContext(Dispatchers.Main) {
                        drugNameTextView.visibility = View.GONE
                        usesTextView.text = "No information found."
                        interactionsTextView.text = "-"
                        sideEffectsTextView.text = "-"
                        usageInstructionsTextView.text = "-"
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                withContext(Dispatchers.Main) {
                    drugNameTextView.visibility = View.GONE
                    usesTextView.text = "Error fetching data."
                    interactionsTextView.text = "-"
                    sideEffectsTextView.text = "-"
                    usageInstructionsTextView.text = "-"
                }
            }
        }
    }

    private fun formatTextAsNumberedList(text: String): String {
        if (text == "N/A" || text.isBlank()) return text

        // Clean and prepare text
        val cleanText = text.replace(Regex("\\s+"), " ").trim()

        // Try different splitting strategies
        var points = mutableListOf<String>()

        // Strategy 1: Split by sentence patterns
        val sentencePattern = Regex("(?<=[.!?])\\s+(?=[A-Z])")
        val sentences = cleanText.split(sentencePattern)
            .map { it.trim() }
            .filter { it.length > 20 } // Filter meaningful sentences
            .take(6)

        if (sentences.size > 1) {
            points = sentences.toMutableList()
        } else {
            // Strategy 2: Split by numbered lists or bullet points
            val numberedPattern = Regex("(?:^|\\s)(?:\\d+[.):]|[•·-])\\s*")
            val numberedSplits = cleanText.split(numberedPattern)
                .map { it.trim() }
                .filter { it.length > 15 }
                .take(5)

            if (numberedSplits.size > 1) {
                points = numberedSplits.toMutableList()
            } else {
                // Strategy 3: Split by parenthetical content or key phrases
                val keyPhrases = listOf(
                    "indicated for", "used for", "treatment of", "contraindicated",
                    "side effects", "adverse reactions", "drug interactions",
                    "dosage", "administration", "warnings", "precautions"
                )

                for (phrase in keyPhrases) {
                    val regex = Regex("(?i)\\b$phrase\\b[^.]*\\.")
                    val matches = regex.findAll(cleanText).map { it.value.trim() }.toList()
                    if (matches.isNotEmpty()) {
                        points.addAll(matches.take(3))
                    }
                }

                // If still no good points, chunk by length
                if (points.isEmpty()) {
                    val words = cleanText.split(" ")
                    val chunkSize = maxOf(20, words.size / 4)
                    points = words.chunked(chunkSize) { chunk ->
                        chunk.joinToString(" ")
                    }.filter { it.length > 20 }.take(4).toMutableList()
                }
            }
        }

        if (points.isEmpty()) return "📝 $cleanText"

        // Format as numbered list with emojis and better formatting
        return points.take(5).mapIndexed { index, point ->
            val cleanPoint = point.trim()
                .removePrefix(".")
                .removePrefix(":")
                .removePrefix("-")
                .removePrefix("•")
                .removePrefix("·")
                .trim()
                .replaceFirstChar { if (it.isLowerCase()) it.titlecase() else it.toString() }
                .let { if (!it.endsWith(".") && !it.endsWith("!") && !it.endsWith("?")) "$it." else it }

            "📌 ${index + 1}. $cleanPoint"
        }.joinToString("\n\n")
    }
}
