// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemSearchResultBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView messageText;

  @NonNull
  public final TextView messageTime;

  @NonNull
  public final TextView senderName;

  private ItemSearchResultBinding(@NonNull MaterialCardView rootView, @NonNull TextView messageText,
      @NonNull TextView messageTime, @NonNull TextView senderName) {
    this.rootView = rootView;
    this.messageText = messageText;
    this.messageTime = messageTime;
    this.senderName = senderName;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSearchResultBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSearchResultBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_search_result, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSearchResultBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.messageText;
      TextView messageText = ViewBindings.findChildViewById(rootView, id);
      if (messageText == null) {
        break missingId;
      }

      id = R.id.messageTime;
      TextView messageTime = ViewBindings.findChildViewById(rootView, id);
      if (messageTime == null) {
        break missingId;
      }

      id = R.id.senderName;
      TextView senderName = ViewBindings.findChildViewById(rootView, id);
      if (senderName == null) {
        break missingId;
      }

      return new ItemSearchResultBinding((MaterialCardView) rootView, messageText, messageTime,
          senderName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
