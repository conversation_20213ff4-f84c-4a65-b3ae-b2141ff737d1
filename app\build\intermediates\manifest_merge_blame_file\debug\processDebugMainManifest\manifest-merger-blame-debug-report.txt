1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.kpitrackerapp"
4    android:versionCode="1"
5    android:versionName="1.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Required for notifications on Android 13+ -->
12    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
12-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:6:5-76
12-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:6:22-74
13    <!-- Required for running foreground services (used by WorkManager when setForeground is called) -->
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
14-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:8:5-76
14-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:8:22-74
15    <!-- Required for Camera -->
16    <uses-permission android:name="android.permission.CAMERA" />
16-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:10:5-64
16-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:10:22-62
17    <!-- Required for Calendar Integration -->
18    <uses-permission android:name="android.permission.READ_CALENDAR" />
18-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:12:5-72
18-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:12:22-69
19    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
19-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:13:5-73
19-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:13:22-70
20    <!-- Required for Speech Recognition -->
21    <uses-permission android:name="android.permission.RECORD_AUDIO" />
21-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:15:5-71
21-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:15:22-68
22    <uses-permission android:name="android.permission.INTERNET" />
22-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:16:5-67
22-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:16:22-64
23    <!-- Required for Location Services -->
24    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
24-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:18:5-79
24-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:18:22-76
25    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
25-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:19:5-81
25-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:19:22-78
26    <!-- Required for Setting Alarms -->
27    <uses-permission android:name="android.permission.SET_ALARM" />
27-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:21:5-68
27-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:21:22-65
28    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
28-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:22:5-78
28-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:22:22-75
29
30    <!-- Declare camera feature, but don't require it if app can function without -->
31    <uses-feature
31-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:25:5-84
32        android:name="android.hardware.camera"
32-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:25:19-57
33        android:required="false" />
33-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:25:58-82
34
35    <!-- Queries for specific apps we want to detect (Android 11+ requirement) -->
36    <queries>
36-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:28:5-40:15
37
38        <!-- WhatsApp Regular -->
39        <package android:name="com.whatsapp" />
39-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:30:9-48
39-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:30:18-45
40        <!-- WhatsApp Business -->
41        <package android:name="com.whatsapp.w4b" />
41-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:32:9-52
41-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:32:18-49
42        <!-- Gmail -->
43        <package android:name="com.google.android.gm" />
43-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:34:9-57
43-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:34:18-54
44        <!-- Email intent -->
45        <intent>
45-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:36:9-39:18
46            <action android:name="android.intent.action.SENDTO" />
46-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:37:13-67
46-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:37:21-64
47
48            <data android:scheme="mailto" />
48-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:38:13-45
48-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:38:19-42
49        </intent>
50    </queries>
51
52    <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
53    <!-- <uses-sdk android:minSdkVersion="14"/> -->
54    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
54-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:22:5-79
54-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:22:22-76
55    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
55-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:24:5-68
55-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:24:22-65
56    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
56-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:26:5-82
56-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:26:22-79
57    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
57-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
57-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
58    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
58-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
58-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
59    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
59-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
59-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
60    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
60-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
60-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
61    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
61-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
61-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
62
63    <permission
63-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
64        android:name="com.example.kpitrackerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
64-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
65        android:protectionLevel="signature" />
65-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
66
67    <uses-permission android:name="com.example.kpitrackerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
67-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
67-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
68
69    <application
69-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:42:5-254:19
70        android:name="com.example.kpitrackerapp.KpiTrackerApplication"
70-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:43:9-46
71        android:allowBackup="true"
71-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:44:9-35
72        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
72-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
73        android:dataExtractionRules="@xml/data_extraction_rules"
73-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:45:9-65
74        android:debuggable="true"
75        android:extractNativeLibs="false"
76        android:fullBackupContent="@xml/backup_rules"
76-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:46:9-54
77        android:icon="@mipmap/ic_launcher"
77-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:48:9-43
78        android:label="@string/app_name"
78-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:47:9-41
79        android:networkSecurityConfig="@xml/network_security_config"
79-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:52:9-69
80        android:roundIcon="@mipmap/ic_launcher"
80-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:49:9-48
81        android:supportsRtl="true"
81-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:50:9-35
82        android:testOnly="true"
83        android:theme="@style/Theme.KPITrackerApp" >
83-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:51:9-51
84
85        <!-- Optional: Request OCR module download on install/update -->
86        <meta-data
86-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:56:9-58:35
87            android:name="com.google.mlkit.vision.DEPENDENCIES"
87-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:57:13-64
88            android:value="ocr" />
88-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:58:13-32
89
90        <activity
90-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:60:9-68:20
91            android:name="com.example.kpitrackerapp.MainActivity"
91-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:61:13-41
92            android:exported="true"
92-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:62:13-36
93            android:theme="@style/Theme.KPITrackerApp.NoActionBar" > <!-- Apply NoActionBar theme here -->
93-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:63:13-67
94            <intent-filter>
94-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:64:13-67:29
95                <action android:name="android.intent.action.MAIN" />
95-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:65:17-69
95-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:65:25-66
96
97                <category android:name="android.intent.category.LAUNCHER" />
97-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:66:17-77
97-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:66:27-74
98            </intent-filter>
99        </activity>
100        <activity
100-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:69:9-73:70
101            android:name="com.example.kpitrackerapp.ui.AddEditKpiActivity"
101-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:70:13-50
102            android:label="@string/add_kpi_title"
102-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:71:13-50
103            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
103-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:72:13-55
104            android:theme="@style/Theme.KPITrackerApp.NoActionBar" /> <!-- This is now for Tasks -->
104-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:73:13-67
105        <activity
105-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:74:9-78:70
106            android:name="com.example.kpitrackerapp.ui.AddEditKpiOriginalActivity"
106-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:75:13-58
107            android:label="@string/add_kpi_title"
107-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:76:13-50
108            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
108-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:77:13-55
109            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
109-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:78:13-67
110        <activity
110-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:79:9-82:58
111            android:name="com.example.kpitrackerapp.ui.KpiDetailActivity"
111-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:80:13-49
112            android:label="@string/kpi_detail_title"
112-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:81:13-53
113            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
113-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:82:13-55
114        <activity
114-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:83:9-87:70
115            android:name="com.example.kpitrackerapp.ui.ModernReportActivity"
115-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:84:13-52
116            android:label="Interactive Performance Report"
116-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:85:13-59
117            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
117-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:86:13-55
118            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
118-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:87:13-67
119        <activity
119-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:88:9-91:58
120            android:name="com.example.kpitrackerapp.ui.ExpireManagementActivity"
120-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:89:13-56
121            android:label="@string/action_expiry_management"
121-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:90:13-61
122            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
122-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:91:13-55
123        <activity
123-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:92:9-95:66
124            android:name="com.example.kpitrackerapp.ui.SearchEditProgressActivity"
124-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:93:13-58
125            android:label="@string/search_edit_progress_title"
125-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:94:13-63
126            android:parentActivityName="com.example.kpitrackerapp.ui.KpiDetailActivity" />
126-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:95:13-63
127        <activity
127-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:96:9-99:58
128            android:name="com.example.kpitrackerapp.ui.OcrActivity"
128-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:97:13-43
129            android:label="@string/ocr_activity_title"
129-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:98:13-55
130            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
130-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:99:13-55
131        <activity
131-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:100:9-103:60
132            android:name="com.example.kpitrackerapp.ui.OcrReviewActivity"
132-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:101:13-49
133            android:label="@string/review_ocr_results_title"
133-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:102:13-61
134            android:parentActivityName="com.example.kpitrackerapp.ui.OcrActivity" /> <!-- Parent is OcrActivity -->
134-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:103:13-57
135        <!-- Removed SmartListAnalysisActivity declaration -->
136        <activity
136-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:105:9-108:66
137            android:name="com.example.kpitrackerapp.ui.ExcelImportActivity"
137-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:106:13-51
138            android:label="Import from Excel"
138-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:107:13-46
139            android:parentActivityName="com.example.kpitrackerapp.ui.KpiDetailActivity" />
139-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:108:13-63
140        <activity
140-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:109:9-112:68
141            android:name="com.example.kpitrackerapp.ui.ExcelReviewActivity"
141-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:110:13-51
142            android:label="Review Excel Import"
142-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:111:13-48
143            android:parentActivityName="com.example.kpitrackerapp.ui.ExcelImportActivity" />
143-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:112:13-65
144        <activity
144-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:114:9-118:70
145            android:name="com.example.kpitrackerapp.ui.UserKpiListActivity"
145-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:115:13-51
146            android:label="@string/user_kpi_list_title"
146-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:116:13-56
147            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
147-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:117:13-55
148            android:theme="@style/Theme.KPITrackerApp.NoActionBar" /> <!-- Apply NoActionBar theme -->
148-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:118:13-67
149        <activity
149-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:119:9-123:70
150            android:name="com.example.kpitrackerapp.ui.TaskManagementActivity"
150-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:120:13-54
151            android:label="Task Management"
151-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:121:13-44
152            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
152-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:122:13-55
153            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
153-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:123:13-67
154        <activity
154-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:124:9-128:70
155            android:name="com.example.kpitrackerapp.ui.TaskReportActivity"
155-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:125:13-50
156            android:label="Task Reports"
156-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:126:13-41
157            android:parentActivityName="com.example.kpitrackerapp.ui.TaskManagementActivity"
157-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:127:13-68
158            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
158-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:128:13-67
159
160        <!-- Login and User Management Activities -->
161        <activity
161-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:131:9-135:40
162            android:name="com.example.kpitrackerapp.ui.LoginActivity"
162-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:132:13-45
163            android:exported="false"
163-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:135:13-37
164            android:label="Login"
164-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:133:13-34
165            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
165-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:134:13-67
166        <activity
166-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:136:9-141:40
167            android:name="com.example.kpitrackerapp.ui.CreateUserActivity"
167-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:137:13-50
168            android:exported="false"
168-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:141:13-37
169            android:label="Create User"
169-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:138:13-40
170            android:parentActivityName="com.example.kpitrackerapp.ui.LoginActivity"
170-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:139:13-59
171            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
171-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:140:13-67
172
173        <!-- Admin Dashboard Activity -->
174        <activity
174-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:144:9-149:40
175            android:name="com.example.kpitrackerapp.AdminDashboardActivity"
175-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:145:13-51
176            android:exported="false"
176-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:149:13-37
177            android:label="Admin Dashboard"
177-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:146:13-44
178            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
178-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:147:13-55
179            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
179-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:148:13-67
180
181        <!-- Chat Activities -->
182        <activity
182-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:152:9-157:40
183            android:name="com.example.kpitrackerapp.ui.ChatListActivity"
183-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:153:13-48
184            android:exported="false"
184-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:157:13-37
185            android:label="Messages"
185-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:154:13-37
186            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
186-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:155:13-55
187            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
187-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:156:13-67
188        <activity
188-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:159:9-164:40
189            android:name="com.example.kpitrackerapp.ui.ChatActivity"
189-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:160:13-44
190            android:exported="false"
190-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:164:13-37
191            android:label="Chat"
191-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:161:13-33
192            android:parentActivityName="com.example.kpitrackerapp.ui.ChatListActivity"
192-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:162:13-62
193            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
193-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:163:13-67
194
195        <!-- Task Reminder Settings Activity -->
196        <activity
196-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:167:9-172:40
197            android:name="com.example.kpitrackerapp.ui.TaskReminderSettingsActivity"
197-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:168:13-60
198            android:exported="false"
198-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:172:13-37
199            android:label="Task Reminder Settings"
199-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:169:13-51
200            android:parentActivityName="com.example.kpitrackerapp.ui.TaskManagementActivity"
200-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:170:13-68
201            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
201-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:171:13-67
202
203        <!-- Auto Send Settings Activity -->
204        <activity
204-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:175:9-180:40
205            android:name="com.example.kpitrackerapp.ui.AutoSendSettingsActivity"
205-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:176:13-56
206            android:exported="false"
206-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:180:13-37
207            android:label="Auto Send Settings"
207-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:177:13-47
208            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
208-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:178:13-55
209            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
209-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:179:13-67
210
211        <!-- Advanced Task Activity -->
212        <activity
212-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:183:9-188:40
213            android:name="com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity"
213-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:184:13-59
214            android:exported="false"
214-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:188:13-37
215            android:label="إضافة مهمة متقدمة"
215-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:185:13-46
216            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
216-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:186:13-55
217            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
217-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:187:13-67
218
219        <!-- Modern Add Task Activity -->
220        <activity
220-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:191:9-196:40
221            android:name="com.example.kpitrackerapp.ui.ModernAddTaskActivity"
221-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:192:13-53
222            android:exported="false"
222-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:196:13-37
223            android:label="Add New Task"
223-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:193:13-41
224            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
224-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:194:13-55
225            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
225-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:195:13-67
226
227        <!-- Pomodoro Timer Activity -->
228        <activity
228-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:199:9-205:52
229            android:name="com.example.kpitrackerapp.ui.PomodoroTimerActivity"
229-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:200:13-53
230            android:exported="false"
230-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:204:13-37
231            android:label="Pomodoro Timer"
231-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:201:13-43
232            android:parentActivityName="com.example.kpitrackerapp.ui.TaskManagementActivity"
232-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:202:13-68
233            android:screenOrientation="portrait"
233-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:205:13-49
234            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
234-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:203:13-67
235
236        <!-- Add other activities, services, etc. here -->
237        <activity
237-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:208:9-211:58
238            android:name="com.example.kpitrackerapp.ui.NotificationsActivity"
238-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:209:13-53
239            android:exported="false"
239-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:210:13-37
240            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
240-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:211:13-55
241
242        <!-- Firebase Messaging Service -->
243        <service
243-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:214:9-220:19
244            android:name="com.example.kpitrackerapp.services.KPIFirebaseMessagingService"
244-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:215:13-65
245            android:exported="false" >
245-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:216:13-37
246            <intent-filter>
246-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:217:13-219:29
247                <action android:name="com.google.firebase.MESSAGING_EVENT" />
247-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:218:17-78
247-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:218:25-75
248            </intent-filter>
249        </service>
250
251        <!-- Firebase Messaging default notification icon -->
252        <meta-data
252-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:223:9-225:60
253            android:name="com.google.firebase.messaging.default_notification_icon"
253-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:224:13-83
254            android:resource="@drawable/ic_notification" />
254-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:225:13-57
255
256        <!-- Firebase Messaging default notification color -->
257        <meta-data
257-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:228:9-230:52
258            android:name="com.google.firebase.messaging.default_notification_color"
258-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:229:13-84
259            android:resource="@color/purple_500" />
259-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:230:13-49
260
261        <!-- FileProvider for sharing camera image URI -->
262        <provider
263            android:name="androidx.core.content.FileProvider"
263-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:234:13-62
264            android:authorities="com.example.kpitrackerapp.provider"
264-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:235:13-60
265            android:exported="false"
265-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:236:13-37
266            android:grantUriPermissions="true" >
266-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:237:13-47
267            <meta-data
267-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:238:13-240:54
268                android:name="android.support.FILE_PROVIDER_PATHS"
268-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:239:17-67
269                android:resource="@xml/file_paths" />
269-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:240:17-51
270        </provider>
271
272        <!-- Disable WorkManager automatic initialization since we use Configuration.Provider -->
273        <provider
274            android:name="androidx.startup.InitializationProvider"
274-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:245:13-67
275            android:authorities="com.example.kpitrackerapp.androidx-startup"
275-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:246:13-68
276            android:exported="false" >
276-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:247:13-37
277            <meta-data
277-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\a5046b8718eb7718c37e191f5a52f4c5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
278                android:name="androidx.emoji2.text.EmojiCompatInitializer"
278-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\a5046b8718eb7718c37e191f5a52f4c5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
279                android:value="androidx.startup" />
279-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\a5046b8718eb7718c37e191f5a52f4c5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
280            <meta-data
280-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\8979a47168c13891bf6142b3909bb09c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
281                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
281-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\8979a47168c13891bf6142b3909bb09c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
282                android:value="androidx.startup" />
282-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\8979a47168c13891bf6142b3909bb09c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
283            <meta-data
283-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
284                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
284-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
285                android:value="androidx.startup" />
285-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
286        </provider>
287
288        <service
288-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:9:9-15:19
289            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
289-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:10:13-91
290            android:directBootAware="true"
290-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:17:13-43
291            android:exported="false" >
291-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:11:13-37
292            <meta-data
292-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:12:13-14:85
293                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
293-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:13:17-114
294                android:value="com.google.firebase.components.ComponentRegistrar" />
294-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:14:17-82
295            <meta-data
295-->[com.google.mlkit:vision-common:17.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\1063076d438e1847fa9d9ed50fcacbf0\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
296                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
296-->[com.google.mlkit:vision-common:17.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\1063076d438e1847fa9d9ed50fcacbf0\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
297                android:value="com.google.firebase.components.ComponentRegistrar" />
297-->[com.google.mlkit:vision-common:17.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\1063076d438e1847fa9d9ed50fcacbf0\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
298            <meta-data
298-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:20:13-22:85
299                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
299-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:21:17-120
300                android:value="com.google.firebase.components.ComponentRegistrar" />
300-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:22:17-82
301        </service>
302
303        <provider
303-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:9:9-13:38
304            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
304-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:10:13-78
305            android:authorities="com.example.kpitrackerapp.mlkitinitprovider"
305-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:11:13-69
306            android:exported="false"
306-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:12:13-37
307            android:initOrder="99" />
307-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:13:13-35
308
309        <service
309-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:9:9-15:19
310            android:name="com.google.firebase.components.ComponentDiscoveryService"
310-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:10:13-84
311            android:directBootAware="true"
311-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:32:13-43
312            android:exported="false" >
312-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:11:13-37
313            <meta-data
313-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:12:13-14:85
314                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
314-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:13:17-127
315                android:value="com.google.firebase.components.ComponentRegistrar" />
315-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:14:17-82
316            <meta-data
316-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:29:13-31:85
317                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
317-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:30:17-120
318                android:value="com.google.firebase.components.ComponentRegistrar" />
318-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:31:17-82
319            <meta-data
319-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:32:13-34:85
320                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
320-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:33:17-109
321                android:value="com.google.firebase.components.ComponentRegistrar" />
321-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:34:17-82
322            <meta-data
322-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\80cfd5dd6a9dcc5f71fdfc2bfdc91067\transformed\jetified-firebase-messaging-ktx-23.4.0\AndroidManifest.xml:26:13-28:85
323                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar"
323-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\80cfd5dd6a9dcc5f71fdfc2bfdc91067\transformed\jetified-firebase-messaging-ktx-23.4.0\AndroidManifest.xml:27:17-129
324                android:value="com.google.firebase.components.ComponentRegistrar" />
324-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\80cfd5dd6a9dcc5f71fdfc2bfdc91067\transformed\jetified-firebase-messaging-ktx-23.4.0\AndroidManifest.xml:28:17-82
325            <meta-data
325-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:57:13-59:85
326                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
326-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:58:17-122
327                android:value="com.google.firebase.components.ComponentRegistrar" />
327-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:59:17-82
328            <meta-data
328-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:60:13-62:85
329                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
329-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:61:17-119
330                android:value="com.google.firebase.components.ComponentRegistrar" />
330-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:62:17-82
331            <meta-data
331-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\e1e5edf221d9e2eedd44e81a2a865970\transformed\jetified-firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
332                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
332-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\e1e5edf221d9e2eedd44e81a2a865970\transformed\jetified-firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
333                android:value="com.google.firebase.components.ComponentRegistrar" />
333-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\e1e5edf221d9e2eedd44e81a2a865970\transformed\jetified-firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
334            <meta-data
334-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
335                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
335-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
336                android:value="com.google.firebase.components.ComponentRegistrar" />
336-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
337            <meta-data
337-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
338                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
338-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
339                android:value="com.google.firebase.components.ComponentRegistrar" />
339-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
340            <meta-data
340-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
341                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
341-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
342                android:value="com.google.firebase.components.ComponentRegistrar" />
342-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
343            <meta-data
343-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\0d006008fe6bff343360bc682c7a0697\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
344                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
344-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\0d006008fe6bff343360bc682c7a0697\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
345                android:value="com.google.firebase.components.ComponentRegistrar" />
345-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\0d006008fe6bff343360bc682c7a0697\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
346            <meta-data
346-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
347                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
347-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:36:17-109
348                android:value="com.google.firebase.components.ComponentRegistrar" />
348-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:37:17-82
349            <meta-data
349-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\b7c9fb2db29bcc8c4004f9c995f69ff4\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
350                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
350-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\b7c9fb2db29bcc8c4004f9c995f69ff4\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
351                android:value="com.google.firebase.components.ComponentRegistrar" />
351-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\b7c9fb2db29bcc8c4004f9c995f69ff4\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
352        </service>
353
354        <receiver
354-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:29:9-40:20
355            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
355-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:30:13-78
356            android:exported="true"
356-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:31:13-36
357            android:permission="com.google.android.c2dm.permission.SEND" >
357-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:32:13-73
358            <intent-filter>
358-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:33:13-35:29
359                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
359-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:34:17-81
359-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:34:25-78
360            </intent-filter>
361
362            <meta-data
362-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:37:13-39:40
363                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
363-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:38:17-92
364                android:value="true" />
364-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:39:17-37
365        </receiver>
366        <!--
367             FirebaseMessagingService performs security checks at runtime,
368             but set to not exported to explicitly avoid allowing another app to call it.
369        -->
370        <service
370-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:46:9-53:19
371            android:name="com.google.firebase.messaging.FirebaseMessagingService"
371-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:47:13-82
372            android:directBootAware="true"
372-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:48:13-43
373            android:exported="false" >
373-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:49:13-37
374            <intent-filter android:priority="-500" >
374-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:217:13-219:29
375                <action android:name="com.google.firebase.MESSAGING_EVENT" />
375-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:218:17-78
375-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:218:25-75
376            </intent-filter>
377        </service>
378
379        <activity
379-->[com.google.android.gms:play-services-base:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
380            android:name="com.google.android.gms.common.api.GoogleApiActivity"
380-->[com.google.android.gms:play-services-base:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
381            android:exported="false"
381-->[com.google.android.gms:play-services-base:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
382            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
382-->[com.google.android.gms:play-services-base:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
383
384        <property
384-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
385            android:name="android.adservices.AD_SERVICES_CONFIG"
385-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
386            android:resource="@xml/ga_ad_services_config" />
386-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
387
388        <provider
388-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
389            android:name="com.google.firebase.provider.FirebaseInitProvider"
389-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:24:13-77
390            android:authorities="com.example.kpitrackerapp.firebaseinitprovider"
390-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:25:13-72
391            android:directBootAware="true"
391-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:26:13-43
392            android:exported="false"
392-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:27:13-37
393            android:initOrder="100" />
393-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:28:13-36
394
395        <service
395-->[androidx.room:room-runtime:2.6.1] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
396            android:name="androidx.room.MultiInstanceInvalidationService"
396-->[androidx.room:room-runtime:2.6.1] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
397            android:directBootAware="true"
397-->[androidx.room:room-runtime:2.6.1] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
398            android:exported="false" />
398-->[androidx.room:room-runtime:2.6.1] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
399        <service
399-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
400            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
400-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
401            android:directBootAware="false"
401-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
402            android:enabled="@bool/enable_system_alarm_service_default"
402-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
403            android:exported="false" />
403-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
404        <service
404-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
405            android:name="androidx.work.impl.background.systemjob.SystemJobService"
405-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
406            android:directBootAware="false"
406-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
407            android:enabled="@bool/enable_system_job_service_default"
407-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
408            android:exported="true"
408-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
409            android:permission="android.permission.BIND_JOB_SERVICE" />
409-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
410        <service
410-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
411            android:name="androidx.work.impl.foreground.SystemForegroundService"
411-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
412            android:directBootAware="false"
412-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
413            android:enabled="@bool/enable_system_foreground_service_default"
413-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
414            android:exported="false" />
414-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
415
416        <receiver
416-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
417            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
417-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
418            android:directBootAware="false"
418-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
419            android:enabled="true"
419-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
420            android:exported="false" />
420-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
421        <receiver
421-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
422            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
422-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
423            android:directBootAware="false"
423-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
424            android:enabled="false"
424-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
425            android:exported="false" >
425-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
426            <intent-filter>
426-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
427                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
427-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
427-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
428                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
428-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
428-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
429            </intent-filter>
430        </receiver>
431        <receiver
431-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
432            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
432-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
433            android:directBootAware="false"
433-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
434            android:enabled="false"
434-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
435            android:exported="false" >
435-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
436            <intent-filter>
436-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
437                <action android:name="android.intent.action.BATTERY_OKAY" />
437-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
437-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
438                <action android:name="android.intent.action.BATTERY_LOW" />
438-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
438-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
439            </intent-filter>
440        </receiver>
441        <receiver
441-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
442            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
442-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
443            android:directBootAware="false"
443-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
444            android:enabled="false"
444-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
445            android:exported="false" >
445-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
446            <intent-filter>
446-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
447                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
447-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
447-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
448                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
448-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
448-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
449            </intent-filter>
450        </receiver>
451        <receiver
451-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
452            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
452-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
453            android:directBootAware="false"
453-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
454            android:enabled="false"
454-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
455            android:exported="false" >
455-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
456            <intent-filter>
456-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
457                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
457-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
457-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
458            </intent-filter>
459        </receiver>
460        <receiver
460-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
461            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
461-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
462            android:directBootAware="false"
462-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
463            android:enabled="false"
463-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
464            android:exported="false" >
464-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
465            <intent-filter>
465-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
466                <action android:name="android.intent.action.BOOT_COMPLETED" />
466-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
466-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
467                <action android:name="android.intent.action.TIME_SET" />
467-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
467-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
468                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
468-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
468-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
469            </intent-filter>
470        </receiver>
471        <receiver
471-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
472            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
472-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
473            android:directBootAware="false"
473-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
474            android:enabled="@bool/enable_system_alarm_service_default"
474-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
475            android:exported="false" >
475-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
476            <intent-filter>
476-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
477                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
477-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
477-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
478            </intent-filter>
479        </receiver>
480        <receiver
480-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
481            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
481-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
482            android:directBootAware="false"
482-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
483            android:enabled="true"
483-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
484            android:exported="true"
484-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
485            android:permission="android.permission.DUMP" >
485-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
486            <intent-filter>
486-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
487                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
487-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
487-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
488            </intent-filter>
489        </receiver>
490        <receiver
490-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
491            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
491-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
492            android:enabled="true"
492-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
493            android:exported="false" >
493-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
494        </receiver>
495
496        <service
496-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
497            android:name="com.google.android.gms.measurement.AppMeasurementService"
497-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
498            android:enabled="true"
498-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
499            android:exported="false" />
499-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
500        <service
500-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
501            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
501-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
502            android:enabled="true"
502-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
503            android:exported="false"
503-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
504            android:permission="android.permission.BIND_JOB_SERVICE" />
504-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
505
506        <uses-library
506-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\64b2a28493382675ec9ae515f82793b7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
507            android:name="android.ext.adservices"
507-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\64b2a28493382675ec9ae515f82793b7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
508            android:required="false" />
508-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\64b2a28493382675ec9ae515f82793b7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
509
510        <meta-data
510-->[com.google.android.gms:play-services-basement:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\44fac508c06d1fa71634b2a2e87bd97f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
511            android:name="com.google.android.gms.version"
511-->[com.google.android.gms:play-services-basement:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\44fac508c06d1fa71634b2a2e87bd97f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
512            android:value="@integer/google_play_services_version" />
512-->[com.google.android.gms:play-services-basement:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\44fac508c06d1fa71634b2a2e87bd97f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
513
514        <receiver
514-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
515            android:name="androidx.profileinstaller.ProfileInstallReceiver"
515-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
516            android:directBootAware="false"
516-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
517            android:enabled="true"
517-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
518            android:exported="true"
518-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
519            android:permission="android.permission.DUMP" >
519-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
520            <intent-filter>
520-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
521                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
521-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
521-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
522            </intent-filter>
523            <intent-filter>
523-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
524                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
524-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
524-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
525            </intent-filter>
526            <intent-filter>
526-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
527                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
527-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
527-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
528            </intent-filter>
529            <intent-filter>
529-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
530                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
530-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
530-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
531            </intent-filter>
532        </receiver>
533
534        <service
534-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
535            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
535-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
536            android:exported="false" >
536-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
537            <meta-data
537-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
538                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
538-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
539                android:value="cct" />
539-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
540        </service>
541        <service
541-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
542            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
542-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
543            android:exported="false"
543-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
544            android:permission="android.permission.BIND_JOB_SERVICE" >
544-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
545        </service>
546
547        <receiver
547-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
548            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
548-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
549            android:exported="false" />
549-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
550    </application>
551
552</manifest>
