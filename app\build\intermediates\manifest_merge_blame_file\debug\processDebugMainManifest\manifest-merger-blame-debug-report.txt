1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.kpitrackerapp"
4    android:versionCode="1"
5    android:versionName="1.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Required for notifications on Android 13+ -->
12    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
12-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:6:5-76
12-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:6:22-74
13    <!-- Required for running foreground services (used by WorkManager when setForeground is called) -->
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
14-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:8:5-76
14-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:8:22-74
15    <!-- Required for Camera -->
16    <uses-permission android:name="android.permission.CAMERA" />
16-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:10:5-64
16-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:10:22-62
17    <!-- Required for Calendar Integration -->
18    <uses-permission android:name="android.permission.READ_CALENDAR" />
18-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:12:5-72
18-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:12:22-69
19    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
19-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:13:5-73
19-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:13:22-70
20    <!-- Required for Speech Recognition -->
21    <uses-permission android:name="android.permission.RECORD_AUDIO" />
21-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:15:5-71
21-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:15:22-68
22    <uses-permission android:name="android.permission.INTERNET" />
22-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:16:5-67
22-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:16:22-64
23    <!-- Required for Location Services -->
24    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
24-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:18:5-79
24-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:18:22-76
25    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
25-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:19:5-81
25-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:19:22-78
26    <!-- Required for Setting Alarms -->
27    <uses-permission android:name="android.permission.SET_ALARM" />
27-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:21:5-68
27-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:21:22-65
28    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
28-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:22:5-78
28-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:22:22-75
29
30    <!-- Declare camera feature, but don't require it if app can function without -->
31    <uses-feature
31-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:25:5-84
32        android:name="android.hardware.camera"
32-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:25:19-57
33        android:required="false" />
33-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:25:58-82
34
35    <!-- Queries for specific apps we want to detect (Android 11+ requirement) -->
36    <queries>
36-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:28:5-40:15
37
38        <!-- WhatsApp Regular -->
39        <package android:name="com.whatsapp" />
39-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:30:9-48
39-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:30:18-45
40        <!-- WhatsApp Business -->
41        <package android:name="com.whatsapp.w4b" />
41-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:32:9-52
41-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:32:18-49
42        <!-- Gmail -->
43        <package android:name="com.google.android.gm" />
43-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:34:9-57
43-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:34:18-54
44        <!-- Email intent -->
45        <intent>
45-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:36:9-39:18
46            <action android:name="android.intent.action.SENDTO" />
46-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:37:13-67
46-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:37:21-64
47
48            <data android:scheme="mailto" />
48-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:38:13-45
48-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:38:19-42
49        </intent>
50    </queries>
51
52    <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
53    <!-- <uses-sdk android:minSdkVersion="14"/> -->
54    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
54-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:22:5-79
54-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:22:22-76
55    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
55-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:24:5-68
55-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:24:22-65
56    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
56-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:26:5-82
56-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:26:22-79
57    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
57-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
57-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
58    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
58-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
58-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
59    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
59-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
59-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
60    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
60-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
60-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
61    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
61-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
61-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
62
63    <permission
63-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
64        android:name="com.example.kpitrackerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
64-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
65        android:protectionLevel="signature" />
65-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
66
67    <uses-permission android:name="com.example.kpitrackerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
67-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
67-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
68
69    <application
69-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:42:5-237:19
70        android:name="com.example.kpitrackerapp.KpiTrackerApplication"
70-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:43:9-46
71        android:allowBackup="true"
71-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:44:9-35
72        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
72-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
73        android:dataExtractionRules="@xml/data_extraction_rules"
73-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:45:9-65
74        android:debuggable="true"
75        android:extractNativeLibs="false"
76        android:fullBackupContent="@xml/backup_rules"
76-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:46:9-54
77        android:icon="@mipmap/ic_launcher"
77-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:48:9-43
78        android:label="@string/app_name"
78-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:47:9-41
79        android:networkSecurityConfig="@xml/network_security_config"
79-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:52:9-69
80        android:roundIcon="@mipmap/ic_launcher"
80-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:49:9-48
81        android:supportsRtl="true"
81-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:50:9-35
82        android:testOnly="true"
83        android:theme="@style/Theme.KPITrackerApp" >
83-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:51:9-51
84
85        <!-- Optional: Request OCR module download on install/update -->
86        <meta-data
86-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:56:9-58:35
87            android:name="com.google.mlkit.vision.DEPENDENCIES"
87-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:57:13-64
88            android:value="ocr" />
88-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:58:13-32
89
90        <activity
90-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:60:9-68:20
91            android:name="com.example.kpitrackerapp.MainActivity"
91-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:61:13-41
92            android:exported="true"
92-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:62:13-36
93            android:theme="@style/Theme.KPITrackerApp.NoActionBar" > <!-- Apply NoActionBar theme here -->
93-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:63:13-67
94            <intent-filter>
94-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:64:13-67:29
95                <action android:name="android.intent.action.MAIN" />
95-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:65:17-69
95-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:65:25-66
96
97                <category android:name="android.intent.category.LAUNCHER" />
97-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:66:17-77
97-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:66:27-74
98            </intent-filter>
99        </activity>
100        <activity
100-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:69:9-73:70
101            android:name="com.example.kpitrackerapp.ui.AddEditKpiActivity"
101-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:70:13-50
102            android:label="@string/add_kpi_title"
102-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:71:13-50
103            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
103-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:72:13-55
104            android:theme="@style/Theme.KPITrackerApp.NoActionBar" /> <!-- This is now for Tasks -->
104-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:73:13-67
105        <activity
105-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:74:9-78:70
106            android:name="com.example.kpitrackerapp.ui.AddEditKpiOriginalActivity"
106-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:75:13-58
107            android:label="@string/add_kpi_title"
107-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:76:13-50
108            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
108-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:77:13-55
109            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
109-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:78:13-67
110        <activity
110-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:79:9-82:58
111            android:name="com.example.kpitrackerapp.ui.KpiDetailActivity"
111-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:80:13-49
112            android:label="@string/kpi_detail_title"
112-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:81:13-53
113            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
113-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:82:13-55
114        <activity
114-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:83:9-87:70
115            android:name="com.example.kpitrackerapp.ui.ModernReportActivity"
115-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:84:13-52
116            android:label="Interactive Performance Report"
116-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:85:13-59
117            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
117-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:86:13-55
118            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
118-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:87:13-67
119        <activity
119-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:88:9-91:58
120            android:name="com.example.kpitrackerapp.ui.ExpireManagementActivity"
120-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:89:13-56
121            android:label="@string/action_expiry_management"
121-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:90:13-61
122            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
122-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:91:13-55
123        <activity
123-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:92:9-95:66
124            android:name="com.example.kpitrackerapp.ui.SearchEditProgressActivity"
124-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:93:13-58
125            android:label="@string/search_edit_progress_title"
125-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:94:13-63
126            android:parentActivityName="com.example.kpitrackerapp.ui.KpiDetailActivity" />
126-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:95:13-63
127        <activity
127-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:96:9-99:58
128            android:name="com.example.kpitrackerapp.ui.OcrActivity"
128-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:97:13-43
129            android:label="@string/ocr_activity_title"
129-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:98:13-55
130            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
130-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:99:13-55
131        <activity
131-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:100:9-103:60
132            android:name="com.example.kpitrackerapp.ui.OcrReviewActivity"
132-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:101:13-49
133            android:label="@string/review_ocr_results_title"
133-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:102:13-61
134            android:parentActivityName="com.example.kpitrackerapp.ui.OcrActivity" /> <!-- Parent is OcrActivity -->
134-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:103:13-57
135        <!-- Removed SmartListAnalysisActivity declaration -->
136        <activity
136-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:105:9-108:66
137            android:name="com.example.kpitrackerapp.ui.ExcelImportActivity"
137-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:106:13-51
138            android:label="Import from Excel"
138-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:107:13-46
139            android:parentActivityName="com.example.kpitrackerapp.ui.KpiDetailActivity" />
139-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:108:13-63
140        <activity
140-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:109:9-112:68
141            android:name="com.example.kpitrackerapp.ui.ExcelReviewActivity"
141-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:110:13-51
142            android:label="Review Excel Import"
142-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:111:13-48
143            android:parentActivityName="com.example.kpitrackerapp.ui.ExcelImportActivity" />
143-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:112:13-65
144        <activity
144-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:114:9-118:70
145            android:name="com.example.kpitrackerapp.ui.UserKpiListActivity"
145-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:115:13-51
146            android:label="@string/user_kpi_list_title"
146-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:116:13-56
147            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
147-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:117:13-55
148            android:theme="@style/Theme.KPITrackerApp.NoActionBar" /> <!-- Apply NoActionBar theme -->
148-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:118:13-67
149        <activity
149-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:119:9-123:70
150            android:name="com.example.kpitrackerapp.ui.TaskManagementActivity"
150-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:120:13-54
151            android:label="Task Management"
151-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:121:13-44
152            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
152-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:122:13-55
153            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
153-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:123:13-67
154        <activity
154-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:124:9-128:70
155            android:name="com.example.kpitrackerapp.ui.TaskReportActivity"
155-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:125:13-50
156            android:label="Task Reports"
156-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:126:13-41
157            android:parentActivityName="com.example.kpitrackerapp.ui.TaskManagementActivity"
157-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:127:13-68
158            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
158-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:128:13-67
159
160        <!-- Login and User Management Activities -->
161        <activity
161-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:131:9-135:40
162            android:name="com.example.kpitrackerapp.ui.LoginActivity"
162-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:132:13-45
163            android:exported="false"
163-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:135:13-37
164            android:label="Login"
164-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:133:13-34
165            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
165-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:134:13-67
166        <activity
166-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:136:9-141:40
167            android:name="com.example.kpitrackerapp.ui.CreateUserActivity"
167-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:137:13-50
168            android:exported="false"
168-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:141:13-37
169            android:label="Create User"
169-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:138:13-40
170            android:parentActivityName="com.example.kpitrackerapp.ui.LoginActivity"
170-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:139:13-59
171            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
171-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:140:13-67
172
173        <!-- Admin Dashboard Activity -->
174        <activity
174-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:144:9-149:40
175            android:name="com.example.kpitrackerapp.AdminDashboardActivity"
175-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:145:13-51
176            android:exported="false"
176-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:149:13-37
177            android:label="Admin Dashboard"
177-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:146:13-44
178            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
178-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:147:13-55
179            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
179-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:148:13-67
180
181        <!-- Chat Activities -->
182        <activity
182-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:152:9-157:40
183            android:name="com.example.kpitrackerapp.ui.ChatListActivity"
183-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:153:13-48
184            android:exported="false"
184-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:157:13-37
185            android:label="Messages"
185-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:154:13-37
186            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
186-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:155:13-55
187            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
187-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:156:13-67
188        <activity
188-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:159:9-164:40
189            android:name="com.example.kpitrackerapp.ui.ChatActivity"
189-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:160:13-44
190            android:exported="false"
190-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:164:13-37
191            android:label="Chat"
191-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:161:13-33
192            android:parentActivityName="com.example.kpitrackerapp.ui.ChatListActivity"
192-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:162:13-62
193            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
193-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:163:13-67
194
195        <!-- Task Reminder Settings Activity -->
196        <activity
196-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:167:9-172:40
197            android:name="com.example.kpitrackerapp.ui.TaskReminderSettingsActivity"
197-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:168:13-60
198            android:exported="false"
198-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:172:13-37
199            android:label="Task Reminder Settings"
199-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:169:13-51
200            android:parentActivityName="com.example.kpitrackerapp.ui.TaskManagementActivity"
200-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:170:13-68
201            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
201-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:171:13-67
202
203        <!-- Auto Send Settings Activity -->
204        <activity
204-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:175:9-180:40
205            android:name="com.example.kpitrackerapp.ui.AutoSendSettingsActivity"
205-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:176:13-56
206            android:exported="false"
206-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:180:13-37
207            android:label="Auto Send Settings"
207-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:177:13-47
208            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
208-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:178:13-55
209            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
209-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:179:13-67
210
211        <!-- Advanced Task Activity -->
212        <activity
212-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:183:9-188:40
213            android:name="com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity"
213-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:184:13-59
214            android:exported="false"
214-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:188:13-37
215            android:label="إضافة مهمة متقدمة"
215-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:185:13-46
216            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
216-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:186:13-55
217            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
217-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:187:13-67
218
219        <!-- Add other activities, services, etc. here -->
220        <activity
220-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:191:9-194:58
221            android:name="com.example.kpitrackerapp.ui.NotificationsActivity"
221-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:192:13-53
222            android:exported="false"
222-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:193:13-37
223            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
223-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:194:13-55
224
225        <!-- Firebase Messaging Service -->
226        <service
226-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:197:9-203:19
227            android:name="com.example.kpitrackerapp.services.KPIFirebaseMessagingService"
227-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:198:13-65
228            android:exported="false" >
228-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:199:13-37
229            <intent-filter>
229-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:200:13-202:29
230                <action android:name="com.google.firebase.MESSAGING_EVENT" />
230-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:201:17-78
230-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:201:25-75
231            </intent-filter>
232        </service>
233
234        <!-- Firebase Messaging default notification icon -->
235        <meta-data
235-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:206:9-208:60
236            android:name="com.google.firebase.messaging.default_notification_icon"
236-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:207:13-83
237            android:resource="@drawable/ic_notification" />
237-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:208:13-57
238
239        <!-- Firebase Messaging default notification color -->
240        <meta-data
240-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:211:9-213:52
241            android:name="com.google.firebase.messaging.default_notification_color"
241-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:212:13-84
242            android:resource="@color/purple_500" />
242-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:213:13-49
243
244        <!-- FileProvider for sharing camera image URI -->
245        <provider
246            android:name="androidx.core.content.FileProvider"
246-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:217:13-62
247            android:authorities="com.example.kpitrackerapp.provider"
247-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:218:13-60
248            android:exported="false"
248-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:219:13-37
249            android:grantUriPermissions="true" >
249-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:220:13-47
250            <meta-data
250-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:221:13-223:54
251                android:name="android.support.FILE_PROVIDER_PATHS"
251-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:222:17-67
252                android:resource="@xml/file_paths" />
252-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:223:17-51
253        </provider>
254
255        <!-- Disable WorkManager automatic initialization since we use Configuration.Provider -->
256        <provider
257            android:name="androidx.startup.InitializationProvider"
257-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:228:13-67
258            android:authorities="com.example.kpitrackerapp.androidx-startup"
258-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:229:13-68
259            android:exported="false" >
259-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:230:13-37
260            <meta-data
260-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\a5046b8718eb7718c37e191f5a52f4c5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
261                android:name="androidx.emoji2.text.EmojiCompatInitializer"
261-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\a5046b8718eb7718c37e191f5a52f4c5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
262                android:value="androidx.startup" />
262-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\a5046b8718eb7718c37e191f5a52f4c5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
263            <meta-data
263-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\8979a47168c13891bf6142b3909bb09c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
264                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
264-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\8979a47168c13891bf6142b3909bb09c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
265                android:value="androidx.startup" />
265-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\8979a47168c13891bf6142b3909bb09c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
266            <meta-data
266-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
267                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
267-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
268                android:value="androidx.startup" />
268-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
269        </provider>
270
271        <service
271-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:9:9-15:19
272            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
272-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:10:13-91
273            android:directBootAware="true"
273-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:17:13-43
274            android:exported="false" >
274-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:11:13-37
275            <meta-data
275-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:12:13-14:85
276                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
276-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:13:17-114
277                android:value="com.google.firebase.components.ComponentRegistrar" />
277-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:14:17-82
278            <meta-data
278-->[com.google.mlkit:vision-common:17.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\1063076d438e1847fa9d9ed50fcacbf0\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
279                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
279-->[com.google.mlkit:vision-common:17.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\1063076d438e1847fa9d9ed50fcacbf0\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
280                android:value="com.google.firebase.components.ComponentRegistrar" />
280-->[com.google.mlkit:vision-common:17.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\1063076d438e1847fa9d9ed50fcacbf0\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
281            <meta-data
281-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:20:13-22:85
282                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
282-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:21:17-120
283                android:value="com.google.firebase.components.ComponentRegistrar" />
283-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:22:17-82
284        </service>
285
286        <provider
286-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:9:9-13:38
287            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
287-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:10:13-78
288            android:authorities="com.example.kpitrackerapp.mlkitinitprovider"
288-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:11:13-69
289            android:exported="false"
289-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:12:13-37
290            android:initOrder="99" />
290-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:13:13-35
291
292        <service
292-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:9:9-15:19
293            android:name="com.google.firebase.components.ComponentDiscoveryService"
293-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:10:13-84
294            android:directBootAware="true"
294-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:32:13-43
295            android:exported="false" >
295-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:11:13-37
296            <meta-data
296-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:12:13-14:85
297                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
297-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:13:17-127
298                android:value="com.google.firebase.components.ComponentRegistrar" />
298-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:14:17-82
299            <meta-data
299-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:29:13-31:85
300                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
300-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:30:17-120
301                android:value="com.google.firebase.components.ComponentRegistrar" />
301-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:31:17-82
302            <meta-data
302-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:32:13-34:85
303                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
303-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:33:17-109
304                android:value="com.google.firebase.components.ComponentRegistrar" />
304-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:34:17-82
305            <meta-data
305-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\80cfd5dd6a9dcc5f71fdfc2bfdc91067\transformed\jetified-firebase-messaging-ktx-23.4.0\AndroidManifest.xml:26:13-28:85
306                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar"
306-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\80cfd5dd6a9dcc5f71fdfc2bfdc91067\transformed\jetified-firebase-messaging-ktx-23.4.0\AndroidManifest.xml:27:17-129
307                android:value="com.google.firebase.components.ComponentRegistrar" />
307-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\80cfd5dd6a9dcc5f71fdfc2bfdc91067\transformed\jetified-firebase-messaging-ktx-23.4.0\AndroidManifest.xml:28:17-82
308            <meta-data
308-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:57:13-59:85
309                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
309-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:58:17-122
310                android:value="com.google.firebase.components.ComponentRegistrar" />
310-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:59:17-82
311            <meta-data
311-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:60:13-62:85
312                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
312-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:61:17-119
313                android:value="com.google.firebase.components.ComponentRegistrar" />
313-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:62:17-82
314            <meta-data
314-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\e1e5edf221d9e2eedd44e81a2a865970\transformed\jetified-firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
315                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
315-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\e1e5edf221d9e2eedd44e81a2a865970\transformed\jetified-firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
316                android:value="com.google.firebase.components.ComponentRegistrar" />
316-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\e1e5edf221d9e2eedd44e81a2a865970\transformed\jetified-firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
317            <meta-data
317-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
318                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
318-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
319                android:value="com.google.firebase.components.ComponentRegistrar" />
319-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
320            <meta-data
320-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
321                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
321-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
322                android:value="com.google.firebase.components.ComponentRegistrar" />
322-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
323            <meta-data
323-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
324                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
324-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
325                android:value="com.google.firebase.components.ComponentRegistrar" />
325-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
326            <meta-data
326-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\0d006008fe6bff343360bc682c7a0697\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
327                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
327-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\0d006008fe6bff343360bc682c7a0697\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
328                android:value="com.google.firebase.components.ComponentRegistrar" />
328-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\0d006008fe6bff343360bc682c7a0697\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
329            <meta-data
329-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
330                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
330-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:36:17-109
331                android:value="com.google.firebase.components.ComponentRegistrar" />
331-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:37:17-82
332            <meta-data
332-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\b7c9fb2db29bcc8c4004f9c995f69ff4\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
333                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
333-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\b7c9fb2db29bcc8c4004f9c995f69ff4\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
334                android:value="com.google.firebase.components.ComponentRegistrar" />
334-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\b7c9fb2db29bcc8c4004f9c995f69ff4\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
335        </service>
336
337        <receiver
337-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:29:9-40:20
338            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
338-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:30:13-78
339            android:exported="true"
339-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:31:13-36
340            android:permission="com.google.android.c2dm.permission.SEND" >
340-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:32:13-73
341            <intent-filter>
341-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:33:13-35:29
342                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
342-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:34:17-81
342-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:34:25-78
343            </intent-filter>
344
345            <meta-data
345-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:37:13-39:40
346                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
346-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:38:17-92
347                android:value="true" />
347-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:39:17-37
348        </receiver>
349        <!--
350             FirebaseMessagingService performs security checks at runtime,
351             but set to not exported to explicitly avoid allowing another app to call it.
352        -->
353        <service
353-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:46:9-53:19
354            android:name="com.google.firebase.messaging.FirebaseMessagingService"
354-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:47:13-82
355            android:directBootAware="true"
355-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:48:13-43
356            android:exported="false" >
356-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:49:13-37
357            <intent-filter android:priority="-500" >
357-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:200:13-202:29
358                <action android:name="com.google.firebase.MESSAGING_EVENT" />
358-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:201:17-78
358-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:201:25-75
359            </intent-filter>
360        </service>
361
362        <activity
362-->[com.google.android.gms:play-services-base:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
363            android:name="com.google.android.gms.common.api.GoogleApiActivity"
363-->[com.google.android.gms:play-services-base:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
364            android:exported="false"
364-->[com.google.android.gms:play-services-base:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
365            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
365-->[com.google.android.gms:play-services-base:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
366
367        <property
367-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
368            android:name="android.adservices.AD_SERVICES_CONFIG"
368-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
369            android:resource="@xml/ga_ad_services_config" />
369-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
370
371        <provider
371-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
372            android:name="com.google.firebase.provider.FirebaseInitProvider"
372-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:24:13-77
373            android:authorities="com.example.kpitrackerapp.firebaseinitprovider"
373-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:25:13-72
374            android:directBootAware="true"
374-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:26:13-43
375            android:exported="false"
375-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:27:13-37
376            android:initOrder="100" />
376-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:28:13-36
377
378        <service
378-->[androidx.room:room-runtime:2.6.1] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
379            android:name="androidx.room.MultiInstanceInvalidationService"
379-->[androidx.room:room-runtime:2.6.1] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
380            android:directBootAware="true"
380-->[androidx.room:room-runtime:2.6.1] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
381            android:exported="false" />
381-->[androidx.room:room-runtime:2.6.1] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
382        <service
382-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
383            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
383-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
384            android:directBootAware="false"
384-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
385            android:enabled="@bool/enable_system_alarm_service_default"
385-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
386            android:exported="false" />
386-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
387        <service
387-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
388            android:name="androidx.work.impl.background.systemjob.SystemJobService"
388-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
389            android:directBootAware="false"
389-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
390            android:enabled="@bool/enable_system_job_service_default"
390-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
391            android:exported="true"
391-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
392            android:permission="android.permission.BIND_JOB_SERVICE" />
392-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
393        <service
393-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
394            android:name="androidx.work.impl.foreground.SystemForegroundService"
394-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
395            android:directBootAware="false"
395-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
396            android:enabled="@bool/enable_system_foreground_service_default"
396-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
397            android:exported="false" />
397-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
398
399        <receiver
399-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
400            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
400-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
401            android:directBootAware="false"
401-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
402            android:enabled="true"
402-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
403            android:exported="false" />
403-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
404        <receiver
404-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
405            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
405-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
406            android:directBootAware="false"
406-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
407            android:enabled="false"
407-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
408            android:exported="false" >
408-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
409            <intent-filter>
409-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
410                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
410-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
410-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
411                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
411-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
411-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
412            </intent-filter>
413        </receiver>
414        <receiver
414-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
415            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
415-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
416            android:directBootAware="false"
416-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
417            android:enabled="false"
417-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
418            android:exported="false" >
418-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
419            <intent-filter>
419-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
420                <action android:name="android.intent.action.BATTERY_OKAY" />
420-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
420-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
421                <action android:name="android.intent.action.BATTERY_LOW" />
421-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
421-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
422            </intent-filter>
423        </receiver>
424        <receiver
424-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
425            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
425-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
426            android:directBootAware="false"
426-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
427            android:enabled="false"
427-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
428            android:exported="false" >
428-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
429            <intent-filter>
429-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
430                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
430-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
430-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
431                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
431-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
431-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
432            </intent-filter>
433        </receiver>
434        <receiver
434-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
435            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
435-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
436            android:directBootAware="false"
436-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
437            android:enabled="false"
437-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
438            android:exported="false" >
438-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
439            <intent-filter>
439-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
440                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
440-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
440-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
441            </intent-filter>
442        </receiver>
443        <receiver
443-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
444            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
444-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
445            android:directBootAware="false"
445-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
446            android:enabled="false"
446-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
447            android:exported="false" >
447-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
448            <intent-filter>
448-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
449                <action android:name="android.intent.action.BOOT_COMPLETED" />
449-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
449-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
450                <action android:name="android.intent.action.TIME_SET" />
450-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
450-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
451                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
451-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
451-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
452            </intent-filter>
453        </receiver>
454        <receiver
454-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
455            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
455-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
456            android:directBootAware="false"
456-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
457            android:enabled="@bool/enable_system_alarm_service_default"
457-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
458            android:exported="false" >
458-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
459            <intent-filter>
459-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
460                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
460-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
460-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
461            </intent-filter>
462        </receiver>
463        <receiver
463-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
464            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
464-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
465            android:directBootAware="false"
465-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
466            android:enabled="true"
466-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
467            android:exported="true"
467-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
468            android:permission="android.permission.DUMP" >
468-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
469            <intent-filter>
469-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
470                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
470-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
470-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
471            </intent-filter>
472        </receiver>
473        <receiver
473-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
474            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
474-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
475            android:enabled="true"
475-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
476            android:exported="false" >
476-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
477        </receiver>
478
479        <service
479-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
480            android:name="com.google.android.gms.measurement.AppMeasurementService"
480-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
481            android:enabled="true"
481-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
482            android:exported="false" />
482-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
483        <service
483-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
484            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
484-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
485            android:enabled="true"
485-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
486            android:exported="false"
486-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
487            android:permission="android.permission.BIND_JOB_SERVICE" />
487-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
488
489        <uses-library
489-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\64b2a28493382675ec9ae515f82793b7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
490            android:name="android.ext.adservices"
490-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\64b2a28493382675ec9ae515f82793b7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
491            android:required="false" />
491-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\64b2a28493382675ec9ae515f82793b7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
492
493        <meta-data
493-->[com.google.android.gms:play-services-basement:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\44fac508c06d1fa71634b2a2e87bd97f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
494            android:name="com.google.android.gms.version"
494-->[com.google.android.gms:play-services-basement:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\44fac508c06d1fa71634b2a2e87bd97f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
495            android:value="@integer/google_play_services_version" />
495-->[com.google.android.gms:play-services-basement:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\44fac508c06d1fa71634b2a2e87bd97f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
496
497        <receiver
497-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
498            android:name="androidx.profileinstaller.ProfileInstallReceiver"
498-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
499            android:directBootAware="false"
499-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
500            android:enabled="true"
500-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
501            android:exported="true"
501-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
502            android:permission="android.permission.DUMP" >
502-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
503            <intent-filter>
503-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
504                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
504-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
504-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
505            </intent-filter>
506            <intent-filter>
506-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
507                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
507-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
507-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
508            </intent-filter>
509            <intent-filter>
509-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
510                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
510-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
510-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
511            </intent-filter>
512            <intent-filter>
512-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
513                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
513-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
513-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
514            </intent-filter>
515        </receiver>
516
517        <service
517-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
518            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
518-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
519            android:exported="false" >
519-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
520            <meta-data
520-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
521                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
521-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
522                android:value="cct" />
522-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
523        </service>
524        <service
524-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
525            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
525-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
526            android:exported="false"
526-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
527            android:permission="android.permission.BIND_JOB_SERVICE" >
527-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
528        </service>
529
530        <receiver
530-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
531            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
531-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
532            android:exported="false" />
532-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
533    </application>
534
535</manifest>
