1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.kpitrackerapp"
4    android:versionCode="1"
5    android:versionName="1.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Required for notifications on Android 13+ -->
12    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
12-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:6:5-76
12-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:6:22-74
13    <!-- Required for running foreground services (used by WorkManager when setForeground is called) -->
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
14-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:8:5-76
14-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:8:22-74
15    <!-- Required for Camera -->
16    <uses-permission android:name="android.permission.CAMERA" />
16-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:10:5-64
16-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:10:22-62
17    <!-- Required for Calendar Integration -->
18    <uses-permission android:name="android.permission.READ_CALENDAR" />
18-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:12:5-72
18-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:12:22-69
19    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
19-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:13:5-73
19-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:13:22-70
20    <!-- Required for Speech Recognition -->
21    <uses-permission android:name="android.permission.RECORD_AUDIO" />
21-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:15:5-71
21-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:15:22-68
22    <uses-permission android:name="android.permission.INTERNET" />
22-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:16:5-67
22-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:16:22-64
23    <!-- Required for Location Services -->
24    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
24-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:18:5-79
24-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:18:22-76
25    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
25-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:19:5-81
25-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:19:22-78
26    <!-- Required for Setting Alarms -->
27    <uses-permission android:name="android.permission.SET_ALARM" />
27-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:21:5-68
27-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:21:22-65
28    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
28-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:22:5-78
28-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:22:22-75
29
30    <!-- Declare camera feature, but don't require it if app can function without -->
31    <uses-feature
31-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:25:5-84
32        android:name="android.hardware.camera"
32-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:25:19-57
33        android:required="false" />
33-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:25:58-82
34
35    <!-- Queries for specific apps we want to detect (Android 11+ requirement) -->
36    <queries>
36-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:28:5-40:15
37
38        <!-- WhatsApp Regular -->
39        <package android:name="com.whatsapp" />
39-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:30:9-48
39-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:30:18-45
40        <!-- WhatsApp Business -->
41        <package android:name="com.whatsapp.w4b" />
41-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:32:9-52
41-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:32:18-49
42        <!-- Gmail -->
43        <package android:name="com.google.android.gm" />
43-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:34:9-57
43-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:34:18-54
44        <!-- Email intent -->
45        <intent>
45-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:36:9-39:18
46            <action android:name="android.intent.action.SENDTO" />
46-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:37:13-67
46-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:37:21-64
47
48            <data android:scheme="mailto" />
48-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:38:13-45
48-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:38:19-42
49        </intent>
50    </queries>
51
52    <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
53    <!-- <uses-sdk android:minSdkVersion="14"/> -->
54    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
54-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:22:5-79
54-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:22:22-76
55    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
55-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:24:5-68
55-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:24:22-65
56    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
56-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:26:5-82
56-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:26:22-79
57    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
57-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
57-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
58    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
58-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
58-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
59    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
59-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
59-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
60    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
60-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
60-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
61    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
61-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
61-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
62
63    <permission
63-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
64        android:name="com.example.kpitrackerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
64-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
65        android:protectionLevel="signature" />
65-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
66
67    <uses-permission android:name="com.example.kpitrackerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
67-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
67-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
68
69    <application
69-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:42:5-237:19
70        android:name="com.example.kpitrackerapp.KpiTrackerApplication"
70-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:43:9-46
71        android:allowBackup="true"
71-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:44:9-35
72        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
72-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
73        android:dataExtractionRules="@xml/data_extraction_rules"
73-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:45:9-65
74        android:debuggable="true"
75        android:extractNativeLibs="false"
76        android:fullBackupContent="@xml/backup_rules"
76-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:46:9-54
77        android:icon="@mipmap/ic_launcher"
77-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:48:9-43
78        android:label="@string/app_name"
78-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:47:9-41
79        android:networkSecurityConfig="@xml/network_security_config"
79-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:52:9-69
80        android:roundIcon="@mipmap/ic_launcher"
80-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:49:9-48
81        android:supportsRtl="true"
81-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:50:9-35
82        android:theme="@style/Theme.KPITrackerApp" >
82-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:51:9-51
83
84        <!-- Optional: Request OCR module download on install/update -->
85        <meta-data
85-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:56:9-58:35
86            android:name="com.google.mlkit.vision.DEPENDENCIES"
86-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:57:13-64
87            android:value="ocr" />
87-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:58:13-32
88
89        <activity
89-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:60:9-68:20
90            android:name="com.example.kpitrackerapp.MainActivity"
90-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:61:13-41
91            android:exported="true"
91-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:62:13-36
92            android:theme="@style/Theme.KPITrackerApp.NoActionBar" > <!-- Apply NoActionBar theme here -->
92-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:63:13-67
93            <intent-filter>
93-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:64:13-67:29
94                <action android:name="android.intent.action.MAIN" />
94-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:65:17-69
94-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:65:25-66
95
96                <category android:name="android.intent.category.LAUNCHER" />
96-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:66:17-77
96-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:66:27-74
97            </intent-filter>
98        </activity>
99        <activity
99-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:69:9-73:70
100            android:name="com.example.kpitrackerapp.ui.AddEditKpiActivity"
100-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:70:13-50
101            android:label="@string/add_kpi_title"
101-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:71:13-50
102            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
102-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:72:13-55
103            android:theme="@style/Theme.KPITrackerApp.NoActionBar" /> <!-- This is now for Tasks -->
103-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:73:13-67
104        <activity
104-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:74:9-78:70
105            android:name="com.example.kpitrackerapp.ui.AddEditKpiOriginalActivity"
105-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:75:13-58
106            android:label="@string/add_kpi_title"
106-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:76:13-50
107            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
107-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:77:13-55
108            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
108-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:78:13-67
109        <activity
109-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:79:9-82:58
110            android:name="com.example.kpitrackerapp.ui.KpiDetailActivity"
110-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:80:13-49
111            android:label="@string/kpi_detail_title"
111-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:81:13-53
112            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
112-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:82:13-55
113        <activity
113-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:83:9-87:70
114            android:name="com.example.kpitrackerapp.ui.ModernReportActivity"
114-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:84:13-52
115            android:label="Interactive Performance Report"
115-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:85:13-59
116            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
116-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:86:13-55
117            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
117-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:87:13-67
118        <activity
118-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:88:9-91:58
119            android:name="com.example.kpitrackerapp.ui.ExpireManagementActivity"
119-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:89:13-56
120            android:label="@string/action_expiry_management"
120-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:90:13-61
121            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
121-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:91:13-55
122        <activity
122-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:92:9-95:66
123            android:name="com.example.kpitrackerapp.ui.SearchEditProgressActivity"
123-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:93:13-58
124            android:label="@string/search_edit_progress_title"
124-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:94:13-63
125            android:parentActivityName="com.example.kpitrackerapp.ui.KpiDetailActivity" />
125-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:95:13-63
126        <activity
126-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:96:9-99:58
127            android:name="com.example.kpitrackerapp.ui.OcrActivity"
127-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:97:13-43
128            android:label="@string/ocr_activity_title"
128-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:98:13-55
129            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
129-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:99:13-55
130        <activity
130-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:100:9-103:60
131            android:name="com.example.kpitrackerapp.ui.OcrReviewActivity"
131-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:101:13-49
132            android:label="@string/review_ocr_results_title"
132-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:102:13-61
133            android:parentActivityName="com.example.kpitrackerapp.ui.OcrActivity" /> <!-- Parent is OcrActivity -->
133-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:103:13-57
134        <!-- Removed SmartListAnalysisActivity declaration -->
135        <activity
135-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:105:9-108:66
136            android:name="com.example.kpitrackerapp.ui.ExcelImportActivity"
136-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:106:13-51
137            android:label="Import from Excel"
137-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:107:13-46
138            android:parentActivityName="com.example.kpitrackerapp.ui.KpiDetailActivity" />
138-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:108:13-63
139        <activity
139-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:109:9-112:68
140            android:name="com.example.kpitrackerapp.ui.ExcelReviewActivity"
140-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:110:13-51
141            android:label="Review Excel Import"
141-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:111:13-48
142            android:parentActivityName="com.example.kpitrackerapp.ui.ExcelImportActivity" />
142-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:112:13-65
143        <activity
143-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:114:9-118:70
144            android:name="com.example.kpitrackerapp.ui.UserKpiListActivity"
144-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:115:13-51
145            android:label="@string/user_kpi_list_title"
145-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:116:13-56
146            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
146-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:117:13-55
147            android:theme="@style/Theme.KPITrackerApp.NoActionBar" /> <!-- Apply NoActionBar theme -->
147-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:118:13-67
148        <activity
148-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:119:9-123:70
149            android:name="com.example.kpitrackerapp.ui.TaskManagementActivity"
149-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:120:13-54
150            android:label="Task Management"
150-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:121:13-44
151            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
151-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:122:13-55
152            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
152-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:123:13-67
153        <activity
153-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:124:9-128:70
154            android:name="com.example.kpitrackerapp.ui.TaskReportActivity"
154-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:125:13-50
155            android:label="Task Reports"
155-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:126:13-41
156            android:parentActivityName="com.example.kpitrackerapp.ui.TaskManagementActivity"
156-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:127:13-68
157            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
157-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:128:13-67
158
159        <!-- Login and User Management Activities -->
160        <activity
160-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:131:9-135:40
161            android:name="com.example.kpitrackerapp.ui.LoginActivity"
161-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:132:13-45
162            android:exported="false"
162-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:135:13-37
163            android:label="Login"
163-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:133:13-34
164            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
164-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:134:13-67
165        <activity
165-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:136:9-141:40
166            android:name="com.example.kpitrackerapp.ui.CreateUserActivity"
166-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:137:13-50
167            android:exported="false"
167-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:141:13-37
168            android:label="Create User"
168-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:138:13-40
169            android:parentActivityName="com.example.kpitrackerapp.ui.LoginActivity"
169-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:139:13-59
170            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
170-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:140:13-67
171
172        <!-- Admin Dashboard Activity -->
173        <activity
173-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:144:9-149:40
174            android:name="com.example.kpitrackerapp.AdminDashboardActivity"
174-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:145:13-51
175            android:exported="false"
175-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:149:13-37
176            android:label="Admin Dashboard"
176-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:146:13-44
177            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
177-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:147:13-55
178            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
178-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:148:13-67
179
180        <!-- Chat Activities -->
181        <activity
181-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:152:9-157:40
182            android:name="com.example.kpitrackerapp.ui.ChatListActivity"
182-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:153:13-48
183            android:exported="false"
183-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:157:13-37
184            android:label="Messages"
184-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:154:13-37
185            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
185-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:155:13-55
186            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
186-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:156:13-67
187        <activity
187-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:159:9-164:40
188            android:name="com.example.kpitrackerapp.ui.ChatActivity"
188-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:160:13-44
189            android:exported="false"
189-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:164:13-37
190            android:label="Chat"
190-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:161:13-33
191            android:parentActivityName="com.example.kpitrackerapp.ui.ChatListActivity"
191-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:162:13-62
192            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
192-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:163:13-67
193
194        <!-- Task Reminder Settings Activity -->
195        <activity
195-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:167:9-172:40
196            android:name="com.example.kpitrackerapp.ui.TaskReminderSettingsActivity"
196-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:168:13-60
197            android:exported="false"
197-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:172:13-37
198            android:label="Task Reminder Settings"
198-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:169:13-51
199            android:parentActivityName="com.example.kpitrackerapp.ui.TaskManagementActivity"
199-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:170:13-68
200            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
200-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:171:13-67
201
202        <!-- Auto Send Settings Activity -->
203        <activity
203-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:175:9-180:40
204            android:name="com.example.kpitrackerapp.ui.AutoSendSettingsActivity"
204-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:176:13-56
205            android:exported="false"
205-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:180:13-37
206            android:label="Auto Send Settings"
206-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:177:13-47
207            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
207-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:178:13-55
208            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
208-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:179:13-67
209
210        <!-- Advanced Task Activity -->
211        <activity
211-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:183:9-188:40
212            android:name="com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity"
212-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:184:13-59
213            android:exported="false"
213-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:188:13-37
214            android:label="إضافة مهمة متقدمة"
214-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:185:13-46
215            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
215-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:186:13-55
216            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
216-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:187:13-67
217
218        <!-- Add other activities, services, etc. here -->
219        <activity
219-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:191:9-194:58
220            android:name="com.example.kpitrackerapp.ui.NotificationsActivity"
220-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:192:13-53
221            android:exported="false"
221-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:193:13-37
222            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
222-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:194:13-55
223
224        <!-- Firebase Messaging Service -->
225        <service
225-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:197:9-203:19
226            android:name="com.example.kpitrackerapp.services.KPIFirebaseMessagingService"
226-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:198:13-65
227            android:exported="false" >
227-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:199:13-37
228            <intent-filter>
228-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:200:13-202:29
229                <action android:name="com.google.firebase.MESSAGING_EVENT" />
229-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:201:17-78
229-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:201:25-75
230            </intent-filter>
231        </service>
232
233        <!-- Firebase Messaging default notification icon -->
234        <meta-data
234-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:206:9-208:60
235            android:name="com.google.firebase.messaging.default_notification_icon"
235-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:207:13-83
236            android:resource="@drawable/ic_notification" />
236-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:208:13-57
237
238        <!-- Firebase Messaging default notification color -->
239        <meta-data
239-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:211:9-213:52
240            android:name="com.google.firebase.messaging.default_notification_color"
240-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:212:13-84
241            android:resource="@color/purple_500" />
241-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:213:13-49
242
243        <!-- FileProvider for sharing camera image URI -->
244        <provider
245            android:name="androidx.core.content.FileProvider"
245-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:217:13-62
246            android:authorities="com.example.kpitrackerapp.provider"
246-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:218:13-60
247            android:exported="false"
247-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:219:13-37
248            android:grantUriPermissions="true" >
248-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:220:13-47
249            <meta-data
249-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:221:13-223:54
250                android:name="android.support.FILE_PROVIDER_PATHS"
250-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:222:17-67
251                android:resource="@xml/file_paths" />
251-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:223:17-51
252        </provider>
253
254        <!-- Disable WorkManager automatic initialization since we use Configuration.Provider -->
255        <provider
256            android:name="androidx.startup.InitializationProvider"
256-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:228:13-67
257            android:authorities="com.example.kpitrackerapp.androidx-startup"
257-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:229:13-68
258            android:exported="false" >
258-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:230:13-37
259            <meta-data
259-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5046b8718eb7718c37e191f5a52f4c5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
260                android:name="androidx.emoji2.text.EmojiCompatInitializer"
260-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5046b8718eb7718c37e191f5a52f4c5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
261                android:value="androidx.startup" />
261-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5046b8718eb7718c37e191f5a52f4c5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
262            <meta-data
262-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8979a47168c13891bf6142b3909bb09c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
263                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
263-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8979a47168c13891bf6142b3909bb09c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
264                android:value="androidx.startup" />
264-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8979a47168c13891bf6142b3909bb09c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
265            <meta-data
265-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
266                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
266-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
267                android:value="androidx.startup" />
267-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
268        </provider>
269
270        <service
270-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:9:9-15:19
271            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
271-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:10:13-91
272            android:directBootAware="true"
272-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:17:13-43
273            android:exported="false" >
273-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:11:13-37
274            <meta-data
274-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:12:13-14:85
275                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
275-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:13:17-114
276                android:value="com.google.firebase.components.ComponentRegistrar" />
276-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:14:17-82
277            <meta-data
277-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1063076d438e1847fa9d9ed50fcacbf0\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
278                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
278-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1063076d438e1847fa9d9ed50fcacbf0\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
279                android:value="com.google.firebase.components.ComponentRegistrar" />
279-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1063076d438e1847fa9d9ed50fcacbf0\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
280            <meta-data
280-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:20:13-22:85
281                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
281-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:21:17-120
282                android:value="com.google.firebase.components.ComponentRegistrar" />
282-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:22:17-82
283        </service>
284
285        <provider
285-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:9:9-13:38
286            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
286-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:10:13-78
287            android:authorities="com.example.kpitrackerapp.mlkitinitprovider"
287-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:11:13-69
288            android:exported="false"
288-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:12:13-37
289            android:initOrder="99" />
289-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:13:13-35
290
291        <service
291-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:9:9-15:19
292            android:name="com.google.firebase.components.ComponentDiscoveryService"
292-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:10:13-84
293            android:directBootAware="true"
293-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:32:13-43
294            android:exported="false" >
294-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:11:13-37
295            <meta-data
295-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:12:13-14:85
296                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
296-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:13:17-127
297                android:value="com.google.firebase.components.ComponentRegistrar" />
297-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:14:17-82
298            <meta-data
298-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:29:13-31:85
299                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
299-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:30:17-120
300                android:value="com.google.firebase.components.ComponentRegistrar" />
300-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:31:17-82
301            <meta-data
301-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:32:13-34:85
302                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
302-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:33:17-109
303                android:value="com.google.firebase.components.ComponentRegistrar" />
303-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:34:17-82
304            <meta-data
304-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80cfd5dd6a9dcc5f71fdfc2bfdc91067\transformed\jetified-firebase-messaging-ktx-23.4.0\AndroidManifest.xml:26:13-28:85
305                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar"
305-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80cfd5dd6a9dcc5f71fdfc2bfdc91067\transformed\jetified-firebase-messaging-ktx-23.4.0\AndroidManifest.xml:27:17-129
306                android:value="com.google.firebase.components.ComponentRegistrar" />
306-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80cfd5dd6a9dcc5f71fdfc2bfdc91067\transformed\jetified-firebase-messaging-ktx-23.4.0\AndroidManifest.xml:28:17-82
307            <meta-data
307-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:57:13-59:85
308                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
308-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:58:17-122
309                android:value="com.google.firebase.components.ComponentRegistrar" />
309-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:59:17-82
310            <meta-data
310-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:60:13-62:85
311                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
311-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:61:17-119
312                android:value="com.google.firebase.components.ComponentRegistrar" />
312-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:62:17-82
313            <meta-data
313-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1e5edf221d9e2eedd44e81a2a865970\transformed\jetified-firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
314                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
314-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1e5edf221d9e2eedd44e81a2a865970\transformed\jetified-firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
315                android:value="com.google.firebase.components.ComponentRegistrar" />
315-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1e5edf221d9e2eedd44e81a2a865970\transformed\jetified-firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
316            <meta-data
316-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
317                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
317-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
318                android:value="com.google.firebase.components.ComponentRegistrar" />
318-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
319            <meta-data
319-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
320                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
320-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
321                android:value="com.google.firebase.components.ComponentRegistrar" />
321-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
322            <meta-data
322-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
323                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
323-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
324                android:value="com.google.firebase.components.ComponentRegistrar" />
324-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
325            <meta-data
325-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d006008fe6bff343360bc682c7a0697\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
326                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
326-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d006008fe6bff343360bc682c7a0697\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
327                android:value="com.google.firebase.components.ComponentRegistrar" />
327-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d006008fe6bff343360bc682c7a0697\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
328            <meta-data
328-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
329                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
329-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:36:17-109
330                android:value="com.google.firebase.components.ComponentRegistrar" />
330-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:37:17-82
331            <meta-data
331-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7c9fb2db29bcc8c4004f9c995f69ff4\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
332                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
332-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7c9fb2db29bcc8c4004f9c995f69ff4\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
333                android:value="com.google.firebase.components.ComponentRegistrar" />
333-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7c9fb2db29bcc8c4004f9c995f69ff4\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
334        </service>
335
336        <receiver
336-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:29:9-40:20
337            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
337-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:30:13-78
338            android:exported="true"
338-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:31:13-36
339            android:permission="com.google.android.c2dm.permission.SEND" >
339-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:32:13-73
340            <intent-filter>
340-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:33:13-35:29
341                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
341-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:34:17-81
341-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:34:25-78
342            </intent-filter>
343
344            <meta-data
344-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:37:13-39:40
345                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
345-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:38:17-92
346                android:value="true" />
346-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:39:17-37
347        </receiver>
348        <!--
349             FirebaseMessagingService performs security checks at runtime,
350             but set to not exported to explicitly avoid allowing another app to call it.
351        -->
352        <service
352-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:46:9-53:19
353            android:name="com.google.firebase.messaging.FirebaseMessagingService"
353-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:47:13-82
354            android:directBootAware="true"
354-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:48:13-43
355            android:exported="false" >
355-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:49:13-37
356            <intent-filter android:priority="-500" >
356-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:200:13-202:29
357                <action android:name="com.google.firebase.MESSAGING_EVENT" />
357-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:201:17-78
357-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:201:25-75
358            </intent-filter>
359        </service>
360
361        <activity
361-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
362            android:name="com.google.android.gms.common.api.GoogleApiActivity"
362-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
363            android:exported="false"
363-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
364            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
364-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
365
366        <property
366-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
367            android:name="android.adservices.AD_SERVICES_CONFIG"
367-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
368            android:resource="@xml/ga_ad_services_config" />
368-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
369
370        <provider
370-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
371            android:name="com.google.firebase.provider.FirebaseInitProvider"
371-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:24:13-77
372            android:authorities="com.example.kpitrackerapp.firebaseinitprovider"
372-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:25:13-72
373            android:directBootAware="true"
373-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:26:13-43
374            android:exported="false"
374-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:27:13-37
375            android:initOrder="100" />
375-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:28:13-36
376
377        <service
377-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
378            android:name="androidx.room.MultiInstanceInvalidationService"
378-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
379            android:directBootAware="true"
379-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
380            android:exported="false" />
380-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
381        <service
381-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
382            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
382-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
383            android:directBootAware="false"
383-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
384            android:enabled="@bool/enable_system_alarm_service_default"
384-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
385            android:exported="false" />
385-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
386        <service
386-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
387            android:name="androidx.work.impl.background.systemjob.SystemJobService"
387-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
388            android:directBootAware="false"
388-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
389            android:enabled="@bool/enable_system_job_service_default"
389-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
390            android:exported="true"
390-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
391            android:permission="android.permission.BIND_JOB_SERVICE" />
391-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
392        <service
392-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
393            android:name="androidx.work.impl.foreground.SystemForegroundService"
393-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
394            android:directBootAware="false"
394-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
395            android:enabled="@bool/enable_system_foreground_service_default"
395-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
396            android:exported="false" />
396-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
397
398        <receiver
398-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
399            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
399-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
400            android:directBootAware="false"
400-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
401            android:enabled="true"
401-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
402            android:exported="false" />
402-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
403        <receiver
403-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
404            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
404-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
405            android:directBootAware="false"
405-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
406            android:enabled="false"
406-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
407            android:exported="false" >
407-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
408            <intent-filter>
408-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
409                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
409-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
409-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
410                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
410-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
410-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
411            </intent-filter>
412        </receiver>
413        <receiver
413-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
414            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
414-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
415            android:directBootAware="false"
415-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
416            android:enabled="false"
416-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
417            android:exported="false" >
417-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
418            <intent-filter>
418-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
419                <action android:name="android.intent.action.BATTERY_OKAY" />
419-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
419-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
420                <action android:name="android.intent.action.BATTERY_LOW" />
420-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
420-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
421            </intent-filter>
422        </receiver>
423        <receiver
423-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
424            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
424-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
425            android:directBootAware="false"
425-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
426            android:enabled="false"
426-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
427            android:exported="false" >
427-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
428            <intent-filter>
428-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
429                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
429-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
429-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
430                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
430-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
430-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
431            </intent-filter>
432        </receiver>
433        <receiver
433-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
434            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
434-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
435            android:directBootAware="false"
435-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
436            android:enabled="false"
436-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
437            android:exported="false" >
437-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
438            <intent-filter>
438-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
439                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
439-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
439-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
440            </intent-filter>
441        </receiver>
442        <receiver
442-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
443            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
443-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
444            android:directBootAware="false"
444-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
445            android:enabled="false"
445-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
446            android:exported="false" >
446-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
447            <intent-filter>
447-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
448                <action android:name="android.intent.action.BOOT_COMPLETED" />
448-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
448-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
449                <action android:name="android.intent.action.TIME_SET" />
449-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
449-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
450                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
450-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
450-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
451            </intent-filter>
452        </receiver>
453        <receiver
453-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
454            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
454-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
455            android:directBootAware="false"
455-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
456            android:enabled="@bool/enable_system_alarm_service_default"
456-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
457            android:exported="false" >
457-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
458            <intent-filter>
458-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
459                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
459-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
459-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
460            </intent-filter>
461        </receiver>
462        <receiver
462-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
463            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
463-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
464            android:directBootAware="false"
464-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
465            android:enabled="true"
465-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
466            android:exported="true"
466-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
467            android:permission="android.permission.DUMP" >
467-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
468            <intent-filter>
468-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
469                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
469-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
469-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
470            </intent-filter>
471        </receiver>
472        <receiver
472-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
473            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
473-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
474            android:enabled="true"
474-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
475            android:exported="false" >
475-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
476        </receiver>
477
478        <service
478-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
479            android:name="com.google.android.gms.measurement.AppMeasurementService"
479-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
480            android:enabled="true"
480-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
481            android:exported="false" />
481-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
482        <service
482-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
483            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
483-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
484            android:enabled="true"
484-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
485            android:exported="false"
485-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
486            android:permission="android.permission.BIND_JOB_SERVICE" />
486-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
487
488        <uses-library
488-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64b2a28493382675ec9ae515f82793b7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
489            android:name="android.ext.adservices"
489-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64b2a28493382675ec9ae515f82793b7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
490            android:required="false" />
490-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64b2a28493382675ec9ae515f82793b7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
491
492        <meta-data
492-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44fac508c06d1fa71634b2a2e87bd97f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
493            android:name="com.google.android.gms.version"
493-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44fac508c06d1fa71634b2a2e87bd97f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
494            android:value="@integer/google_play_services_version" />
494-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44fac508c06d1fa71634b2a2e87bd97f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
495
496        <receiver
496-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
497            android:name="androidx.profileinstaller.ProfileInstallReceiver"
497-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
498            android:directBootAware="false"
498-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
499            android:enabled="true"
499-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
500            android:exported="true"
500-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
501            android:permission="android.permission.DUMP" >
501-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
502            <intent-filter>
502-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
503                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
503-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
503-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
504            </intent-filter>
505            <intent-filter>
505-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
506                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
506-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
506-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
507            </intent-filter>
508            <intent-filter>
508-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
509                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
509-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
509-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
510            </intent-filter>
511            <intent-filter>
511-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
512                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
512-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
512-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
513            </intent-filter>
514        </receiver>
515
516        <service
516-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
517            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
517-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
518            android:exported="false" >
518-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
519            <meta-data
519-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
520                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
520-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
521                android:value="cct" />
521-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
522        </service>
523        <service
523-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
524            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
524-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
525            android:exported="false"
525-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
526            android:permission="android.permission.BIND_JOB_SERVICE" >
526-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
527        </service>
528
529        <receiver
529-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
530            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
530-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
531            android:exported="false" />
531-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
532    </application>
533
534</manifest>
