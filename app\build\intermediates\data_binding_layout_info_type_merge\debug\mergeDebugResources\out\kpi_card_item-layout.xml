<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="kpi_card_item" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\kpi_card_item.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/kpi_card_item_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="411" endOffset="51"/></Target><Target id="@+id/kpiTitleTextView" view="TextView"><Expressions/><location startLine="21" startOffset="8" endLine="31" endOffset="47"/></Target><Target id="@+id/starRatingTextView" view="TextView"><Expressions/><location startLine="34" startOffset="8" endLine="44" endOffset="39"/></Target><Target id="@+id/ownerChip" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="47" startOffset="8" endLine="57" endOffset="34"/></Target><Target id="@+id/kpiSubtitleTextView" view="TextView"><Expressions/><location startLine="60" startOffset="9" endLine="70" endOffset="48"/></Target><Target id="@+id/targetIcon" view="ImageView"><Expressions/><location startLine="74" startOffset="8" endLine="83" endOffset="63"/></Target><Target id="@+id/targetTextView" view="TextView"><Expressions/><location startLine="86" startOffset="8" endLine="96" endOffset="64"/></Target><Target id="@+id/targetValueTextView" view="TextView"><Expressions/><location startLine="98" startOffset="8" endLine="108" endOffset="33"/></Target><Target id="@+id/currentIcon" view="ImageView"><Expressions/><location startLine="111" startOffset="8" endLine="120" endOffset="64"/></Target><Target id="@+id/currentTextView" view="TextView"><Expressions/><location startLine="123" startOffset="8" endLine="133" endOffset="65"/></Target><Target id="@+id/currentValueTextView" view="TextView"><Expressions/><location startLine="135" startOffset="8" endLine="145" endOffset="30"/></Target><Target id="@+id/currentPeriodTargetIcon" view="ImageView"><Expressions/><location startLine="148" startOffset="8" endLine="157" endOffset="92"/></Target><Target id="@+id/currentPeriodTargetTextView" view="TextView"><Expressions/><location startLine="160" startOffset="8" endLine="170" endOffset="77"/></Target><Target id="@+id/currentPeriodTargetValueTextView" view="TextView"><Expressions/><location startLine="172" startOffset="8" endLine="182" endOffset="33"/></Target><Target id="@+id/currentPeriodAchievementIcon" view="ImageView"><Expressions/><location startLine="185" startOffset="8" endLine="194" endOffset="97"/></Target><Target id="@+id/currentPeriodAchievementTextView" view="TextView"><Expressions/><location startLine="197" startOffset="8" endLine="207" endOffset="82"/></Target><Target id="@+id/currentPeriodAchievementValueTextView" view="TextView"><Expressions/><location startLine="209" startOffset="8" endLine="219" endOffset="31"/></Target><Target id="@+id/remainingIcon" view="ImageView"><Expressions/><location startLine="224" startOffset="8" endLine="233" endOffset="90"/></Target><Target id="@+id/remainingTextView" view="TextView"><Expressions/><location startLine="236" startOffset="8" endLine="246" endOffset="67"/></Target><Target id="@+id/remainingValueTextView" view="TextView"><Expressions/><location startLine="248" startOffset="8" endLine="258" endOffset="29"/></Target><Target id="@+id/requiredDailyIcon" view="ImageView"><Expressions/><location startLine="261" startOffset="8" endLine="270" endOffset="85"/></Target><Target id="@+id/requiredDailyTextView" view="TextView"><Expressions/><location startLine="273" startOffset="8" endLine="283" endOffset="71"/></Target><Target id="@+id/requiredDailyValueTextView" view="TextView"><Expressions/><location startLine="285" startOffset="8" endLine="295" endOffset="53"/></Target><Target id="@+id/lastUpdateIcon" view="ImageView"><Expressions/><location startLine="300" startOffset="8" endLine="309" endOffset="82"/></Target><Target id="@+id/lastUpdateTextView" view="TextView"><Expressions/><location startLine="312" startOffset="8" endLine="322" endOffset="68"/></Target><Target id="@+id/lastUpdateValueTextView" view="TextView"><Expressions/><location startLine="324" startOffset="8" endLine="334" endOffset="37"/></Target><Target id="@+id/progressIndicator" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="338" startOffset="8" endLine="351" endOffset="33"/></Target><Target id="@+id/percentageTextView" view="TextView"><Expressions/><location startLine="354" startOffset="8" endLine="364" endOffset="30"/></Target><Target id="@+id/currentAchievementProgressIndicator" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="367" startOffset="8" endLine="380" endOffset="33"/></Target><Target id="@+id/currentAchievementPercentageTextView" view="TextView"><Expressions/><location startLine="383" startOffset="8" endLine="394" endOffset="30"/></Target><Target id="@+id/currentAchievementLabelTextView" view="TextView"><Expressions/><location startLine="397" startOffset="8" endLine="408" endOffset="44"/></Target></Targets></Layout>