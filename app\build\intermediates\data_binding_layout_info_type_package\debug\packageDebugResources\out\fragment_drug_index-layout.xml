<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_drug_index" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\fragment_drug_index.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_drug_index_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="178" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="18" endOffset="53"/></Target><Target id="@+id/searchEditText" view="EditText"><Expressions/><location startLine="32" startOffset="12" endLine="39" endOffset="51"/></Target><Target id="@+id/searchResultsListView" view="ListView"><Expressions/><location startLine="41" startOffset="12" endLine="49" endOffset="43"/></Target><Target id="@+id/drugNameTextView" view="TextView"><Expressions/><location startLine="51" startOffset="12" endLine="58" endOffset="43"/></Target><Target id="@+id/usesTextView" view="TextView"><Expressions/><location startLine="80" startOffset="20" endLine="85" endOffset="42"/></Target><Target id="@+id/interactionsTextView" view="TextView"><Expressions/><location startLine="109" startOffset="20" endLine="114" endOffset="42"/></Target><Target id="@+id/sideEffectsTextView" view="TextView"><Expressions/><location startLine="138" startOffset="20" endLine="143" endOffset="42"/></Target><Target id="@+id/usageInstructionsTextView" view="TextView"><Expressions/><location startLine="167" startOffset="20" endLine="172" endOffset="42"/></Target></Targets></Layout>