// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityDateConverterBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final RadioGroup conversionTypeGroup;

  @NonNull
  public final MaterialButton convertButton;

  @NonNull
  public final MaterialCardView dayCard;

  @NonNull
  public final TextView dayText;

  @NonNull
  public final MaterialCardView monthCard;

  @NonNull
  public final TextView monthText;

  @NonNull
  public final RadioButton radioHijriToMiladi;

  @NonNull
  public final RadioButton radioMiladiToHijri;

  @NonNull
  public final TextView resultDayName;

  @NonNull
  public final TextView resultHijriDate;

  @NonNull
  public final TextView resultHijriMonth;

  @NonNull
  public final TextView resultHijriMonthDays;

  @NonNull
  public final TextView resultMiladiDate;

  @NonNull
  public final TextView resultMiladiMonth;

  @NonNull
  public final TextView resultMiladiMonthDays;

  @NonNull
  public final TextView resultSyrianiMonth;

  @NonNull
  public final MaterialToolbar toolbar;

  @NonNull
  public final MaterialCardView yearCard;

  @NonNull
  public final TextView yearText;

  private ActivityDateConverterBinding(@NonNull CoordinatorLayout rootView,
      @NonNull AppBarLayout appBarLayout, @NonNull RadioGroup conversionTypeGroup,
      @NonNull MaterialButton convertButton, @NonNull MaterialCardView dayCard,
      @NonNull TextView dayText, @NonNull MaterialCardView monthCard, @NonNull TextView monthText,
      @NonNull RadioButton radioHijriToMiladi, @NonNull RadioButton radioMiladiToHijri,
      @NonNull TextView resultDayName, @NonNull TextView resultHijriDate,
      @NonNull TextView resultHijriMonth, @NonNull TextView resultHijriMonthDays,
      @NonNull TextView resultMiladiDate, @NonNull TextView resultMiladiMonth,
      @NonNull TextView resultMiladiMonthDays, @NonNull TextView resultSyrianiMonth,
      @NonNull MaterialToolbar toolbar, @NonNull MaterialCardView yearCard,
      @NonNull TextView yearText) {
    this.rootView = rootView;
    this.appBarLayout = appBarLayout;
    this.conversionTypeGroup = conversionTypeGroup;
    this.convertButton = convertButton;
    this.dayCard = dayCard;
    this.dayText = dayText;
    this.monthCard = monthCard;
    this.monthText = monthText;
    this.radioHijriToMiladi = radioHijriToMiladi;
    this.radioMiladiToHijri = radioMiladiToHijri;
    this.resultDayName = resultDayName;
    this.resultHijriDate = resultHijriDate;
    this.resultHijriMonth = resultHijriMonth;
    this.resultHijriMonthDays = resultHijriMonthDays;
    this.resultMiladiDate = resultMiladiDate;
    this.resultMiladiMonth = resultMiladiMonth;
    this.resultMiladiMonthDays = resultMiladiMonthDays;
    this.resultSyrianiMonth = resultSyrianiMonth;
    this.toolbar = toolbar;
    this.yearCard = yearCard;
    this.yearText = yearText;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityDateConverterBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityDateConverterBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_date_converter, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityDateConverterBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appBarLayout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.conversionTypeGroup;
      RadioGroup conversionTypeGroup = ViewBindings.findChildViewById(rootView, id);
      if (conversionTypeGroup == null) {
        break missingId;
      }

      id = R.id.convertButton;
      MaterialButton convertButton = ViewBindings.findChildViewById(rootView, id);
      if (convertButton == null) {
        break missingId;
      }

      id = R.id.dayCard;
      MaterialCardView dayCard = ViewBindings.findChildViewById(rootView, id);
      if (dayCard == null) {
        break missingId;
      }

      id = R.id.dayText;
      TextView dayText = ViewBindings.findChildViewById(rootView, id);
      if (dayText == null) {
        break missingId;
      }

      id = R.id.monthCard;
      MaterialCardView monthCard = ViewBindings.findChildViewById(rootView, id);
      if (monthCard == null) {
        break missingId;
      }

      id = R.id.monthText;
      TextView monthText = ViewBindings.findChildViewById(rootView, id);
      if (monthText == null) {
        break missingId;
      }

      id = R.id.radioHijriToMiladi;
      RadioButton radioHijriToMiladi = ViewBindings.findChildViewById(rootView, id);
      if (radioHijriToMiladi == null) {
        break missingId;
      }

      id = R.id.radioMiladiToHijri;
      RadioButton radioMiladiToHijri = ViewBindings.findChildViewById(rootView, id);
      if (radioMiladiToHijri == null) {
        break missingId;
      }

      id = R.id.resultDayName;
      TextView resultDayName = ViewBindings.findChildViewById(rootView, id);
      if (resultDayName == null) {
        break missingId;
      }

      id = R.id.resultHijriDate;
      TextView resultHijriDate = ViewBindings.findChildViewById(rootView, id);
      if (resultHijriDate == null) {
        break missingId;
      }

      id = R.id.resultHijriMonth;
      TextView resultHijriMonth = ViewBindings.findChildViewById(rootView, id);
      if (resultHijriMonth == null) {
        break missingId;
      }

      id = R.id.resultHijriMonthDays;
      TextView resultHijriMonthDays = ViewBindings.findChildViewById(rootView, id);
      if (resultHijriMonthDays == null) {
        break missingId;
      }

      id = R.id.resultMiladiDate;
      TextView resultMiladiDate = ViewBindings.findChildViewById(rootView, id);
      if (resultMiladiDate == null) {
        break missingId;
      }

      id = R.id.resultMiladiMonth;
      TextView resultMiladiMonth = ViewBindings.findChildViewById(rootView, id);
      if (resultMiladiMonth == null) {
        break missingId;
      }

      id = R.id.resultMiladiMonthDays;
      TextView resultMiladiMonthDays = ViewBindings.findChildViewById(rootView, id);
      if (resultMiladiMonthDays == null) {
        break missingId;
      }

      id = R.id.resultSyrianiMonth;
      TextView resultSyrianiMonth = ViewBindings.findChildViewById(rootView, id);
      if (resultSyrianiMonth == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.yearCard;
      MaterialCardView yearCard = ViewBindings.findChildViewById(rootView, id);
      if (yearCard == null) {
        break missingId;
      }

      id = R.id.yearText;
      TextView yearText = ViewBindings.findChildViewById(rootView, id);
      if (yearText == null) {
        break missingId;
      }

      return new ActivityDateConverterBinding((CoordinatorLayout) rootView, appBarLayout,
          conversionTypeGroup, convertButton, dayCard, dayText, monthCard, monthText,
          radioHijriToMiladi, radioMiladiToHijri, resultDayName, resultHijriDate, resultHijriMonth,
          resultHijriMonthDays, resultMiladiDate, resultMiladiMonth, resultMiladiMonthDays,
          resultSyrianiMonth, toolbar, yearCard, yearText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
