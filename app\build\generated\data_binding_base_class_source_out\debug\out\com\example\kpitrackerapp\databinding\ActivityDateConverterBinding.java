// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityDateConverterBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final Spinner amPmSpinner;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final Button calculateShiftButton;

  @NonNull
  public final RadioGroup conversionTypeGroup;

  @NonNull
  public final MaterialButton convertButton;

  @NonNull
  public final EditText dayInput;

  @NonNull
  public final EditText hoursInput;

  @NonNull
  public final LinearLayout inputModeLayout;

  @NonNull
  public final EditText monthInput;

  @NonNull
  public final RadioButton radioHijriToMiladi;

  @NonNull
  public final RadioButton radioMiladiToHijri;

  @NonNull
  public final TextView resultDayName;

  @NonNull
  public final TextView resultEndTime;

  @NonNull
  public final TextView resultHijriDate;

  @NonNull
  public final TextView resultHijriMonth;

  @NonNull
  public final TextView resultHijriMonthDays;

  @NonNull
  public final TextView resultMiladiDate;

  @NonNull
  public final TextView resultMiladiMonth;

  @NonNull
  public final TextView resultMiladiMonthDays;

  @NonNull
  public final TextView resultStartTime;

  @NonNull
  public final TextView resultSyrianiMonth;

  @NonNull
  public final TextView resultTotalHours;

  @NonNull
  public final LinearLayout shiftCalculatorSection;

  @NonNull
  public final LinearLayout shiftResultSection;

  @NonNull
  public final EditText startTimeInput;

  @NonNull
  public final MaterialToolbar toolbar;

  @NonNull
  public final Spinner toolsSpinner;

  @NonNull
  public final EditText yearInput;

  private ActivityDateConverterBinding(@NonNull CoordinatorLayout rootView,
      @NonNull Spinner amPmSpinner, @NonNull AppBarLayout appBarLayout,
      @NonNull Button calculateShiftButton, @NonNull RadioGroup conversionTypeGroup,
      @NonNull MaterialButton convertButton, @NonNull EditText dayInput,
      @NonNull EditText hoursInput, @NonNull LinearLayout inputModeLayout,
      @NonNull EditText monthInput, @NonNull RadioButton radioHijriToMiladi,
      @NonNull RadioButton radioMiladiToHijri, @NonNull TextView resultDayName,
      @NonNull TextView resultEndTime, @NonNull TextView resultHijriDate,
      @NonNull TextView resultHijriMonth, @NonNull TextView resultHijriMonthDays,
      @NonNull TextView resultMiladiDate, @NonNull TextView resultMiladiMonth,
      @NonNull TextView resultMiladiMonthDays, @NonNull TextView resultStartTime,
      @NonNull TextView resultSyrianiMonth, @NonNull TextView resultTotalHours,
      @NonNull LinearLayout shiftCalculatorSection, @NonNull LinearLayout shiftResultSection,
      @NonNull EditText startTimeInput, @NonNull MaterialToolbar toolbar,
      @NonNull Spinner toolsSpinner, @NonNull EditText yearInput) {
    this.rootView = rootView;
    this.amPmSpinner = amPmSpinner;
    this.appBarLayout = appBarLayout;
    this.calculateShiftButton = calculateShiftButton;
    this.conversionTypeGroup = conversionTypeGroup;
    this.convertButton = convertButton;
    this.dayInput = dayInput;
    this.hoursInput = hoursInput;
    this.inputModeLayout = inputModeLayout;
    this.monthInput = monthInput;
    this.radioHijriToMiladi = radioHijriToMiladi;
    this.radioMiladiToHijri = radioMiladiToHijri;
    this.resultDayName = resultDayName;
    this.resultEndTime = resultEndTime;
    this.resultHijriDate = resultHijriDate;
    this.resultHijriMonth = resultHijriMonth;
    this.resultHijriMonthDays = resultHijriMonthDays;
    this.resultMiladiDate = resultMiladiDate;
    this.resultMiladiMonth = resultMiladiMonth;
    this.resultMiladiMonthDays = resultMiladiMonthDays;
    this.resultStartTime = resultStartTime;
    this.resultSyrianiMonth = resultSyrianiMonth;
    this.resultTotalHours = resultTotalHours;
    this.shiftCalculatorSection = shiftCalculatorSection;
    this.shiftResultSection = shiftResultSection;
    this.startTimeInput = startTimeInput;
    this.toolbar = toolbar;
    this.toolsSpinner = toolsSpinner;
    this.yearInput = yearInput;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityDateConverterBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityDateConverterBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_date_converter, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityDateConverterBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.amPmSpinner;
      Spinner amPmSpinner = ViewBindings.findChildViewById(rootView, id);
      if (amPmSpinner == null) {
        break missingId;
      }

      id = R.id.appBarLayout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.calculateShiftButton;
      Button calculateShiftButton = ViewBindings.findChildViewById(rootView, id);
      if (calculateShiftButton == null) {
        break missingId;
      }

      id = R.id.conversionTypeGroup;
      RadioGroup conversionTypeGroup = ViewBindings.findChildViewById(rootView, id);
      if (conversionTypeGroup == null) {
        break missingId;
      }

      id = R.id.convertButton;
      MaterialButton convertButton = ViewBindings.findChildViewById(rootView, id);
      if (convertButton == null) {
        break missingId;
      }

      id = R.id.dayInput;
      EditText dayInput = ViewBindings.findChildViewById(rootView, id);
      if (dayInput == null) {
        break missingId;
      }

      id = R.id.hoursInput;
      EditText hoursInput = ViewBindings.findChildViewById(rootView, id);
      if (hoursInput == null) {
        break missingId;
      }

      id = R.id.inputModeLayout;
      LinearLayout inputModeLayout = ViewBindings.findChildViewById(rootView, id);
      if (inputModeLayout == null) {
        break missingId;
      }

      id = R.id.monthInput;
      EditText monthInput = ViewBindings.findChildViewById(rootView, id);
      if (monthInput == null) {
        break missingId;
      }

      id = R.id.radioHijriToMiladi;
      RadioButton radioHijriToMiladi = ViewBindings.findChildViewById(rootView, id);
      if (radioHijriToMiladi == null) {
        break missingId;
      }

      id = R.id.radioMiladiToHijri;
      RadioButton radioMiladiToHijri = ViewBindings.findChildViewById(rootView, id);
      if (radioMiladiToHijri == null) {
        break missingId;
      }

      id = R.id.resultDayName;
      TextView resultDayName = ViewBindings.findChildViewById(rootView, id);
      if (resultDayName == null) {
        break missingId;
      }

      id = R.id.resultEndTime;
      TextView resultEndTime = ViewBindings.findChildViewById(rootView, id);
      if (resultEndTime == null) {
        break missingId;
      }

      id = R.id.resultHijriDate;
      TextView resultHijriDate = ViewBindings.findChildViewById(rootView, id);
      if (resultHijriDate == null) {
        break missingId;
      }

      id = R.id.resultHijriMonth;
      TextView resultHijriMonth = ViewBindings.findChildViewById(rootView, id);
      if (resultHijriMonth == null) {
        break missingId;
      }

      id = R.id.resultHijriMonthDays;
      TextView resultHijriMonthDays = ViewBindings.findChildViewById(rootView, id);
      if (resultHijriMonthDays == null) {
        break missingId;
      }

      id = R.id.resultMiladiDate;
      TextView resultMiladiDate = ViewBindings.findChildViewById(rootView, id);
      if (resultMiladiDate == null) {
        break missingId;
      }

      id = R.id.resultMiladiMonth;
      TextView resultMiladiMonth = ViewBindings.findChildViewById(rootView, id);
      if (resultMiladiMonth == null) {
        break missingId;
      }

      id = R.id.resultMiladiMonthDays;
      TextView resultMiladiMonthDays = ViewBindings.findChildViewById(rootView, id);
      if (resultMiladiMonthDays == null) {
        break missingId;
      }

      id = R.id.resultStartTime;
      TextView resultStartTime = ViewBindings.findChildViewById(rootView, id);
      if (resultStartTime == null) {
        break missingId;
      }

      id = R.id.resultSyrianiMonth;
      TextView resultSyrianiMonth = ViewBindings.findChildViewById(rootView, id);
      if (resultSyrianiMonth == null) {
        break missingId;
      }

      id = R.id.resultTotalHours;
      TextView resultTotalHours = ViewBindings.findChildViewById(rootView, id);
      if (resultTotalHours == null) {
        break missingId;
      }

      id = R.id.shiftCalculatorSection;
      LinearLayout shiftCalculatorSection = ViewBindings.findChildViewById(rootView, id);
      if (shiftCalculatorSection == null) {
        break missingId;
      }

      id = R.id.shiftResultSection;
      LinearLayout shiftResultSection = ViewBindings.findChildViewById(rootView, id);
      if (shiftResultSection == null) {
        break missingId;
      }

      id = R.id.startTimeInput;
      EditText startTimeInput = ViewBindings.findChildViewById(rootView, id);
      if (startTimeInput == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.toolsSpinner;
      Spinner toolsSpinner = ViewBindings.findChildViewById(rootView, id);
      if (toolsSpinner == null) {
        break missingId;
      }

      id = R.id.yearInput;
      EditText yearInput = ViewBindings.findChildViewById(rootView, id);
      if (yearInput == null) {
        break missingId;
      }

      return new ActivityDateConverterBinding((CoordinatorLayout) rootView, amPmSpinner,
          appBarLayout, calculateShiftButton, conversionTypeGroup, convertButton, dayInput,
          hoursInput, inputModeLayout, monthInput, radioHijriToMiladi, radioMiladiToHijri,
          resultDayName, resultEndTime, resultHijriDate, resultHijriMonth, resultHijriMonthDays,
          resultMiladiDate, resultMiladiMonth, resultMiladiMonthDays, resultStartTime,
          resultSyrianiMonth, resultTotalHours, shiftCalculatorSection, shiftResultSection,
          startTimeInput, toolbar, toolsSpinner, yearInput);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
