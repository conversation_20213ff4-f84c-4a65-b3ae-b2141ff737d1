<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.9.1" type="incidents">

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.9.1 is available: 8.11.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.11.0 is difficult: 8.9.3)">
        <fix-alternatives>
            <fix-replace
                description="Change to 8.11.0"
                family="Update versions"
                oldString="8.9.1"
                replacement="8.11.0"
                priority="0"/>
            <fix-replace
                description="Change to 8.9.3"
                family="Update versions"
                robot="true"
                independent="true"
                oldString="8.9.1"
                replacement="8.9.3"
                priority="0"/>
        </fix-alternatives>
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="18"
            endLine="2"
            endColumn="14"
            endOffset="25"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0">
        <fix-replace
            description="Change to 1.16.0"
            family="Update versions"
            oldString="1.12.0"
            replacement="1.16.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="4"
            column="11"
            startOffset="128"
            endLine="4"
            endColumn="19"
            endOffset="136"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="6"
            column="16"
            startOffset="171"
            endLine="6"
            endColumn="23"
            endOffset="178"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1">
        <fix-replace
            description="Change to 3.6.1"
            family="Update versions"
            oldString="3.5.1"
            replacement="3.6.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="7"
            column="16"
            startOffset="195"
            endLine="7"
            endColumn="23"
            endOffset="202"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.6.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="8"
            column="13"
            startOffset="216"
            endLine="8"
            endColumn="20"
            endOffset="223"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.android.material:material than 1.11.0 is available: 1.12.0">
        <fix-replace
            description="Change to 1.12.0"
            family="Update versions"
            oldString="1.11.0"
            replacement="1.12.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="9"
            column="12"
            startOffset="236"
            endLine="9"
            endColumn="20"
            endOffset="244"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1">
        <fix-replace
            description="Change to 2.2.1"
            family="Update versions"
            oldString="2.1.4"
            replacement="2.2.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="10"
            column="20"
            startOffset="265"
            endLine="10"
            endColumn="27"
            endOffset="272"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.activity:activity-ktx than 1.8.2 is available: 1.10.1">
        <fix-replace
            description="Change to 1.10.1"
            family="Update versions"
            oldString="1.8.2"
            replacement="1.10.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="11"
            column="12"
            startOffset="285"
            endLine="11"
            endColumn="19"
            endOffset="292"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.2">
        <fix-replace
            description="Change to 2.7.2"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.2"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="12"
            column="8"
            startOffset="320"
            endLine="12"
            endColumn="15"
            endOffset="327"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.2">
        <fix-replace
            description="Change to 2.7.2"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.2"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="12"
            column="8"
            startOffset="320"
            endLine="12"
            endColumn="15"
            endOffset="327"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.2">
        <fix-replace
            description="Change to 2.7.2"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.2"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="12"
            column="8"
            startOffset="320"
            endLine="12"
            endColumn="15"
            endOffset="327"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.7.0 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="14"
            column="13"
            startOffset="433"
            endLine="14"
            endColumn="20"
            endOffset="440"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.7.0 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="14"
            column="13"
            startOffset="433"
            endLine="14"
            endColumn="20"
            endOffset="440"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.recyclerview:recyclerview than 1.3.2 is available: 1.4.0">
        <fix-replace
            description="Change to 1.4.0"
            family="Update versions"
            oldString="1.3.2"
            replacement="1.4.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="15"
            column="16"
            startOffset="502"
            endLine="15"
            endColumn="23"
            endOffset="509"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.work:work-runtime-ktx than 2.9.0 is available: 2.10.2">
        <fix-replace
            description="Change to 2.10.2"
            family="Update versions"
            oldString="2.9.0"
            replacement="2.10.2"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="18"
            column="15"
            startOffset="659"
            endLine="18"
            endColumn="22"
            endOffset="666"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.fragment:fragment-ktx than 1.6.2 is available: 1.8.8">
        <fix-replace
            description="Change to 1.8.8"
            family="Update versions"
            oldString="1.6.2"
            replacement="1.8.8"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="20"
            column="15"
            startOffset="744"
            endLine="20"
            endColumn="22"
            endOffset="751"/>
    </incident>

</incidents>
