// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentPerformanceBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final LinearLayout cardDateConverter;

  @NonNull
  public final LinearLayout cardDrugIndex;

  @NonNull
  public final LinearLayout cardExpiryManagement;

  @NonNull
  public final LinearLayout cardInteractiveReport;

  @NonNull
  public final LinearLayout cardTaskFollowUp;

  private FragmentPerformanceBinding(@NonNull ScrollView rootView,
      @NonNull LinearLayout cardDateConverter, @NonNull LinearLayout cardDrugIndex,
      @NonNull LinearLayout cardExpiryManagement, @NonNull LinearLayout cardInteractiveReport,
      @NonNull LinearLayout cardTaskFollowUp) {
    this.rootView = rootView;
    this.cardDateConverter = cardDateConverter;
    this.cardDrugIndex = cardDrugIndex;
    this.cardExpiryManagement = cardExpiryManagement;
    this.cardInteractiveReport = cardInteractiveReport;
    this.cardTaskFollowUp = cardTaskFollowUp;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentPerformanceBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentPerformanceBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_performance, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentPerformanceBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cardDateConverter;
      LinearLayout cardDateConverter = ViewBindings.findChildViewById(rootView, id);
      if (cardDateConverter == null) {
        break missingId;
      }

      id = R.id.cardDrugIndex;
      LinearLayout cardDrugIndex = ViewBindings.findChildViewById(rootView, id);
      if (cardDrugIndex == null) {
        break missingId;
      }

      id = R.id.cardExpiryManagement;
      LinearLayout cardExpiryManagement = ViewBindings.findChildViewById(rootView, id);
      if (cardExpiryManagement == null) {
        break missingId;
      }

      id = R.id.cardInteractiveReport;
      LinearLayout cardInteractiveReport = ViewBindings.findChildViewById(rootView, id);
      if (cardInteractiveReport == null) {
        break missingId;
      }

      id = R.id.cardTaskFollowUp;
      LinearLayout cardTaskFollowUp = ViewBindings.findChildViewById(rootView, id);
      if (cardTaskFollowUp == null) {
        break missingId;
      }

      return new FragmentPerformanceBinding((ScrollView) rootView, cardDateConverter, cardDrugIndex,
          cardExpiryManagement, cardInteractiveReport, cardTaskFollowUp);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
