<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_date_converter" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_date_converter.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_date_converter_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="706" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="10" startOffset="4" endLine="27" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="18" startOffset="8" endLine="25" endOffset="46"/></Target><Target id="@+id/shiftCalculatorSection" view="LinearLayout"><Expressions/><location startLine="44" startOffset="12" endLine="241" endOffset="26"/></Target><Target id="@+id/hoursInput" view="EditText"><Expressions/><location startLine="81" startOffset="20" endLine="91" endOffset="48"/></Target><Target id="@+id/startTimeInput" view="EditText"><Expressions/><location startLine="111" startOffset="20" endLine="121" endOffset="48"/></Target><Target id="@+id/amPmSpinner" view="Spinner"><Expressions/><location startLine="123" startOffset="20" endLine="128" endOffset="58"/></Target><Target id="@+id/calculateShiftButton" view="Button"><Expressions/><location startLine="133" startOffset="16" endLine="142" endOffset="56"/></Target><Target id="@+id/shiftResultSection" view="LinearLayout"><Expressions/><location startLine="145" startOffset="16" endLine="239" endOffset="30"/></Target><Target id="@+id/resultStartTime" view="TextView"><Expressions/><location startLine="178" startOffset="24" endLine="185" endOffset="61"/></Target><Target id="@+id/resultEndTime" view="TextView"><Expressions/><location startLine="203" startOffset="24" endLine="210" endOffset="61"/></Target><Target id="@+id/resultTotalHours" view="TextView"><Expressions/><location startLine="227" startOffset="24" endLine="235" endOffset="61"/></Target><Target id="@+id/dateConverterSection" view="LinearLayout"><Expressions/><location startLine="244" startOffset="12" endLine="699" endOffset="26"/></Target><Target id="@+id/conversionTypeGroup" view="RadioGroup"><Expressions/><location startLine="262" startOffset="12" endLine="291" endOffset="24"/></Target><Target id="@+id/radioMiladiToHijri" view="RadioButton"><Expressions/><location startLine="270" startOffset="16" endLine="279" endOffset="53"/></Target><Target id="@+id/radioHijriToMiladi" view="RadioButton"><Expressions/><location startLine="281" startOffset="16" endLine="289" endOffset="50"/></Target><Target id="@+id/inputModeLayout" view="LinearLayout"><Expressions/><location startLine="298" startOffset="12" endLine="394" endOffset="26"/></Target><Target id="@+id/dayInput" view="EditText"><Expressions/><location startLine="322" startOffset="20" endLine="331" endOffset="48"/></Target><Target id="@+id/monthInput" view="EditText"><Expressions/><location startLine="352" startOffset="20" endLine="361" endOffset="48"/></Target><Target id="@+id/yearInput" view="EditText"><Expressions/><location startLine="381" startOffset="20" endLine="390" endOffset="48"/></Target><Target id="@+id/convertButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="397" startOffset="12" endLine="407" endOffset="52"/></Target><Target id="@+id/resultMiladiDate" view="TextView"><Expressions/><location startLine="441" startOffset="24" endLine="450" endOffset="63"/></Target><Target id="@+id/resultHijriDate" view="TextView"><Expressions/><location startLine="475" startOffset="24" endLine="484" endOffset="63"/></Target><Target id="@+id/resultDayName" view="TextView"><Expressions/><location startLine="510" startOffset="24" endLine="519" endOffset="63"/></Target><Target id="@+id/resultHijriMonth" view="TextView"><Expressions/><location startLine="544" startOffset="24" endLine="553" endOffset="63"/></Target><Target id="@+id/resultMiladiMonth" view="TextView"><Expressions/><location startLine="579" startOffset="24" endLine="588" endOffset="63"/></Target><Target id="@+id/resultSyrianiMonth" view="TextView"><Expressions/><location startLine="613" startOffset="24" endLine="622" endOffset="63"/></Target><Target id="@+id/resultHijriMonthDays" view="TextView"><Expressions/><location startLine="648" startOffset="24" endLine="657" endOffset="63"/></Target><Target id="@+id/resultMiladiMonthDays" view="TextView"><Expressions/><location startLine="682" startOffset="24" endLine="691" endOffset="63"/></Target></Targets></Layout>