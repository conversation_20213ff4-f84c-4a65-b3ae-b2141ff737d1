package com.example.kpitrackerapp

import android.Manifest
import android.content.Context // Needed for SharedPreferences
import android.content.Intent
import android.content.SharedPreferences // Needed for SharedPreferences
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.drawable.GradientDrawable // Needed for dynamic gradient
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.ContextMenu // Import ContextMenu
import android.view.LayoutInflater // Import LayoutInflater for dialog
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.AdapterView // Add AdapterView import
import android.widget.ArrayAdapter
import android.widget.Button // Import Button
import android.widget.LinearLayout // Import LinearLayout
import android.widget.Spinner
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher // Import ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider // Needed for sharing bitmap
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.OneTimeWorkRequestBuilder // For testing TaskReminderWorker
import androidx.work.OutOfQuotaPolicy // Import for expedited work
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import com.bumptech.glide.Glide // Import Glide
import com.example.kpitrackerapp.databinding.ActivityMainBinding
import com.example.kpitrackerapp.databinding.DialogSelectCardColorsBinding // Import dialog binding
import com.example.kpitrackerapp.models.Kpi // Import Kpi model
import com.example.kpitrackerapp.models.KpiProgressEntry // Import KpiProgressEntry
import com.example.kpitrackerapp.models.KpiUnit // Import KpiUnit
import com.example.kpitrackerapp.persistence.AppDatabase
// import com.example.kpitrackerapp.ui.AddEditKpiActivity // This is now for Tasks
import com.example.kpitrackerapp.ui.AddEditKpiOriginalActivity // Import for original KPI add/edit
import com.example.kpitrackerapp.ui.AddEditProgressDialogFragment // Keep for now, might be used elsewhere
import com.example.kpitrackerapp.ui.ExpireManagementActivity // Keep for menu
import com.example.kpitrackerapp.ui.OcrActivity
import com.example.kpitrackerapp.ui.OnUserSummaryActionsListener // Import the listener for user summary
import com.example.kpitrackerapp.ui.ModernReportActivity // Import ModernReportActivity
import com.example.kpitrackerapp.ui.TaskManagementActivity // Import TaskManagementActivity
import com.example.kpitrackerapp.ui.UserKpiListActivity // Import for navigation from summary card
import com.example.kpitrackerapp.ui.UserSummaryAdapter // Use UserSummaryAdapter
import com.example.kpitrackerapp.ui.UserSummaryItem // Import UserSummaryItem
import com.example.kpitrackerapp.ui.LoginActivity // Import LoginActivity
import com.example.kpitrackerapp.models.UserRole // Import UserRole
import com.example.kpitrackerapp.utils.DragDropHelper
import com.example.kpitrackerapp.utils.CardAnimationHelper
import com.example.kpitrackerapp.utils.SessionManager // Import SessionManager
import com.example.kpitrackerapp.utils.FirebaseMessageManager
import com.example.kpitrackerapp.utils.ThemeManager
import com.example.kpitrackerapp.utils.LanguageManager
import com.example.kpitrackerapp.utils.AlarmClockManager
import com.example.kpitrackerapp.utils.AppNotificationManager
import com.example.kpitrackerapp.fragments.MainDashboardFragment
import com.example.kpitrackerapp.fragments.PerformanceFragment
import com.example.kpitrackerapp.fragments.MessagesFragment
import com.example.kpitrackerapp.fragments.AccountFragment
import androidx.fragment.app.Fragment
import com.example.kpitrackerapp.viewmodels.KpiViewModel
import com.example.kpitrackerapp.viewmodels.KpiViewModelFactory
import com.example.kpitrackerapp.workers.ExpiryNotificationWorker
import com.example.kpitrackerapp.workers.TaskReminderWorker // Import TaskReminderWorker
import com.github.dhaval2404.colorpicker.ColorPickerDialog
import com.github.dhaval2404.colorpicker.model.ColorShape
import com.google.android.material.dialog.MaterialAlertDialogBuilder // Use Material dialogs
import java.io.File // Needed for sharing bitmap
import java.io.FileOutputStream // Needed for sharing bitmap
import java.io.IOException // Needed for sharing bitmap
import java.text.NumberFormat // Add NumberFormat import
import java.text.SimpleDateFormat // Add SimpleDateFormat import
import java.util.Calendar // Add Calendar import
import java.util.Date // Import Date
import java.util.Locale // Add Locale import
import java.util.concurrent.TimeUnit
import kotlinx.coroutines.Dispatchers // Import Dispatchers
import kotlinx.coroutines.flow.first // Import first operator
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext // Import withContext

// Implement OnUserSummaryActionsListener for UserSummaryAdapter
class MainActivity : AppCompatActivity(), OnUserSummaryActionsListener {

    private lateinit var binding: ActivityMainBinding
    private val kpiViewModel: KpiViewModel by viewModels {
        val database = AppDatabase.getDatabase(this)
        KpiViewModelFactory(
            application,
            database.kpiDao(),
            database.progressEntryDao(),
            database.userDao(),
            database.userKpiAssignmentDao()
        )
    }

    private val mainMonthFilterOptions = mutableListOf<Pair<Calendar?, String>>()
    private var selectedMainFilterCalendar: Calendar? = Calendar.getInstance()
    private var selectedUserSummaryItem: UserSummaryItem? = null
    private var selectedItemView: View? = null

    private lateinit var sharedPreferences: SharedPreferences
    private val PREFS_NAME = "OverallCardPrefs"
    private val KEY_START_COLOR = "overall_start_color"
    private val KEY_END_COLOR = "overall_end_color"
    private val KEY_IMAGE_URI = "overall_image_uri"

    // Track if order has changed to avoid unnecessary saves
    private var orderChanged = false

    private lateinit var imagePickerLauncher: ActivityResultLauncher<String>

    private val createCsvLauncher = registerForActivityResult(ActivityResultContracts.CreateDocument("text/csv")) { uri: Uri? ->
        uri?.let { fileUri ->
            selectedUserSummaryItem?.let { userSummary ->
                exportUserDataToUri(userSummary.userId, userSummary.userName, fileUri)
            } ?: run {
                Log.e("MainActivity", "Cannot export CSV: selectedUserSummaryItem is null.")
                Toast.makeText(this, "Error: User data not available for export.", Toast.LENGTH_SHORT).show()
            }
        } ?: run {
            Log.d("MainActivity", "CSV file creation cancelled by user.")
            Toast.makeText(this, "CSV export cancelled.", Toast.LENGTH_SHORT).show()
        }
    }

    private val requestPermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted: Boolean ->
            if (isGranted) {
                Log.i("MainActivity", "Notification permission granted.")
            } else {
                Log.w("MainActivity", "Notification permission denied.")
                Toast.makeText(this, "Notification permission denied. Alerts will not be shown.", Toast.LENGTH_LONG).show()
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        // Apply theme and language before calling super.onCreate()
        ThemeManager.initializeTheme(this)
        LanguageManager.applyLanguage(this)

        super.onCreate(savedInstanceState)
        Log.i("MainActivity", "onCreate: STARTING MainActivity")

        // Initialize SessionManager
        SessionManager.init(this)

        // Check if user is logged in
        if (!SessionManager.isLoggedIn()) {
            redirectToLogin()
            return
        }

        // Initialize notification channels
        AppNotificationManager.initializeNotificationChannels(this)

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        Log.i("MainActivity", "onCreate started.")

        // Setup toolbar
        setSupportActionBar(binding.toolbar)
        Log.d("MainActivity", "Toolbar setup complete.")

        // Date display removed as requested
        Log.d("MainActivity", "Date display removed.")

        sharedPreferences = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        Log.d("MainActivity", "SharedPreferences initialized.")

        setupImagePickerLauncher()
        setupMasterCardDisplay()
        setupAdminIndicator()
        setupDeveloperOptions()
        setupBottomNavigation()

        registerForContextMenu(binding.overallSummaryCardInclude.root)
        Log.d("MainActivity", "Context menus registered.")

        // Load default fragment if no saved state
        if (savedInstanceState == null) {
            loadFragment(MainDashboardFragment())
        }

        checkAndRequestNotificationPermission()
        scheduleExpiryNotifications()
        scheduleTaskReminders()

        // Initialize Firebase messaging
        initializeFirebaseMessaging()

        Log.i("MainActivity", "onCreate completed successfully.")
    }

    private fun setupMasterCardDisplay() {
        val includedBinding = binding.overallSummaryCardInclude
        val overallCardView = includedBinding.overallSummaryCardView
        val kpiDetailsContainer = includedBinding.kpiDetailsContainer
        val toggleButton = binding.btnCreateMasterCard

        loadAndApplyOverallCardBackground()

        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                kpiViewModel.overallAggregatedKpiSummaries.collect { summaryList ->
                    kpiDetailsContainer.removeAllViews()
                    if (summaryList.isNotEmpty()) {
                        if (overallCardView.visibility != View.GONE) {
                           overallCardView.visibility = View.VISIBLE
                        }
                        summaryList.forEach { summaryItem ->
                            val itemView = LayoutInflater.from(this@MainActivity).inflate(
                                R.layout.kpi_summary_detail_item,
                                kpiDetailsContainer,
                                false
                            )
                            val nameTextView = itemView.findViewById<android.widget.TextView>(R.id.kpiDetailNameTextView)
                            val monthlyTextView = itemView.findViewById<android.widget.TextView>(R.id.kpiDetailMonthlyPercentTextView)
                            val annualTextView = itemView.findViewById<android.widget.TextView>(R.id.kpiDetailAnnualPercentTextView)

                            nameTextView.text = summaryItem.kpiName
                            monthlyTextView.text = getString(R.string.summary_detail_monthly_format, summaryItem.monthlyPercentage)
                            annualTextView.text = getString(R.string.summary_detail_annual_format, summaryItem.annualPercentage)

                            val textColor = Color.WHITE
                            nameTextView.setTextColor(textColor)
                            monthlyTextView.setTextColor(textColor)
                            annualTextView.setTextColor(textColor)
                            kpiDetailsContainer.addView(itemView)
                        }
                    } else {
                        // Show default summary even when no KPI data
                        overallCardView.visibility = View.VISIBLE
                        addDefaultSummaryData(kpiDetailsContainer)
                    }
                    Log.d("MainActivity", "Updated Overall Summary Card with ${summaryList.size} KPI details. Card visibility: ${overallCardView.visibility}")
                }
            }
        }

        overallCardView.setOnClickListener {
            Log.d("MainActivity", "Overall Summary Card clicked.")
            val intent = Intent(this, UserKpiListActivity::class.java).apply {
                putExtra(UserKpiListActivity.EXTRA_USER_NAME, "Overall Aggregated KPIs")
            }
            startActivity(intent)
        }

        // Setup toggle button for master card
        toggleButton.text = getString(R.string.toggle_master_card)
        toggleButton.setOnClickListener {
            val isCurrentlyVisible = overallCardView.visibility == View.VISIBLE
            if (isCurrentlyVisible) {
                overallCardView.visibility = View.GONE
                toggleButton.text = getString(R.string.show_master_card)
            } else {
                if (kpiViewModel.overallAggregatedKpiSummaries.value.isNotEmpty()) {
                    overallCardView.visibility = View.VISIBLE
                    toggleButton.text = getString(R.string.hide_master_card)
                } else {
                    Toast.makeText(this, getString(R.string.master_card_no_data), Toast.LENGTH_SHORT).show()
                    overallCardView.visibility = View.GONE
                }
            }
            Log.d("MainActivity", "Toggled Overall Summary Card visibility via button to: ${overallCardView.visibility}")
        }
    }

    private fun setupImagePickerLauncher() {
        imagePickerLauncher = registerForActivityResult(ActivityResultContracts.GetContent()) { uri: Uri? ->
            if (uri != null) {
                Log.d("MainActivity", "Image selected by picker: $uri (Master Card Icon Reverted - This should not be called for it)")
            } else {
                Log.d("MainActivity", "No image selected by picker.")
            }
        }
    }

    private fun saveOverallCardColors(startColor: Int?, endColor: Int?) {
        with(sharedPreferences.edit()) {
            if (startColor != null) {
                putInt(KEY_START_COLOR, startColor)
            } else {
                remove(KEY_START_COLOR)
            }
            if (endColor != null) {
                putInt(KEY_END_COLOR, endColor)
            } else {
                remove(KEY_END_COLOR)
            }
            apply()
        }
        Log.d("MainActivity", "Saved Overall Card Colors - Start: $startColor, End: $endColor")
        updateOverallCardBackgroundViews(startColor, endColor)
    }

    private fun loadOverallCardColors(): Pair<Int?, Int?> {
        val startColor = if (sharedPreferences.contains(KEY_START_COLOR)) sharedPreferences.getInt(KEY_START_COLOR, 0) else null
        val endColor = if (sharedPreferences.contains(KEY_END_COLOR)) sharedPreferences.getInt(KEY_END_COLOR, 0) else null
        Log.d("MainActivity", "Loaded Overall Card Colors - Start: $startColor, End: $endColor")
        return Pair(startColor, endColor)
    }

    private fun loadAndApplyOverallCardBackground() {
        val (startColor, endColor) = loadOverallCardColors()
        updateOverallCardBackgroundViews(startColor, endColor)
    }

    private fun updateOverallCardBackgroundViews(startColor: Int?, endColor: Int?) {
        val topView = binding.overallSummaryCardInclude.topBackgroundView

        val actualTopColor = startColor ?: ContextCompat.getColor(this, R.color.purple_500)

        topView.setBackgroundColor(actualTopColor)

        Log.d("MainActivity", "Updated Overall Card background color: $actualTopColor")
    }


    private fun addDefaultSummaryData(container: LinearLayout) {
        // Add task reminder settings options
        val reminderSettings = listOf(
            Triple("تذكيرات البريد الإلكتروني", "مفعل", "📧"),
            Triple("تذكيرات واتساب", "مفعل", "💬"),
            Triple("الإشعارات المحلية", "مفعل", "🔔"),
            Triple("التوقيت الافتراضي", "يوم واحد قبل", "⏰")
        )

        reminderSettings.forEach { (title, value, emoji) ->
            val itemView = LayoutInflater.from(this).inflate(
                R.layout.kpi_summary_detail_item,
                container,
                false
            )
            val nameTextView = itemView.findViewById<android.widget.TextView>(R.id.kpiDetailNameTextView)
            val monthlyTextView = itemView.findViewById<android.widget.TextView>(R.id.kpiDetailMonthlyPercentTextView)
            val annualTextView = itemView.findViewById<android.widget.TextView>(R.id.kpiDetailAnnualPercentTextView)

            nameTextView.text = "$emoji $title"
            monthlyTextView.text = value
            annualTextView.text = ""

            val textColor = Color.WHITE
            nameTextView.setTextColor(textColor)
            monthlyTextView.setTextColor(textColor)
            annualTextView.setTextColor(textColor)

            container.addView(itemView)
        }
    }

    private fun showOverallCardColorSelectionDialog() {
        val dialogBinding = DialogSelectCardColorsBinding.inflate(LayoutInflater.from(this))
        val dialog = MaterialAlertDialogBuilder(this)
            .setTitle("Select Overall Summary Colors")
            .setView(dialogBinding.root)
            .create()

        val (currentTopColor, currentBottomColor) = loadOverallCardColors()
        var selectedStartColor: Int? = currentTopColor
        var selectedEndColor: Int? = currentBottomColor

        val defaultStartColor = ContextCompat.getColor(this, R.color.purple_500)
        val defaultEndColor = ContextCompat.getColor(this, R.color.purple_700)

        dialogBinding.topColorPreview.setBackgroundColor(selectedStartColor ?: defaultStartColor)
        dialogBinding.bottomColorPreview.setBackgroundColor(selectedEndColor ?: defaultEndColor)

        dialogBinding.buttonPickTopColor.setOnClickListener {
            ColorPickerDialog
                .Builder(this)
                .setTitle("Select Start Color")
                .setColorShape(ColorShape.SQAURE)
                .setDefaultColor(selectedStartColor ?: defaultStartColor)
                .setColorListener { color, _ ->
                    selectedStartColor = color
                    dialogBinding.topColorPreview.setBackgroundColor(color)
                }
                .show()
        }

        dialogBinding.buttonPickBottomColor.setOnClickListener {
            ColorPickerDialog
                .Builder(this)
                .setTitle("Select End Color")
                .setColorShape(ColorShape.SQAURE)
                .setDefaultColor(selectedEndColor ?: defaultEndColor)
                .setColorListener { color, _ ->
                    selectedEndColor = color
                    dialogBinding.bottomColorPreview.setBackgroundColor(color)
                }
                .show()
        }

        dialogBinding.buttonResetColors.setOnClickListener {
            selectedStartColor = null
            selectedEndColor = null
            dialogBinding.topColorPreview.setBackgroundColor(defaultStartColor)
            dialogBinding.bottomColorPreview.setBackgroundColor(defaultEndColor)
            saveOverallCardColors(null, null)
            Toast.makeText(this, getString(R.string.color_reset_success), Toast.LENGTH_SHORT).show()
            dialog.dismiss()
        }

        dialogBinding.buttonCancelColorSelection.setOnClickListener {
            dialog.dismiss()
        }

        dialogBinding.buttonSaveColors.setOnClickListener {
            saveOverallCardColors(selectedStartColor, selectedEndColor)
            Toast.makeText(this, getString(R.string.color_set_success), Toast.LENGTH_SHORT).show()
            dialog.dismiss()
        }
        dialog.show()
    }

    private fun saveOverallCardImageUri(uriString: String?) {
        with(sharedPreferences.edit()) {
            if (uriString != null) {
                putString(KEY_IMAGE_URI, uriString)
            } else {
                remove(KEY_IMAGE_URI)
            }
            apply()
        }
        Log.d("MainActivity", "Saved Overall Card Image URI (Master Card Icon Reverted): $uriString")
    }

    private fun loadOverallCardImageUri(): String? {
        return sharedPreferences.getString(KEY_IMAGE_URI, null).also {
             Log.d("MainActivity", "Loaded Overall Card Image URI (Master Card Icon Reverted): $it")
        }
    }



    override fun onUserSummaryClicked(userSummaryItem: UserSummaryItem) {
        Log.d("MainActivity", "User summary card clicked for user: ${userSummaryItem.userName}")
        val intent = Intent(this, UserKpiListActivity::class.java).apply {
            putExtra(UserKpiListActivity.EXTRA_USER_ID, userSummaryItem.userId)
            putExtra(UserKpiListActivity.EXTRA_USER_NAME, userSummaryItem.userName)
        }
        startActivity(intent)
    }

    override fun onUserSummaryLongClicked(userSummaryItem: UserSummaryItem, itemView: View) {
        Log.d("MainActivity", "User summary card long-clicked for user: ${userSummaryItem.userName}")
        selectedUserSummaryItem = userSummaryItem
        selectedItemView = itemView

        val currentUserId = SessionManager.getCurrentUserId()

        if (userSummaryItem.userId == currentUserId) {
            // My card - full options
            showMyCardActions(userSummaryItem)
        } else {
            // Other user's card - limited options
            showOtherUserActions(userSummaryItem)
        }
    }

    override fun onUserSummaryDoubleClicked(userSummaryItem: UserSummaryItem) {
        Log.d("MainActivity", "User summary card double-clicked for user: ${userSummaryItem.userName}")
        // Add to favorites or mark as important
        Toast.makeText(this, "${userSummaryItem.userName} marked as favorite!", Toast.LENGTH_SHORT).show()
        // You can implement favorite functionality here
    }

    override fun onUserSummarySwipedLeft(userSummaryItem: UserSummaryItem) {
        Log.d("MainActivity", "User summary card swiped left for user: ${userSummaryItem.userName}")
        // Store the current user for quick actions
        selectedUserSummaryItem = userSummaryItem
        // Show quick actions menu
        showQuickActionsDialog(userSummaryItem)
    }

    override fun onUserSummarySwipedRight(userSummaryItem: UserSummaryItem) {
        Log.d("MainActivity", "User summary card swiped right for user: ${userSummaryItem.userName}")
        // Show quick details
        showQuickDetailsDialog(userSummaryItem)
    }

    override fun onUserSummaryMoved(fromPosition: Int, toPosition: Int) {
        Log.d("MainActivity", "User summary moved from $fromPosition to $toPosition")
        // User summary move functionality is now handled by fragments

        // Mark that order has changed
        orderChanged = true

        // Note: Order will be saved when drag ends to avoid multiple saves during drag
    }

    override fun onUserSummaryDragStarted(userSummaryItem: UserSummaryItem) {
        Log.d("MainActivity", "Drag started for user: ${userSummaryItem.userName}")
        // Show a subtle toast with drag instructions
        Toast.makeText(this, "📱 Dragging ${userSummaryItem.userName} - Move to reorder", Toast.LENGTH_SHORT).show()
    }

    override fun onUserSummaryDragEnded(userSummaryItem: UserSummaryItem) {
        Log.d("MainActivity", "Drag ended for user: ${userSummaryItem.userName}")
        // Save the new order to database
        saveUserOrder()
    }

    override fun onCreateContextMenu(
        menu: ContextMenu,
        v: View,
        menuInfo: ContextMenu.ContextMenuInfo?
    ) {
        super.onCreateContextMenu(menu, v, menuInfo)
        when (v.id) {
            R.id.kpiRecyclerView -> {
                menuInflater.inflate(R.menu.user_summary_context_menu, menu)
                selectedUserSummaryItem?.let {
                    menu.setHeaderTitle("Actions for ${it.userName}")
                }
            }
            binding.overallSummaryCardInclude.root.id -> {
                menuInflater.inflate(R.menu.overall_summary_context_menu, menu)
                menu.setHeaderTitle("Overall Summary Actions")
            }
        }
    }

    override fun onContextItemSelected(item: MenuItem): Boolean {
        if (item.itemId == R.id.context_overall_change_color) {
            Log.d("MainActivity", "Context menu: Change Overall Summary color selected")
            showOverallCardColorSelectionDialog()
            return true
        }

        val userSummary = selectedUserSummaryItem
        if (userSummary != null) {
            return when (item.itemId) {
                R.id.action_change_user_card_color -> {
                    Log.d("MainActivity", "Context menu: Change color selected for ${userSummary.userName}")
                    showUserColorSelectionDialog(userSummary)
                    true
                }
                R.id.action_share_user_card_image -> {
                    Log.d("MainActivity", "Context menu: Share card selected for ${userSummary.userName}")
                    selectedItemView?.let { viewToShare ->
                        captureAndShareView(viewToShare, "kpi_user_card_${userSummary.userName}.png")
                    } ?: run {
                        Log.e("MainActivity", "selectedItemView is null, cannot share card image.")
                        Toast.makeText(this, "Error: Could not find view to share.", Toast.LENGTH_SHORT).show()
                    }
                    true
                }
                R.id.action_delete_user_kpi -> {
                    Log.d("MainActivity", "Context menu: Delete KPI selected for ${userSummary.userName}")
                    confirmDeleteUserAndData(userSummary)
                    true
                }
                R.id.action_export_user_data_csv -> {
                    Log.d("MainActivity", "Context menu: Export data selected for ${userSummary.userName}")
                    initiateUserDataExport(userSummary)
                    true
                }
                else -> super.onContextItemSelected(item)
            }
        }
        return super.onContextItemSelected(item)
    }

    private fun initiateUserDataExport(userSummary: UserSummaryItem) {
        val sdf = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
        val timestamp = sdf.format(Date())
        val suggestedFilename = "kpi_export_${userSummary.userName.replace(" ", "_")}_$timestamp.csv"
        createCsvLauncher.launch(suggestedFilename)
    }

    private fun exportUserDataToUri(userId: String, userName: String, uri: Uri) {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val exportData: List<Pair<Kpi, KpiProgressEntry>> = kpiViewModel.getUserProgressEntriesForExport(userId)

                if (exportData.isEmpty()) {
                    withContext(Dispatchers.Main) {
                        Toast.makeText(this@MainActivity, "No progress data found for user '$userName' to export.", Toast.LENGTH_LONG).show()
                    }
                    return@launch
                }

                val csvBuilder = StringBuilder()
                csvBuilder.append("KPI Name,Date,Value\n")

                val csvDateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                exportData.sortedBy { it.second.date }.forEach { (kpi: Kpi, entry: KpiProgressEntry) ->
                    val kpiName = kpi.name
                    val formattedDate = csvDateFormat.format(entry.date)
                    val safeKpiName = if (kpiName.contains(",")) "\"$kpiName\"" else kpiName
                    csvBuilder.append("$safeKpiName,${formattedDate},${entry.value}\n")
                }

                contentResolver.openOutputStream(uri)?.use { outputStream ->
                    outputStream.write(csvBuilder.toString().toByteArray())
                } ?: throw IOException("Failed to open output stream for URI: $uri")

                withContext(Dispatchers.Main) {
                    Toast.makeText(this@MainActivity, "Data for '$userName' exported successfully.", Toast.LENGTH_LONG).show()
                }

            } catch (e: Exception) {
                Log.e("MainActivity", "Error exporting user data to CSV for user $userId", e)
                withContext(Dispatchers.Main) {
                    Toast.makeText(this@MainActivity, "Error exporting data: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    private fun captureAndShareView(view: View, filename: String) {
        try {
            Log.d("MainActivity", "Starting to capture view for sharing: $filename")
            val bitmap = Bitmap.createBitmap(view.width, view.height, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(bitmap)
            val bgDrawable = view.background
            if (bgDrawable != null) {
                bgDrawable.draw(canvas)
            } else {
                canvas.drawColor(Color.WHITE)
            }
            view.draw(canvas)
            Log.d("MainActivity", "Successfully captured bitmap, now sharing...")
            shareBitmap(bitmap, filename)
        } catch (e: Exception) {
            Log.e("MainActivity", "Error capturing view as bitmap", e)
            Toast.makeText(this, "Error capturing card image: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun shareBitmap(bitmap: Bitmap, filename: String) {
        try {
            Log.d("MainActivity", "Starting to share bitmap with filename: $filename")

            // Create cache directory for images
            val cachePath = File(cacheDir, "images")
            if (!cachePath.exists()) {
                val created = cachePath.mkdirs()
                Log.d("MainActivity", "Cache directory created: $created")
            }

            // Create the file
            val filePath = File(cachePath, filename)
            Log.d("MainActivity", "File path: ${filePath.absolutePath}")

            // Save bitmap to file
            val fileOutputStream = FileOutputStream(filePath)
            val compressed = bitmap.compress(Bitmap.CompressFormat.PNG, 100, fileOutputStream)
            fileOutputStream.close()
            Log.d("MainActivity", "Bitmap compressed successfully: $compressed")

            // Get content URI using FileProvider
            val contentUri: Uri? = try {
                val uri = FileProvider.getUriForFile(this, "${applicationContext.packageName}.provider", filePath)
                Log.d("MainActivity", "Content URI created: $uri")
                uri
            } catch (e: IllegalArgumentException) {
                Log.e("MainActivity", "FileProvider error: ${e.message}")
                Log.e("MainActivity", "File path: ${filePath.absolutePath}")
                Log.e("MainActivity", "File exists: ${filePath.exists()}")
                null
            }

            if (contentUri != null) {
                // Create share intent
                val shareIntent = Intent(Intent.ACTION_SEND).apply {
                    type = "image/png"
                    putExtra(Intent.EXTRA_STREAM, contentUri)
                    putExtra(Intent.EXTRA_SUBJECT, "KPI Card")
                    putExtra(Intent.EXTRA_TEXT, "Sharing KPI card image")
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }

                Log.d("MainActivity", "Starting share activity")
                startActivity(Intent.createChooser(shareIntent, "Share Card Image"))
                Toast.makeText(this, "Card image ready to share!", Toast.LENGTH_SHORT).show()
            } else {
                Log.e("MainActivity", "Failed to get content URI for sharing.")
                Toast.makeText(this, "Failed to prepare image for sharing.", Toast.LENGTH_SHORT).show()
            }

        } catch (e: IOException) {
            Log.e("MainActivity", "IOException while sharing bitmap", e)
            Toast.makeText(this, "Error saving image: ${e.message}", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Log.e("MainActivity", "Unexpected error while sharing bitmap", e)
            Toast.makeText(this, "Error sharing image: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun confirmDeleteUserAndData(userSummary: UserSummaryItem) {
        MaterialAlertDialogBuilder(this)
            .setTitle("Delete User and KPIs?")
            .setMessage("Are you sure you want to delete user '${userSummary.userName}' and all their assigned KPIs and progress? This action cannot be undone.")
            .setNegativeButton(R.string.dialog_cancel, null)
            .setPositiveButton("Delete") { _, _ ->
                deleteUserAndData(userSummary.userId, userSummary.userName)
            }
            .show()
    }

    private fun deleteUserAndData(userId: String, userName: String) {
        lifecycleScope.launch {
            try {
            kpiViewModel.deleteUserAndAssignments(userId)
            Log.i("MainActivity", "Successfully requested deletion for user: $userName (ID: $userId)")
            Toast.makeText(this@MainActivity, "User '$userName' and associated data deleted.", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                Log.e("MainActivity", "Error deleting user $userName (ID: $userId)", e)
                Toast.makeText(this@MainActivity, "Error deleting user '$userName'.", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun showUserColorSelectionDialog(userSummaryItem: UserSummaryItem) {
        val dialogBinding = DialogSelectCardColorsBinding.inflate(LayoutInflater.from(this))
        val dialog = MaterialAlertDialogBuilder(this)
            .setTitle(getString(R.string.select_card_gradient_colors))
            .setView(dialogBinding.root)
            .create()

        var selectedTopColor: Int? = userSummaryItem.topColor
        var selectedBottomColor: Int? = userSummaryItem.bottomColor

        val defaultTopColor = ContextCompat.getColor(this, R.color.summary_card_top_red)
        val defaultBottomColor = ContextCompat.getColor(this, R.color.summary_card_bottom_yellow)

        dialogBinding.topColorPreview.setBackgroundColor(selectedTopColor ?: defaultTopColor)
        dialogBinding.bottomColorPreview.setBackgroundColor(selectedBottomColor ?: defaultBottomColor)

        dialogBinding.buttonPickTopColor.setOnClickListener {
            ColorPickerDialog
                .Builder(this)
                .setTitle("Select Top Color")
                .setColorShape(ColorShape.SQAURE)
                .setDefaultColor(selectedTopColor ?: defaultTopColor)
                .setColorListener { color, _ ->
                    selectedTopColor = color
                    dialogBinding.topColorPreview.setBackgroundColor(color)
                }
                .show()
        }

        dialogBinding.buttonPickBottomColor.setOnClickListener {
            ColorPickerDialog
                .Builder(this)
                .setTitle("Select Bottom Color")
                .setColorShape(ColorShape.SQAURE)
                .setDefaultColor(selectedBottomColor ?: defaultBottomColor)
                .setColorListener { color, _ ->
                    selectedBottomColor = color
                    dialogBinding.bottomColorPreview.setBackgroundColor(color)
                }
                .show()
        }

        dialogBinding.buttonResetColors.setOnClickListener {
            selectedTopColor = null
            selectedBottomColor = null
            dialogBinding.topColorPreview.setBackgroundColor(defaultTopColor)
            dialogBinding.bottomColorPreview.setBackgroundColor(defaultBottomColor)
            Toast.makeText(this, getString(R.string.color_reset_success), Toast.LENGTH_SHORT).show()
        }

        dialogBinding.buttonCancelColorSelection.setOnClickListener {
            dialog.dismiss()
        }

        dialogBinding.buttonSaveColors.setOnClickListener {
            kpiViewModel.updateUserCardColors(userSummaryItem.userId, selectedTopColor, selectedBottomColor)
            Toast.makeText(this, getString(R.string.color_set_success), Toast.LENGTH_SHORT).show()
            dialog.dismiss()
        }
        dialog.show()
    }



    private fun redirectToLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }



    private fun showLogoutConfirmation() {
        MaterialAlertDialogBuilder(this)
            .setTitle("Logout")
            .setMessage("Are you sure you want to logout?")
            .setPositiveButton("Logout") { _, _ ->
                performLogout()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun performLogout() {
        SessionManager.logout()
        redirectToLogin()
    }

    private fun showSwitchUserDialog() {
        MaterialAlertDialogBuilder(this)
            .setTitle("Switch User")
            .setMessage("Choose an option:")
            .setPositiveButton("Login as Different User") { _, _ ->
                // Clear session and go to login
                SessionManager.logout()
                redirectToLogin()
            }
            .setNeutralButton("Login as Admin") { _, _ ->
                // Clear session and go to login with admin mode
                SessionManager.logout()
                val intent = Intent(this, LoginActivity::class.java)
                intent.putExtra("ADMIN_MODE", true)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                startActivity(intent)
                finish()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun openAdminDashboard() {
        val intent = Intent(this, AdminDashboardActivity::class.java)
        startActivity(intent)
    }

    private fun openChatList() {
        val intent = Intent(this, com.example.kpitrackerapp.ui.ChatListActivity::class.java)
        startActivity(intent)
    }

    private fun openAutoSendSettings() {
        val intent = Intent(this, com.example.kpitrackerapp.ui.AutoSendSettingsActivity::class.java)
        startActivity(intent)
    }

    private fun openAdvancedTaskCreator() {
        val intent = Intent(this, com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity::class.java)
        startActivity(intent)
    }

    private fun showMyCardActions(userSummaryItem: UserSummaryItem) {
        val baseActions = mutableListOf(
            "✏️ Edit Profile",
            "📊 Add Progress",
            "📈 View My Reports",
            "🎨 Change Colors",
            "📤 Export My Data",
            "📱 Share My Card",
            "💬 Start Chat",
            "📤 Auto Send Settings"
        )

        // Add admin actions if user is admin
        if (SessionManager.isAdmin()) {
            baseActions.addAll(listOf(
                "🔧 Admin Tools",
                "👥 Manage Users",
                "📊 System Reports"
            ))
        }

        val actions = baseActions.toTypedArray()

        MaterialAlertDialogBuilder(this)
            .setTitle("My Card - ${userSummaryItem.userName}")
            .setItems(actions) { _, which ->
                when (which) {
                    0 -> editUserProfile(userSummaryItem)
                    1 -> quickAddProgress(userSummaryItem)
                    2 -> viewMyReports(userSummaryItem)
                    3 -> showUserColorSelectionDialog(userSummaryItem)
                    4 -> initiateUserDataExport(userSummaryItem)
                    5 -> shareUserCard(userSummaryItem)
                    6 -> startChatFromCard(userSummaryItem)
                    7 -> openAutoSendSettings()
                    8 -> if (SessionManager.isAdmin()) openAdminDashboard()
                    9 -> if (SessionManager.isAdmin()) showAdminUserManagement()
                    10 -> if (SessionManager.isAdmin()) showAdminSystemReports()
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun showOtherUserActions(userSummaryItem: UserSummaryItem) {
        val baseActions = mutableListOf(
            "👁️ View Details",
            "📊 View Reports",
            "💬 Start Chat",
            "🤝 Ask for Advice",
            "🎉 Send Congratulations",
            "📱 Share Card"
        )

        // Add admin actions if user is admin
        if (SessionManager.isAdmin()) {
            baseActions.addAll(listOf(
                "🎨 Change Colors",
                "⚙️ Edit User",
                "🔒 Manage Permissions",
                "📤 Export User Data"
            ))
        }

        val actions = baseActions.toTypedArray()

        MaterialAlertDialogBuilder(this)
            .setTitle("${userSummaryItem.userName}'s Card")
            .setItems(actions) { _, which ->
                when (which) {
                    0 -> viewUserDetails(userSummaryItem)
                    1 -> viewUserReports(userSummaryItem)
                    2 -> startChatFromCard(userSummaryItem)
                    3 -> startAdviceChat(userSummaryItem)
                    4 -> sendCongratulations(userSummaryItem)
                    5 -> shareUserCard(userSummaryItem)
                    6 -> if (SessionManager.isAdmin()) showUserColorSelectionDialog(userSummaryItem)
                    7 -> if (SessionManager.isAdmin()) adminEditUser(userSummaryItem)
                    8 -> if (SessionManager.isAdmin()) adminManagePermissions(userSummaryItem)
                    9 -> if (SessionManager.isAdmin()) adminExportUserData(userSummaryItem)
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    // Action methods
    private fun editUserProfile(userSummaryItem: UserSummaryItem) {
        Toast.makeText(this, "Edit profile functionality coming soon", Toast.LENGTH_SHORT).show()
        // TODO: Navigate to edit profile activity
    }

    private fun quickAddProgress(userSummaryItem: UserSummaryItem) {
        Toast.makeText(this, "Quick add progress functionality coming soon", Toast.LENGTH_SHORT).show()
        // TODO: Show quick add progress dialog
    }

    private fun viewMyReports(userSummaryItem: UserSummaryItem) {
        val intent = Intent(this, ModernReportActivity::class.java)
        startActivity(intent)
    }

    private fun viewUserDetails(userSummaryItem: UserSummaryItem) {
        onUserSummaryClicked(userSummaryItem)
    }

    private fun viewUserReports(userSummaryItem: UserSummaryItem) {
        val intent = Intent(this, ModernReportActivity::class.java)
        startActivity(intent)
    }

    private fun startChatFromCard(userSummaryItem: UserSummaryItem) {
        val intent = Intent(this, com.example.kpitrackerapp.ui.ChatActivity::class.java).apply {
            putExtra("OTHER_USER_ID", userSummaryItem.userId)
            putExtra("OTHER_USER_NAME", userSummaryItem.userName)
            putExtra("OTHER_USER_IMAGE", userSummaryItem.userImagePath)
        }
        startActivity(intent)
    }

    private fun startAdviceChat(userSummaryItem: UserSummaryItem) {
        Toast.makeText(this, "Ask for advice functionality coming soon", Toast.LENGTH_SHORT).show()
        // TODO: Start advice chat
    }

    private fun sendCongratulations(userSummaryItem: UserSummaryItem) {
        Toast.makeText(this, "Congratulations sent to ${userSummaryItem.userName}!", Toast.LENGTH_SHORT).show()
        // TODO: Send congratulations message
    }

    // Admin Functions
    private fun showAdminUserManagement() {
        Toast.makeText(this, "User Management coming soon!", Toast.LENGTH_SHORT).show()
        // TODO: Navigate to User Management Activity
    }

    private fun showAdminSystemReports() {
        Toast.makeText(this, "System Reports coming soon!", Toast.LENGTH_SHORT).show()
        // TODO: Navigate to System Reports Activity
    }

    private fun adminEditUser(userSummaryItem: UserSummaryItem) {
        if (!SessionManager.hasPermission("MANAGE_USERS")) {
            Toast.makeText(this, "Access denied. User management permission required.", Toast.LENGTH_SHORT).show()
            return
        }

        Toast.makeText(this, "Admin Edit User: ${userSummaryItem.userName}", Toast.LENGTH_SHORT).show()
        // TODO: Navigate to Admin User Edit Activity
    }

    private fun adminManagePermissions(userSummaryItem: UserSummaryItem) {
        if (!SessionManager.isSuperAdmin()) {
            Toast.makeText(this, "Access denied. Super Admin privileges required.", Toast.LENGTH_SHORT).show()
            return
        }

        Toast.makeText(this, "Manage Permissions: ${userSummaryItem.userName}", Toast.LENGTH_SHORT).show()
        // TODO: Show permissions management dialog
    }

    private fun adminExportUserData(userSummaryItem: UserSummaryItem) {
        if (!SessionManager.hasPermission("MANAGE_USERS")) {
            Toast.makeText(this, "Access denied. User management permission required.", Toast.LENGTH_SHORT).show()
            return
        }

        Toast.makeText(this, "Exporting data for: ${userSummaryItem.userName}", Toast.LENGTH_SHORT).show()
        initiateUserDataExport(userSummaryItem)
    }

    private fun setupAdminIndicator() {
        // Update action bar title to show admin status
        val currentUser = SessionManager.getCurrentUser()
        val role = SessionManager.getCurrentUserRole()

        when (role) {
            UserRole.SUPER_ADMIN -> {
                supportActionBar?.title = "KPI Tracker - Super Admin"
                supportActionBar?.subtitle = "Welcome, ${currentUser?.name}"
            }
            UserRole.ADMIN -> {
                supportActionBar?.title = "KPI Tracker - Admin"
                supportActionBar?.subtitle = "Welcome, ${currentUser?.name}"
            }
            UserRole.USER -> {
                supportActionBar?.title = "KPI Tracker"
                supportActionBar?.subtitle = "Welcome, ${currentUser?.name}"
            }
        }
    }

    private fun setupBottomNavigation() {
        binding.bottomNavigation.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.nav_dashboard -> {
                    loadFragment(MainDashboardFragment())
                    supportActionBar?.title = "Dashboard"
                    // Show master card button on dashboard
                    showMasterCardButton(true)
                    true
                }
                R.id.nav_performance -> {
                    loadFragment(PerformanceFragment())
                    supportActionBar?.title = "Performance"
                    // Hide master card button on performance page
                    showMasterCardButton(false)
                    true
                }
                R.id.nav_messages -> {
                    loadFragment(MessagesFragment())
                    supportActionBar?.title = "Messages"
                    // Hide master card button on messages page
                    showMasterCardButton(false)
                    true
                }
                R.id.nav_account -> {
                    loadFragment(AccountFragment())
                    supportActionBar?.title = "Account"
                    // Hide master card button on account page
                    showMasterCardButton(false)
                    true
                }
                else -> false
            }
        }

        // Set default selection
        binding.bottomNavigation.selectedItemId = R.id.nav_dashboard
    }

    private fun loadFragment(fragment: Fragment) {
        supportFragmentManager.beginTransaction()
            .replace(R.id.fragmentContainer, fragment)
            .commit()
    }

    private fun showMasterCardButton(show: Boolean) {
        val toggleButton = binding.btnCreateMasterCard
        val overallCardView = binding.overallSummaryCardInclude.overallSummaryCardView

        if (show) {
            toggleButton.visibility = View.VISIBLE
            // Keep the overall card visibility as it was
        } else {
            toggleButton.visibility = View.GONE
            // Also hide the overall card when button is hidden
            overallCardView.visibility = View.GONE
        }

        Log.d("MainActivity", "Master card button visibility set to: ${if (show) "VISIBLE" else "GONE"}")
    }

    private fun setupDeveloperOptions() {
        // Add secret tap gesture to clear session (for development/testing)
        var tapCount = 0
        val maxTaps = 7 // Secret: tap 7 times on the title

        supportActionBar?.let { actionBar ->
            // This is a workaround since we can't directly set click listener on action bar
            // We'll use the main layout instead
            binding.root.setOnClickListener {
                tapCount++
                if (tapCount >= maxTaps) {
                    showDeveloperOptions()
                    tapCount = 0
                }

                // Reset counter after 3 seconds
                binding.root.postDelayed({
                    tapCount = 0
                }, 3000)
            }
        }
    }

    private fun showDeveloperOptions() {
        MaterialAlertDialogBuilder(this)
            .setTitle("🔧 Developer Options")
            .setMessage("Choose a developer action:")
            .setPositiveButton("Clear Session") { _, _ ->
                SessionManager.clearAllData()
                Toast.makeText(this, "Session cleared! Redirecting to login...", Toast.LENGTH_SHORT).show()
                redirectToLogin()
            }
            .setNeutralButton("Reset App Data") { _, _ ->
                showResetAppDataConfirmation()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun showResetAppDataConfirmation() {
        MaterialAlertDialogBuilder(this)
            .setTitle("⚠️ Reset App Data")
            .setMessage("This will delete ALL data including users, KPIs, and progress. This action cannot be undone!\n\nAre you sure?")
            .setPositiveButton("Yes, Reset Everything") { _, _ ->
                resetAppData()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun resetAppData() {
        lifecycleScope.launch {
            try {
                // Clear session
                SessionManager.clearAllData()

                // Clear SharedPreferences
                sharedPreferences.edit().clear().apply()

                // Clear database (this would require adding a method to clear all tables)
                // For now, we'll just clear session and preferences

                Toast.makeText(this@MainActivity, "App data reset! Redirecting to login...", Toast.LENGTH_LONG).show()
                redirectToLogin()
            } catch (e: Exception) {
                Toast.makeText(this@MainActivity, "Error resetting data: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun setupRecyclerView() {
        // RecyclerView setup is now handled by fragments
    }

     private fun observeUserSummaries() {
         // User summaries observation is now handled by fragments
     }

    private fun setupFab() {
        // FAB setup is now handled by fragments
    }



    private fun setupFilterChips() {
        // Filter chips setup is now handled by fragments
    }

    private fun checkAndRequestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            when {
                ContextCompat.checkSelfPermission(
                    this,
                    Manifest.permission.POST_NOTIFICATIONS
                ) == PackageManager.PERMISSION_GRANTED -> {
                    Log.i("MainActivity", "Notification permission already granted.")
                }
                shouldShowRequestPermissionRationale(Manifest.permission.POST_NOTIFICATIONS) -> {
                    Log.i("MainActivity", "Showing rationale for notification permission.")
                    requestPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
                }
                else -> {
                    Log.i("MainActivity", "Requesting notification permission.")
                    requestPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
                }
            }
        } else {
            Log.i("MainActivity", "Notification permission not required for this Android version.")
        }
    }

    private fun scheduleExpiryNotifications() {
        val workRequest = PeriodicWorkRequestBuilder<ExpiryNotificationWorker>(1, TimeUnit.DAYS)
            .build()
        WorkManager.getInstance(this).enqueueUniquePeriodicWork(
            "ExpiryNotificationWork",
            ExistingPeriodicWorkPolicy.KEEP,
            workRequest
        )
        Log.i("MainActivity", "Scheduled periodic expiry notification worker.")
    }

    private fun scheduleTaskReminders() {
        // FOR TESTING: Use Expedited OneTimeWorkRequest
        val workRequest = OneTimeWorkRequestBuilder<TaskReminderWorker>()
            // .setInitialDelay(10, TimeUnit.SECONDS) // Optional: very short delay
            .setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST) // Request to run as soon as possible
            .build()

        WorkManager.getInstance(this).enqueue(workRequest)

        Log.i("MainActivity", "Scheduled EXPEDITED ONE-TIME task reminder worker for TESTING. WorkRequest ID: ${workRequest.id}")
        // You can observe the worker's status for debugging:
        WorkManager.getInstance(this).getWorkInfoByIdLiveData(workRequest.id)
            .observe(this) { workInfo ->
                if (workInfo != null) {
                    Log.d("MainActivity", "TaskReminderWorker status: ${workInfo.state}")
                    if (workInfo.state == androidx.work.WorkInfo.State.FAILED) {
                        Log.e("MainActivity", "TaskReminderWorker failed. OutputData: ${workInfo.outputData}")
                    }
                }
            }
    }

    // --- Quick Actions and Details Dialogs ---
    private fun showQuickActionsDialog(userSummaryItem: UserSummaryItem) {
        val actions = arrayOf(
            "Edit User",
            "Add Progress",
            "View Reports",
            "Export Data",
            "Share Card",
            "Change Colors"
        )

        MaterialAlertDialogBuilder(this)
            .setTitle("Quick Actions - ${userSummaryItem.userName}")
            .setItems(actions) { _, which ->
                when (which) {
                    0 -> {
                        // Edit User - Navigate to user edit screen
                        Toast.makeText(this, "Edit user functionality", Toast.LENGTH_SHORT).show()
                    }
                    1 -> {
                        // Add Progress - Quick add progress
                        Toast.makeText(this, "Quick add progress", Toast.LENGTH_SHORT).show()
                    }
                    2 -> {
                        // View Reports
                        val intent = Intent(this, ModernReportActivity::class.java)
                        startActivity(intent)
                    }
                    3 -> {
                        // Export Data
                        initiateUserDataExport(userSummaryItem)
                    }
                    4 -> {
                        // Share Card - Find the view in RecyclerView
                        shareUserCard(userSummaryItem)
                    }
                    5 -> {
                        // Change Colors
                        showUserColorSelectionDialog(userSummaryItem)
                    }
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun showQuickDetailsDialog(userSummaryItem: UserSummaryItem) {
        val details = StringBuilder()
        details.append("User: ${userSummaryItem.userName}\n\n")
        details.append("Total KPIs: ${userSummaryItem.kpiCount}\n\n")
        details.append("KPI Details:\n")

        userSummaryItem.kpiDetails.forEach { kpiDetail ->
            details.append("• ${kpiDetail.kpiName}\n")
            details.append("  Monthly: ${kpiDetail.monthlyPercentage ?: 0}%\n")
            details.append("  Annual: ${kpiDetail.annualPercentage ?: 0}%\n\n")
        }

        MaterialAlertDialogBuilder(this)
            .setTitle("Quick Details")
            .setMessage(details.toString())
            .setPositiveButton("View Full Details") { _, _ ->
                onUserSummaryClicked(userSummaryItem)
            }
            .setNegativeButton("Close", null)
            .show()
    }

    private fun shareUserCard(userSummaryItem: UserSummaryItem) {
        // Share user card functionality is now handled by fragments
        Toast.makeText(this, "Share functionality - Coming Soon!", Toast.LENGTH_SHORT).show()
    }

    /**
     * Save the current user order to SharedPreferences
     */
    private fun saveUserOrder() {
        // Only save if order has actually changed
        if (!orderChanged) {
            Log.d("MainActivity", "Order hasn't changed, skipping save")
            return
        }

        // Save user order functionality is now handled by fragments
    }

    /**
     * Load and apply saved user order
     */
    private fun loadUserOrder(): Map<String, Int> {
        return try {
            val orderString = sharedPreferences.getString("user_order", "") ?: ""
            if (orderString.isNotEmpty()) {
                orderString.split(",").associate { pair ->
                    val (userId, position) = pair.split(":")
                    userId to position.toInt()
                }
            } else {
                emptyMap()
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "Error loading user order", e)
            emptyMap()
        }
    }

    /**
     * Apply saved order to user list
     */
    private fun applySavedOrder(userList: List<UserSummaryItem>): List<UserSummaryItem> {
        val savedOrder = loadUserOrder()
        return if (savedOrder.isNotEmpty()) {
            try {
                userList.sortedBy { user ->
                    savedOrder[user.userId] ?: Int.MAX_VALUE
                }
            } catch (e: Exception) {
                Log.e("MainActivity", "Error applying saved order", e)
                userList
            }
        } else {
            userList
        }
    }

    /**
     * Show drag and drop hint to user on first launch
     */
    private fun showDragDropHintIfNeeded() {
        // Drag drop hint functionality is now handled by fragments
    }

    // --- End Quick Actions and Details ---

    override fun onPause() {
        super.onPause()
        // Save user order when app goes to background (only if changed)
        if (orderChanged) {
            saveUserOrder()
            Log.d("MainActivity", "MainActivity paused - order saved.")
        } else {
            Log.d("MainActivity", "MainActivity paused - no order changes to save.")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // Save user order one final time when app is destroyed (only if changed)
        if (orderChanged) {
            saveUserOrder()
            Log.d("MainActivity", "MainActivity destroyed - order saved.")
        } else {
            Log.d("MainActivity", "MainActivity destroyed - no order changes to save.")
        }
    }

    // --- Menu and Notifications ---

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.main, menu)
        updateNotificationBadge(menu)

        // Show/hide admin dashboard based on user role
        val adminDashboardItem = menu.findItem(R.id.action_admin_dashboard)
        adminDashboardItem?.isVisible = SessionManager.canAccessAdminDashboard()

        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_notifications -> {
                openNotifications()
                true
            }
            R.id.action_view_modern_report -> {
                startActivity(Intent(this, ModernReportActivity::class.java))
                true
            }
            R.id.action_expiry_management -> {
                startActivity(Intent(this, ExpireManagementActivity::class.java))
                true
            }
            R.id.action_ocr -> {
                startActivity(Intent(this, OcrActivity::class.java))
                true
            }
            R.id.action_chat -> {
                openChatList()
                true
            }
            R.id.action_auto_send_settings -> {
                openAutoSendSettings()
                true
            }
            R.id.action_advanced_task -> {
                openAdvancedTaskCreator()
                true
            }
            R.id.action_test_alarm -> {
                testAlarmIntegration()
                true
            }
            R.id.action_admin_dashboard -> {
                if (SessionManager.canAccessAdminDashboard()) {
                    openAdminDashboard()
                } else {
                    Toast.makeText(this, "Access denied. Admin privileges required.", Toast.LENGTH_SHORT).show()
                }
                true
            }
            R.id.action_switch_user -> {
                showSwitchUserDialog()
                true
            }
            R.id.action_logout -> {
                showLogoutConfirmation()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun updateNotificationBadge(menu: Menu) {
        val notificationItem = menu.findItem(R.id.action_notifications)
        if (notificationItem != null) {
            val unreadCount = AppNotificationManager.getUnreadNotificationsCount(this)
            if (unreadCount > 0) {
                notificationItem.title = "🔔 Notifications ($unreadCount)"
            } else {
                notificationItem.title = "🔔 Notifications"
            }
        }
    }

    private fun openNotifications() {
        val intent = Intent(this, com.example.kpitrackerapp.ui.NotificationsActivity::class.java)
        startActivity(intent)
    }

    override fun onResume() {
        super.onResume()
        // Update notification badge when returning to activity
        invalidateOptionsMenu()
    }

    // --- Firebase Messaging Initialization ---

    private fun initializeFirebaseMessaging() {
        lifecycleScope.launch {
            try {
                val currentUserId = SessionManager.getCurrentUserId()
                if (currentUserId != null) {
                    val success = FirebaseMessageManager.initializeMessaging(this@MainActivity, currentUserId)
                    if (success) {
                        Log.d("MainActivity", "Firebase messaging initialized successfully")
                    } else {
                        Log.w("MainActivity", "Firebase messaging initialization failed, using offline mode")
                    }
                } else {
                    Log.w("MainActivity", "No current user ID, skipping Firebase initialization")
                }
            } catch (e: Exception) {
                Log.e("MainActivity", "Error initializing Firebase messaging", e)
            }
        }
    }

    // --- Alarm Integration Testing ---

    /**
     * اختبار تكامل المنبهات مع تطبيق الساعة
     */
    private fun testAlarmIntegration() {
        val alarmManager = AlarmClockManager(this)

        // إظهار حوار اختبار المنبه
        MaterialAlertDialogBuilder(this)
            .setTitle("⏰ اختبار تكامل المنبه")
            .setMessage("اختر نوع الاختبار:")
            .setPositiveButton("اختبار المنبه") { _, _ ->
                val success = alarmManager.addTestAlarm()
                if (success) {
                    Toast.makeText(this, "✅ تم إرسال طلب إضافة منبه تجريبي", Toast.LENGTH_LONG).show()
                } else {
                    Toast.makeText(this, "❌ فشل في إضافة المنبه التجريبي", Toast.LENGTH_LONG).show()
                }
            }
            .setNeutralButton("فحص النظام") { _, _ ->
                val testResults = alarmManager.testAlarmIntegration()
                MaterialAlertDialogBuilder(this)
                    .setTitle("📋 نتائج فحص النظام")
                    .setMessage(testResults)
                    .setPositiveButton("موافق", null)
                    .show()
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    // --- End Permissions and Background Tasks ---
}
