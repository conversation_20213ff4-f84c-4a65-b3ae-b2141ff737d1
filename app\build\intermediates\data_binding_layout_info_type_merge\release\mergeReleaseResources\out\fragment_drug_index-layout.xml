<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_drug_index" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\fragment_drug_index.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_drug_index_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="257" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="18" endOffset="53"/></Target><Target id="@+id/searchEditText" view="EditText"><Expressions/><location startLine="32" startOffset="12" endLine="39" endOffset="51"/></Target><Target id="@+id/drugAutoComplete" view="AutoCompleteTextView"><Expressions/><location startLine="41" startOffset="12" endLine="54" endOffset="43"/></Target><Target id="@+id/drugNameTextView" view="TextView"><Expressions/><location startLine="56" startOffset="12" endLine="63" endOffset="43"/></Target><Target id="@+id/usesTextView" view="TextView"><Expressions/><location startLine="85" startOffset="20" endLine="90" endOffset="42"/></Target><Target id="@+id/interactionsTextView" view="TextView"><Expressions/><location startLine="114" startOffset="20" endLine="119" endOffset="42"/></Target><Target id="@+id/sideEffectsTextView" view="TextView"><Expressions/><location startLine="143" startOffset="20" endLine="148" endOffset="42"/></Target><Target id="@+id/usageInstructionsTextView" view="TextView"><Expressions/><location startLine="172" startOffset="20" endLine="177" endOffset="42"/></Target><Target id="@+id/priceCardView" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="182" startOffset="12" endLine="253" endOffset="47"/></Target><Target id="@+id/selectedDrugNameTextView" view="TextView"><Expressions/><location startLine="208" startOffset="20" endLine="217" endOffset="53"/></Target><Target id="@+id/selectedDrugDetailsTextView" view="TextView"><Expressions/><location startLine="219" startOffset="20" endLine="227" endOffset="53"/></Target><Target id="@+id/priceTextView" view="TextView"><Expressions/><location startLine="229" startOffset="20" endLine="240" endOffset="48"/></Target><Target id="@+id/manufacturerTextView" view="TextView"><Expressions/><location startLine="242" startOffset="20" endLine="250" endOffset="53"/></Target></Targets></Layout>