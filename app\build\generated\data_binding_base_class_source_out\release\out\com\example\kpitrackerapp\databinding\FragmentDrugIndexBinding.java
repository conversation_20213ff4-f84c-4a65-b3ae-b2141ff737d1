// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentDrugIndexBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final AutoCompleteTextView drugAutoComplete;

  @NonNull
  public final TextView drugNameTextView;

  @NonNull
  public final TextView interactionsTextView;

  @NonNull
  public final TextView manufacturerTextView;

  @NonNull
  public final CardView priceCardView;

  @NonNull
  public final TextView priceTextView;

  @NonNull
  public final EditText searchEditText;

  @NonNull
  public final TextView selectedDrugDetailsTextView;

  @NonNull
  public final TextView selectedDrugNameTextView;

  @NonNull
  public final TextView sideEffectsTextView;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView usageInstructionsTextView;

  @NonNull
  public final TextView usesTextView;

  private FragmentDrugIndexBinding(@NonNull LinearLayout rootView,
      @NonNull AutoCompleteTextView drugAutoComplete, @NonNull TextView drugNameTextView,
      @NonNull TextView interactionsTextView, @NonNull TextView manufacturerTextView,
      @NonNull CardView priceCardView, @NonNull TextView priceTextView,
      @NonNull EditText searchEditText, @NonNull TextView selectedDrugDetailsTextView,
      @NonNull TextView selectedDrugNameTextView, @NonNull TextView sideEffectsTextView,
      @NonNull Toolbar toolbar, @NonNull TextView usageInstructionsTextView,
      @NonNull TextView usesTextView) {
    this.rootView = rootView;
    this.drugAutoComplete = drugAutoComplete;
    this.drugNameTextView = drugNameTextView;
    this.interactionsTextView = interactionsTextView;
    this.manufacturerTextView = manufacturerTextView;
    this.priceCardView = priceCardView;
    this.priceTextView = priceTextView;
    this.searchEditText = searchEditText;
    this.selectedDrugDetailsTextView = selectedDrugDetailsTextView;
    this.selectedDrugNameTextView = selectedDrugNameTextView;
    this.sideEffectsTextView = sideEffectsTextView;
    this.toolbar = toolbar;
    this.usageInstructionsTextView = usageInstructionsTextView;
    this.usesTextView = usesTextView;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDrugIndexBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDrugIndexBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_drug_index, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDrugIndexBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.drugAutoComplete;
      AutoCompleteTextView drugAutoComplete = ViewBindings.findChildViewById(rootView, id);
      if (drugAutoComplete == null) {
        break missingId;
      }

      id = R.id.drugNameTextView;
      TextView drugNameTextView = ViewBindings.findChildViewById(rootView, id);
      if (drugNameTextView == null) {
        break missingId;
      }

      id = R.id.interactionsTextView;
      TextView interactionsTextView = ViewBindings.findChildViewById(rootView, id);
      if (interactionsTextView == null) {
        break missingId;
      }

      id = R.id.manufacturerTextView;
      TextView manufacturerTextView = ViewBindings.findChildViewById(rootView, id);
      if (manufacturerTextView == null) {
        break missingId;
      }

      id = R.id.priceCardView;
      CardView priceCardView = ViewBindings.findChildViewById(rootView, id);
      if (priceCardView == null) {
        break missingId;
      }

      id = R.id.priceTextView;
      TextView priceTextView = ViewBindings.findChildViewById(rootView, id);
      if (priceTextView == null) {
        break missingId;
      }

      id = R.id.searchEditText;
      EditText searchEditText = ViewBindings.findChildViewById(rootView, id);
      if (searchEditText == null) {
        break missingId;
      }

      id = R.id.selectedDrugDetailsTextView;
      TextView selectedDrugDetailsTextView = ViewBindings.findChildViewById(rootView, id);
      if (selectedDrugDetailsTextView == null) {
        break missingId;
      }

      id = R.id.selectedDrugNameTextView;
      TextView selectedDrugNameTextView = ViewBindings.findChildViewById(rootView, id);
      if (selectedDrugNameTextView == null) {
        break missingId;
      }

      id = R.id.sideEffectsTextView;
      TextView sideEffectsTextView = ViewBindings.findChildViewById(rootView, id);
      if (sideEffectsTextView == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.usageInstructionsTextView;
      TextView usageInstructionsTextView = ViewBindings.findChildViewById(rootView, id);
      if (usageInstructionsTextView == null) {
        break missingId;
      }

      id = R.id.usesTextView;
      TextView usesTextView = ViewBindings.findChildViewById(rootView, id);
      if (usesTextView == null) {
        break missingId;
      }

      return new FragmentDrugIndexBinding((LinearLayout) rootView, drugAutoComplete,
          drugNameTextView, interactionsTextView, manufacturerTextView, priceCardView,
          priceTextView, searchEditText, selectedDrugDetailsTextView, selectedDrugNameTextView,
          sideEffectsTextView, toolbar, usageInstructionsTextView, usesTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
