package com.example.kpitrackerapp.persistence

import androidx.room.*
import com.example.kpitrackerapp.models.ChatMessage
import com.example.kpitrackerapp.models.Conversation
import com.example.kpitrackerapp.models.ConversationItem
import com.example.kpitrackerapp.models.User
import kotlinx.coroutines.flow.Flow

@Dao
interface ChatDao {

    // ==================== MESSAGES ====================

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMessage(message: ChatMessage): Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMessages(messages: List<ChatMessage>)

    @Update
    suspend fun updateMessage(message: ChatMessage)

    @Delete
    suspend fun deleteMessage(message: ChatMessage)

    @Query("DELETE FROM chat_messages WHERE id = :messageId")
    suspend fun deleteMessageById(messageId: String)

    @Query("SELECT * FROM chat_messages WHERE conversationId = :conversationId ORDER BY timestamp ASC")
    fun getMessagesForConversation(conversationId: String): Flow<List<ChatMessage>>

    @Query("SELECT * FROM chat_messages WHERE conversationId = :conversationId ORDER BY timestamp DESC LIMIT :limit OFFSET :offset")
    suspend fun getMessagesForConversationPaged(conversationId: String, limit: Int, offset: Int): List<ChatMessage>

    @Query("SELECT * FROM chat_messages WHERE id = :messageId")
    suspend fun getMessageById(messageId: String): ChatMessage?

    @Query("UPDATE chat_messages SET isRead = 1 WHERE conversationId = :conversationId AND receiverId = :userId AND isRead = 0")
    suspend fun markMessagesAsRead(conversationId: String, userId: String)

    @Query("UPDATE chat_messages SET isDelivered = 1 WHERE conversationId = :conversationId AND senderId != :userId AND isDelivered = 0")
    suspend fun markMessagesAsDelivered(conversationId: String, userId: String)

    @Query("SELECT COUNT(*) FROM chat_messages WHERE receiverId = :userId AND isRead = 0")
    suspend fun getUnreadMessageCount(userId: String): Int

    @Query("SELECT COUNT(*) FROM chat_messages WHERE conversationId = :conversationId AND receiverId = :userId AND isRead = 0")
    suspend fun getUnreadMessageCountForConversation(conversationId: String, userId: String): Int

    @Query("SELECT * FROM chat_messages WHERE conversationId = :conversationId ORDER BY timestamp DESC LIMIT 1")
    suspend fun getLastMessageForConversation(conversationId: String): ChatMessage?

    // Search messages
    @Query("SELECT * FROM chat_messages WHERE conversationId = :conversationId AND message LIKE '%' || :query || '%' ORDER BY timestamp DESC")
    suspend fun searchMessagesInConversation(conversationId: String, query: String): List<ChatMessage>

    @Query("SELECT * FROM chat_messages WHERE (senderId = :userId OR receiverId = :userId) AND message LIKE '%' || :query || '%' ORDER BY timestamp DESC")
    suspend fun searchAllMessages(userId: String, query: String): List<ChatMessage>

    // ==================== CONVERSATIONS ====================

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertConversation(conversation: Conversation): Long

    @Update
    suspend fun updateConversation(conversation: Conversation)

    @Delete
    suspend fun deleteConversation(conversation: Conversation)

    @Query("DELETE FROM chat_messages WHERE conversationId = :conversationId")
    suspend fun deleteAllMessagesInConversation(conversationId: String)

    @Query("SELECT * FROM conversations WHERE id = :conversationId")
    suspend fun getConversationById(conversationId: String): Conversation?

    @Query("SELECT * FROM conversations WHERE (participant1Id = :user1Id AND participant2Id = :user2Id) OR (participant1Id = :user2Id AND participant2Id = :user1Id)")
    suspend fun getConversationBetweenUsers(user1Id: String, user2Id: String): Conversation?

    @Query("""
        SELECT c.*,
               CASE
                   WHEN c.participant1Id = :userId THEN u2.name
                   ELSE u1.name
               END as otherParticipantName,
               CASE
                   WHEN c.participant1Id = :userId THEN u2.imagePath
                   ELSE u1.imagePath
               END as otherParticipantImage,
               CASE
                   WHEN c.participant1Id = :userId THEN c.unreadCount1
                   ELSE c.unreadCount2
               END as unreadCount
        FROM conversations c
        INNER JOIN users u1 ON c.participant1Id = u1.id
        INNER JOIN users u2 ON c.participant2Id = u2.id
        WHERE c.participant1Id = :userId OR c.participant2Id = :userId
        ORDER BY c.lastMessageTime DESC
    """)
    fun getConversationsForUser(userId: String): Flow<List<ConversationWithDetails>>

    @Query("""
        SELECT c.*,
               CASE
                   WHEN c.participant1Id = :userId THEN u2.name
                   ELSE u1.name
               END as otherParticipantName,
               CASE
                   WHEN c.participant1Id = :userId THEN u2.imagePath
                   ELSE u1.imagePath
               END as otherParticipantImage,
               CASE
                   WHEN c.participant1Id = :userId THEN c.unreadCount1
                   ELSE c.unreadCount2
               END as unreadCount
        FROM conversations c
        INNER JOIN users u1 ON c.participant1Id = u1.id
        INNER JOIN users u2 ON c.participant2Id = u2.id
        WHERE (c.participant1Id = :userId OR c.participant2Id = :userId)
        AND ((c.participant1Id = :userId AND c.isArchived1 = 0) OR (c.participant2Id = :userId AND c.isArchived2 = 0))
        ORDER BY c.lastMessageTime DESC
    """)
    fun getActiveConversationsForUser(userId: String): Flow<List<ConversationWithDetails>>

    @Query("UPDATE conversations SET unreadCount1 = 0 WHERE id = :conversationId AND participant1Id = :userId")
    suspend fun clearUnreadCountForParticipant1(conversationId: String, userId: String)

    @Query("UPDATE conversations SET unreadCount2 = 0 WHERE id = :conversationId AND participant2Id = :userId")
    suspend fun clearUnreadCountForParticipant2(conversationId: String, userId: String)

    @Query("""
        UPDATE conversations
        SET unreadCount1 = CASE WHEN participant1Id = :receiverId THEN unreadCount1 + 1 ELSE unreadCount1 END,
            unreadCount2 = CASE WHEN participant2Id = :receiverId THEN unreadCount2 + 1 ELSE unreadCount2 END,
            lastMessage = :lastMessage,
            lastMessageTime = :timestamp,
            lastMessageSenderId = :senderId
        WHERE id = :conversationId
    """)
    suspend fun updateConversationWithNewMessage(
        conversationId: String,
        lastMessage: String,
        timestamp: Long,
        senderId: String,
        receiverId: String
    )

    // Archive/Unarchive conversations
    @Query("UPDATE conversations SET isArchived1 = :isArchived WHERE id = :conversationId AND participant1Id = :userId")
    suspend fun setArchiveStatusForParticipant1(conversationId: String, userId: String, isArchived: Boolean)

    @Query("UPDATE conversations SET isArchived2 = :isArchived WHERE id = :conversationId AND participant2Id = :userId")
    suspend fun setArchiveStatusForParticipant2(conversationId: String, userId: String, isArchived: Boolean)

    // Archive conversation for a specific user
    suspend fun archiveConversation(conversationId: String, userId: String) {
        setArchiveStatusForParticipant1(conversationId, userId, true)
        setArchiveStatusForParticipant2(conversationId, userId, true)
    }

    // Mute/Unmute conversations
    @Query("UPDATE conversations SET isMuted1 = :isMuted WHERE id = :conversationId AND participant1Id = :userId")
    suspend fun setMuteStatusForParticipant1(conversationId: String, userId: String, isMuted: Boolean)

    @Query("UPDATE conversations SET isMuted2 = :isMuted WHERE id = :conversationId AND participant2Id = :userId")
    suspend fun setMuteStatusForParticipant2(conversationId: String, userId: String, isMuted: Boolean)

    // ==================== UTILITY QUERIES ====================

    @Query("SELECT COUNT(*) FROM conversations WHERE participant1Id = :userId OR participant2Id = :userId")
    suspend fun getConversationCountForUser(userId: String): Int

    @Query("""
        SELECT SUM(
            CASE
                WHEN participant1Id = :userId THEN unreadCount1
                WHEN participant2Id = :userId THEN unreadCount2
                ELSE 0
            END
        ) as totalUnread
        FROM conversations
        WHERE participant1Id = :userId OR participant2Id = :userId
    """)
    suspend fun getTotalUnreadCountForUser(userId: String): Int

    // Get users that the current user has conversations with
    @Query("""
        SELECT DISTINCT u.* FROM users u
        INNER JOIN conversations c ON (u.id = c.participant1Id OR u.id = c.participant2Id)
        WHERE (c.participant1Id = :userId OR c.participant2Id = :userId) AND u.id != :userId
        ORDER BY u.name ASC
    """)
    suspend fun getUsersWithConversations(userId: String): List<User>

    // Clean up old messages (optional, for performance)
    @Query("DELETE FROM chat_messages WHERE timestamp < :cutoffTime")
    suspend fun deleteOldMessages(cutoffTime: Long)
}

// Data class for conversation with additional details
data class ConversationWithDetails(
    val id: String,
    val participant1Id: String,
    val participant2Id: String,
    val lastMessage: String?,
    val lastMessageTime: Long,
    val lastMessageSenderId: String?,
    val unreadCount1: Int,
    val unreadCount2: Int,
    val isArchived1: Boolean,
    val isArchived2: Boolean,
    val isMuted1: Boolean,
    val isMuted2: Boolean,
    val createdAt: Long,
    val otherParticipantName: String,
    val otherParticipantImage: String?,
    val unreadCount: Int
)
