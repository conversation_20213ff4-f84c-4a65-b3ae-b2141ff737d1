[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_add_edit_kpi_original.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_add_edit_kpi_original.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\dialog_custom_pomodoro.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\dialog_custom_pomodoro.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\fragment_performance.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\fragment_performance.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\report_table_row_colored.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\report_table_row_colored.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_search_edit_progress.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_search_edit_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\item_chat_date_header.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_chat_date_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\fragment_main_dashboard.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\fragment_main_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\user_filter_dialog.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\user_filter_dialog.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\compact_report_table.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\compact_report_table.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\dialog_select_card_colors.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\dialog_select_card_colors.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\item_admin_dashboard_user.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_admin_dashboard_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\fragment_messages.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\fragment_messages.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\item_admin_dashboard_stat.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_admin_dashboard_stat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_create_user.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_create_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\report_table_row_tabular.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\report_table_row_tabular.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\chart_marker_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\chart_marker_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\dialog_kpi_actions.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\dialog_kpi_actions.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\unified_report_table_row.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\unified_report_table_row.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_chat.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_chat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_expire_management.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_expire_management.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\overall_summary_card_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\overall_summary_card_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\user_summary_card_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\user_summary_card_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_admin_dashboard.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_admin_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\fragment_dashboard.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\fragment_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_date_converter.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_date_converter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\unified_report_table.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\unified_report_table.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\kpi_card_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\kpi_card_item.xml"}, {"merged": "com.example.kpitrackerapp-mergeDebugResources-55:/layout/activity_date_converter.xml", "source": "com.example.kpitrackerapp-main-58:/layout/activity_date_converter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\progress_entry_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\progress_entry_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_notifications.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_notifications.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\divider_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\divider_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\item_admin_dashboard_action.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_admin_dashboard_action.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\ocr_review_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\ocr_review_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\item_chat_message_sent.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_chat_message_sent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\kpi_detail_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\kpi_detail_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\dialog_search_messages.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\dialog_search_messages.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\dialog_add_edit_progress.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\dialog_add_edit_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\item_task.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_user_kpi_list.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_user_kpi_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\dialog_progress_update.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\dialog_progress_update.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_kpi_detail.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_kpi_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\kpi_list_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\kpi_list_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\item_chat_message_received.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_chat_message_received.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_task_reminder_settings.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_task_reminder_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\item_task_report.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_task_report.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_report.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_report.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_excel_import.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_excel_import.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\compact_report_table_row.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\compact_report_table_row.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_ocr.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_ocr.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\fragment_account.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\fragment_account.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_add_edit_kpi.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_add_edit_kpi.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_task_management.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_task_management.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\item_recent_user.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_recent_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\item_admin_dashboard_activity.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_admin_dashboard_activity.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\unified_report_table_row_binding.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\unified_report_table_row_binding.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\item_admin_dashboard_header.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_admin_dashboard_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\item_search_result.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_search_result.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_add_task_modern.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_add_task_modern.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\excel_review_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\excel_review_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\user_checkbox_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\user_checkbox_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_pomodoro_timer.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_pomodoro_timer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\item_notification.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\item_conversation.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_conversation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_auto_send_settings.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_auto_send_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_add_edit_task_enhanced.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_add_edit_task_enhanced.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_excel_review.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_excel_review.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_ocr_review.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_ocr_review.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_task_report.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_task_report.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\item_subtask_mini.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_subtask_mini.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_login.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\item_user_list.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_user_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\dialog_custom_reminder.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\dialog_custom_reminder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_modern_report.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_modern_report.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\item_chat_system_message.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_chat_system_message.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\dialog_edit_task.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\dialog_edit_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\report_table_row.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\report_table_row.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\item_task_enhanced.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_task_enhanced.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\activity_chat_list.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_chat_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-mergeDebugResources-55:\\layout\\kpi_summary_detail_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\kpi_summary_detail_item.xml"}]