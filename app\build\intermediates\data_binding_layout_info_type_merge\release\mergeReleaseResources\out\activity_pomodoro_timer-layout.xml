<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_pomodoro_timer" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_pomodoro_timer.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_pomodoro_timer_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="205" endOffset="53"/></Target><Target id="@+id/taskNameText" view="TextView"><Expressions/><location startLine="44" startOffset="20" endLine="53" endOffset="59"/></Target><Target id="@+id/timerDurationText" view="TextView"><Expressions/><location startLine="55" startOffset="20" endLine="61" endOffset="53"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="81" startOffset="20" endLine="90" endOffset="84"/></Target><Target id="@+id/timerDisplay" view="TextView"><Expressions/><location startLine="99" startOffset="24" endLine="107" endOffset="60"/></Target><Target id="@+id/progressText" view="TextView"><Expressions/><location startLine="109" startOffset="24" endLine="116" endOffset="60"/></Target><Target id="@+id/timerStatusText" view="TextView"><Expressions/><location startLine="125" startOffset="12" endLine="133" endOffset="52"/></Target><Target id="@+id/startPauseButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="143" startOffset="16" endLine="153" endOffset="50"/></Target><Target id="@+id/stopButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="155" startOffset="16" endLine="166" endOffset="49"/></Target><Target id="@+id/addTimeButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="177" startOffset="16" endLine="186" endOffset="44"/></Target><Target id="@+id/skipBreakButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="188" startOffset="16" endLine="197" endOffset="44"/></Target></Targets></Layout>