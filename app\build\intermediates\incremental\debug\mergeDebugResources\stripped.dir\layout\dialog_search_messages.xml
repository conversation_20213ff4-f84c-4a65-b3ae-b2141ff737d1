<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <com.google.android.material.textfield.TextInputLayout
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="🔍 Search messages..."
        android:layout_marginBottom="16dp"
        app:boxCornerRadiusTopStart="12dp"
        app:boxCornerRadiusTopEnd="12dp"
        app:boxCornerRadiusBottomStart="12dp"
        app:boxCornerRadiusBottomEnd="12dp"
        app:startIconDrawable="@drawable/ic_search"
        app:startIconTint="#6B7280">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/searchInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:imeOptions="actionSearch" />
    </com.google.android.material.textfield.TextInputLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Search Results:"
        android:textSize="14sp"
        android:textStyle="bold"
        android:textColor="#1A1A1A"
        android:layout_marginBottom="8dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/searchResults"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:background="#F8F9FA"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="💡 Tip: Type at least 2 characters to search"
        android:textSize="12sp"
        android:textColor="#6B7280"
        android:layout_marginTop="8dp" />

</LinearLayout>
