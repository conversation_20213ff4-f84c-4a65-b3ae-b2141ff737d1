package com.example.kpitrackerapp.utils

import java.util.*
import kotlin.math.*

object HijriCalendar {
    
    data class HijriDate(
        val year: Int,
        val month: Int,
        val day: Int
    )
    
    // Hijri epoch (July 16, 622 CE)
    private const val HIJRI_EPOCH = 1948439.5
    
    // Average length of a Hijri year in days
    private const val HIJRI_YEAR_LENGTH = 354.36667
    
    // Average length of a Hijri month in days
    private const val HIJRI_MONTH_LENGTH = 29.530588
    
    /**
     * Convert Gregorian date to Hijri date
     */
    fun gregorianToHijri(gregorianCalendar: Calendar): HijriDate {
        val julianDay = gregorian<PERSON>o<PERSON>ulian(gregorianCalendar)
        return julian<PERSON>o<PERSON>ijri(julianDay)
    }
    
    /**
     * Convert Hijri date to Gregorian date
     */
    fun hijriToGregorian(hijriDate: HijriDate): Calendar {
        val julianDay = hijriTo<PERSON>ulian(hijriDate)
        return julian<PERSON>o<PERSON>re<PERSON>ian(julianDay)
    }
    
    /**
     * Get number of days in a Hijri month
     */
    fun getDaysInHijriMonth(month: Int, year: Int): Int {
        // Hijri months alternate between 30 and 29 days
        // with the 12th month having 30 days in leap years
        return when (month) {
            1, 3, 5, 7, 9, 11 -> 30
            2, 4, 6, 8, 10 -> 29
            12 -> if (isHijriLeapYear(year)) 30 else 29
            else -> 29
        }
    }
    
    /**
     * Check if a Hijri year is a leap year
     */
    private fun isHijriLeapYear(year: Int): Boolean {
        // In a 30-year cycle, years 2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29 are leap years
        val cycle = year % 30
        return cycle in listOf(2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29)
    }
    
    /**
     * Convert Gregorian calendar to Julian day number
     */
    private fun gregorianToJulian(calendar: Calendar): Double {
        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH) + 1 // Calendar.MONTH is 0-based
        val day = calendar.get(Calendar.DAY_OF_MONTH)
        
        val a = (14 - month) / 12
        val y = year - a
        val m = month + 12 * a - 3
        
        val julianDay = day + (153 * m + 2) / 5 + 365 * y + y / 4 - y / 100 + y / 400 + 1721119
        
        return julianDay.toDouble()
    }
    
    /**
     * Convert Julian day number to Gregorian calendar
     */
    private fun julianToGregorian(julianDay: Double): Calendar {
        val jd = julianDay.toInt()
        val a = jd + 32044
        val b = (4 * a + 3) / 146097
        val c = a - (146097 * b) / 4
        val d = (4 * c + 3) / 1461
        val e = c - (1461 * d) / 4
        val m = (5 * e + 2) / 153
        
        val day = e - (153 * m + 2) / 5 + 1
        val month = m + 3 - 12 * (m / 10)
        val year = 100 * b + d - 4800 + m / 10
        
        val calendar = Calendar.getInstance()
        calendar.set(year, month - 1, day) // Calendar.MONTH is 0-based
        return calendar
    }
    
    /**
     * Convert Julian day number to Hijri date
     */
    private fun julianToHijri(julianDay: Double): HijriDate {
        val daysSinceEpoch = julianDay - HIJRI_EPOCH
        
        // Approximate year
        var year = floor(daysSinceEpoch / HIJRI_YEAR_LENGTH).toInt() + 1
        
        // Adjust year if necessary
        while (hijriToJulian(HijriDate(year, 1, 1)) > julianDay) {
            year--
        }
        while (hijriToJulian(HijriDate(year + 1, 1, 1)) <= julianDay) {
            year++
        }
        
        // Find month
        var month = 1
        while (month <= 12 && hijriToJulian(HijriDate(year, month + 1, 1)) <= julianDay) {
            month++
        }
        
        // Find day
        val monthStart = hijriToJulian(HijriDate(year, month, 1))
        val day = (julianDay - monthStart + 1).toInt()
        
        return HijriDate(year, month, day)
    }
    
    /**
     * Convert Hijri date to Julian day number
     */
    private fun hijriToJulian(hijriDate: HijriDate): Double {
        val year = hijriDate.year
        val month = hijriDate.month
        val day = hijriDate.day
        
        // Calculate total days from Hijri epoch
        var totalDays = 0.0
        
        // Add days for complete years
        for (y in 1 until year) {
            totalDays += if (isHijriLeapYear(y)) 355 else 354
        }
        
        // Add days for complete months in the current year
        for (m in 1 until month) {
            totalDays += getDaysInHijriMonth(m, year)
        }
        
        // Add remaining days
        totalDays += day - 1
        
        return HIJRI_EPOCH + totalDays
    }
}
