<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/overallSummaryCardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="16dp"
    android:layout_marginTop="8dp"
    android:layout_marginEnd="16dp"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/white"> <!-- Changed background color -->

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Top Background (Example: Purple) -->
        <View
            android:id="@+id/topBackgroundView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/purple_500"
            app:layout_constraintTop_toTopOf="@id/topContentContainer"
            app:layout_constraintStart_toStartOf="@id/topContentContainer"
            app:layout_constraintEnd_toEndOf="@id/topContentContainer"
            app:layout_constraintBottom_toBottomOf="@id/topContentContainer" />

        <!-- Container for Top Section Content (KPI Details) -->
        <LinearLayout
            android:id="@+id/topContentContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:gravity="center_vertical"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">



            <!-- Container for dynamically added KPI detail views -->
            <LinearLayout
                android:id="@+id/kpiDetailsContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="8dp"/>

        </LinearLayout>



    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
