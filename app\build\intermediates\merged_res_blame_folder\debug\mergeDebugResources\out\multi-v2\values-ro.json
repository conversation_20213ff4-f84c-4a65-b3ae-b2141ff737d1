{"logs": [{"outputFile": "com.example.kpitrackerapp-mergeDebugResources-54:/values-ro/values-ro.xml", "map": [{"source": "C:\\Gradle\\gradle-8.4\\caches\\8.11.1\\transforms\\36b4f88c4f8ab3af336856b4860e45b2\\transformed\\material-1.11.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,413,501,588,684,774,875,996,1080,1146,1241,1315,1375,1459,1521,1587,1645,1718,1781,1837,1956,2013,2074,2130,2204,2349,2435,2519,2652,2734,2817,2963,3053,3133,3188,3239,3305,3378,3456,3544,3629,3700,3777,3851,3923,4029,4120,4194,4289,4387,4461,4541,4642,4695,4781,4847,4936,5026,5088,5152,5215,5289,5401,5511,5621,5726,5785,5840", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,91,87,86,95,89,100,120,83,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,83,132,81,82,145,89,79,54,50,65,72,77,87,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78", "endOffsets": "316,408,496,583,679,769,870,991,1075,1141,1236,1310,1370,1454,1516,1582,1640,1713,1776,1832,1951,2008,2069,2125,2199,2344,2430,2514,2647,2729,2812,2958,3048,3128,3183,3234,3300,3373,3451,3539,3624,3695,3772,3846,3918,4024,4115,4189,4284,4382,4456,4536,4637,4690,4776,4842,4931,5021,5083,5147,5210,5284,5396,5506,5616,5721,5780,5835,5914"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3117,3209,3297,3384,3480,4297,4398,4519,6863,6929,7024,7098,7158,7242,7304,7370,7428,7501,7564,7620,7739,7796,7857,7913,7987,8132,8218,8302,8435,8517,8600,8746,8836,8916,8971,9022,9088,9161,9239,9327,9412,9483,9560,9634,9706,9812,9903,9977,10072,10170,10244,10324,10425,10478,10564,10630,10719,10809,10871,10935,10998,11072,11184,11294,11404,11509,11568,11623", "endLines": "6,34,35,36,37,38,46,47,48,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126", "endColumns": "12,91,87,86,95,89,100,120,83,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,83,132,81,82,145,89,79,54,50,65,72,77,87,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78", "endOffsets": "366,3204,3292,3379,3475,3565,4393,4514,4598,6924,7019,7093,7153,7237,7299,7365,7423,7496,7559,7615,7734,7791,7852,7908,7982,8127,8213,8297,8430,8512,8595,8741,8831,8911,8966,9017,9083,9156,9234,9322,9407,9478,9555,9629,9701,9807,9898,9972,10067,10165,10239,10319,10420,10473,10559,10625,10714,10804,10866,10930,10993,11067,11179,11289,11399,11504,11563,11618,11697"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\8.11.1\\transforms\\44fac508c06d1fa71634b2a2e87bd97f\\transformed\\jetified-play-services-basement-18.1.0\\res\\values-ro\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "57", "startColumns": "4", "startOffsets": "5623", "endColumns": "144", "endOffsets": "5763"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\8.11.1\\transforms\\d69c07a7d8d82aa3a4b3977e5b803b90\\transformed\\core-1.12.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "39,40,41,42,43,44,45,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3570,3668,3770,3870,3969,4071,4180,11786", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "3663,3765,3865,3964,4066,4175,4292,11882"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\8.11.1\\transforms\\1fb6da15b525de990376081401abcb58\\transformed\\appcompat-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1855,1938,2050,2158,2258,2372,2478,2584,2748,2851", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "221,325,438,522,626,747,832,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1850,1933,2045,2153,2253,2367,2473,2579,2743,2846,2930"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "371,492,596,709,793,897,1018,1103,1183,1274,1367,1462,1556,1656,1749,1844,1938,2029,2121,2204,2316,2424,2524,2638,2744,2850,3014,11702", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "487,591,704,788,892,1013,1098,1178,1269,1362,1457,1551,1651,1744,1839,1933,2024,2116,2199,2311,2419,2519,2633,2739,2845,3009,3112,11781"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\8.11.1\\transforms\\32f7978bc74fb055e711be763c16e3bd\\transformed\\jetified-play-services-base-18.1.0\\res\\values-ro\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,452,577,682,829,957,1076,1181,1339,1445,1600,1728,1870,2032,2099,2162", "endColumns": "102,155,124,104,146,127,118,104,157,105,154,127,141,161,66,62,77", "endOffsets": "295,451,576,681,828,956,1075,1180,1338,1444,1599,1727,1869,2031,2098,2161,2239"}, "to": {"startLines": "49,50,51,52,53,54,55,56,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4603,4710,4870,4999,5108,5259,5391,5514,5768,5930,6040,6199,6331,6477,6643,6714,6781", "endColumns": "106,159,128,108,150,131,122,108,161,109,158,131,145,165,70,66,81", "endOffsets": "4705,4865,4994,5103,5254,5386,5509,5618,5925,6035,6194,6326,6472,6638,6709,6776,6858"}}]}]}