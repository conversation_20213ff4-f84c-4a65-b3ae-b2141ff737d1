package com.example.kpitrackerapp.ui

import android.app.DatePickerDialog
import android.os.Bundle
import android.widget.NumberPicker
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.example.kpitrackerapp.R
import com.example.kpitrackerapp.databinding.ActivityDateConverterBinding
import com.example.kpitrackerapp.utils.HijriCalendar
import java.text.SimpleDateFormat
import java.util.*

class DateConverterActivity : AppCompatActivity() {

    private lateinit var binding: ActivityDateConverterBinding
    private var selectedDay = 1
    private var selectedMonth = 1
    private var selectedYear = 2024

    private var isHijriToMiladi = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDateConverterBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupViews()
        setupClickListeners()

        // Set current date as default
        val calendar = Calendar.getInstance()
        selectedDay = calendar.get(Calendar.DAY_OF_MONTH)
        selectedMonth = calendar.get(Calendar.MONTH) + 1
        selectedYear = calendar.get(Calendar.YEAR)
        updateInputFields()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        binding.toolbar.setNavigationOnClickListener {
            onBackPressed()
        }
    }

    private fun setupViews() {
        setupRadioGroup()
        setupInputFields()
    }



    private fun setupRadioGroup() {
        binding.conversionTypeGroup.setOnCheckedChangeListener { _, checkedId ->
            isHijriToMiladi = checkedId == R.id.radioHijriToMiladi
            clearResults()
            updateInputFieldsLabels()
        }
    }

    private fun setupInputFields() {
        // Add text watchers for input fields
        binding.dayInput.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: android.text.Editable?) {
                if (s?.isNotEmpty() == true) {
                    try {
                        selectedDay = s.toString().toInt()
                    } catch (e: NumberFormatException) {
                        // Handle invalid input
                    }
                }
            }
        })

        binding.monthInput.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: android.text.Editable?) {
                if (s?.isNotEmpty() == true) {
                    try {
                        selectedMonth = s.toString().toInt()
                    } catch (e: NumberFormatException) {
                        // Handle invalid input
                    }
                }
            }
        })

        binding.yearInput.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: android.text.Editable?) {
                if (s?.isNotEmpty() == true) {
                    try {
                        selectedYear = s.toString().toInt()
                    } catch (e: NumberFormatException) {
                        // Handle invalid input
                    }
                }
            }
        })
    }



    private fun setupClickListeners() {
        binding.convertButton.setOnClickListener {
            convertDate()
        }
    }

    private fun updateInputFields() {
        binding.dayInput.setText(selectedDay.toString())
        binding.monthInput.setText(selectedMonth.toString())
        binding.yearInput.setText(selectedYear.toString())
    }

    private fun updateInputFieldsLabels() {
        // Labels are already set in XML, no need to change them
        // The conversion type determines what calendar system the user is inputting
    }

    private fun convertDate() {
        try {
            // Get values from input fields
            getValuesFromInputFields()

            if (isHijriToMiladi) {
                convertHijriToMiladi()
            } else {
                convertMiladiToHijri()
            }
        } catch (e: Exception) {
            // Handle conversion error
            clearResults()
            android.widget.Toast.makeText(this, "خطأ في التاريخ المدخل", android.widget.Toast.LENGTH_SHORT).show()
        }
    }

    private fun getValuesFromInputFields() {
        try {
            val dayText = binding.dayInput.text.toString()
            val monthText = binding.monthInput.text.toString()
            val yearText = binding.yearInput.text.toString()

            if (dayText.isNotEmpty()) selectedDay = dayText.toInt()
            if (monthText.isNotEmpty()) selectedMonth = monthText.toInt()
            if (yearText.isNotEmpty()) selectedYear = yearText.toInt()

            // Validate ranges
            if (selectedDay < 1 || selectedDay > 31) throw IllegalArgumentException("Invalid day")
            if (selectedMonth < 1 || selectedMonth > 12) throw IllegalArgumentException("Invalid month")
            if (selectedYear < 1) throw IllegalArgumentException("Invalid year")

        } catch (e: Exception) {
            throw IllegalArgumentException("Invalid input values")
        }
    }

    private fun convertMiladiToHijri() {
        val gregorianCalendar = Calendar.getInstance().apply {
            set(selectedYear, selectedMonth - 1, selectedDay)
        }

        val hijriDate = HijriCalendar.gregorianToHijri(gregorianCalendar)

        // Update results
        binding.resultMiladiDate.text = "$selectedDay/$selectedMonth/$selectedYear"
        binding.resultHijriDate.text = "${hijriDate.day}/${hijriDate.month}/${hijriDate.year}"
        binding.resultDayName.text = getDayName(gregorianCalendar.get(Calendar.DAY_OF_WEEK))
        binding.resultHijriMonth.text = getHijriMonthName(hijriDate.month)
        binding.resultMiladiMonth.text = getGregorianMonthName(selectedMonth)
        binding.resultSyrianiMonth.text = getSyrianiMonthName(selectedMonth)
        binding.resultHijriMonthDays.text = HijriCalendar.getDaysInHijriMonth(hijriDate.month, hijriDate.year).toString()
        binding.resultMiladiMonthDays.text = getDaysInGregorianMonth(selectedMonth, selectedYear).toString()
    }

    private fun convertHijriToMiladi() {
        val hijriDate = HijriCalendar.HijriDate(selectedYear, selectedMonth, selectedDay)
        val gregorianCalendar = HijriCalendar.hijriToGregorian(hijriDate)

        val gregorianDay = gregorianCalendar.get(Calendar.DAY_OF_MONTH)
        val gregorianMonth = gregorianCalendar.get(Calendar.MONTH) + 1
        val gregorianYear = gregorianCalendar.get(Calendar.YEAR)

        // Update results
        binding.resultMiladiDate.text = "$gregorianDay/$gregorianMonth/$gregorianYear"
        binding.resultHijriDate.text = "$selectedDay/$selectedMonth/$selectedYear"
        binding.resultDayName.text = getDayName(gregorianCalendar.get(Calendar.DAY_OF_WEEK))
        binding.resultHijriMonth.text = getHijriMonthName(selectedMonth)
        binding.resultMiladiMonth.text = getGregorianMonthName(gregorianMonth)
        binding.resultSyrianiMonth.text = getSyrianiMonthName(gregorianMonth)
        binding.resultHijriMonthDays.text = HijriCalendar.getDaysInHijriMonth(selectedMonth, selectedYear).toString()
        binding.resultMiladiMonthDays.text = getDaysInGregorianMonth(gregorianMonth, gregorianYear).toString()
    }

    private fun clearResults() {
        binding.resultMiladiDate.text = ""
        binding.resultHijriDate.text = ""
        binding.resultDayName.text = ""
        binding.resultHijriMonth.text = ""
        binding.resultMiladiMonth.text = ""
        binding.resultSyrianiMonth.text = ""
        binding.resultHijriMonthDays.text = ""
        binding.resultMiladiMonthDays.text = ""
    }

    private fun getDaysInGregorianMonth(month: Int, year: Int): Int {
        val calendar = Calendar.getInstance()
        calendar.set(year, month - 1, 1)
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
    }

    private fun getDayName(dayOfWeek: Int): String {
        return when (dayOfWeek) {
            Calendar.SUNDAY -> "الأحد"
            Calendar.MONDAY -> "الإثنين"
            Calendar.TUESDAY -> "الثلاثاء"
            Calendar.WEDNESDAY -> "الأربعاء"
            Calendar.THURSDAY -> "الخميس"
            Calendar.FRIDAY -> "الجمعة"
            Calendar.SATURDAY -> "السبت"
            else -> ""
        }
    }

    private fun getHijriMonthName(month: Int): String {
        return when (month) {
            1 -> "محرم"
            2 -> "صفر"
            3 -> "ربيع الأول"
            4 -> "ربيع الثاني"
            5 -> "جمادى الأولى"
            6 -> "جمادى الثانية"
            7 -> "رجب"
            8 -> "شعبان"
            9 -> "رمضان"
            10 -> "شوال"
            11 -> "ذو القعدة"
            12 -> "ذو الحجة"
            else -> ""
        }
    }

    private fun getGregorianMonthName(month: Int): String {
        return when (month) {
            1 -> "يناير"
            2 -> "فبراير"
            3 -> "مارس"
            4 -> "أبريل"
            5 -> "مايو"
            6 -> "يونيو"
            7 -> "يوليو"
            8 -> "أغسطس"
            9 -> "سبتمبر"
            10 -> "أكتوبر"
            11 -> "نوفمبر"
            12 -> "ديسمبر"
            else -> ""
        }
    }

    private fun getSyrianiMonthName(month: Int): String {
        return when (month) {
            1 -> "كانون الثاني"
            2 -> "شباط"
            3 -> "آذار"
            4 -> "نيسان"
            5 -> "أيار"
            6 -> "حزيران"
            7 -> "تموز"
            8 -> "آب"
            9 -> "أيلول"
            10 -> "تشرين الأول"
            11 -> "تشرين الثاني"
            12 -> "كانون الأول"
            else -> ""
        }
    }
}
