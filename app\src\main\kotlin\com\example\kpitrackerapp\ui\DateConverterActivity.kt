package com.example.kpitrackerapp.ui

import android.app.DatePickerDialog
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.NumberPicker
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.example.kpitrackerapp.R
import com.example.kpitrackerapp.databinding.ActivityDateConverterBinding
import com.example.kpitrackerapp.utils.HijriCalendar
import java.text.SimpleDateFormat
import java.util.*

class DateConverterActivity : AppCompatActivity() {

    private lateinit var binding: ActivityDateConverterBinding
    private var selectedDay = 1
    private var selectedMonth = 1
    private var selectedYear = 2024

    private var isHijriToMiladi = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDateConverterBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupViews()
        setupClickListeners()

        // Set current date as default
        val calendar = Calendar.getInstance()
        selectedDay = calendar.get(Calendar.DAY_OF_MONTH)
        selectedMonth = calendar.get(Calendar.MONTH) + 1
        selectedYear = calendar.get(Calendar.YEAR)
        updateInputFields()
        updateInputFieldsLabels()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        binding.toolbar.setNavigationOnClickListener {
            onBackPressed()
        }
    }

    private fun setupViews() {
        setupAmPmSpinner()
        setupRadioGroup()
        setupInputFields()
    }

    private fun setupAmPmSpinner() {
        val amPmAdapter = ArrayAdapter.createFromResource(
            this,
            R.array.am_pm_array,
            android.R.layout.simple_spinner_item
        )
        amPmAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.amPmSpinner.adapter = amPmAdapter
    }



    private fun setupRadioGroup() {
        binding.conversionTypeGroup.setOnCheckedChangeListener { _, checkedId ->
            isHijriToMiladi = checkedId == R.id.radioHijriToMiladi
            clearResults()
            updateInputFieldsLabels()
        }
    }

    private fun setupInputFields() {
        // Add text watchers for input fields
        binding.dayInput.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: android.text.Editable?) {
                if (s?.isNotEmpty() == true) {
                    try {
                        selectedDay = s.toString().toInt()
                    } catch (e: NumberFormatException) {
                        // Handle invalid input
                    }
                }
            }
        })

        binding.monthInput.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: android.text.Editable?) {
                if (s?.isNotEmpty() == true) {
                    try {
                        selectedMonth = s.toString().toInt()
                    } catch (e: NumberFormatException) {
                        // Handle invalid input
                    }
                }
            }
        })

        binding.yearInput.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: android.text.Editable?) {
                if (s?.isNotEmpty() == true) {
                    try {
                        selectedYear = s.toString().toInt()
                    } catch (e: NumberFormatException) {
                        // Handle invalid input
                    }
                }
            }
        })
    }



    private fun setupClickListeners() {
        binding.convertButton.setOnClickListener {
            convertDate()
        }

        binding.calculateShiftButton.setOnClickListener {
            calculateShift()
        }
    }

    private fun updateInputFields() {
        binding.dayInput.setText(selectedDay.toString())
        binding.monthInput.setText(selectedMonth.toString())
        binding.yearInput.setText(selectedYear.toString())
    }

    private fun updateInputFieldsLabels() {
        // Update hint text based on conversion type
        if (isHijriToMiladi) {
            // User is inputting Hijri date
            binding.dayInput.hint = "اليوم (هجري)"
            binding.monthInput.hint = "الشهر (هجري)"
            binding.yearInput.hint = "السنة (هجري)"
        } else {
            // User is inputting Gregorian date
            binding.dayInput.hint = "اليوم (ميلادي)"
            binding.monthInput.hint = "الشهر (ميلادي)"
            binding.yearInput.hint = "السنة (ميلادي)"
        }
    }

    private fun convertDate() {
        try {
            // Get values from input fields
            getValuesFromInputFields()

            // Validate date according to conversion type
            if (!isValidDateForConversionType()) {
                clearResults()
                val message = if (isHijriToMiladi) {
                    "يرجى إدخال تاريخ هجري صحيح"
                } else {
                    "يرجى إدخال تاريخ ميلادي صحيح"
                }
                android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show()
                return
            }

            if (isHijriToMiladi) {
                convertHijriToMiladi()
            } else {
                convertMiladiToHijri()
            }
        } catch (e: Exception) {
            // Handle conversion error
            clearResults()
            android.widget.Toast.makeText(this, "خطأ في التاريخ المدخل", android.widget.Toast.LENGTH_SHORT).show()
        }
    }

    private fun isValidDateForConversionType(): Boolean {
        return try {
            if (isHijriToMiladi) {
                // Validate as Hijri date
                isValidHijriDate(selectedDay, selectedMonth, selectedYear)
            } else {
                // Validate as Gregorian date
                isValidGregorianDate(selectedDay, selectedMonth, selectedYear)
            }
        } catch (e: Exception) {
            false
        }
    }

    private fun isValidHijriDate(day: Int, month: Int, year: Int): Boolean {
        // Hijri year range (approximately 1300-1500 AH)
        // Current Hijri year is around 1445, so reasonable range is 1300-1500
        if (year < 1300 || year > 1500) return false
        if (month < 1 || month > 12) return false
        if (day < 1) return false

        // If year looks like Gregorian (1900-2100), it's probably wrong input
        if (year >= 1900 && year <= 2100) return false

        // Check maximum days in Hijri month
        val maxDays = try {
            HijriCalendar.getDaysInHijriMonth(month, year)
        } catch (e: Exception) {
            30 // Default fallback
        }

        return day <= maxDays
    }

    private fun isValidGregorianDate(day: Int, month: Int, year: Int): Boolean {
        // Gregorian year range (reasonable range)
        if (year < 1900 || year > 2100) return false
        if (month < 1 || month > 12) return false
        if (day < 1) return false

        // If year looks like Hijri (1300-1500), it's probably wrong input
        if (year >= 1300 && year <= 1500) return false

        // Check maximum days in Gregorian month
        val maxDays = getDaysInGregorianMonth(month, year)
        return day <= maxDays
    }

    private fun getValuesFromInputFields() {
        try {
            val dayText = binding.dayInput.text.toString()
            val monthText = binding.monthInput.text.toString()
            val yearText = binding.yearInput.text.toString()

            if (dayText.isNotEmpty()) selectedDay = dayText.toInt()
            if (monthText.isNotEmpty()) selectedMonth = monthText.toInt()
            if (yearText.isNotEmpty()) selectedYear = yearText.toInt()

            // Basic validation
            if (selectedDay < 1 || selectedDay > 31) throw IllegalArgumentException("Invalid day")
            if (selectedMonth < 1 || selectedMonth > 12) throw IllegalArgumentException("Invalid month")
            if (selectedYear < 1) throw IllegalArgumentException("Invalid year")

        } catch (e: Exception) {
            throw IllegalArgumentException("Invalid input values")
        }
    }

    private fun convertMiladiToHijri() {
        val gregorianCalendar = Calendar.getInstance().apply {
            set(selectedYear, selectedMonth - 1, selectedDay)
        }

        val hijriDate = HijriCalendar.gregorianToHijri(gregorianCalendar)

        // Update results
        binding.resultMiladiDate.text = "$selectedDay/$selectedMonth/$selectedYear"
        binding.resultHijriDate.text = "${hijriDate.day}/${hijriDate.month}/${hijriDate.year}"
        binding.resultDayName.text = getDayName(gregorianCalendar.get(Calendar.DAY_OF_WEEK))
        binding.resultHijriMonth.text = getHijriMonthName(hijriDate.month)
        binding.resultMiladiMonth.text = getGregorianMonthName(selectedMonth)
        binding.resultSyrianiMonth.text = getSyrianiMonthName(selectedMonth)
        binding.resultHijriMonthDays.text = HijriCalendar.getDaysInHijriMonth(hijriDate.month, hijriDate.year).toString()
        binding.resultMiladiMonthDays.text = getDaysInGregorianMonth(selectedMonth, selectedYear).toString()
    }

    private fun convertHijriToMiladi() {
        val hijriDate = HijriCalendar.HijriDate(selectedYear, selectedMonth, selectedDay)
        val gregorianCalendar = HijriCalendar.hijriToGregorian(hijriDate)

        val gregorianDay = gregorianCalendar.get(Calendar.DAY_OF_MONTH)
        val gregorianMonth = gregorianCalendar.get(Calendar.MONTH) + 1
        val gregorianYear = gregorianCalendar.get(Calendar.YEAR)

        // Update results
        binding.resultMiladiDate.text = "$gregorianDay/$gregorianMonth/$gregorianYear"
        binding.resultHijriDate.text = "$selectedDay/$selectedMonth/$selectedYear"
        binding.resultDayName.text = getDayName(gregorianCalendar.get(Calendar.DAY_OF_WEEK))
        binding.resultHijriMonth.text = getHijriMonthName(selectedMonth)
        binding.resultMiladiMonth.text = getGregorianMonthName(gregorianMonth)
        binding.resultSyrianiMonth.text = getSyrianiMonthName(gregorianMonth)
        binding.resultHijriMonthDays.text = HijriCalendar.getDaysInHijriMonth(selectedMonth, selectedYear).toString()
        binding.resultMiladiMonthDays.text = getDaysInGregorianMonth(gregorianMonth, gregorianYear).toString()
    }

    private fun clearResults() {
        binding.resultMiladiDate.text = ""
        binding.resultHijriDate.text = ""
        binding.resultDayName.text = ""
        binding.resultHijriMonth.text = ""
        binding.resultMiladiMonth.text = ""
        binding.resultSyrianiMonth.text = ""
        binding.resultHijriMonthDays.text = ""
        binding.resultMiladiMonthDays.text = ""
    }

    private fun getDaysInGregorianMonth(month: Int, year: Int): Int {
        val calendar = Calendar.getInstance()
        calendar.set(year, month - 1, 1)
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
    }

    private fun getDayName(dayOfWeek: Int): String {
        return when (dayOfWeek) {
            Calendar.SUNDAY -> "الأحد"
            Calendar.MONDAY -> "الإثنين"
            Calendar.TUESDAY -> "الثلاثاء"
            Calendar.WEDNESDAY -> "الأربعاء"
            Calendar.THURSDAY -> "الخميس"
            Calendar.FRIDAY -> "الجمعة"
            Calendar.SATURDAY -> "السبت"
            else -> ""
        }
    }

    private fun getHijriMonthName(month: Int): String {
        return when (month) {
            1 -> "محرم"
            2 -> "صفر"
            3 -> "ربيع الأول"
            4 -> "ربيع الثاني"
            5 -> "جمادى الأولى"
            6 -> "جمادى الثانية"
            7 -> "رجب"
            8 -> "شعبان"
            9 -> "رمضان"
            10 -> "شوال"
            11 -> "ذو القعدة"
            12 -> "ذو الحجة"
            else -> ""
        }
    }

    private fun getGregorianMonthName(month: Int): String {
        return when (month) {
            1 -> "يناير"
            2 -> "فبراير"
            3 -> "مارس"
            4 -> "أبريل"
            5 -> "مايو"
            6 -> "يونيو"
            7 -> "يوليو"
            8 -> "أغسطس"
            9 -> "سبتمبر"
            10 -> "أكتوبر"
            11 -> "نوفمبر"
            12 -> "ديسمبر"
            else -> ""
        }
    }

    private fun getSyrianiMonthName(month: Int): String {
        return when (month) {
            1 -> "كانون الثاني"
            2 -> "شباط"
            3 -> "آذار"
            4 -> "نيسان"
            5 -> "أيار"
            6 -> "حزيران"
            7 -> "تموز"
            8 -> "آب"
            9 -> "أيلول"
            10 -> "تشرين الأول"
            11 -> "تشرين الثاني"
            12 -> "كانون الأول"
            else -> ""
        }
    }

    private fun calculateShift() {
        try {
            val hoursText = binding.hoursInput.text.toString()
            val startTimeText = binding.startTimeInput.text.toString()

            if (hoursText.isEmpty() || startTimeText.isEmpty()) {
                android.widget.Toast.makeText(this, "يرجى إدخال جميع البيانات", android.widget.Toast.LENGTH_SHORT).show()
                return
            }

            val hours = hoursText.toInt()
            if (hours <= 0 || hours > 24) {
                android.widget.Toast.makeText(this, "يرجى إدخال عدد ساعات صحيح (1-24)", android.widget.Toast.LENGTH_SHORT).show()
                return
            }

            // Parse start time
            val timeParts = startTimeText.split(":")
            if (timeParts.size != 2) {
                android.widget.Toast.makeText(this, "يرجى إدخال الوقت بصيغة صحيحة (مثال: 5:00)", android.widget.Toast.LENGTH_SHORT).show()
                return
            }

            val startHour = timeParts[0].toInt()
            val startMinute = timeParts[1].toInt()

            if (startHour < 1 || startHour > 12 || startMinute < 0 || startMinute > 59) {
                android.widget.Toast.makeText(this, "يرجى إدخال وقت صحيح", android.widget.Toast.LENGTH_SHORT).show()
                return
            }

            // Get AM/PM selection
            val isAM = binding.amPmSpinner.selectedItemPosition == 0

            // Convert to 24-hour format
            var start24Hour = startHour
            if (!isAM && startHour != 12) {
                start24Hour += 12
            } else if (isAM && startHour == 12) {
                start24Hour = 0
            }

            // Calculate end time
            val startCalendar = Calendar.getInstance().apply {
                set(Calendar.HOUR_OF_DAY, start24Hour)
                set(Calendar.MINUTE, startMinute)
                set(Calendar.SECOND, 0)
            }

            val endCalendar = Calendar.getInstance().apply {
                timeInMillis = startCalendar.timeInMillis
                add(Calendar.HOUR_OF_DAY, hours)
            }

            // Format times for display
            val timeFormat12 = SimpleDateFormat("h:mm a", Locale("ar"))
            val timeFormat24 = SimpleDateFormat("HH:mm", Locale.getDefault())

            val startTime12 = timeFormat12.format(startCalendar.time)
            val endTime12 = timeFormat12.format(endCalendar.time)

            // Show results
            binding.resultStartTime.text = startTime12
            binding.resultEndTime.text = endTime12
            binding.resultTotalHours.text = "$hours ساعة"

            binding.shiftResultSection.visibility = View.VISIBLE

        } catch (e: Exception) {
            android.widget.Toast.makeText(this, "خطأ في البيانات المدخلة", android.widget.Toast.LENGTH_SHORT).show()
        }
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.date_converter_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.menu_shift_calculator -> {
                // Show shift calculator
                binding.shiftCalculatorSection.visibility = View.VISIBLE
                android.widget.Toast.makeText(this, "shift hour calculator activated", android.widget.Toast.LENGTH_SHORT).show()
                true
            }
            R.id.menu_date_converter -> {
                // Hide shift calculator (show date converter)
                binding.shiftCalculatorSection.visibility = View.GONE
                android.widget.Toast.makeText(this, "محول التاريخ activated", android.widget.Toast.LENGTH_SHORT).show()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
}
