package com.example.kpitrackerapp.ui

import android.app.DatePickerDialog
import android.os.Bundle
import android.widget.NumberPicker
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.example.kpitrackerapp.R
import com.example.kpitrackerapp.databinding.ActivityDateConverterBinding
import com.example.kpitrackerapp.utils.HijriCalendar
import java.text.SimpleDateFormat
import java.util.*

class DateConverterActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityDateConverterBinding
    private var selectedDay = 1
    private var selectedMonth = 1
    private var selectedYear = 2024
    private var isHijriToMiladi = false
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDateConverterBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupToolbar()
        setupViews()
        setupClickListeners()
        
        // Set current date as default
        val calendar = Calendar.getInstance()
        selectedDay = calendar.get(Calendar.DAY_OF_MONTH)
        selectedMonth = calendar.get(Calendar.MONTH) + 1
        selectedYear = calendar.get(Calendar.YEAR)
        updateDateDisplay()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        binding.toolbar.setNavigationOnClickListener {
            onBackPressed()
        }
    }
    
    private fun setupViews() {
        binding.conversionTypeGroup.setOnCheckedChangeListener { _, checkedId ->
            isHijriToMiladi = checkedId == R.id.radioHijriToMiladi
            updateDateDisplay()
            clearResults()
        }
    }
    
    private fun setupClickListeners() {
        binding.dayCard.setOnClickListener {
            showDayPicker()
        }
        
        binding.monthCard.setOnClickListener {
            showMonthPicker()
        }
        
        binding.yearCard.setOnClickListener {
            showYearPicker()
        }
        
        binding.convertButton.setOnClickListener {
            convertDate()
        }
    }
    
    private fun showDayPicker() {
        val maxDays = if (isHijriToMiladi) {
            HijriCalendar.getDaysInHijriMonth(selectedMonth, selectedYear)
        } else {
            getDaysInGregorianMonth(selectedMonth, selectedYear)
        }
        
        showNumberPicker("اختر اليوم", 1, maxDays, selectedDay) { day ->
            selectedDay = day
            updateDateDisplay()
        }
    }
    
    private fun showMonthPicker() {
        showNumberPicker("اختر الشهر", 1, 12, selectedMonth) { month ->
            selectedMonth = month
            // Adjust day if it exceeds the new month's days
            val maxDays = if (isHijriToMiladi) {
                HijriCalendar.getDaysInHijriMonth(selectedMonth, selectedYear)
            } else {
                getDaysInGregorianMonth(selectedMonth, selectedYear)
            }
            if (selectedDay > maxDays) {
                selectedDay = maxDays
            }
            updateDateDisplay()
        }
    }
    
    private fun showYearPicker() {
        val currentYear = Calendar.getInstance().get(Calendar.YEAR)
        val minYear = if (isHijriToMiladi) 1300 else 1900
        val maxYear = if (isHijriToMiladi) 1500 else currentYear + 10
        
        showNumberPicker("اختر السنة", minYear, maxYear, selectedYear) { year ->
            selectedYear = year
            // Adjust day if it exceeds the new month's days
            val maxDays = if (isHijriToMiladi) {
                HijriCalendar.getDaysInHijriMonth(selectedMonth, selectedYear)
            } else {
                getDaysInGregorianMonth(selectedMonth, selectedYear)
            }
            if (selectedDay > maxDays) {
                selectedDay = maxDays
            }
            updateDateDisplay()
        }
    }
    
    private fun showNumberPicker(title: String, min: Int, max: Int, current: Int, onSelected: (Int) -> Unit) {
        val numberPicker = NumberPicker(this).apply {
            minValue = min
            maxValue = max
            value = current
            wrapSelectorWheel = false
        }
        
        AlertDialog.Builder(this)
            .setTitle(title)
            .setView(numberPicker)
            .setPositiveButton("موافق") { _, _ ->
                onSelected(numberPicker.value)
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }
    
    private fun updateDateDisplay() {
        binding.dayText.text = selectedDay.toString()
        binding.monthText.text = selectedMonth.toString()
        binding.yearText.text = selectedYear.toString()
    }
    
    private fun convertDate() {
        try {
            if (isHijriToMiladi) {
                convertHijriToMiladi()
            } else {
                convertMiladiToHijri()
            }
        } catch (e: Exception) {
            // Handle conversion error
            clearResults()
        }
    }
    
    private fun convertMiladiToHijri() {
        val gregorianCalendar = Calendar.getInstance().apply {
            set(selectedYear, selectedMonth - 1, selectedDay)
        }
        
        val hijriDate = HijriCalendar.gregorianToHijri(gregorianCalendar)
        
        // Update results
        binding.resultMiladiDate.text = "$selectedDay/$selectedMonth/$selectedYear"
        binding.resultHijriDate.text = "${hijriDate.day}/${hijriDate.month}/${hijriDate.year}"
        binding.resultDayName.text = getDayName(gregorianCalendar.get(Calendar.DAY_OF_WEEK))
        binding.resultHijriMonth.text = getHijriMonthName(hijriDate.month)
        binding.resultMiladiMonth.text = getGregorianMonthName(selectedMonth)
        binding.resultSyrianiMonth.text = getSyrianiMonthName(selectedMonth)
        binding.resultHijriMonthDays.text = HijriCalendar.getDaysInHijriMonth(hijriDate.month, hijriDate.year).toString()
        binding.resultMiladiMonthDays.text = getDaysInGregorianMonth(selectedMonth, selectedYear).toString()
    }
    
    private fun convertHijriToMiladi() {
        val hijriDate = HijriCalendar.HijriDate(selectedYear, selectedMonth, selectedDay)
        val gregorianCalendar = HijriCalendar.hijriToGregorian(hijriDate)
        
        val gregorianDay = gregorianCalendar.get(Calendar.DAY_OF_MONTH)
        val gregorianMonth = gregorianCalendar.get(Calendar.MONTH) + 1
        val gregorianYear = gregorianCalendar.get(Calendar.YEAR)
        
        // Update results
        binding.resultMiladiDate.text = "$gregorianDay/$gregorianMonth/$gregorianYear"
        binding.resultHijriDate.text = "$selectedDay/$selectedMonth/$selectedYear"
        binding.resultDayName.text = getDayName(gregorianCalendar.get(Calendar.DAY_OF_WEEK))
        binding.resultHijriMonth.text = getHijriMonthName(selectedMonth)
        binding.resultMiladiMonth.text = getGregorianMonthName(gregorianMonth)
        binding.resultSyrianiMonth.text = getSyrianiMonthName(gregorianMonth)
        binding.resultHijriMonthDays.text = HijriCalendar.getDaysInHijriMonth(selectedMonth, selectedYear).toString()
        binding.resultMiladiMonthDays.text = getDaysInGregorianMonth(gregorianMonth, gregorianYear).toString()
    }
    
    private fun clearResults() {
        binding.resultMiladiDate.text = ""
        binding.resultHijriDate.text = ""
        binding.resultDayName.text = ""
        binding.resultHijriMonth.text = ""
        binding.resultMiladiMonth.text = ""
        binding.resultSyrianiMonth.text = ""
        binding.resultHijriMonthDays.text = ""
        binding.resultMiladiMonthDays.text = ""
    }
    
    private fun getDaysInGregorianMonth(month: Int, year: Int): Int {
        val calendar = Calendar.getInstance()
        calendar.set(year, month - 1, 1)
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
    }
    
    private fun getDayName(dayOfWeek: Int): String {
        return when (dayOfWeek) {
            Calendar.SUNDAY -> "الأحد"
            Calendar.MONDAY -> "الإثنين"
            Calendar.TUESDAY -> "الثلاثاء"
            Calendar.WEDNESDAY -> "الأربعاء"
            Calendar.THURSDAY -> "الخميس"
            Calendar.FRIDAY -> "الجمعة"
            Calendar.SATURDAY -> "السبت"
            else -> ""
        }
    }
    
    private fun getHijriMonthName(month: Int): String {
        return when (month) {
            1 -> "محرم"
            2 -> "صفر"
            3 -> "ربيع الأول"
            4 -> "ربيع الثاني"
            5 -> "جمادى الأولى"
            6 -> "جمادى الثانية"
            7 -> "رجب"
            8 -> "شعبان"
            9 -> "رمضان"
            10 -> "شوال"
            11 -> "ذو القعدة"
            12 -> "ذو الحجة"
            else -> ""
        }
    }
    
    private fun getGregorianMonthName(month: Int): String {
        return when (month) {
            1 -> "يناير"
            2 -> "فبراير"
            3 -> "مارس"
            4 -> "أبريل"
            5 -> "مايو"
            6 -> "يونيو"
            7 -> "يوليو"
            8 -> "أغسطس"
            9 -> "سبتمبر"
            10 -> "أكتوبر"
            11 -> "نوفمبر"
            12 -> "ديسمبر"
            else -> ""
        }
    }
    
    private fun getSyrianiMonthName(month: Int): String {
        return when (month) {
            1 -> "كانون الثاني"
            2 -> "شباط"
            3 -> "آذار"
            4 -> "نيسان"
            5 -> "أيار"
            6 -> "حزيران"
            7 -> "تموز"
            8 -> "آب"
            9 -> "أيلول"
            10 -> "تشرين الأول"
            11 -> "تشرين الثاني"
            12 -> "كانون الأول"
            else -> ""
        }
    }
}
