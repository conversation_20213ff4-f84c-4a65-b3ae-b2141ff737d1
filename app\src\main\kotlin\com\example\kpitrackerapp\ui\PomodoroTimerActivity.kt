package com.example.kpitrackerapp.ui

import android.content.Intent
import android.media.MediaPlayer
import android.os.Bundle
import android.os.CountDownTimer
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.example.kpitrackerapp.R
import com.example.kpitrackerapp.databinding.ActivityPomodoroTimerBinding
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import java.util.concurrent.TimeUnit

class PomodoroTimerActivity : AppCompatActivity() {

    private lateinit var binding: ActivityPomodoroTimerBinding
    private var countDownTimer: CountDownTimer? = null
    private var timeLeftInMillis: Long = 0
    private var totalTimeInMillis: Long = 0
    private var isTimerRunning = false
    private var isPaused = false
    
    private var taskId: Int = -1
    private var taskName: String = ""
    private var timerMinutes: Int = 25
    
    private var mediaPlayer: MediaPlayer? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPomodoroTimerBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Get data from intent
        taskId = intent.getIntExtra("TASK_ID", -1)
        taskName = intent.getStringExtra("TASK_NAME") ?: "مهمة غير محددة"
        timerMinutes = intent.getIntExtra("TIMER_MINUTES", 25)
        
        setupUI()
        setupTimer()
        setupClickListeners()
    }

    private fun setupUI() {
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "🍅 جلسة التركيز"
        
        binding.taskNameText.text = taskName
        binding.timerDurationText.text = "$timerMinutes دقيقة"
        
        totalTimeInMillis = timerMinutes * 60 * 1000L
        timeLeftInMillis = totalTimeInMillis
        
        updateTimerDisplay()
        updateProgressBar()
    }

    private fun setupTimer() {
        // Timer is not started automatically
        binding.startPauseButton.text = "▶️ بدء"
        binding.startPauseButton.setBackgroundColor(ContextCompat.getColor(this, R.color.green))
    }

    private fun setupClickListeners() {
        binding.startPauseButton.setOnClickListener {
            if (isTimerRunning) {
                pauseTimer()
            } else {
                startTimer()
            }
        }
        
        binding.stopButton.setOnClickListener {
            showStopConfirmationDialog()
        }
        
        binding.addTimeButton.setOnClickListener {
            addTime(5) // Add 5 minutes
        }
        
        binding.skipBreakButton.setOnClickListener {
            finishSession(false)
        }
    }

    private fun startTimer() {
        countDownTimer = object : CountDownTimer(timeLeftInMillis, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                timeLeftInMillis = millisUntilFinished
                updateTimerDisplay()
                updateProgressBar()
            }

            override fun onFinish() {
                onTimerFinished()
            }
        }.start()
        
        isTimerRunning = true
        isPaused = false
        
        binding.startPauseButton.text = "⏸️ إيقاف مؤقت"
        binding.startPauseButton.setBackgroundColor(ContextCompat.getColor(this, R.color.orange))
        binding.timerStatusText.text = "🔥 جلسة نشطة - ركز!"
        binding.timerStatusText.setTextColor(ContextCompat.getColor(this, R.color.green))
    }

    private fun pauseTimer() {
        countDownTimer?.cancel()
        isTimerRunning = false
        isPaused = true
        
        binding.startPauseButton.text = "▶️ متابعة"
        binding.startPauseButton.setBackgroundColor(ContextCompat.getColor(this, R.color.green))
        binding.timerStatusText.text = "⏸️ متوقف مؤقتاً"
        binding.timerStatusText.setTextColor(ContextCompat.getColor(this, R.color.orange))
    }

    private fun stopTimer() {
        countDownTimer?.cancel()
        isTimerRunning = false
        isPaused = false
        
        binding.startPauseButton.text = "▶️ بدء"
        binding.startPauseButton.setBackgroundColor(ContextCompat.getColor(this, R.color.green))
        binding.timerStatusText.text = "⏹️ تم الإيقاف"
        binding.timerStatusText.setTextColor(ContextCompat.getColor(this, R.color.red))
    }

    private fun onTimerFinished() {
        isTimerRunning = false
        playCompletionSound()
        showCompletionDialog()
    }

    private fun updateTimerDisplay() {
        val minutes = TimeUnit.MILLISECONDS.toMinutes(timeLeftInMillis)
        val seconds = TimeUnit.MILLISECONDS.toSeconds(timeLeftInMillis) % 60
        
        binding.timerDisplay.text = String.format("%02d:%02d", minutes, seconds)
    }

    private fun updateProgressBar() {
        val progress = ((totalTimeInMillis - timeLeftInMillis) * 100 / totalTimeInMillis).toInt()
        binding.progressBar.progress = progress
        binding.progressText.text = "$progress%"
    }

    private fun addTime(minutes: Int) {
        timeLeftInMillis += minutes * 60 * 1000L
        totalTimeInMillis += minutes * 60 * 1000L
        updateTimerDisplay()
        updateProgressBar()
        
        binding.timerStatusText.text = "⏰ تم إضافة $minutes دقائق"
        binding.timerStatusText.setTextColor(ContextCompat.getColor(this, R.color.blue))
    }

    private fun showStopConfirmationDialog() {
        MaterialAlertDialogBuilder(this)
            .setTitle("⏹️ إيقاف الجلسة")
            .setMessage("هل أنت متأكد من إيقاف جلسة التركيز؟")
            .setPositiveButton("نعم، أوقف") { _, _ ->
                stopTimer()
                finishSession(false)
            }
            .setNegativeButton("متابعة الجلسة", null)
            .show()
    }

    private fun showCompletionDialog() {
        MaterialAlertDialogBuilder(this)
            .setTitle("🎉 تهانينا!")
            .setMessage("لقد أكملت جلسة التركيز بنجاح!\n\n⏰ المدة: $timerMinutes دقيقة\n📋 المهمة: $taskName\n\nهل تريد أخذ استراحة قصيرة؟")
            .setPositiveButton("استراحة 5 دقائق") { _, _ ->
                startBreakTimer(5)
            }
            .setNeutralButton("استراحة 15 دقيقة") { _, _ ->
                startBreakTimer(15)
            }
            .setNegativeButton("إنهاء الجلسة") { _, _ ->
                finishSession(true)
            }
            .setCancelable(false)
            .show()
    }

    private fun startBreakTimer(breakMinutes: Int) {
        binding.taskNameText.text = "استراحة"
        binding.timerDurationText.text = "$breakMinutes دقيقة"
        binding.timerStatusText.text = "☕ وقت الاستراحة"
        binding.timerStatusText.setTextColor(ContextCompat.getColor(this, R.color.blue))
        
        // Change UI for break
        binding.addTimeButton.visibility = View.GONE
        binding.skipBreakButton.visibility = View.VISIBLE
        
        totalTimeInMillis = breakMinutes * 60 * 1000L
        timeLeftInMillis = totalTimeInMillis
        
        updateTimerDisplay()
        updateProgressBar()
        
        // Auto-start break timer
        startTimer()
    }

    private fun playCompletionSound() {
        try {
            mediaPlayer = MediaPlayer.create(this, R.raw.notification_sound)
            mediaPlayer?.start()
            mediaPlayer?.setOnCompletionListener {
                it.release()
                mediaPlayer = null
            }
        } catch (e: Exception) {
            // If sound file doesn't exist, just continue
        }
    }

    private fun finishSession(completed: Boolean) {
        val resultIntent = Intent().apply {
            putExtra("TASK_ID", taskId)
            putExtra("COMPLETED", completed)
            putExtra("DURATION_MINUTES", timerMinutes)
        }
        setResult(if (completed) RESULT_OK else RESULT_CANCELED, resultIntent)
        finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        countDownTimer?.cancel()
        mediaPlayer?.release()
    }

    override fun onSupportNavigateUp(): Boolean {
        if (isTimerRunning) {
            showStopConfirmationDialog()
        } else {
            finishSession(false)
        }
        return true
    }

    override fun onBackPressed() {
        if (isTimerRunning) {
            showStopConfirmationDialog()
        } else {
            super.onBackPressed()
        }
    }
}
