  Application android.app  Dialog android.app  ActivityAddEditKpiBinding android.app.Activity  !ActivityAddEditKpiOriginalBinding android.app.Activity  ActivityAdminDashboardBinding android.app.Activity  ActivityAutoSendSettingsBinding android.app.Activity  ActivityChatBinding android.app.Activity  ActivityChatListBinding android.app.Activity  ActivityCreateUserBinding android.app.Activity  ActivityExcelImportBinding android.app.Activity  ActivityExcelReviewBinding android.app.Activity  ActivityExpireManagementBinding android.app.Activity  ActivityKpiDetailBinding android.app.Activity  ActivityLoginBinding android.app.Activity  ActivityMainBinding android.app.Activity  ActivityModernReportBinding android.app.Activity  ActivityNotificationsBinding android.app.Activity  ActivityOcrBinding android.app.Activity  ActivityOcrReviewBinding android.app.Activity  ActivityReportBinding android.app.Activity  ActivityResultLauncher android.app.Activity  !ActivitySearchEditProgressBinding android.app.Activity  ActivityTaskManagementBinding android.app.Activity  #ActivityTaskReminderSettingsBinding android.app.Activity  ActivityTaskReportBinding android.app.Activity  ActivityUserKpiListBinding android.app.Activity  AdminDashboardAdapter android.app.Activity  Array android.app.Activity  ArrayAdapter android.app.Activity  	ArrayList android.app.Activity  Bitmap android.app.Activity  Boolean android.app.Activity  Calendar android.app.Activity  ChatMessageAdapter android.app.Activity  
ChatViewModel android.app.Activity  ConversationAdapter android.app.Activity  EnhancedTaskAdapter android.app.Activity  ExcelReviewAdapter android.app.Activity  Int android.app.Activity  Intent android.app.Activity  Kpi android.app.Activity  KpiListAdapter android.app.Activity  KpiProgressEntry android.app.Activity  
KpiReportData android.app.Activity  KpiViewModel android.app.Activity  List android.app.Activity  LocationManager android.app.Activity  NotificationAdapter android.app.Activity  
OcrResultItem android.app.Activity  OcrReviewAdapter android.app.Activity  RecentUsersAdapter android.app.Activity  SharedPreferences android.app.Activity  String android.app.Activity  Task android.app.Activity  TaskReportAdapter android.app.Activity  
TaskViewModel android.app.Activity  TextRecognizer android.app.Activity  Uri android.app.Activity  User android.app.Activity  UserFilterItem android.app.Activity  UserListAdapter android.app.Activity  UserRole android.app.Activity  UserSummaryItem android.app.Activity  ValueFormatter android.app.Activity  View android.app.Activity  com android.app.Activity  
Configuration android.app.Application  Boolean android.app.Dialog  Context android.app.Dialog  List android.app.Dialog  Unit android.app.Dialog  UserFilterAdapter android.app.Dialog  UserFilterDialogBinding android.app.Dialog  UserFilterItem android.app.Dialog  Context android.content  Intent android.content  SharedPreferences android.content  ActivityAddEditKpiBinding android.content.Context  !ActivityAddEditKpiOriginalBinding android.content.Context  ActivityAdminDashboardBinding android.content.Context  ActivityAutoSendSettingsBinding android.content.Context  ActivityChatBinding android.content.Context  ActivityChatListBinding android.content.Context  ActivityCreateUserBinding android.content.Context  ActivityExcelImportBinding android.content.Context  ActivityExcelReviewBinding android.content.Context  ActivityExpireManagementBinding android.content.Context  ActivityKpiDetailBinding android.content.Context  ActivityLoginBinding android.content.Context  ActivityMainBinding android.content.Context  ActivityModernReportBinding android.content.Context  ActivityNotificationsBinding android.content.Context  ActivityOcrBinding android.content.Context  ActivityOcrReviewBinding android.content.Context  ActivityReportBinding android.content.Context  ActivityResultLauncher android.content.Context  !ActivitySearchEditProgressBinding android.content.Context  ActivityTaskManagementBinding android.content.Context  #ActivityTaskReminderSettingsBinding android.content.Context  ActivityTaskReportBinding android.content.Context  ActivityUserKpiListBinding android.content.Context  AdminDashboardAdapter android.content.Context  Array android.content.Context  ArrayAdapter android.content.Context  	ArrayList android.content.Context  Bitmap android.content.Context  Boolean android.content.Context  Calendar android.content.Context  ChatMessageAdapter android.content.Context  
ChatViewModel android.content.Context  
Configuration android.content.Context  ConversationAdapter android.content.Context  EnhancedTaskAdapter android.content.Context  ExcelReviewAdapter android.content.Context  Int android.content.Context  Intent android.content.Context  Kpi android.content.Context  KpiListAdapter android.content.Context  KpiProgressEntry android.content.Context  
KpiReportData android.content.Context  KpiViewModel android.content.Context  List android.content.Context  LocationManager android.content.Context  NotificationAdapter android.content.Context  
OcrResultItem android.content.Context  OcrReviewAdapter android.content.Context  RecentUsersAdapter android.content.Context  SharedPreferences android.content.Context  String android.content.Context  Task android.content.Context  TaskReportAdapter android.content.Context  
TaskViewModel android.content.Context  TextRecognizer android.content.Context  Uri android.content.Context  User android.content.Context  UserFilterItem android.content.Context  UserListAdapter android.content.Context  UserRole android.content.Context  UserSummaryItem android.content.Context  ValueFormatter android.content.Context  View android.content.Context  com android.content.Context  ActivityAddEditKpiBinding android.content.ContextWrapper  !ActivityAddEditKpiOriginalBinding android.content.ContextWrapper  ActivityAdminDashboardBinding android.content.ContextWrapper  ActivityAutoSendSettingsBinding android.content.ContextWrapper  ActivityChatBinding android.content.ContextWrapper  ActivityChatListBinding android.content.ContextWrapper  ActivityCreateUserBinding android.content.ContextWrapper  ActivityExcelImportBinding android.content.ContextWrapper  ActivityExcelReviewBinding android.content.ContextWrapper  ActivityExpireManagementBinding android.content.ContextWrapper  ActivityKpiDetailBinding android.content.ContextWrapper  ActivityLoginBinding android.content.ContextWrapper  ActivityMainBinding android.content.ContextWrapper  ActivityModernReportBinding android.content.ContextWrapper  ActivityNotificationsBinding android.content.ContextWrapper  ActivityOcrBinding android.content.ContextWrapper  ActivityOcrReviewBinding android.content.ContextWrapper  ActivityReportBinding android.content.ContextWrapper  ActivityResultLauncher android.content.ContextWrapper  !ActivitySearchEditProgressBinding android.content.ContextWrapper  ActivityTaskManagementBinding android.content.ContextWrapper  #ActivityTaskReminderSettingsBinding android.content.ContextWrapper  ActivityTaskReportBinding android.content.ContextWrapper  ActivityUserKpiListBinding android.content.ContextWrapper  AdminDashboardAdapter android.content.ContextWrapper  Array android.content.ContextWrapper  ArrayAdapter android.content.ContextWrapper  	ArrayList android.content.ContextWrapper  Bitmap android.content.ContextWrapper  Boolean android.content.ContextWrapper  Calendar android.content.ContextWrapper  ChatMessageAdapter android.content.ContextWrapper  
ChatViewModel android.content.ContextWrapper  
Configuration android.content.ContextWrapper  ConversationAdapter android.content.ContextWrapper  EnhancedTaskAdapter android.content.ContextWrapper  ExcelReviewAdapter android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  Kpi android.content.ContextWrapper  KpiListAdapter android.content.ContextWrapper  KpiProgressEntry android.content.ContextWrapper  
KpiReportData android.content.ContextWrapper  KpiViewModel android.content.ContextWrapper  List android.content.ContextWrapper  LocationManager android.content.ContextWrapper  NotificationAdapter android.content.ContextWrapper  
OcrResultItem android.content.ContextWrapper  OcrReviewAdapter android.content.ContextWrapper  RecentUsersAdapter android.content.ContextWrapper  SharedPreferences android.content.ContextWrapper  String android.content.ContextWrapper  Task android.content.ContextWrapper  TaskReportAdapter android.content.ContextWrapper  
TaskViewModel android.content.ContextWrapper  TextRecognizer android.content.ContextWrapper  Uri android.content.ContextWrapper  User android.content.ContextWrapper  UserFilterItem android.content.ContextWrapper  UserListAdapter android.content.ContextWrapper  UserRole android.content.ContextWrapper  UserSummaryItem android.content.ContextWrapper  ValueFormatter android.content.ContextWrapper  View android.content.ContextWrapper  com android.content.ContextWrapper  Bitmap android.graphics  LocationManager android.location  Uri android.net  
Parcelable 
android.os  TextWatcher android.text  GestureDetector android.view  View android.view  ActivityAddEditKpiBinding  android.view.ContextThemeWrapper  !ActivityAddEditKpiOriginalBinding  android.view.ContextThemeWrapper  ActivityAdminDashboardBinding  android.view.ContextThemeWrapper  ActivityAutoSendSettingsBinding  android.view.ContextThemeWrapper  ActivityChatBinding  android.view.ContextThemeWrapper  ActivityChatListBinding  android.view.ContextThemeWrapper  ActivityCreateUserBinding  android.view.ContextThemeWrapper  ActivityExcelImportBinding  android.view.ContextThemeWrapper  ActivityExcelReviewBinding  android.view.ContextThemeWrapper  ActivityExpireManagementBinding  android.view.ContextThemeWrapper  ActivityKpiDetailBinding  android.view.ContextThemeWrapper  ActivityLoginBinding  android.view.ContextThemeWrapper  ActivityMainBinding  android.view.ContextThemeWrapper  ActivityModernReportBinding  android.view.ContextThemeWrapper  ActivityNotificationsBinding  android.view.ContextThemeWrapper  ActivityOcrBinding  android.view.ContextThemeWrapper  ActivityOcrReviewBinding  android.view.ContextThemeWrapper  ActivityReportBinding  android.view.ContextThemeWrapper  ActivityResultLauncher  android.view.ContextThemeWrapper  !ActivitySearchEditProgressBinding  android.view.ContextThemeWrapper  ActivityTaskManagementBinding  android.view.ContextThemeWrapper  #ActivityTaskReminderSettingsBinding  android.view.ContextThemeWrapper  ActivityTaskReportBinding  android.view.ContextThemeWrapper  ActivityUserKpiListBinding  android.view.ContextThemeWrapper  AdminDashboardAdapter  android.view.ContextThemeWrapper  Array  android.view.ContextThemeWrapper  ArrayAdapter  android.view.ContextThemeWrapper  	ArrayList  android.view.ContextThemeWrapper  Bitmap  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Calendar  android.view.ContextThemeWrapper  ChatMessageAdapter  android.view.ContextThemeWrapper  
ChatViewModel  android.view.ContextThemeWrapper  ConversationAdapter  android.view.ContextThemeWrapper  EnhancedTaskAdapter  android.view.ContextThemeWrapper  ExcelReviewAdapter  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  Kpi  android.view.ContextThemeWrapper  KpiListAdapter  android.view.ContextThemeWrapper  KpiProgressEntry  android.view.ContextThemeWrapper  
KpiReportData  android.view.ContextThemeWrapper  KpiViewModel  android.view.ContextThemeWrapper  List  android.view.ContextThemeWrapper  LocationManager  android.view.ContextThemeWrapper  NotificationAdapter  android.view.ContextThemeWrapper  
OcrResultItem  android.view.ContextThemeWrapper  OcrReviewAdapter  android.view.ContextThemeWrapper  RecentUsersAdapter  android.view.ContextThemeWrapper  SharedPreferences  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  Task  android.view.ContextThemeWrapper  TaskReportAdapter  android.view.ContextThemeWrapper  
TaskViewModel  android.view.ContextThemeWrapper  TextRecognizer  android.view.ContextThemeWrapper  Uri  android.view.ContextThemeWrapper  User  android.view.ContextThemeWrapper  UserFilterItem  android.view.ContextThemeWrapper  UserListAdapter  android.view.ContextThemeWrapper  UserRole  android.view.ContextThemeWrapper  UserSummaryItem  android.view.ContextThemeWrapper  ValueFormatter  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  com  android.view.ContextThemeWrapper  Context android.view.View  Int android.view.View  OnTouchListener android.view.View  TextView android.view.View  Context android.view.ViewGroup  Int android.view.ViewGroup  TextView android.view.ViewGroup  ArrayAdapter android.widget  Button android.widget  CheckBox android.widget  EditText android.widget  	ImageView android.widget  TextView android.widget  Context android.widget.RelativeLayout  Int android.widget.RelativeLayout  TextView android.widget.RelativeLayout  ActivityAddEditKpiBinding #androidx.activity.ComponentActivity  !ActivityAddEditKpiOriginalBinding #androidx.activity.ComponentActivity  ActivityAdminDashboardBinding #androidx.activity.ComponentActivity  ActivityAutoSendSettingsBinding #androidx.activity.ComponentActivity  ActivityChatBinding #androidx.activity.ComponentActivity  ActivityChatListBinding #androidx.activity.ComponentActivity  ActivityCreateUserBinding #androidx.activity.ComponentActivity  ActivityExcelImportBinding #androidx.activity.ComponentActivity  ActivityExcelReviewBinding #androidx.activity.ComponentActivity  ActivityExpireManagementBinding #androidx.activity.ComponentActivity  ActivityKpiDetailBinding #androidx.activity.ComponentActivity  ActivityLoginBinding #androidx.activity.ComponentActivity  ActivityMainBinding #androidx.activity.ComponentActivity  ActivityModernReportBinding #androidx.activity.ComponentActivity  ActivityNotificationsBinding #androidx.activity.ComponentActivity  ActivityOcrBinding #androidx.activity.ComponentActivity  ActivityOcrReviewBinding #androidx.activity.ComponentActivity  ActivityReportBinding #androidx.activity.ComponentActivity  ActivityResultLauncher #androidx.activity.ComponentActivity  !ActivitySearchEditProgressBinding #androidx.activity.ComponentActivity  ActivityTaskManagementBinding #androidx.activity.ComponentActivity  #ActivityTaskReminderSettingsBinding #androidx.activity.ComponentActivity  ActivityTaskReportBinding #androidx.activity.ComponentActivity  ActivityUserKpiListBinding #androidx.activity.ComponentActivity  AdminDashboardAdapter #androidx.activity.ComponentActivity  Array #androidx.activity.ComponentActivity  ArrayAdapter #androidx.activity.ComponentActivity  	ArrayList #androidx.activity.ComponentActivity  Bitmap #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Calendar #androidx.activity.ComponentActivity  ChatMessageAdapter #androidx.activity.ComponentActivity  
ChatViewModel #androidx.activity.ComponentActivity  ConversationAdapter #androidx.activity.ComponentActivity  EnhancedTaskAdapter #androidx.activity.ComponentActivity  ExcelReviewAdapter #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  Kpi #androidx.activity.ComponentActivity  KpiListAdapter #androidx.activity.ComponentActivity  KpiProgressEntry #androidx.activity.ComponentActivity  
KpiReportData #androidx.activity.ComponentActivity  KpiViewModel #androidx.activity.ComponentActivity  List #androidx.activity.ComponentActivity  LocationManager #androidx.activity.ComponentActivity  NotificationAdapter #androidx.activity.ComponentActivity  
OcrResultItem #androidx.activity.ComponentActivity  OcrReviewAdapter #androidx.activity.ComponentActivity  RecentUsersAdapter #androidx.activity.ComponentActivity  SharedPreferences #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  Task #androidx.activity.ComponentActivity  TaskReportAdapter #androidx.activity.ComponentActivity  
TaskViewModel #androidx.activity.ComponentActivity  TextRecognizer #androidx.activity.ComponentActivity  Uri #androidx.activity.ComponentActivity  User #androidx.activity.ComponentActivity  UserFilterItem #androidx.activity.ComponentActivity  UserListAdapter #androidx.activity.ComponentActivity  UserRole #androidx.activity.ComponentActivity  UserSummaryItem #androidx.activity.ComponentActivity  ValueFormatter #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  com #androidx.activity.ComponentActivity  ActivityResultLauncher androidx.activity.result  AppCompatActivity androidx.appcompat.app  ActivityAddEditKpiBinding (androidx.appcompat.app.AppCompatActivity  !ActivityAddEditKpiOriginalBinding (androidx.appcompat.app.AppCompatActivity  ActivityAdminDashboardBinding (androidx.appcompat.app.AppCompatActivity  ActivityAutoSendSettingsBinding (androidx.appcompat.app.AppCompatActivity  ActivityChatBinding (androidx.appcompat.app.AppCompatActivity  ActivityChatListBinding (androidx.appcompat.app.AppCompatActivity  ActivityCreateUserBinding (androidx.appcompat.app.AppCompatActivity  ActivityExcelImportBinding (androidx.appcompat.app.AppCompatActivity  ActivityExcelReviewBinding (androidx.appcompat.app.AppCompatActivity  ActivityExpireManagementBinding (androidx.appcompat.app.AppCompatActivity  ActivityKpiDetailBinding (androidx.appcompat.app.AppCompatActivity  ActivityLoginBinding (androidx.appcompat.app.AppCompatActivity  ActivityMainBinding (androidx.appcompat.app.AppCompatActivity  ActivityModernReportBinding (androidx.appcompat.app.AppCompatActivity  ActivityNotificationsBinding (androidx.appcompat.app.AppCompatActivity  ActivityOcrBinding (androidx.appcompat.app.AppCompatActivity  ActivityOcrReviewBinding (androidx.appcompat.app.AppCompatActivity  ActivityReportBinding (androidx.appcompat.app.AppCompatActivity  ActivityResultLauncher (androidx.appcompat.app.AppCompatActivity  !ActivitySearchEditProgressBinding (androidx.appcompat.app.AppCompatActivity  ActivityTaskManagementBinding (androidx.appcompat.app.AppCompatActivity  #ActivityTaskReminderSettingsBinding (androidx.appcompat.app.AppCompatActivity  ActivityTaskReportBinding (androidx.appcompat.app.AppCompatActivity  ActivityUserKpiListBinding (androidx.appcompat.app.AppCompatActivity  AdminDashboardAdapter (androidx.appcompat.app.AppCompatActivity  Array (androidx.appcompat.app.AppCompatActivity  ArrayAdapter (androidx.appcompat.app.AppCompatActivity  	ArrayList (androidx.appcompat.app.AppCompatActivity  Bitmap (androidx.appcompat.app.AppCompatActivity  Boolean (androidx.appcompat.app.AppCompatActivity  Calendar (androidx.appcompat.app.AppCompatActivity  ChatMessageAdapter (androidx.appcompat.app.AppCompatActivity  
ChatViewModel (androidx.appcompat.app.AppCompatActivity  ConversationAdapter (androidx.appcompat.app.AppCompatActivity  EnhancedTaskAdapter (androidx.appcompat.app.AppCompatActivity  ExcelReviewAdapter (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  Kpi (androidx.appcompat.app.AppCompatActivity  KpiListAdapter (androidx.appcompat.app.AppCompatActivity  KpiProgressEntry (androidx.appcompat.app.AppCompatActivity  
KpiReportData (androidx.appcompat.app.AppCompatActivity  KpiViewModel (androidx.appcompat.app.AppCompatActivity  List (androidx.appcompat.app.AppCompatActivity  LocationManager (androidx.appcompat.app.AppCompatActivity  NotificationAdapter (androidx.appcompat.app.AppCompatActivity  
OcrResultItem (androidx.appcompat.app.AppCompatActivity  OcrReviewAdapter (androidx.appcompat.app.AppCompatActivity  RecentUsersAdapter (androidx.appcompat.app.AppCompatActivity  SharedPreferences (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  Task (androidx.appcompat.app.AppCompatActivity  TaskReportAdapter (androidx.appcompat.app.AppCompatActivity  
TaskViewModel (androidx.appcompat.app.AppCompatActivity  TextRecognizer (androidx.appcompat.app.AppCompatActivity  Uri (androidx.appcompat.app.AppCompatActivity  User (androidx.appcompat.app.AppCompatActivity  UserFilterItem (androidx.appcompat.app.AppCompatActivity  UserListAdapter (androidx.appcompat.app.AppCompatActivity  UserRole (androidx.appcompat.app.AppCompatActivity  UserSummaryItem (androidx.appcompat.app.AppCompatActivity  ValueFormatter (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  com (androidx.appcompat.app.AppCompatActivity  ActivityAddEditKpiBinding #androidx.core.app.ComponentActivity  !ActivityAddEditKpiOriginalBinding #androidx.core.app.ComponentActivity  ActivityAdminDashboardBinding #androidx.core.app.ComponentActivity  ActivityAutoSendSettingsBinding #androidx.core.app.ComponentActivity  ActivityChatBinding #androidx.core.app.ComponentActivity  ActivityChatListBinding #androidx.core.app.ComponentActivity  ActivityCreateUserBinding #androidx.core.app.ComponentActivity  ActivityExcelImportBinding #androidx.core.app.ComponentActivity  ActivityExcelReviewBinding #androidx.core.app.ComponentActivity  ActivityExpireManagementBinding #androidx.core.app.ComponentActivity  ActivityKpiDetailBinding #androidx.core.app.ComponentActivity  ActivityLoginBinding #androidx.core.app.ComponentActivity  ActivityMainBinding #androidx.core.app.ComponentActivity  ActivityModernReportBinding #androidx.core.app.ComponentActivity  ActivityNotificationsBinding #androidx.core.app.ComponentActivity  ActivityOcrBinding #androidx.core.app.ComponentActivity  ActivityOcrReviewBinding #androidx.core.app.ComponentActivity  ActivityReportBinding #androidx.core.app.ComponentActivity  ActivityResultLauncher #androidx.core.app.ComponentActivity  !ActivitySearchEditProgressBinding #androidx.core.app.ComponentActivity  ActivityTaskManagementBinding #androidx.core.app.ComponentActivity  #ActivityTaskReminderSettingsBinding #androidx.core.app.ComponentActivity  ActivityTaskReportBinding #androidx.core.app.ComponentActivity  ActivityUserKpiListBinding #androidx.core.app.ComponentActivity  AdminDashboardAdapter #androidx.core.app.ComponentActivity  Array #androidx.core.app.ComponentActivity  ArrayAdapter #androidx.core.app.ComponentActivity  	ArrayList #androidx.core.app.ComponentActivity  Bitmap #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Calendar #androidx.core.app.ComponentActivity  ChatMessageAdapter #androidx.core.app.ComponentActivity  
ChatViewModel #androidx.core.app.ComponentActivity  ConversationAdapter #androidx.core.app.ComponentActivity  EnhancedTaskAdapter #androidx.core.app.ComponentActivity  ExcelReviewAdapter #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  Kpi #androidx.core.app.ComponentActivity  KpiListAdapter #androidx.core.app.ComponentActivity  KpiProgressEntry #androidx.core.app.ComponentActivity  
KpiReportData #androidx.core.app.ComponentActivity  KpiViewModel #androidx.core.app.ComponentActivity  List #androidx.core.app.ComponentActivity  LocationManager #androidx.core.app.ComponentActivity  NotificationAdapter #androidx.core.app.ComponentActivity  
OcrResultItem #androidx.core.app.ComponentActivity  OcrReviewAdapter #androidx.core.app.ComponentActivity  RecentUsersAdapter #androidx.core.app.ComponentActivity  SharedPreferences #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  Task #androidx.core.app.ComponentActivity  TaskReportAdapter #androidx.core.app.ComponentActivity  
TaskViewModel #androidx.core.app.ComponentActivity  TextRecognizer #androidx.core.app.ComponentActivity  Uri #androidx.core.app.ComponentActivity  User #androidx.core.app.ComponentActivity  UserFilterItem #androidx.core.app.ComponentActivity  UserListAdapter #androidx.core.app.ComponentActivity  UserRole #androidx.core.app.ComponentActivity  UserSummaryItem #androidx.core.app.ComponentActivity  ValueFormatter #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  com #androidx.core.app.ComponentActivity  DialogFragment androidx.fragment.app  Fragment androidx.fragment.app  Button $androidx.fragment.app.DialogFragment  EditText $androidx.fragment.app.DialogFragment  KpiProgressEntry $androidx.fragment.app.DialogFragment  KpiViewModel $androidx.fragment.app.DialogFragment  String $androidx.fragment.app.DialogFragment  TextView $androidx.fragment.app.DialogFragment  AdminDashboardAdapter androidx.fragment.app.Fragment  Button androidx.fragment.app.Fragment  CardAnimationHelper androidx.fragment.app.Fragment  DragDropHelper androidx.fragment.app.Fragment  EditText androidx.fragment.app.Fragment  FragmentAccountBinding androidx.fragment.app.Fragment  FragmentDashboardBinding androidx.fragment.app.Fragment  FragmentMainDashboardBinding androidx.fragment.app.Fragment  FragmentMessagesBinding androidx.fragment.app.Fragment  FragmentPerformanceBinding androidx.fragment.app.Fragment  KpiProgressEntry androidx.fragment.app.Fragment  KpiViewModel androidx.fragment.app.Fragment  String androidx.fragment.app.Fragment  TextView androidx.fragment.app.Fragment  UserSummaryAdapter androidx.fragment.app.Fragment  ActivityAddEditKpiBinding &androidx.fragment.app.FragmentActivity  !ActivityAddEditKpiOriginalBinding &androidx.fragment.app.FragmentActivity  ActivityAdminDashboardBinding &androidx.fragment.app.FragmentActivity  ActivityAutoSendSettingsBinding &androidx.fragment.app.FragmentActivity  ActivityChatBinding &androidx.fragment.app.FragmentActivity  ActivityChatListBinding &androidx.fragment.app.FragmentActivity  ActivityCreateUserBinding &androidx.fragment.app.FragmentActivity  ActivityExcelImportBinding &androidx.fragment.app.FragmentActivity  ActivityExcelReviewBinding &androidx.fragment.app.FragmentActivity  ActivityExpireManagementBinding &androidx.fragment.app.FragmentActivity  ActivityKpiDetailBinding &androidx.fragment.app.FragmentActivity  ActivityLoginBinding &androidx.fragment.app.FragmentActivity  ActivityMainBinding &androidx.fragment.app.FragmentActivity  ActivityModernReportBinding &androidx.fragment.app.FragmentActivity  ActivityNotificationsBinding &androidx.fragment.app.FragmentActivity  ActivityOcrBinding &androidx.fragment.app.FragmentActivity  ActivityOcrReviewBinding &androidx.fragment.app.FragmentActivity  ActivityReportBinding &androidx.fragment.app.FragmentActivity  ActivityResultLauncher &androidx.fragment.app.FragmentActivity  !ActivitySearchEditProgressBinding &androidx.fragment.app.FragmentActivity  ActivityTaskManagementBinding &androidx.fragment.app.FragmentActivity  #ActivityTaskReminderSettingsBinding &androidx.fragment.app.FragmentActivity  ActivityTaskReportBinding &androidx.fragment.app.FragmentActivity  ActivityUserKpiListBinding &androidx.fragment.app.FragmentActivity  AdminDashboardAdapter &androidx.fragment.app.FragmentActivity  Array &androidx.fragment.app.FragmentActivity  ArrayAdapter &androidx.fragment.app.FragmentActivity  	ArrayList &androidx.fragment.app.FragmentActivity  Bitmap &androidx.fragment.app.FragmentActivity  Boolean &androidx.fragment.app.FragmentActivity  Calendar &androidx.fragment.app.FragmentActivity  ChatMessageAdapter &androidx.fragment.app.FragmentActivity  
ChatViewModel &androidx.fragment.app.FragmentActivity  ConversationAdapter &androidx.fragment.app.FragmentActivity  EnhancedTaskAdapter &androidx.fragment.app.FragmentActivity  ExcelReviewAdapter &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  Kpi &androidx.fragment.app.FragmentActivity  KpiListAdapter &androidx.fragment.app.FragmentActivity  KpiProgressEntry &androidx.fragment.app.FragmentActivity  
KpiReportData &androidx.fragment.app.FragmentActivity  KpiViewModel &androidx.fragment.app.FragmentActivity  List &androidx.fragment.app.FragmentActivity  LocationManager &androidx.fragment.app.FragmentActivity  NotificationAdapter &androidx.fragment.app.FragmentActivity  
OcrResultItem &androidx.fragment.app.FragmentActivity  OcrReviewAdapter &androidx.fragment.app.FragmentActivity  RecentUsersAdapter &androidx.fragment.app.FragmentActivity  SharedPreferences &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  Task &androidx.fragment.app.FragmentActivity  TaskReportAdapter &androidx.fragment.app.FragmentActivity  
TaskViewModel &androidx.fragment.app.FragmentActivity  TextRecognizer &androidx.fragment.app.FragmentActivity  Uri &androidx.fragment.app.FragmentActivity  User &androidx.fragment.app.FragmentActivity  UserFilterItem &androidx.fragment.app.FragmentActivity  UserListAdapter &androidx.fragment.app.FragmentActivity  UserRole &androidx.fragment.app.FragmentActivity  UserSummaryItem &androidx.fragment.app.FragmentActivity  ValueFormatter &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  com &androidx.fragment.app.FragmentActivity  AndroidViewModel androidx.lifecycle  Calendar androidx.lifecycle  LiveData androidx.lifecycle  Pair androidx.lifecycle  	ViewModel androidx.lifecycle  Application #androidx.lifecycle.AndroidViewModel  Boolean #androidx.lifecycle.AndroidViewModel  ChatMessageItem #androidx.lifecycle.AndroidViewModel  ConversationItem #androidx.lifecycle.AndroidViewModel  Int #androidx.lifecycle.AndroidViewModel  List #androidx.lifecycle.AndroidViewModel  LiveData #androidx.lifecycle.AndroidViewModel  
QuickStats #androidx.lifecycle.AndroidViewModel  String #androidx.lifecycle.AndroidViewModel  
SubtaskDao #androidx.lifecycle.AndroidViewModel  Task #androidx.lifecycle.AndroidViewModel  
TaskAnalytics #androidx.lifecycle.AndroidViewModel  TaskCategory #androidx.lifecycle.AndroidViewModel  TaskCategoryDao #androidx.lifecycle.AndroidViewModel  TaskDao #androidx.lifecycle.AndroidViewModel  TaskPriority #androidx.lifecycle.AndroidViewModel  TaskQuadrant #androidx.lifecycle.AndroidViewModel  User #androidx.lifecycle.AndroidViewModel  Application androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  Calendar androidx.lifecycle.ViewModel  ChatMessageItem androidx.lifecycle.ViewModel  Context androidx.lifecycle.ViewModel  ConversationItem androidx.lifecycle.ViewModel  Flow androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  Kpi androidx.lifecycle.ViewModel  KpiDao androidx.lifecycle.ViewModel  KpiProgressEntry androidx.lifecycle.ViewModel  KpiProgressEntryDao androidx.lifecycle.ViewModel  
KpiReportData androidx.lifecycle.ViewModel  KpiSummaryDetail androidx.lifecycle.ViewModel  KpiWithProgress androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  LiveData androidx.lifecycle.ViewModel  Long androidx.lifecycle.ViewModel  Map androidx.lifecycle.ViewModel  MasterCardData androidx.lifecycle.ViewModel  
OcrResultItem androidx.lifecycle.ViewModel  OptIn androidx.lifecycle.ViewModel  Pair androidx.lifecycle.ViewModel  
QuickStats androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  
SubtaskDao androidx.lifecycle.ViewModel  Task androidx.lifecycle.ViewModel  
TaskAnalytics androidx.lifecycle.ViewModel  TaskCategory androidx.lifecycle.ViewModel  TaskCategoryDao androidx.lifecycle.ViewModel  TaskDao androidx.lifecycle.ViewModel  TaskPriority androidx.lifecycle.ViewModel  TaskQuadrant androidx.lifecycle.ViewModel  User androidx.lifecycle.ViewModel  UserDao androidx.lifecycle.ViewModel  UserKpiAssignmentDao androidx.lifecycle.ViewModel  UserSummaryItem androidx.lifecycle.ViewModel  ItemTouchHelper androidx.recyclerview.widget  ListAdapter androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  SimpleCallback ,androidx.recyclerview.widget.ItemTouchHelper  Int 5androidx.recyclerview.widget.ItemTouchHelper.Callback  RecyclerView 5androidx.recyclerview.widget.ItemTouchHelper.Callback  Unit 5androidx.recyclerview.widget.ItemTouchHelper.Callback  Int ;androidx.recyclerview.widget.ItemTouchHelper.SimpleCallback  RecyclerView ;androidx.recyclerview.widget.ItemTouchHelper.SimpleCallback  Unit ;androidx.recyclerview.widget.ItemTouchHelper.SimpleCallback  	ImageView (androidx.recyclerview.widget.ListAdapter  Int (androidx.recyclerview.widget.ListAdapter  KpiProgressEntry (androidx.recyclerview.widget.ListAdapter  List (androidx.recyclerview.widget.ListAdapter  Map (androidx.recyclerview.widget.ListAdapter  MaterialCardView (androidx.recyclerview.widget.ListAdapter  OnTaskActionsListener (androidx.recyclerview.widget.ListAdapter  OnUserSummaryActionsListener (androidx.recyclerview.widget.ListAdapter  RecyclerView (androidx.recyclerview.widget.ListAdapter  String (androidx.recyclerview.widget.ListAdapter  TextView (androidx.recyclerview.widget.ListAdapter  Unit (androidx.recyclerview.widget.ListAdapter  View (androidx.recyclerview.widget.ListAdapter  androidx (androidx.recyclerview.widget.ListAdapter  Adapter )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  AdminDashboardItem 1androidx.recyclerview.widget.RecyclerView.Adapter  CheckBox 1androidx.recyclerview.widget.RecyclerView.Adapter  	ImageView 1androidx.recyclerview.widget.RecyclerView.Adapter  Int 1androidx.recyclerview.widget.RecyclerView.Adapter  KpiProgressEntry 1androidx.recyclerview.widget.RecyclerView.Adapter  List 1androidx.recyclerview.widget.RecyclerView.Adapter  Map 1androidx.recyclerview.widget.RecyclerView.Adapter  MaterialCardView 1androidx.recyclerview.widget.RecyclerView.Adapter  MutableList 1androidx.recyclerview.widget.RecyclerView.Adapter  
OcrResultItem 1androidx.recyclerview.widget.RecyclerView.Adapter  OcrReviewItemBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  OnTaskActionsListener 1androidx.recyclerview.widget.RecyclerView.Adapter  OnUserSummaryActionsListener 1androidx.recyclerview.widget.RecyclerView.Adapter  RecyclerView 1androidx.recyclerview.widget.RecyclerView.Adapter  String 1androidx.recyclerview.widget.RecyclerView.Adapter  TextView 1androidx.recyclerview.widget.RecyclerView.Adapter  TextWatcher 1androidx.recyclerview.widget.RecyclerView.Adapter  Unit 1androidx.recyclerview.widget.RecyclerView.Adapter  View 1androidx.recyclerview.widget.RecyclerView.Adapter  androidx 1androidx.recyclerview.widget.RecyclerView.Adapter  CheckBox 4androidx.recyclerview.widget.RecyclerView.ViewHolder  	ImageView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  MaterialCardView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  OcrReviewItemBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  TextView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  TextWatcher 4androidx.recyclerview.widget.RecyclerView.ViewHolder  View 4androidx.recyclerview.widget.RecyclerView.ViewHolder  
ColumnInfo 
androidx.room  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  
ForeignKey 
androidx.room  Index 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  RoomDatabase 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  CASCADE androidx.room.ForeignKey  CASCADE "androidx.room.ForeignKey.Companion  invoke "androidx.room.ForeignKey.Companion  IGNORE  androidx.room.OnConflictStrategy  REPLACE  androidx.room.OnConflictStrategy  IGNORE *androidx.room.OnConflictStrategy.Companion  REPLACE *androidx.room.OnConflictStrategy.Companion  AppDatabase androidx.room.RoomDatabase  ChatDao androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  KpiDao androidx.room.RoomDatabase  KpiProgressEntryDao androidx.room.RoomDatabase  	Migration androidx.room.RoomDatabase  
SubtaskDao androidx.room.RoomDatabase  SupportSQLiteDatabase androidx.room.RoomDatabase  TaskCategoryDao androidx.room.RoomDatabase  TaskDao androidx.room.RoomDatabase  UserDao androidx.room.RoomDatabase  UserKpiAssignmentDao androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  	Migration androidx.room.migration  SupportSQLiteDatabase !androidx.room.migration.Migration  SupportSQLiteDatabase androidx.sqlite.db  execSQL (androidx.sqlite.db.SupportSQLiteDatabase  
Configuration 
androidx.work  CoroutineWorker 
androidx.work  WorkerParameters 
androidx.work  Provider androidx.work.Configuration  Context androidx.work.CoroutineWorker  WorkerParameters androidx.work.CoroutineWorker  Context androidx.work.ListenableWorker  WorkerParameters androidx.work.ListenableWorker  ActivityAdminDashboardBinding com.example.kpitrackerapp  ActivityMainBinding com.example.kpitrackerapp  String com.example.kpitrackerapp  databinding com.example.kpitrackerapp  ActivityAdminDashboardBinding 0com.example.kpitrackerapp.AdminDashboardActivity  AdminDashboardAdapter 0com.example.kpitrackerapp.AdminDashboardActivity  
Configuration /com.example.kpitrackerapp.KpiTrackerApplication  
Configuration 9com.example.kpitrackerapp.KpiTrackerApplication.Companion  ActivityMainBinding &com.example.kpitrackerapp.MainActivity  ActivityResultLauncher &com.example.kpitrackerapp.MainActivity  Calendar &com.example.kpitrackerapp.MainActivity  KpiViewModel &com.example.kpitrackerapp.MainActivity  SharedPreferences &com.example.kpitrackerapp.MainActivity  String &com.example.kpitrackerapp.MainActivity  UserSummaryItem &com.example.kpitrackerapp.MainActivity  View &com.example.kpitrackerapp.MainActivity  AdminDashboardAdapter "com.example.kpitrackerapp.adapters  ChatMessageAdapter "com.example.kpitrackerapp.adapters  ConversationAdapter "com.example.kpitrackerapp.adapters  NotificationAdapter "com.example.kpitrackerapp.adapters  Unit "com.example.kpitrackerapp.adapters  UserFilterAdapter "com.example.kpitrackerapp.adapters  UserListAdapter "com.example.kpitrackerapp.adapters  AdminDashboardItem 8com.example.kpitrackerapp.adapters.AdminDashboardAdapter  MaterialCardView 8com.example.kpitrackerapp.adapters.AdminDashboardAdapter  RecyclerView 8com.example.kpitrackerapp.adapters.AdminDashboardAdapter  TextView 8com.example.kpitrackerapp.adapters.AdminDashboardAdapter  Unit 8com.example.kpitrackerapp.adapters.AdminDashboardAdapter  View 8com.example.kpitrackerapp.adapters.AdminDashboardAdapter  MaterialCardView Mcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.ActionCardViewHolder  TextView Mcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.ActionCardViewHolder  View Mcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.ActionCardViewHolder  MaterialCardView Ocom.example.kpitrackerapp.adapters.AdminDashboardAdapter.ActivityCardViewHolder  TextView Ocom.example.kpitrackerapp.adapters.AdminDashboardAdapter.ActivityCardViewHolder  View Ocom.example.kpitrackerapp.adapters.AdminDashboardAdapter.ActivityCardViewHolder  AdminDashboardItem Bcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.Companion  MaterialCardView Bcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.Companion  RecyclerView Bcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.Companion  TextView Bcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.Companion  Unit Bcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.Companion  View Bcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.Companion  TextView Icom.example.kpitrackerapp.adapters.AdminDashboardAdapter.HeaderViewHolder  View Icom.example.kpitrackerapp.adapters.AdminDashboardAdapter.HeaderViewHolder  MaterialCardView Kcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.StatCardViewHolder  TextView Kcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.StatCardViewHolder  View Kcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.StatCardViewHolder  MaterialCardView Kcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.UserCardViewHolder  TextView Kcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.UserCardViewHolder  View Kcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.UserCardViewHolder  MaterialCardView 5com.example.kpitrackerapp.adapters.ChatMessageAdapter  RecyclerView 5com.example.kpitrackerapp.adapters.ChatMessageAdapter  TextView 5com.example.kpitrackerapp.adapters.ChatMessageAdapter  View 5com.example.kpitrackerapp.adapters.ChatMessageAdapter  MaterialCardView ?com.example.kpitrackerapp.adapters.ChatMessageAdapter.Companion  RecyclerView ?com.example.kpitrackerapp.adapters.ChatMessageAdapter.Companion  TextView ?com.example.kpitrackerapp.adapters.ChatMessageAdapter.Companion  View ?com.example.kpitrackerapp.adapters.ChatMessageAdapter.Companion  TextView Jcom.example.kpitrackerapp.adapters.ChatMessageAdapter.DateHeaderViewHolder  View Jcom.example.kpitrackerapp.adapters.ChatMessageAdapter.DateHeaderViewHolder  MaterialCardView Ocom.example.kpitrackerapp.adapters.ChatMessageAdapter.ReceivedMessageViewHolder  TextView Ocom.example.kpitrackerapp.adapters.ChatMessageAdapter.ReceivedMessageViewHolder  View Ocom.example.kpitrackerapp.adapters.ChatMessageAdapter.ReceivedMessageViewHolder  MaterialCardView Kcom.example.kpitrackerapp.adapters.ChatMessageAdapter.SentMessageViewHolder  TextView Kcom.example.kpitrackerapp.adapters.ChatMessageAdapter.SentMessageViewHolder  View Kcom.example.kpitrackerapp.adapters.ChatMessageAdapter.SentMessageViewHolder  TextView Mcom.example.kpitrackerapp.adapters.ChatMessageAdapter.SystemMessageViewHolder  View Mcom.example.kpitrackerapp.adapters.ChatMessageAdapter.SystemMessageViewHolder  ConversationViewHolder 6com.example.kpitrackerapp.adapters.ConversationAdapter  	ImageView 6com.example.kpitrackerapp.adapters.ConversationAdapter  MaterialCardView 6com.example.kpitrackerapp.adapters.ConversationAdapter  RecyclerView 6com.example.kpitrackerapp.adapters.ConversationAdapter  TextView 6com.example.kpitrackerapp.adapters.ConversationAdapter  View 6com.example.kpitrackerapp.adapters.ConversationAdapter  	ImageView Mcom.example.kpitrackerapp.adapters.ConversationAdapter.ConversationViewHolder  MaterialCardView Mcom.example.kpitrackerapp.adapters.ConversationAdapter.ConversationViewHolder  TextView Mcom.example.kpitrackerapp.adapters.ConversationAdapter.ConversationViewHolder  View Mcom.example.kpitrackerapp.adapters.ConversationAdapter.ConversationViewHolder  NotificationViewHolder 6com.example.kpitrackerapp.adapters.NotificationAdapter  RecyclerView 6com.example.kpitrackerapp.adapters.NotificationAdapter  TextView 6com.example.kpitrackerapp.adapters.NotificationAdapter  View 6com.example.kpitrackerapp.adapters.NotificationAdapter  TextView Mcom.example.kpitrackerapp.adapters.NotificationAdapter.NotificationViewHolder  View Mcom.example.kpitrackerapp.adapters.NotificationAdapter.NotificationViewHolder  CheckBox 4com.example.kpitrackerapp.adapters.UserFilterAdapter  RecyclerView 4com.example.kpitrackerapp.adapters.UserFilterAdapter  UserViewHolder 4com.example.kpitrackerapp.adapters.UserFilterAdapter  View 4com.example.kpitrackerapp.adapters.UserFilterAdapter  CheckBox Ccom.example.kpitrackerapp.adapters.UserFilterAdapter.UserViewHolder  View Ccom.example.kpitrackerapp.adapters.UserFilterAdapter.UserViewHolder  	ImageView 2com.example.kpitrackerapp.adapters.UserListAdapter  MaterialCardView 2com.example.kpitrackerapp.adapters.UserListAdapter  RecyclerView 2com.example.kpitrackerapp.adapters.UserListAdapter  TextView 2com.example.kpitrackerapp.adapters.UserListAdapter  UserViewHolder 2com.example.kpitrackerapp.adapters.UserListAdapter  View 2com.example.kpitrackerapp.adapters.UserListAdapter  	ImageView Acom.example.kpitrackerapp.adapters.UserListAdapter.UserViewHolder  MaterialCardView Acom.example.kpitrackerapp.adapters.UserListAdapter.UserViewHolder  TextView Acom.example.kpitrackerapp.adapters.UserListAdapter.UserViewHolder  View Acom.example.kpitrackerapp.adapters.UserListAdapter.UserViewHolder  FragmentAccountBinding #com.example.kpitrackerapp.fragments  FragmentDashboardBinding #com.example.kpitrackerapp.fragments  FragmentMainDashboardBinding #com.example.kpitrackerapp.fragments  FragmentMessagesBinding #com.example.kpitrackerapp.fragments  FragmentPerformanceBinding #com.example.kpitrackerapp.fragments  FragmentAccountBinding 3com.example.kpitrackerapp.fragments.AccountFragment  AdminDashboardAdapter 5com.example.kpitrackerapp.fragments.DashboardFragment  FragmentDashboardBinding 5com.example.kpitrackerapp.fragments.DashboardFragment  CardAnimationHelper 9com.example.kpitrackerapp.fragments.MainDashboardFragment  DragDropHelper 9com.example.kpitrackerapp.fragments.MainDashboardFragment  FragmentMainDashboardBinding 9com.example.kpitrackerapp.fragments.MainDashboardFragment  KpiViewModel 9com.example.kpitrackerapp.fragments.MainDashboardFragment  UserSummaryAdapter 9com.example.kpitrackerapp.fragments.MainDashboardFragment  FragmentMessagesBinding 4com.example.kpitrackerapp.fragments.MessagesFragment  FragmentPerformanceBinding 7com.example.kpitrackerapp.fragments.PerformanceFragment  ActivityAdminDashboardBinding  com.example.kpitrackerapp.models  AdminDashboardItem  com.example.kpitrackerapp.models  AttachmentType  com.example.kpitrackerapp.models  Boolean  com.example.kpitrackerapp.models  ChatMessage  com.example.kpitrackerapp.models  ChatMessageItem  com.example.kpitrackerapp.models  Conversation  com.example.kpitrackerapp.models  ConversationItem  com.example.kpitrackerapp.models  Double  com.example.kpitrackerapp.models  EnergyLevel  com.example.kpitrackerapp.models  Int  com.example.kpitrackerapp.models  Kpi  com.example.kpitrackerapp.models  KpiProgressEntry  com.example.kpitrackerapp.models  KpiUnit  com.example.kpitrackerapp.models  Long  com.example.kpitrackerapp.models  MessageType  com.example.kpitrackerapp.models  
OcrResultItem  com.example.kpitrackerapp.models  
QuickStats  com.example.kpitrackerapp.models  String  com.example.kpitrackerapp.models  Subtask  com.example.kpitrackerapp.models  Task  com.example.kpitrackerapp.models  
TaskAnalytics  com.example.kpitrackerapp.models  TaskCategory  com.example.kpitrackerapp.models  TaskImportance  com.example.kpitrackerapp.models  TaskPriority  com.example.kpitrackerapp.models  TaskQuadrant  com.example.kpitrackerapp.models  User  com.example.kpitrackerapp.models  UserFilterItem  com.example.kpitrackerapp.models  UserKpiAssignment  com.example.kpitrackerapp.models  UserRole  com.example.kpitrackerapp.models  AttachmentType ,com.example.kpitrackerapp.models.ChatMessage  Boolean ,com.example.kpitrackerapp.models.ChatMessage  Long ,com.example.kpitrackerapp.models.ChatMessage  MessageType ,com.example.kpitrackerapp.models.ChatMessage  
PrimaryKey ,com.example.kpitrackerapp.models.ChatMessage  String ,com.example.kpitrackerapp.models.ChatMessage  Boolean -com.example.kpitrackerapp.models.Conversation  Int -com.example.kpitrackerapp.models.Conversation  Long -com.example.kpitrackerapp.models.Conversation  
PrimaryKey -com.example.kpitrackerapp.models.Conversation  String -com.example.kpitrackerapp.models.Conversation  
ColumnInfo $com.example.kpitrackerapp.models.Kpi  Date $com.example.kpitrackerapp.models.Kpi  Double $com.example.kpitrackerapp.models.Kpi  KpiUnit $com.example.kpitrackerapp.models.Kpi  
PrimaryKey $com.example.kpitrackerapp.models.Kpi  String $com.example.kpitrackerapp.models.Kpi  
ColumnInfo 1com.example.kpitrackerapp.models.KpiProgressEntry  Date 1com.example.kpitrackerapp.models.KpiProgressEntry  Double 1com.example.kpitrackerapp.models.KpiProgressEntry  
PrimaryKey 1com.example.kpitrackerapp.models.KpiProgressEntry  String 1com.example.kpitrackerapp.models.KpiProgressEntry  Boolean .com.example.kpitrackerapp.models.OcrResultItem  Date .com.example.kpitrackerapp.models.OcrResultItem  Double .com.example.kpitrackerapp.models.OcrResultItem  Long .com.example.kpitrackerapp.models.OcrResultItem  String .com.example.kpitrackerapp.models.OcrResultItem  Int .com.example.kpitrackerapp.models.SmartListItem  SerializedName .com.example.kpitrackerapp.models.SmartListItem  String .com.example.kpitrackerapp.models.SmartListItem  Boolean (com.example.kpitrackerapp.models.Subtask  
ColumnInfo (com.example.kpitrackerapp.models.Subtask  Date (com.example.kpitrackerapp.models.Subtask  Int (com.example.kpitrackerapp.models.Subtask  
PrimaryKey (com.example.kpitrackerapp.models.Subtask  String (com.example.kpitrackerapp.models.Subtask  Boolean %com.example.kpitrackerapp.models.Task  
ColumnInfo %com.example.kpitrackerapp.models.Task  Date %com.example.kpitrackerapp.models.Task  Double %com.example.kpitrackerapp.models.Task  EnergyLevel %com.example.kpitrackerapp.models.Task  Int %com.example.kpitrackerapp.models.Task  
PrimaryKey %com.example.kpitrackerapp.models.Task  String %com.example.kpitrackerapp.models.Task  TaskImportance %com.example.kpitrackerapp.models.Task  TaskPriority %com.example.kpitrackerapp.models.Task  Boolean -com.example.kpitrackerapp.models.TaskCategory  
ColumnInfo -com.example.kpitrackerapp.models.TaskCategory  Date -com.example.kpitrackerapp.models.TaskCategory  
PrimaryKey -com.example.kpitrackerapp.models.TaskCategory  String -com.example.kpitrackerapp.models.TaskCategory  Boolean %com.example.kpitrackerapp.models.User  Int %com.example.kpitrackerapp.models.User  Long %com.example.kpitrackerapp.models.User  
PrimaryKey %com.example.kpitrackerapp.models.User  String %com.example.kpitrackerapp.models.User  UserRole %com.example.kpitrackerapp.models.User  Boolean /com.example.kpitrackerapp.models.UserFilterItem  String /com.example.kpitrackerapp.models.UserFilterItem  
ColumnInfo 2com.example.kpitrackerapp.models.UserKpiAssignment  Int 2com.example.kpitrackerapp.models.UserKpiAssignment  
PrimaryKey 2com.example.kpitrackerapp.models.UserKpiAssignment  String 2com.example.kpitrackerapp.models.UserKpiAssignment  AppDatabase %com.example.kpitrackerapp.persistence  Boolean %com.example.kpitrackerapp.persistence  
CategoryCount %com.example.kpitrackerapp.persistence  ChatDao %com.example.kpitrackerapp.persistence  ChatMessage %com.example.kpitrackerapp.persistence  Conversation %com.example.kpitrackerapp.persistence  ConversationWithDetails %com.example.kpitrackerapp.persistence  
Converters %com.example.kpitrackerapp.persistence  Dao %com.example.kpitrackerapp.persistence  Delete %com.example.kpitrackerapp.persistence  Double %com.example.kpitrackerapp.persistence  Insert %com.example.kpitrackerapp.persistence  Int %com.example.kpitrackerapp.persistence  Kpi %com.example.kpitrackerapp.persistence  KpiDao %com.example.kpitrackerapp.persistence  KpiProgressEntry %com.example.kpitrackerapp.persistence  KpiProgressEntryDao %com.example.kpitrackerapp.persistence  List %com.example.kpitrackerapp.persistence  Long %com.example.kpitrackerapp.persistence  OnConflictStrategy %com.example.kpitrackerapp.persistence  
PriorityCount %com.example.kpitrackerapp.persistence  Query %com.example.kpitrackerapp.persistence  String %com.example.kpitrackerapp.persistence  Subtask %com.example.kpitrackerapp.persistence  
SubtaskDao %com.example.kpitrackerapp.persistence  Task %com.example.kpitrackerapp.persistence  TaskCategory %com.example.kpitrackerapp.persistence  TaskCategoryDao %com.example.kpitrackerapp.persistence  TaskDao %com.example.kpitrackerapp.persistence  Update %com.example.kpitrackerapp.persistence  User %com.example.kpitrackerapp.persistence  UserDao %com.example.kpitrackerapp.persistence  UserKpiAssignment %com.example.kpitrackerapp.persistence  UserKpiAssignmentDao %com.example.kpitrackerapp.persistence  Volatile %com.example.kpitrackerapp.persistence  androidx %com.example.kpitrackerapp.persistence  AppDatabase 1com.example.kpitrackerapp.persistence.AppDatabase  ChatDao 1com.example.kpitrackerapp.persistence.AppDatabase  Context 1com.example.kpitrackerapp.persistence.AppDatabase  KpiDao 1com.example.kpitrackerapp.persistence.AppDatabase  KpiProgressEntryDao 1com.example.kpitrackerapp.persistence.AppDatabase  	Migration 1com.example.kpitrackerapp.persistence.AppDatabase  
SubtaskDao 1com.example.kpitrackerapp.persistence.AppDatabase  SupportSQLiteDatabase 1com.example.kpitrackerapp.persistence.AppDatabase  TaskCategoryDao 1com.example.kpitrackerapp.persistence.AppDatabase  TaskDao 1com.example.kpitrackerapp.persistence.AppDatabase  UserDao 1com.example.kpitrackerapp.persistence.AppDatabase  UserKpiAssignmentDao 1com.example.kpitrackerapp.persistence.AppDatabase  Volatile 1com.example.kpitrackerapp.persistence.AppDatabase  AppDatabase ;com.example.kpitrackerapp.persistence.AppDatabase.Companion  ChatDao ;com.example.kpitrackerapp.persistence.AppDatabase.Companion  Context ;com.example.kpitrackerapp.persistence.AppDatabase.Companion  KpiDao ;com.example.kpitrackerapp.persistence.AppDatabase.Companion  KpiProgressEntryDao ;com.example.kpitrackerapp.persistence.AppDatabase.Companion  	Migration ;com.example.kpitrackerapp.persistence.AppDatabase.Companion  
SubtaskDao ;com.example.kpitrackerapp.persistence.AppDatabase.Companion  SupportSQLiteDatabase ;com.example.kpitrackerapp.persistence.AppDatabase.Companion  TaskCategoryDao ;com.example.kpitrackerapp.persistence.AppDatabase.Companion  TaskDao ;com.example.kpitrackerapp.persistence.AppDatabase.Companion  UserDao ;com.example.kpitrackerapp.persistence.AppDatabase.Companion  UserKpiAssignmentDao ;com.example.kpitrackerapp.persistence.AppDatabase.Companion  Volatile ;com.example.kpitrackerapp.persistence.AppDatabase.Companion  Int 3com.example.kpitrackerapp.persistence.CategoryCount  String 3com.example.kpitrackerapp.persistence.CategoryCount  Boolean -com.example.kpitrackerapp.persistence.ChatDao  ChatMessage -com.example.kpitrackerapp.persistence.ChatDao  Conversation -com.example.kpitrackerapp.persistence.ChatDao  ConversationWithDetails -com.example.kpitrackerapp.persistence.ChatDao  Delete -com.example.kpitrackerapp.persistence.ChatDao  Flow -com.example.kpitrackerapp.persistence.ChatDao  Insert -com.example.kpitrackerapp.persistence.ChatDao  Int -com.example.kpitrackerapp.persistence.ChatDao  List -com.example.kpitrackerapp.persistence.ChatDao  Long -com.example.kpitrackerapp.persistence.ChatDao  OnConflictStrategy -com.example.kpitrackerapp.persistence.ChatDao  Query -com.example.kpitrackerapp.persistence.ChatDao  String -com.example.kpitrackerapp.persistence.ChatDao  Update -com.example.kpitrackerapp.persistence.ChatDao  User -com.example.kpitrackerapp.persistence.ChatDao  Boolean =com.example.kpitrackerapp.persistence.ConversationWithDetails  Int =com.example.kpitrackerapp.persistence.ConversationWithDetails  Long =com.example.kpitrackerapp.persistence.ConversationWithDetails  String =com.example.kpitrackerapp.persistence.ConversationWithDetails  Date 0com.example.kpitrackerapp.persistence.Converters  KpiUnit 0com.example.kpitrackerapp.persistence.Converters  Long 0com.example.kpitrackerapp.persistence.Converters  String 0com.example.kpitrackerapp.persistence.Converters  
TypeConverter 0com.example.kpitrackerapp.persistence.Converters  Delete ,com.example.kpitrackerapp.persistence.KpiDao  Flow ,com.example.kpitrackerapp.persistence.KpiDao  Insert ,com.example.kpitrackerapp.persistence.KpiDao  Kpi ,com.example.kpitrackerapp.persistence.KpiDao  List ,com.example.kpitrackerapp.persistence.KpiDao  OnConflictStrategy ,com.example.kpitrackerapp.persistence.KpiDao  Query ,com.example.kpitrackerapp.persistence.KpiDao  String ,com.example.kpitrackerapp.persistence.KpiDao  Update ,com.example.kpitrackerapp.persistence.KpiDao  Double 9com.example.kpitrackerapp.persistence.KpiProgressEntryDao  Flow 9com.example.kpitrackerapp.persistence.KpiProgressEntryDao  Insert 9com.example.kpitrackerapp.persistence.KpiProgressEntryDao  Int 9com.example.kpitrackerapp.persistence.KpiProgressEntryDao  KpiProgressEntry 9com.example.kpitrackerapp.persistence.KpiProgressEntryDao  List 9com.example.kpitrackerapp.persistence.KpiProgressEntryDao  Long 9com.example.kpitrackerapp.persistence.KpiProgressEntryDao  OnConflictStrategy 9com.example.kpitrackerapp.persistence.KpiProgressEntryDao  Query 9com.example.kpitrackerapp.persistence.KpiProgressEntryDao  String 9com.example.kpitrackerapp.persistence.KpiProgressEntryDao  androidx 9com.example.kpitrackerapp.persistence.KpiProgressEntryDao  Int 3com.example.kpitrackerapp.persistence.PriorityCount  String 3com.example.kpitrackerapp.persistence.PriorityCount  Boolean 0com.example.kpitrackerapp.persistence.SubtaskDao  Date 0com.example.kpitrackerapp.persistence.SubtaskDao  Delete 0com.example.kpitrackerapp.persistence.SubtaskDao  Flow 0com.example.kpitrackerapp.persistence.SubtaskDao  Insert 0com.example.kpitrackerapp.persistence.SubtaskDao  Int 0com.example.kpitrackerapp.persistence.SubtaskDao  List 0com.example.kpitrackerapp.persistence.SubtaskDao  Long 0com.example.kpitrackerapp.persistence.SubtaskDao  OnConflictStrategy 0com.example.kpitrackerapp.persistence.SubtaskDao  Query 0com.example.kpitrackerapp.persistence.SubtaskDao  String 0com.example.kpitrackerapp.persistence.SubtaskDao  Subtask 0com.example.kpitrackerapp.persistence.SubtaskDao  Update 0com.example.kpitrackerapp.persistence.SubtaskDao  Delete 5com.example.kpitrackerapp.persistence.TaskCategoryDao  Flow 5com.example.kpitrackerapp.persistence.TaskCategoryDao  Insert 5com.example.kpitrackerapp.persistence.TaskCategoryDao  Int 5com.example.kpitrackerapp.persistence.TaskCategoryDao  List 5com.example.kpitrackerapp.persistence.TaskCategoryDao  Long 5com.example.kpitrackerapp.persistence.TaskCategoryDao  OnConflictStrategy 5com.example.kpitrackerapp.persistence.TaskCategoryDao  Query 5com.example.kpitrackerapp.persistence.TaskCategoryDao  String 5com.example.kpitrackerapp.persistence.TaskCategoryDao  TaskCategory 5com.example.kpitrackerapp.persistence.TaskCategoryDao  Update 5com.example.kpitrackerapp.persistence.TaskCategoryDao  Boolean -com.example.kpitrackerapp.persistence.TaskDao  
CategoryCount -com.example.kpitrackerapp.persistence.TaskDao  Date -com.example.kpitrackerapp.persistence.TaskDao  Delete -com.example.kpitrackerapp.persistence.TaskDao  Double -com.example.kpitrackerapp.persistence.TaskDao  EnergyLevel -com.example.kpitrackerapp.persistence.TaskDao  Flow -com.example.kpitrackerapp.persistence.TaskDao  Insert -com.example.kpitrackerapp.persistence.TaskDao  Int -com.example.kpitrackerapp.persistence.TaskDao  List -com.example.kpitrackerapp.persistence.TaskDao  Long -com.example.kpitrackerapp.persistence.TaskDao  OnConflictStrategy -com.example.kpitrackerapp.persistence.TaskDao  
PriorityCount -com.example.kpitrackerapp.persistence.TaskDao  Query -com.example.kpitrackerapp.persistence.TaskDao  String -com.example.kpitrackerapp.persistence.TaskDao  Task -com.example.kpitrackerapp.persistence.TaskDao  TaskPriority -com.example.kpitrackerapp.persistence.TaskDao  Update -com.example.kpitrackerapp.persistence.TaskDao  Flow -com.example.kpitrackerapp.persistence.UserDao  Insert -com.example.kpitrackerapp.persistence.UserDao  Int -com.example.kpitrackerapp.persistence.UserDao  List -com.example.kpitrackerapp.persistence.UserDao  Long -com.example.kpitrackerapp.persistence.UserDao  OnConflictStrategy -com.example.kpitrackerapp.persistence.UserDao  Query -com.example.kpitrackerapp.persistence.UserDao  String -com.example.kpitrackerapp.persistence.UserDao  Update -com.example.kpitrackerapp.persistence.UserDao  User -com.example.kpitrackerapp.persistence.UserDao  Flow :com.example.kpitrackerapp.persistence.UserKpiAssignmentDao  Insert :com.example.kpitrackerapp.persistence.UserKpiAssignmentDao  List :com.example.kpitrackerapp.persistence.UserKpiAssignmentDao  OnConflictStrategy :com.example.kpitrackerapp.persistence.UserKpiAssignmentDao  Query :com.example.kpitrackerapp.persistence.UserKpiAssignmentDao  String :com.example.kpitrackerapp.persistence.UserKpiAssignmentDao  UserKpiAssignment :com.example.kpitrackerapp.persistence.UserKpiAssignmentDao  ActivityAddEditKpiBinding com.example.kpitrackerapp.ui  !ActivityAddEditKpiOriginalBinding com.example.kpitrackerapp.ui  ActivityAutoSendSettingsBinding com.example.kpitrackerapp.ui  ActivityChatBinding com.example.kpitrackerapp.ui  ActivityChatListBinding com.example.kpitrackerapp.ui  ActivityCreateUserBinding com.example.kpitrackerapp.ui  ActivityExcelImportBinding com.example.kpitrackerapp.ui  ActivityExcelReviewBinding com.example.kpitrackerapp.ui  ActivityExpireManagementBinding com.example.kpitrackerapp.ui  ActivityKpiDetailBinding com.example.kpitrackerapp.ui  ActivityLoginBinding com.example.kpitrackerapp.ui  ActivityModernReportBinding com.example.kpitrackerapp.ui  ActivityNotificationsBinding com.example.kpitrackerapp.ui  ActivityOcrBinding com.example.kpitrackerapp.ui  ActivityOcrReviewBinding com.example.kpitrackerapp.ui  ActivityReportBinding com.example.kpitrackerapp.ui  !ActivitySearchEditProgressBinding com.example.kpitrackerapp.ui  ActivityTaskManagementBinding com.example.kpitrackerapp.ui  #ActivityTaskReminderSettingsBinding com.example.kpitrackerapp.ui  ActivityTaskReportBinding com.example.kpitrackerapp.ui  ActivityUserKpiListBinding com.example.kpitrackerapp.ui  Array com.example.kpitrackerapp.ui  Boolean com.example.kpitrackerapp.ui  ColoredReportAdapter com.example.kpitrackerapp.ui  CompactReportAdapter com.example.kpitrackerapp.ui  EnhancedTaskActionsListener com.example.kpitrackerapp.ui  EnhancedTaskAdapter com.example.kpitrackerapp.ui  ExcelReviewAdapter com.example.kpitrackerapp.ui  Int com.example.kpitrackerapp.ui  KpiListAdapter com.example.kpitrackerapp.ui  KpiProgressEntryAdapter com.example.kpitrackerapp.ui  
KpiReportData com.example.kpitrackerapp.ui  KpiSummaryDetail com.example.kpitrackerapp.ui  List com.example.kpitrackerapp.ui  Map com.example.kpitrackerapp.ui  MutableList com.example.kpitrackerapp.ui  OcrReviewAdapter com.example.kpitrackerapp.ui  OcrReviewItemBinding com.example.kpitrackerapp.ui  OnKpiActionsListener com.example.kpitrackerapp.ui  OnTaskActionsListener com.example.kpitrackerapp.ui  OnUserSummaryActionsListener com.example.kpitrackerapp.ui  RecentUsersAdapter com.example.kpitrackerapp.ui  String com.example.kpitrackerapp.ui  TaskAdapter com.example.kpitrackerapp.ui  TaskReportAdapter com.example.kpitrackerapp.ui  UnifiedReportAdapter com.example.kpitrackerapp.ui  UnifiedReportRow com.example.kpitrackerapp.ui  Unit com.example.kpitrackerapp.ui  UserFilterDialogBinding com.example.kpitrackerapp.ui  UserSummaryAdapter com.example.kpitrackerapp.ui  UserSummaryItem com.example.kpitrackerapp.ui  androidx com.example.kpitrackerapp.ui  com com.example.kpitrackerapp.ui  ActivityAddEditKpiBinding 8com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity  Int 8com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity  LocationManager 8com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity  String 8com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity  
TaskViewModel 8com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity  TextRecognizer 8com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity  Uri 8com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity  ActivityAddEditKpiBinding Bcom.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity.Companion  Int Bcom.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity.Companion  LocationManager Bcom.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity.Companion  String Bcom.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity.Companion  
TaskViewModel Bcom.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity.Companion  TextRecognizer Bcom.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity.Companion  Uri Bcom.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity.Companion  ActivityAddEditKpiBinding /com.example.kpitrackerapp.ui.AddEditKpiActivity  Int /com.example.kpitrackerapp.ui.AddEditKpiActivity  
TaskViewModel /com.example.kpitrackerapp.ui.AddEditKpiActivity  ActivityAddEditKpiBinding 9com.example.kpitrackerapp.ui.AddEditKpiActivity.Companion  Int 9com.example.kpitrackerapp.ui.AddEditKpiActivity.Companion  
TaskViewModel 9com.example.kpitrackerapp.ui.AddEditKpiActivity.Companion  !ActivityAddEditKpiOriginalBinding 7com.example.kpitrackerapp.ui.AddEditKpiOriginalActivity  ActivityResultLauncher 7com.example.kpitrackerapp.ui.AddEditKpiOriginalActivity  Kpi 7com.example.kpitrackerapp.ui.AddEditKpiOriginalActivity  KpiViewModel 7com.example.kpitrackerapp.ui.AddEditKpiOriginalActivity  List 7com.example.kpitrackerapp.ui.AddEditKpiOriginalActivity  String 7com.example.kpitrackerapp.ui.AddEditKpiOriginalActivity  !ActivityAddEditKpiOriginalBinding Acom.example.kpitrackerapp.ui.AddEditKpiOriginalActivity.Companion  ActivityResultLauncher Acom.example.kpitrackerapp.ui.AddEditKpiOriginalActivity.Companion  Kpi Acom.example.kpitrackerapp.ui.AddEditKpiOriginalActivity.Companion  KpiViewModel Acom.example.kpitrackerapp.ui.AddEditKpiOriginalActivity.Companion  List Acom.example.kpitrackerapp.ui.AddEditKpiOriginalActivity.Companion  String Acom.example.kpitrackerapp.ui.AddEditKpiOriginalActivity.Companion  Button :com.example.kpitrackerapp.ui.AddEditProgressDialogFragment  EditText :com.example.kpitrackerapp.ui.AddEditProgressDialogFragment  KpiProgressEntry :com.example.kpitrackerapp.ui.AddEditProgressDialogFragment  KpiViewModel :com.example.kpitrackerapp.ui.AddEditProgressDialogFragment  String :com.example.kpitrackerapp.ui.AddEditProgressDialogFragment  TextView :com.example.kpitrackerapp.ui.AddEditProgressDialogFragment  Button Dcom.example.kpitrackerapp.ui.AddEditProgressDialogFragment.Companion  EditText Dcom.example.kpitrackerapp.ui.AddEditProgressDialogFragment.Companion  KpiProgressEntry Dcom.example.kpitrackerapp.ui.AddEditProgressDialogFragment.Companion  KpiViewModel Dcom.example.kpitrackerapp.ui.AddEditProgressDialogFragment.Companion  String Dcom.example.kpitrackerapp.ui.AddEditProgressDialogFragment.Companion  TextView Dcom.example.kpitrackerapp.ui.AddEditProgressDialogFragment.Companion  ActivityAutoSendSettingsBinding 5com.example.kpitrackerapp.ui.AutoSendSettingsActivity  com 5com.example.kpitrackerapp.ui.AutoSendSettingsActivity  Context ,com.example.kpitrackerapp.ui.ChartMarkerView  Int ,com.example.kpitrackerapp.ui.ChartMarkerView  TextView ,com.example.kpitrackerapp.ui.ChartMarkerView  ActivityChatBinding )com.example.kpitrackerapp.ui.ChatActivity  ChatMessageAdapter )com.example.kpitrackerapp.ui.ChatActivity  
ChatViewModel )com.example.kpitrackerapp.ui.ChatActivity  String )com.example.kpitrackerapp.ui.ChatActivity  User )com.example.kpitrackerapp.ui.ChatActivity  ActivityChatListBinding -com.example.kpitrackerapp.ui.ChatListActivity  
ChatViewModel -com.example.kpitrackerapp.ui.ChatListActivity  ConversationAdapter -com.example.kpitrackerapp.ui.ChatListActivity  UserListAdapter -com.example.kpitrackerapp.ui.ChatListActivity  Int 1com.example.kpitrackerapp.ui.ColoredReportAdapter  List 1com.example.kpitrackerapp.ui.ColoredReportAdapter  ReportViewHolder 1com.example.kpitrackerapp.ui.ColoredReportAdapter  CompactReportViewHolder 1com.example.kpitrackerapp.ui.CompactReportAdapter  Int 1com.example.kpitrackerapp.ui.CompactReportAdapter  List 1com.example.kpitrackerapp.ui.CompactReportAdapter  Map 1com.example.kpitrackerapp.ui.CompactReportAdapter  String 1com.example.kpitrackerapp.ui.CompactReportAdapter  ActivityCreateUserBinding /com.example.kpitrackerapp.ui.CreateUserActivity  KpiViewModel /com.example.kpitrackerapp.ui.CreateUserActivity  String /com.example.kpitrackerapp.ui.CreateUserActivity  Uri /com.example.kpitrackerapp.ui.CreateUserActivity  ActivityExcelImportBinding 0com.example.kpitrackerapp.ui.ExcelImportActivity  ActivityResultLauncher 0com.example.kpitrackerapp.ui.ExcelImportActivity  Int 0com.example.kpitrackerapp.ui.ExcelImportActivity  Intent 0com.example.kpitrackerapp.ui.ExcelImportActivity  KpiViewModel 0com.example.kpitrackerapp.ui.ExcelImportActivity  String 0com.example.kpitrackerapp.ui.ExcelImportActivity  ActivityExcelImportBinding :com.example.kpitrackerapp.ui.ExcelImportActivity.Companion  ActivityResultLauncher :com.example.kpitrackerapp.ui.ExcelImportActivity.Companion  Int :com.example.kpitrackerapp.ui.ExcelImportActivity.Companion  Intent :com.example.kpitrackerapp.ui.ExcelImportActivity.Companion  KpiViewModel :com.example.kpitrackerapp.ui.ExcelImportActivity.Companion  String :com.example.kpitrackerapp.ui.ExcelImportActivity.Companion  ActivityExcelReviewBinding 0com.example.kpitrackerapp.ui.ExcelReviewActivity  Boolean 0com.example.kpitrackerapp.ui.ExcelReviewActivity  ExcelReviewAdapter 0com.example.kpitrackerapp.ui.ExcelReviewActivity  KpiViewModel 0com.example.kpitrackerapp.ui.ExcelReviewActivity  List 0com.example.kpitrackerapp.ui.ExcelReviewActivity  
OcrResultItem 0com.example.kpitrackerapp.ui.ExcelReviewActivity  String 0com.example.kpitrackerapp.ui.ExcelReviewActivity  ActivityExcelReviewBinding :com.example.kpitrackerapp.ui.ExcelReviewActivity.Companion  Boolean :com.example.kpitrackerapp.ui.ExcelReviewActivity.Companion  ExcelReviewAdapter :com.example.kpitrackerapp.ui.ExcelReviewActivity.Companion  KpiViewModel :com.example.kpitrackerapp.ui.ExcelReviewActivity.Companion  List :com.example.kpitrackerapp.ui.ExcelReviewActivity.Companion  
OcrResultItem :com.example.kpitrackerapp.ui.ExcelReviewActivity.Companion  String :com.example.kpitrackerapp.ui.ExcelReviewActivity.Companion  ExcelReviewViewHolder /com.example.kpitrackerapp.ui.ExcelReviewAdapter  ActivityExpireManagementBinding 5com.example.kpitrackerapp.ui.ExpireManagementActivity  ActivityKpiDetailBinding .com.example.kpitrackerapp.ui.KpiDetailActivity  Boolean .com.example.kpitrackerapp.ui.KpiDetailActivity  Calendar .com.example.kpitrackerapp.ui.KpiDetailActivity  Kpi .com.example.kpitrackerapp.ui.KpiDetailActivity  KpiProgressEntry .com.example.kpitrackerapp.ui.KpiDetailActivity  KpiViewModel .com.example.kpitrackerapp.ui.KpiDetailActivity  List .com.example.kpitrackerapp.ui.KpiDetailActivity  String .com.example.kpitrackerapp.ui.KpiDetailActivity  ValueFormatter .com.example.kpitrackerapp.ui.KpiDetailActivity  ActivityKpiDetailBinding 8com.example.kpitrackerapp.ui.KpiDetailActivity.Companion  Boolean 8com.example.kpitrackerapp.ui.KpiDetailActivity.Companion  Calendar 8com.example.kpitrackerapp.ui.KpiDetailActivity.Companion  Kpi 8com.example.kpitrackerapp.ui.KpiDetailActivity.Companion  KpiProgressEntry 8com.example.kpitrackerapp.ui.KpiDetailActivity.Companion  KpiViewModel 8com.example.kpitrackerapp.ui.KpiDetailActivity.Companion  List 8com.example.kpitrackerapp.ui.KpiDetailActivity.Companion  String 8com.example.kpitrackerapp.ui.KpiDetailActivity.Companion  ValueFormatter 8com.example.kpitrackerapp.ui.KpiDetailActivity.Companion  KpiProgressEntry 4com.example.kpitrackerapp.ui.KpiProgressEntryAdapter  ProgressEntryViewHolder 4com.example.kpitrackerapp.ui.KpiProgressEntryAdapter  Unit 4com.example.kpitrackerapp.ui.KpiProgressEntryAdapter  ActivityLoginBinding *com.example.kpitrackerapp.ui.LoginActivity  KpiViewModel *com.example.kpitrackerapp.ui.LoginActivity  List *com.example.kpitrackerapp.ui.LoginActivity  RecentUsersAdapter *com.example.kpitrackerapp.ui.LoginActivity  User *com.example.kpitrackerapp.ui.LoginActivity  UserRole *com.example.kpitrackerapp.ui.LoginActivity  ActivityModernReportBinding 1com.example.kpitrackerapp.ui.ModernReportActivity  ArrayAdapter 1com.example.kpitrackerapp.ui.ModernReportActivity  Bitmap 1com.example.kpitrackerapp.ui.ModernReportActivity  ChartPeriod 1com.example.kpitrackerapp.ui.ModernReportActivity  Kpi 1com.example.kpitrackerapp.ui.ModernReportActivity  
KpiReportData 1com.example.kpitrackerapp.ui.ModernReportActivity  KpiViewModel 1com.example.kpitrackerapp.ui.ModernReportActivity  List 1com.example.kpitrackerapp.ui.ModernReportActivity  String 1com.example.kpitrackerapp.ui.ModernReportActivity  UserFilterItem 1com.example.kpitrackerapp.ui.ModernReportActivity  ActivityModernReportBinding ;com.example.kpitrackerapp.ui.ModernReportActivity.Companion  ArrayAdapter ;com.example.kpitrackerapp.ui.ModernReportActivity.Companion  Bitmap ;com.example.kpitrackerapp.ui.ModernReportActivity.Companion  Kpi ;com.example.kpitrackerapp.ui.ModernReportActivity.Companion  
KpiReportData ;com.example.kpitrackerapp.ui.ModernReportActivity.Companion  KpiViewModel ;com.example.kpitrackerapp.ui.ModernReportActivity.Companion  List ;com.example.kpitrackerapp.ui.ModernReportActivity.Companion  String ;com.example.kpitrackerapp.ui.ModernReportActivity.Companion  UserFilterItem ;com.example.kpitrackerapp.ui.ModernReportActivity.Companion  ActivityNotificationsBinding 2com.example.kpitrackerapp.ui.NotificationsActivity  NotificationAdapter 2com.example.kpitrackerapp.ui.NotificationsActivity  ActivityOcrBinding (com.example.kpitrackerapp.ui.OcrActivity  ActivityResultLauncher (com.example.kpitrackerapp.ui.OcrActivity  Intent (com.example.kpitrackerapp.ui.OcrActivity  KpiViewModel (com.example.kpitrackerapp.ui.OcrActivity  String (com.example.kpitrackerapp.ui.OcrActivity  Uri (com.example.kpitrackerapp.ui.OcrActivity  ActivityOcrBinding 2com.example.kpitrackerapp.ui.OcrActivity.Companion  ActivityResultLauncher 2com.example.kpitrackerapp.ui.OcrActivity.Companion  Intent 2com.example.kpitrackerapp.ui.OcrActivity.Companion  KpiViewModel 2com.example.kpitrackerapp.ui.OcrActivity.Companion  String 2com.example.kpitrackerapp.ui.OcrActivity.Companion  Uri 2com.example.kpitrackerapp.ui.OcrActivity.Companion  ActivityOcrReviewBinding .com.example.kpitrackerapp.ui.OcrReviewActivity  	ArrayList .com.example.kpitrackerapp.ui.OcrReviewActivity  
OcrResultItem .com.example.kpitrackerapp.ui.OcrReviewActivity  OcrReviewAdapter .com.example.kpitrackerapp.ui.OcrReviewActivity  ActivityOcrReviewBinding 8com.example.kpitrackerapp.ui.OcrReviewActivity.Companion  	ArrayList 8com.example.kpitrackerapp.ui.OcrReviewActivity.Companion  
OcrResultItem 8com.example.kpitrackerapp.ui.OcrReviewActivity.Companion  OcrReviewAdapter 8com.example.kpitrackerapp.ui.OcrReviewActivity.Companion  List -com.example.kpitrackerapp.ui.OcrReviewAdapter  MutableList -com.example.kpitrackerapp.ui.OcrReviewAdapter  
OcrResultItem -com.example.kpitrackerapp.ui.OcrReviewAdapter  OcrReviewItemBinding -com.example.kpitrackerapp.ui.OcrReviewAdapter  RecyclerView -com.example.kpitrackerapp.ui.OcrReviewAdapter  TextWatcher -com.example.kpitrackerapp.ui.OcrReviewAdapter  Unit -com.example.kpitrackerapp.ui.OcrReviewAdapter  
ViewHolder -com.example.kpitrackerapp.ui.OcrReviewAdapter  OcrReviewItemBinding 8com.example.kpitrackerapp.ui.OcrReviewAdapter.ViewHolder  TextWatcher 8com.example.kpitrackerapp.ui.OcrReviewAdapter.ViewHolder  ActivityReportBinding +com.example.kpitrackerapp.ui.ReportActivity  ArrayAdapter +com.example.kpitrackerapp.ui.ReportActivity  ChartPeriod +com.example.kpitrackerapp.ui.ReportActivity  Kpi +com.example.kpitrackerapp.ui.ReportActivity  
KpiReportData +com.example.kpitrackerapp.ui.ReportActivity  KpiViewModel +com.example.kpitrackerapp.ui.ReportActivity  List +com.example.kpitrackerapp.ui.ReportActivity  String +com.example.kpitrackerapp.ui.ReportActivity  ActivityReportBinding 5com.example.kpitrackerapp.ui.ReportActivity.Companion  ArrayAdapter 5com.example.kpitrackerapp.ui.ReportActivity.Companion  Kpi 5com.example.kpitrackerapp.ui.ReportActivity.Companion  
KpiReportData 5com.example.kpitrackerapp.ui.ReportActivity.Companion  KpiViewModel 5com.example.kpitrackerapp.ui.ReportActivity.Companion  List 5com.example.kpitrackerapp.ui.ReportActivity.Companion  String 5com.example.kpitrackerapp.ui.ReportActivity.Companion  !ActivitySearchEditProgressBinding 7com.example.kpitrackerapp.ui.SearchEditProgressActivity  KpiProgressEntry 7com.example.kpitrackerapp.ui.SearchEditProgressActivity  KpiViewModel 7com.example.kpitrackerapp.ui.SearchEditProgressActivity  List 7com.example.kpitrackerapp.ui.SearchEditProgressActivity  String 7com.example.kpitrackerapp.ui.SearchEditProgressActivity  !ActivitySearchEditProgressBinding Acom.example.kpitrackerapp.ui.SearchEditProgressActivity.Companion  KpiProgressEntry Acom.example.kpitrackerapp.ui.SearchEditProgressActivity.Companion  KpiViewModel Acom.example.kpitrackerapp.ui.SearchEditProgressActivity.Companion  List Acom.example.kpitrackerapp.ui.SearchEditProgressActivity.Companion  String Acom.example.kpitrackerapp.ui.SearchEditProgressActivity.Companion  	ImageView (com.example.kpitrackerapp.ui.TaskAdapter  OnTaskActionsListener (com.example.kpitrackerapp.ui.TaskAdapter  RecyclerView (com.example.kpitrackerapp.ui.TaskAdapter  TaskViewHolder (com.example.kpitrackerapp.ui.TaskAdapter  TextView (com.example.kpitrackerapp.ui.TaskAdapter  View (com.example.kpitrackerapp.ui.TaskAdapter  	ImageView 7com.example.kpitrackerapp.ui.TaskAdapter.TaskViewHolder  TextView 7com.example.kpitrackerapp.ui.TaskAdapter.TaskViewHolder  View 7com.example.kpitrackerapp.ui.TaskAdapter.TaskViewHolder  ActivityResultLauncher 3com.example.kpitrackerapp.ui.TaskManagementActivity  ActivityTaskManagementBinding 3com.example.kpitrackerapp.ui.TaskManagementActivity  Array 3com.example.kpitrackerapp.ui.TaskManagementActivity  EnhancedTaskAdapter 3com.example.kpitrackerapp.ui.TaskManagementActivity  String 3com.example.kpitrackerapp.ui.TaskManagementActivity  Task 3com.example.kpitrackerapp.ui.TaskManagementActivity  
TaskViewModel 3com.example.kpitrackerapp.ui.TaskManagementActivity  #ActivityTaskReminderSettingsBinding 9com.example.kpitrackerapp.ui.TaskReminderSettingsActivity  com 9com.example.kpitrackerapp.ui.TaskReminderSettingsActivity  ActivityTaskReportBinding /com.example.kpitrackerapp.ui.TaskReportActivity  List /com.example.kpitrackerapp.ui.TaskReportActivity  Task /com.example.kpitrackerapp.ui.TaskReportActivity  TaskReportAdapter /com.example.kpitrackerapp.ui.TaskReportActivity  
TaskViewModel /com.example.kpitrackerapp.ui.TaskReportActivity  RecyclerView .com.example.kpitrackerapp.ui.TaskReportAdapter  TaskReportViewHolder .com.example.kpitrackerapp.ui.TaskReportAdapter  TextView .com.example.kpitrackerapp.ui.TaskReportAdapter  View .com.example.kpitrackerapp.ui.TaskReportAdapter  TextView Ccom.example.kpitrackerapp.ui.TaskReportAdapter.TaskReportViewHolder  View Ccom.example.kpitrackerapp.ui.TaskReportAdapter.TaskReportViewHolder  Int 1com.example.kpitrackerapp.ui.UnifiedReportAdapter  List 1com.example.kpitrackerapp.ui.UnifiedReportAdapter  ReportRowViewHolder 1com.example.kpitrackerapp.ui.UnifiedReportAdapter  Boolean -com.example.kpitrackerapp.ui.UserFilterDialog  Context -com.example.kpitrackerapp.ui.UserFilterDialog  List -com.example.kpitrackerapp.ui.UserFilterDialog  Unit -com.example.kpitrackerapp.ui.UserFilterDialog  UserFilterAdapter -com.example.kpitrackerapp.ui.UserFilterDialog  UserFilterDialogBinding -com.example.kpitrackerapp.ui.UserFilterDialog  UserFilterItem -com.example.kpitrackerapp.ui.UserFilterDialog  ActivityUserKpiListBinding 0com.example.kpitrackerapp.ui.UserKpiListActivity  Boolean 0com.example.kpitrackerapp.ui.UserKpiListActivity  KpiListAdapter 0com.example.kpitrackerapp.ui.UserKpiListActivity  KpiViewModel 0com.example.kpitrackerapp.ui.UserKpiListActivity  String 0com.example.kpitrackerapp.ui.UserKpiListActivity  ActivityUserKpiListBinding :com.example.kpitrackerapp.ui.UserKpiListActivity.Companion  Boolean :com.example.kpitrackerapp.ui.UserKpiListActivity.Companion  KpiListAdapter :com.example.kpitrackerapp.ui.UserKpiListActivity.Companion  KpiViewModel :com.example.kpitrackerapp.ui.UserKpiListActivity.Companion  String :com.example.kpitrackerapp.ui.UserKpiListActivity.Companion  OnUserSummaryActionsListener /com.example.kpitrackerapp.ui.UserSummaryAdapter  UserSummaryViewHolder /com.example.kpitrackerapp.ui.UserSummaryAdapter  androidx /com.example.kpitrackerapp.ui.UserSummaryAdapter  AppNotificationManager com.example.kpitrackerapp.utils  CardAnimationHelper com.example.kpitrackerapp.utils  DragDropHelper com.example.kpitrackerapp.utils  Int com.example.kpitrackerapp.utils  Unit com.example.kpitrackerapp.utils  NotificationItem 6com.example.kpitrackerapp.utils.AppNotificationManager  Context 1com.example.kpitrackerapp.utils.CardGestureHelper  GestureDetector 1com.example.kpitrackerapp.utils.CardGestureHelper  Unit 1com.example.kpitrackerapp.utils.CardGestureHelper  Int .com.example.kpitrackerapp.utils.DragDropHelper  RecyclerView .com.example.kpitrackerapp.utils.DragDropHelper  Unit .com.example.kpitrackerapp.utils.DragDropHelper  SharedPreferences .com.example.kpitrackerapp.utils.SessionManager  User .com.example.kpitrackerapp.utils.SessionManager  Boolean $com.example.kpitrackerapp.viewmodels  Calendar $com.example.kpitrackerapp.viewmodels  ChatMessageItem $com.example.kpitrackerapp.viewmodels  
ChatViewModel $com.example.kpitrackerapp.viewmodels  ConversationItem $com.example.kpitrackerapp.viewmodels  Double $com.example.kpitrackerapp.viewmodels  Int $com.example.kpitrackerapp.viewmodels  KpiViewModel $com.example.kpitrackerapp.viewmodels  KpiWithProgress $com.example.kpitrackerapp.viewmodels  List $com.example.kpitrackerapp.viewmodels  LiveData $com.example.kpitrackerapp.viewmodels  Long $com.example.kpitrackerapp.viewmodels  Map $com.example.kpitrackerapp.viewmodels  MasterCardData $com.example.kpitrackerapp.viewmodels  OptIn $com.example.kpitrackerapp.viewmodels  Pair $com.example.kpitrackerapp.viewmodels  
QuickStats $com.example.kpitrackerapp.viewmodels  String $com.example.kpitrackerapp.viewmodels  Task $com.example.kpitrackerapp.viewmodels  
TaskAnalytics $com.example.kpitrackerapp.viewmodels  TaskCategory $com.example.kpitrackerapp.viewmodels  TaskPriority $com.example.kpitrackerapp.viewmodels  TaskQuadrant $com.example.kpitrackerapp.viewmodels  
TaskViewModel $com.example.kpitrackerapp.viewmodels  User $com.example.kpitrackerapp.viewmodels  Application 2com.example.kpitrackerapp.viewmodels.ChatViewModel  Boolean 2com.example.kpitrackerapp.viewmodels.ChatViewModel  ChatMessageItem 2com.example.kpitrackerapp.viewmodels.ChatViewModel  ConversationItem 2com.example.kpitrackerapp.viewmodels.ChatViewModel  Int 2com.example.kpitrackerapp.viewmodels.ChatViewModel  List 2com.example.kpitrackerapp.viewmodels.ChatViewModel  LiveData 2com.example.kpitrackerapp.viewmodels.ChatViewModel  String 2com.example.kpitrackerapp.viewmodels.ChatViewModel  User 2com.example.kpitrackerapp.viewmodels.ChatViewModel  Application 1com.example.kpitrackerapp.viewmodels.KpiViewModel  Calendar 1com.example.kpitrackerapp.viewmodels.KpiViewModel  Context 1com.example.kpitrackerapp.viewmodels.KpiViewModel  Flow 1com.example.kpitrackerapp.viewmodels.KpiViewModel  Kpi 1com.example.kpitrackerapp.viewmodels.KpiViewModel  KpiDao 1com.example.kpitrackerapp.viewmodels.KpiViewModel  KpiProgressEntry 1com.example.kpitrackerapp.viewmodels.KpiViewModel  KpiProgressEntryDao 1com.example.kpitrackerapp.viewmodels.KpiViewModel  
KpiReportData 1com.example.kpitrackerapp.viewmodels.KpiViewModel  KpiSummaryDetail 1com.example.kpitrackerapp.viewmodels.KpiViewModel  KpiWithProgress 1com.example.kpitrackerapp.viewmodels.KpiViewModel  List 1com.example.kpitrackerapp.viewmodels.KpiViewModel  LiveData 1com.example.kpitrackerapp.viewmodels.KpiViewModel  Long 1com.example.kpitrackerapp.viewmodels.KpiViewModel  Map 1com.example.kpitrackerapp.viewmodels.KpiViewModel  MasterCardData 1com.example.kpitrackerapp.viewmodels.KpiViewModel  
OcrResultItem 1com.example.kpitrackerapp.viewmodels.KpiViewModel  OptIn 1com.example.kpitrackerapp.viewmodels.KpiViewModel  Pair 1com.example.kpitrackerapp.viewmodels.KpiViewModel  	StateFlow 1com.example.kpitrackerapp.viewmodels.KpiViewModel  String 1com.example.kpitrackerapp.viewmodels.KpiViewModel  User 1com.example.kpitrackerapp.viewmodels.KpiViewModel  UserDao 1com.example.kpitrackerapp.viewmodels.KpiViewModel  UserKpiAssignmentDao 1com.example.kpitrackerapp.viewmodels.KpiViewModel  UserSummaryItem 1com.example.kpitrackerapp.viewmodels.KpiViewModel  Double 4com.example.kpitrackerapp.viewmodels.KpiWithProgress  Int 4com.example.kpitrackerapp.viewmodels.KpiWithProgress  Kpi 4com.example.kpitrackerapp.viewmodels.KpiWithProgress  KpiProgressEntry 4com.example.kpitrackerapp.viewmodels.KpiWithProgress  List 4com.example.kpitrackerapp.viewmodels.KpiWithProgress  Long 4com.example.kpitrackerapp.viewmodels.KpiWithProgress  String 4com.example.kpitrackerapp.viewmodels.KpiWithProgress  Application 2com.example.kpitrackerapp.viewmodels.TaskViewModel  Boolean 2com.example.kpitrackerapp.viewmodels.TaskViewModel  List 2com.example.kpitrackerapp.viewmodels.TaskViewModel  LiveData 2com.example.kpitrackerapp.viewmodels.TaskViewModel  
QuickStats 2com.example.kpitrackerapp.viewmodels.TaskViewModel  String 2com.example.kpitrackerapp.viewmodels.TaskViewModel  
SubtaskDao 2com.example.kpitrackerapp.viewmodels.TaskViewModel  Task 2com.example.kpitrackerapp.viewmodels.TaskViewModel  
TaskAnalytics 2com.example.kpitrackerapp.viewmodels.TaskViewModel  TaskCategory 2com.example.kpitrackerapp.viewmodels.TaskViewModel  TaskCategoryDao 2com.example.kpitrackerapp.viewmodels.TaskViewModel  TaskDao 2com.example.kpitrackerapp.viewmodels.TaskViewModel  TaskPriority 2com.example.kpitrackerapp.viewmodels.TaskViewModel  TaskQuadrant 2com.example.kpitrackerapp.viewmodels.TaskViewModel  Context 8com.example.kpitrackerapp.workers.AutoReportSenderWorker  WorkerParameters 8com.example.kpitrackerapp.workers.AutoReportSenderWorker  Context Bcom.example.kpitrackerapp.workers.AutoReportSenderWorker.Companion  WorkerParameters Bcom.example.kpitrackerapp.workers.AutoReportSenderWorker.Companion  Context :com.example.kpitrackerapp.workers.ExpiryNotificationWorker  WorkerParameters :com.example.kpitrackerapp.workers.ExpiryNotificationWorker  Context Dcom.example.kpitrackerapp.workers.ExpiryNotificationWorker.Companion  WorkerParameters Dcom.example.kpitrackerapp.workers.ExpiryNotificationWorker.Companion  Context 4com.example.kpitrackerapp.workers.TaskReminderWorker  WorkerParameters 4com.example.kpitrackerapp.workers.TaskReminderWorker  Context >com.example.kpitrackerapp.workers.TaskReminderWorker.Companion  WorkerParameters >com.example.kpitrackerapp.workers.TaskReminderWorker.Companion  
MarkerView 'com.github.mikephil.charting.components  Context 2com.github.mikephil.charting.components.MarkerView  Int 2com.github.mikephil.charting.components.MarkerView  TextView 2com.github.mikephil.charting.components.MarkerView  ValueFormatter &com.github.mikephil.charting.formatter  MaterialCardView  com.google.android.material.card  SerializedName com.google.gson.annotations  TextRecognizer com.google.mlkit.vision.text  ActivityAddEditKpiBinding 	java.lang  !ActivityAddEditKpiOriginalBinding 	java.lang  ActivityAdminDashboardBinding 	java.lang  ActivityAutoSendSettingsBinding 	java.lang  ActivityChatBinding 	java.lang  ActivityChatListBinding 	java.lang  ActivityCreateUserBinding 	java.lang  ActivityExcelImportBinding 	java.lang  ActivityExcelReviewBinding 	java.lang  ActivityExpireManagementBinding 	java.lang  ActivityKpiDetailBinding 	java.lang  ActivityLoginBinding 	java.lang  ActivityMainBinding 	java.lang  ActivityModernReportBinding 	java.lang  ActivityNotificationsBinding 	java.lang  ActivityOcrBinding 	java.lang  ActivityOcrReviewBinding 	java.lang  ActivityReportBinding 	java.lang  !ActivitySearchEditProgressBinding 	java.lang  ActivityTaskManagementBinding 	java.lang  #ActivityTaskReminderSettingsBinding 	java.lang  ActivityTaskReportBinding 	java.lang  ActivityUserKpiListBinding 	java.lang  ChatMessage 	java.lang  Conversation 	java.lang  
Converters 	java.lang  FragmentAccountBinding 	java.lang  FragmentDashboardBinding 	java.lang  FragmentMainDashboardBinding 	java.lang  FragmentMessagesBinding 	java.lang  FragmentPerformanceBinding 	java.lang  Kpi 	java.lang  KpiProgressEntry 	java.lang  OcrReviewItemBinding 	java.lang  OnConflictStrategy 	java.lang  Subtask 	java.lang  Task 	java.lang  TaskCategory 	java.lang  User 	java.lang  UserFilterDialogBinding 	java.lang  UserKpiAssignment 	java.lang  androidx 	java.lang  com 	java.lang  ActivityAddEditKpiBinding 	java.util  ActivityCreateUserBinding 	java.util  ActivityExcelImportBinding 	java.util  ActivityExcelReviewBinding 	java.util  	ArrayList 	java.util  Calendar 	java.util  Date 	java.util  LiveData 	java.util  Pair 	java.util  User 	java.util  ActivityAddEditKpiBinding kotlin  !ActivityAddEditKpiOriginalBinding kotlin  ActivityAdminDashboardBinding kotlin  ActivityAutoSendSettingsBinding kotlin  ActivityChatBinding kotlin  ActivityChatListBinding kotlin  ActivityCreateUserBinding kotlin  ActivityExcelImportBinding kotlin  ActivityExcelReviewBinding kotlin  ActivityExpireManagementBinding kotlin  ActivityKpiDetailBinding kotlin  ActivityLoginBinding kotlin  ActivityMainBinding kotlin  ActivityModernReportBinding kotlin  ActivityNotificationsBinding kotlin  ActivityOcrBinding kotlin  ActivityOcrReviewBinding kotlin  ActivityReportBinding kotlin  !ActivitySearchEditProgressBinding kotlin  ActivityTaskManagementBinding kotlin  #ActivityTaskReminderSettingsBinding kotlin  ActivityTaskReportBinding kotlin  ActivityUserKpiListBinding kotlin  Array kotlin  Boolean kotlin  ChatMessage kotlin  Conversation kotlin  
Converters kotlin  Double kotlin  FragmentAccountBinding kotlin  FragmentDashboardBinding kotlin  FragmentMainDashboardBinding kotlin  FragmentMessagesBinding kotlin  FragmentPerformanceBinding kotlin  Int kotlin  Kpi kotlin  KpiProgressEntry kotlin  Long kotlin  OcrReviewItemBinding kotlin  OnConflictStrategy kotlin  OptIn kotlin  Pair kotlin  String kotlin  Subtask kotlin  Task kotlin  TaskCategory kotlin  Unit kotlin  User kotlin  UserFilterDialogBinding kotlin  UserKpiAssignment kotlin  Volatile kotlin  androidx kotlin  arrayOf kotlin  com kotlin  ActivityAddEditKpiBinding kotlin.annotation  !ActivityAddEditKpiOriginalBinding kotlin.annotation  ActivityAdminDashboardBinding kotlin.annotation  ActivityAutoSendSettingsBinding kotlin.annotation  ActivityChatBinding kotlin.annotation  ActivityChatListBinding kotlin.annotation  ActivityCreateUserBinding kotlin.annotation  ActivityExcelImportBinding kotlin.annotation  ActivityExcelReviewBinding kotlin.annotation  ActivityExpireManagementBinding kotlin.annotation  ActivityKpiDetailBinding kotlin.annotation  ActivityLoginBinding kotlin.annotation  ActivityMainBinding kotlin.annotation  ActivityModernReportBinding kotlin.annotation  ActivityNotificationsBinding kotlin.annotation  ActivityOcrBinding kotlin.annotation  ActivityOcrReviewBinding kotlin.annotation  ActivityReportBinding kotlin.annotation  !ActivitySearchEditProgressBinding kotlin.annotation  ActivityTaskManagementBinding kotlin.annotation  #ActivityTaskReminderSettingsBinding kotlin.annotation  ActivityTaskReportBinding kotlin.annotation  ActivityUserKpiListBinding kotlin.annotation  ChatMessage kotlin.annotation  Conversation kotlin.annotation  
Converters kotlin.annotation  FragmentAccountBinding kotlin.annotation  FragmentDashboardBinding kotlin.annotation  FragmentMainDashboardBinding kotlin.annotation  FragmentMessagesBinding kotlin.annotation  FragmentPerformanceBinding kotlin.annotation  Kpi kotlin.annotation  KpiProgressEntry kotlin.annotation  OcrReviewItemBinding kotlin.annotation  OnConflictStrategy kotlin.annotation  Pair kotlin.annotation  Subtask kotlin.annotation  Task kotlin.annotation  TaskCategory kotlin.annotation  User kotlin.annotation  UserFilterDialogBinding kotlin.annotation  UserKpiAssignment kotlin.annotation  Volatile kotlin.annotation  androidx kotlin.annotation  com kotlin.annotation  ActivityAddEditKpiBinding kotlin.collections  !ActivityAddEditKpiOriginalBinding kotlin.collections  ActivityAdminDashboardBinding kotlin.collections  ActivityAutoSendSettingsBinding kotlin.collections  ActivityChatBinding kotlin.collections  ActivityChatListBinding kotlin.collections  ActivityCreateUserBinding kotlin.collections  ActivityExcelImportBinding kotlin.collections  ActivityExcelReviewBinding kotlin.collections  ActivityExpireManagementBinding kotlin.collections  ActivityKpiDetailBinding kotlin.collections  ActivityLoginBinding kotlin.collections  ActivityMainBinding kotlin.collections  ActivityModernReportBinding kotlin.collections  ActivityNotificationsBinding kotlin.collections  ActivityOcrBinding kotlin.collections  ActivityOcrReviewBinding kotlin.collections  ActivityReportBinding kotlin.collections  !ActivitySearchEditProgressBinding kotlin.collections  ActivityTaskManagementBinding kotlin.collections  #ActivityTaskReminderSettingsBinding kotlin.collections  ActivityTaskReportBinding kotlin.collections  ActivityUserKpiListBinding kotlin.collections  ChatMessage kotlin.collections  Conversation kotlin.collections  
Converters kotlin.collections  FragmentAccountBinding kotlin.collections  FragmentDashboardBinding kotlin.collections  FragmentMainDashboardBinding kotlin.collections  FragmentMessagesBinding kotlin.collections  FragmentPerformanceBinding kotlin.collections  Kpi kotlin.collections  KpiProgressEntry kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  OcrReviewItemBinding kotlin.collections  OnConflictStrategy kotlin.collections  Pair kotlin.collections  Subtask kotlin.collections  Task kotlin.collections  TaskCategory kotlin.collections  User kotlin.collections  UserFilterDialogBinding kotlin.collections  UserKpiAssignment kotlin.collections  Volatile kotlin.collections  androidx kotlin.collections  com kotlin.collections  ActivityAddEditKpiBinding kotlin.comparisons  !ActivityAddEditKpiOriginalBinding kotlin.comparisons  ActivityAdminDashboardBinding kotlin.comparisons  ActivityAutoSendSettingsBinding kotlin.comparisons  ActivityChatBinding kotlin.comparisons  ActivityChatListBinding kotlin.comparisons  ActivityCreateUserBinding kotlin.comparisons  ActivityExcelImportBinding kotlin.comparisons  ActivityExcelReviewBinding kotlin.comparisons  ActivityExpireManagementBinding kotlin.comparisons  ActivityKpiDetailBinding kotlin.comparisons  ActivityLoginBinding kotlin.comparisons  ActivityMainBinding kotlin.comparisons  ActivityModernReportBinding kotlin.comparisons  ActivityNotificationsBinding kotlin.comparisons  ActivityOcrBinding kotlin.comparisons  ActivityOcrReviewBinding kotlin.comparisons  ActivityReportBinding kotlin.comparisons  !ActivitySearchEditProgressBinding kotlin.comparisons  ActivityTaskManagementBinding kotlin.comparisons  #ActivityTaskReminderSettingsBinding kotlin.comparisons  ActivityTaskReportBinding kotlin.comparisons  ActivityUserKpiListBinding kotlin.comparisons  ChatMessage kotlin.comparisons  Conversation kotlin.comparisons  
Converters kotlin.comparisons  FragmentAccountBinding kotlin.comparisons  FragmentDashboardBinding kotlin.comparisons  FragmentMainDashboardBinding kotlin.comparisons  FragmentMessagesBinding kotlin.comparisons  FragmentPerformanceBinding kotlin.comparisons  Kpi kotlin.comparisons  KpiProgressEntry kotlin.comparisons  OcrReviewItemBinding kotlin.comparisons  OnConflictStrategy kotlin.comparisons  Pair kotlin.comparisons  Subtask kotlin.comparisons  Task kotlin.comparisons  TaskCategory kotlin.comparisons  User kotlin.comparisons  UserFilterDialogBinding kotlin.comparisons  UserKpiAssignment kotlin.comparisons  Volatile kotlin.comparisons  androidx kotlin.comparisons  com kotlin.comparisons  ActivityAddEditKpiBinding 	kotlin.io  !ActivityAddEditKpiOriginalBinding 	kotlin.io  ActivityAdminDashboardBinding 	kotlin.io  ActivityAutoSendSettingsBinding 	kotlin.io  ActivityChatBinding 	kotlin.io  ActivityChatListBinding 	kotlin.io  ActivityCreateUserBinding 	kotlin.io  ActivityExcelImportBinding 	kotlin.io  ActivityExcelReviewBinding 	kotlin.io  ActivityExpireManagementBinding 	kotlin.io  ActivityKpiDetailBinding 	kotlin.io  ActivityLoginBinding 	kotlin.io  ActivityMainBinding 	kotlin.io  ActivityModernReportBinding 	kotlin.io  ActivityNotificationsBinding 	kotlin.io  ActivityOcrBinding 	kotlin.io  ActivityOcrReviewBinding 	kotlin.io  ActivityReportBinding 	kotlin.io  !ActivitySearchEditProgressBinding 	kotlin.io  ActivityTaskManagementBinding 	kotlin.io  #ActivityTaskReminderSettingsBinding 	kotlin.io  ActivityTaskReportBinding 	kotlin.io  ActivityUserKpiListBinding 	kotlin.io  ChatMessage 	kotlin.io  Conversation 	kotlin.io  
Converters 	kotlin.io  FragmentAccountBinding 	kotlin.io  FragmentDashboardBinding 	kotlin.io  FragmentMainDashboardBinding 	kotlin.io  FragmentMessagesBinding 	kotlin.io  FragmentPerformanceBinding 	kotlin.io  Kpi 	kotlin.io  KpiProgressEntry 	kotlin.io  OcrReviewItemBinding 	kotlin.io  OnConflictStrategy 	kotlin.io  Pair 	kotlin.io  Subtask 	kotlin.io  Task 	kotlin.io  TaskCategory 	kotlin.io  User 	kotlin.io  UserFilterDialogBinding 	kotlin.io  UserKpiAssignment 	kotlin.io  Volatile 	kotlin.io  androidx 	kotlin.io  com 	kotlin.io  ActivityAddEditKpiBinding 
kotlin.jvm  !ActivityAddEditKpiOriginalBinding 
kotlin.jvm  ActivityAdminDashboardBinding 
kotlin.jvm  ActivityAutoSendSettingsBinding 
kotlin.jvm  ActivityChatBinding 
kotlin.jvm  ActivityChatListBinding 
kotlin.jvm  ActivityCreateUserBinding 
kotlin.jvm  ActivityExcelImportBinding 
kotlin.jvm  ActivityExcelReviewBinding 
kotlin.jvm  ActivityExpireManagementBinding 
kotlin.jvm  ActivityKpiDetailBinding 
kotlin.jvm  ActivityLoginBinding 
kotlin.jvm  ActivityMainBinding 
kotlin.jvm  ActivityModernReportBinding 
kotlin.jvm  ActivityNotificationsBinding 
kotlin.jvm  ActivityOcrBinding 
kotlin.jvm  ActivityOcrReviewBinding 
kotlin.jvm  ActivityReportBinding 
kotlin.jvm  !ActivitySearchEditProgressBinding 
kotlin.jvm  ActivityTaskManagementBinding 
kotlin.jvm  #ActivityTaskReminderSettingsBinding 
kotlin.jvm  ActivityTaskReportBinding 
kotlin.jvm  ActivityUserKpiListBinding 
kotlin.jvm  ChatMessage 
kotlin.jvm  Conversation 
kotlin.jvm  
Converters 
kotlin.jvm  FragmentAccountBinding 
kotlin.jvm  FragmentDashboardBinding 
kotlin.jvm  FragmentMainDashboardBinding 
kotlin.jvm  FragmentMessagesBinding 
kotlin.jvm  FragmentPerformanceBinding 
kotlin.jvm  Kpi 
kotlin.jvm  KpiProgressEntry 
kotlin.jvm  OcrReviewItemBinding 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  Pair 
kotlin.jvm  Subtask 
kotlin.jvm  Task 
kotlin.jvm  TaskCategory 
kotlin.jvm  User 
kotlin.jvm  UserFilterDialogBinding 
kotlin.jvm  UserKpiAssignment 
kotlin.jvm  Volatile 
kotlin.jvm  androidx 
kotlin.jvm  com 
kotlin.jvm  ActivityAddEditKpiBinding 
kotlin.ranges  !ActivityAddEditKpiOriginalBinding 
kotlin.ranges  ActivityAdminDashboardBinding 
kotlin.ranges  ActivityAutoSendSettingsBinding 
kotlin.ranges  ActivityChatBinding 
kotlin.ranges  ActivityChatListBinding 
kotlin.ranges  ActivityCreateUserBinding 
kotlin.ranges  ActivityExcelImportBinding 
kotlin.ranges  ActivityExcelReviewBinding 
kotlin.ranges  ActivityExpireManagementBinding 
kotlin.ranges  ActivityKpiDetailBinding 
kotlin.ranges  ActivityLoginBinding 
kotlin.ranges  ActivityMainBinding 
kotlin.ranges  ActivityModernReportBinding 
kotlin.ranges  ActivityNotificationsBinding 
kotlin.ranges  ActivityOcrBinding 
kotlin.ranges  ActivityOcrReviewBinding 
kotlin.ranges  ActivityReportBinding 
kotlin.ranges  !ActivitySearchEditProgressBinding 
kotlin.ranges  ActivityTaskManagementBinding 
kotlin.ranges  #ActivityTaskReminderSettingsBinding 
kotlin.ranges  ActivityTaskReportBinding 
kotlin.ranges  ActivityUserKpiListBinding 
kotlin.ranges  ChatMessage 
kotlin.ranges  Conversation 
kotlin.ranges  
Converters 
kotlin.ranges  FragmentAccountBinding 
kotlin.ranges  FragmentDashboardBinding 
kotlin.ranges  FragmentMainDashboardBinding 
kotlin.ranges  FragmentMessagesBinding 
kotlin.ranges  FragmentPerformanceBinding 
kotlin.ranges  Kpi 
kotlin.ranges  KpiProgressEntry 
kotlin.ranges  OcrReviewItemBinding 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  Pair 
kotlin.ranges  Subtask 
kotlin.ranges  Task 
kotlin.ranges  TaskCategory 
kotlin.ranges  User 
kotlin.ranges  UserFilterDialogBinding 
kotlin.ranges  UserKpiAssignment 
kotlin.ranges  Volatile 
kotlin.ranges  androidx 
kotlin.ranges  com 
kotlin.ranges  KClass kotlin.reflect  ActivityAddEditKpiBinding kotlin.sequences  !ActivityAddEditKpiOriginalBinding kotlin.sequences  ActivityAdminDashboardBinding kotlin.sequences  ActivityAutoSendSettingsBinding kotlin.sequences  ActivityChatBinding kotlin.sequences  ActivityChatListBinding kotlin.sequences  ActivityCreateUserBinding kotlin.sequences  ActivityExcelImportBinding kotlin.sequences  ActivityExcelReviewBinding kotlin.sequences  ActivityExpireManagementBinding kotlin.sequences  ActivityKpiDetailBinding kotlin.sequences  ActivityLoginBinding kotlin.sequences  ActivityMainBinding kotlin.sequences  ActivityModernReportBinding kotlin.sequences  ActivityNotificationsBinding kotlin.sequences  ActivityOcrBinding kotlin.sequences  ActivityOcrReviewBinding kotlin.sequences  ActivityReportBinding kotlin.sequences  !ActivitySearchEditProgressBinding kotlin.sequences  ActivityTaskManagementBinding kotlin.sequences  #ActivityTaskReminderSettingsBinding kotlin.sequences  ActivityTaskReportBinding kotlin.sequences  ActivityUserKpiListBinding kotlin.sequences  ChatMessage kotlin.sequences  Conversation kotlin.sequences  
Converters kotlin.sequences  FragmentAccountBinding kotlin.sequences  FragmentDashboardBinding kotlin.sequences  FragmentMainDashboardBinding kotlin.sequences  FragmentMessagesBinding kotlin.sequences  FragmentPerformanceBinding kotlin.sequences  Kpi kotlin.sequences  KpiProgressEntry kotlin.sequences  OcrReviewItemBinding kotlin.sequences  OnConflictStrategy kotlin.sequences  Pair kotlin.sequences  Subtask kotlin.sequences  Task kotlin.sequences  TaskCategory kotlin.sequences  User kotlin.sequences  UserFilterDialogBinding kotlin.sequences  UserKpiAssignment kotlin.sequences  Volatile kotlin.sequences  androidx kotlin.sequences  com kotlin.sequences  ActivityAddEditKpiBinding kotlin.text  !ActivityAddEditKpiOriginalBinding kotlin.text  ActivityAdminDashboardBinding kotlin.text  ActivityAutoSendSettingsBinding kotlin.text  ActivityChatBinding kotlin.text  ActivityChatListBinding kotlin.text  ActivityCreateUserBinding kotlin.text  ActivityExcelImportBinding kotlin.text  ActivityExcelReviewBinding kotlin.text  ActivityExpireManagementBinding kotlin.text  ActivityKpiDetailBinding kotlin.text  ActivityLoginBinding kotlin.text  ActivityMainBinding kotlin.text  ActivityModernReportBinding kotlin.text  ActivityNotificationsBinding kotlin.text  ActivityOcrBinding kotlin.text  ActivityOcrReviewBinding kotlin.text  ActivityReportBinding kotlin.text  !ActivitySearchEditProgressBinding kotlin.text  ActivityTaskManagementBinding kotlin.text  #ActivityTaskReminderSettingsBinding kotlin.text  ActivityTaskReportBinding kotlin.text  ActivityUserKpiListBinding kotlin.text  ChatMessage kotlin.text  Conversation kotlin.text  
Converters kotlin.text  FragmentAccountBinding kotlin.text  FragmentDashboardBinding kotlin.text  FragmentMainDashboardBinding kotlin.text  FragmentMessagesBinding kotlin.text  FragmentPerformanceBinding kotlin.text  Kpi kotlin.text  KpiProgressEntry kotlin.text  OcrReviewItemBinding kotlin.text  OnConflictStrategy kotlin.text  Pair kotlin.text  Subtask kotlin.text  Task kotlin.text  TaskCategory kotlin.text  User kotlin.text  UserFilterDialogBinding kotlin.text  UserKpiAssignment kotlin.text  Volatile kotlin.text  androidx kotlin.text  com kotlin.text  Calendar kotlinx.coroutines.flow  Flow kotlinx.coroutines.flow  LiveData kotlinx.coroutines.flow  Pair kotlinx.coroutines.flow  
QuickStats kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  Task kotlinx.coroutines.flow  
TaskAnalytics kotlinx.coroutines.flow  TaskCategory kotlinx.coroutines.flow  TaskPriority kotlinx.coroutines.flow  TaskQuadrant kotlinx.coroutines.flow  	Parcelize kotlinx.parcelize  ActivityExcelImportBinding org.apache.poi.ss.usermodel  Bundle android.app.Activity  IntArray android.app.Activity  Menu android.app.Activity  MenuItem android.app.Activity  Bundle android.app.Dialog  
RemoteMessage android.app.Service  String android.app.Service  Bundle android.content.Context  IntArray android.content.Context  Menu android.content.Context  MenuItem android.content.Context  
RemoteMessage android.content.Context  Bundle android.content.ContextWrapper  IntArray android.content.ContextWrapper  Menu android.content.ContextWrapper  MenuItem android.content.ContextWrapper  
RemoteMessage android.content.ContextWrapper  Canvas android.graphics  Bundle 
android.os  LayoutInflater android.view  Menu android.view  MenuItem android.view  MotionEvent android.view  	ViewGroup android.view  Bundle  android.view.ContextThemeWrapper  IntArray  android.view.ContextThemeWrapper  Menu  android.view.ContextThemeWrapper  MenuItem  android.view.ContextThemeWrapper  Entry android.view.View  	Highlight android.view.View  MPPointF android.view.View  Entry android.view.ViewGroup  	Highlight android.view.ViewGroup  MPPointF android.view.ViewGroup  Entry android.widget.RelativeLayout  	Highlight android.widget.RelativeLayout  MPPointF android.widget.RelativeLayout  Bundle #androidx.activity.ComponentActivity  IntArray #androidx.activity.ComponentActivity  Menu #androidx.activity.ComponentActivity  MenuItem #androidx.activity.ComponentActivity  Bundle (androidx.appcompat.app.AppCompatActivity  IntArray (androidx.appcompat.app.AppCompatActivity  Menu (androidx.appcompat.app.AppCompatActivity  MenuItem (androidx.appcompat.app.AppCompatActivity  Bundle #androidx.core.app.ComponentActivity  IntArray #androidx.core.app.ComponentActivity  Menu #androidx.core.app.ComponentActivity  MenuItem #androidx.core.app.ComponentActivity  Bundle androidx.fragment.app.Fragment  LayoutInflater androidx.fragment.app.Fragment  View androidx.fragment.app.Fragment  	ViewGroup androidx.fragment.app.Fragment  Bundle &androidx.fragment.app.FragmentActivity  IntArray &androidx.fragment.app.FragmentActivity  Menu &androidx.fragment.app.FragmentActivity  MenuItem &androidx.fragment.app.FragmentActivity  ViewModelProvider androidx.lifecycle  Factory $androidx.lifecycle.ViewModelProvider  DiffUtil androidx.recyclerview.widget  ItemCallback %androidx.recyclerview.widget.DiffUtil  AppNotificationManager 2androidx.recyclerview.widget.DiffUtil.ItemCallback  Boolean 2androidx.recyclerview.widget.DiffUtil.ItemCallback  ChatMessageItem 2androidx.recyclerview.widget.DiffUtil.ItemCallback  ConversationItem 2androidx.recyclerview.widget.DiffUtil.ItemCallback  KpiProgressEntry 2androidx.recyclerview.widget.DiffUtil.ItemCallback  
KpiReportData 2androidx.recyclerview.widget.DiffUtil.ItemCallback  KpiWithProgress 2androidx.recyclerview.widget.DiffUtil.ItemCallback  
OcrResultItem 2androidx.recyclerview.widget.DiffUtil.ItemCallback  Task 2androidx.recyclerview.widget.DiffUtil.ItemCallback  UnifiedReportRow 2androidx.recyclerview.widget.DiffUtil.ItemCallback  User 2androidx.recyclerview.widget.DiffUtil.ItemCallback  UserSummaryItem 2androidx.recyclerview.widget.DiffUtil.ItemCallback  Boolean 5androidx.recyclerview.widget.ItemTouchHelper.Callback  Canvas 5androidx.recyclerview.widget.ItemTouchHelper.Callback  Float 5androidx.recyclerview.widget.ItemTouchHelper.Callback  Boolean ;androidx.recyclerview.widget.ItemTouchHelper.SimpleCallback  Canvas ;androidx.recyclerview.widget.ItemTouchHelper.SimpleCallback  Float ;androidx.recyclerview.widget.ItemTouchHelper.SimpleCallback  AppNotificationManager (androidx.recyclerview.widget.ListAdapter  Boolean (androidx.recyclerview.widget.ListAdapter  DiffUtil (androidx.recyclerview.widget.ListAdapter  
KpiReportData (androidx.recyclerview.widget.ListAdapter  KpiWithProgress (androidx.recyclerview.widget.ListAdapter  
OcrResultItem (androidx.recyclerview.widget.ListAdapter  Task (androidx.recyclerview.widget.ListAdapter  UnifiedReportRow (androidx.recyclerview.widget.ListAdapter  User (androidx.recyclerview.widget.ListAdapter  UserSummaryItem (androidx.recyclerview.widget.ListAdapter  	ViewGroup (androidx.recyclerview.widget.ListAdapter  AppNotificationManager 1androidx.recyclerview.widget.RecyclerView.Adapter  Boolean 1androidx.recyclerview.widget.RecyclerView.Adapter  DiffUtil 1androidx.recyclerview.widget.RecyclerView.Adapter  
KpiReportData 1androidx.recyclerview.widget.RecyclerView.Adapter  KpiWithProgress 1androidx.recyclerview.widget.RecyclerView.Adapter  Task 1androidx.recyclerview.widget.RecyclerView.Adapter  UnifiedReportRow 1androidx.recyclerview.widget.RecyclerView.Adapter  User 1androidx.recyclerview.widget.RecyclerView.Adapter  UserSummaryItem 1androidx.recyclerview.widget.RecyclerView.Adapter  	ViewGroup 1androidx.recyclerview.widget.RecyclerView.Adapter  Result androidx.work.CoroutineWorker  Result androidx.work.ListenableWorker  Boolean com.example.kpitrackerapp  Boolean 0com.example.kpitrackerapp.AdminDashboardActivity  Bundle 0com.example.kpitrackerapp.AdminDashboardActivity  Menu 0com.example.kpitrackerapp.AdminDashboardActivity  MenuItem 0com.example.kpitrackerapp.AdminDashboardActivity  Boolean "com.example.kpitrackerapp.adapters  Int "com.example.kpitrackerapp.adapters  Int 8com.example.kpitrackerapp.adapters.AdminDashboardAdapter  	ViewGroup 8com.example.kpitrackerapp.adapters.AdminDashboardAdapter  Int Bcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.Companion  	ViewGroup Bcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.Companion  Int 5com.example.kpitrackerapp.adapters.ChatMessageAdapter  	ViewGroup 5com.example.kpitrackerapp.adapters.ChatMessageAdapter  Int ?com.example.kpitrackerapp.adapters.ChatMessageAdapter.Companion  	ViewGroup ?com.example.kpitrackerapp.adapters.ChatMessageAdapter.Companion  Boolean :com.example.kpitrackerapp.adapters.ChatMessageDiffCallback  ChatMessageItem :com.example.kpitrackerapp.adapters.ChatMessageDiffCallback  Int 6com.example.kpitrackerapp.adapters.ConversationAdapter  	ViewGroup 6com.example.kpitrackerapp.adapters.ConversationAdapter  Boolean ;com.example.kpitrackerapp.adapters.ConversationDiffCallback  ConversationItem ;com.example.kpitrackerapp.adapters.ConversationDiffCallback  AppNotificationManager 6com.example.kpitrackerapp.adapters.NotificationAdapter  Boolean 6com.example.kpitrackerapp.adapters.NotificationAdapter  DiffUtil 6com.example.kpitrackerapp.adapters.NotificationAdapter  Int 6com.example.kpitrackerapp.adapters.NotificationAdapter  	ViewGroup 6com.example.kpitrackerapp.adapters.NotificationAdapter  AppNotificationManager Ocom.example.kpitrackerapp.adapters.NotificationAdapter.NotificationDiffCallback  Boolean Ocom.example.kpitrackerapp.adapters.NotificationAdapter.NotificationDiffCallback  Boolean 3com.example.kpitrackerapp.adapters.UserDiffCallback  User 3com.example.kpitrackerapp.adapters.UserDiffCallback  Int 4com.example.kpitrackerapp.adapters.UserFilterAdapter  	ViewGroup 4com.example.kpitrackerapp.adapters.UserFilterAdapter  Int 2com.example.kpitrackerapp.adapters.UserListAdapter  	ViewGroup 2com.example.kpitrackerapp.adapters.UserListAdapter  Bundle 3com.example.kpitrackerapp.fragments.AccountFragment  LayoutInflater 3com.example.kpitrackerapp.fragments.AccountFragment  View 3com.example.kpitrackerapp.fragments.AccountFragment  	ViewGroup 3com.example.kpitrackerapp.fragments.AccountFragment  Bundle 5com.example.kpitrackerapp.fragments.DashboardFragment  LayoutInflater 5com.example.kpitrackerapp.fragments.DashboardFragment  View 5com.example.kpitrackerapp.fragments.DashboardFragment  	ViewGroup 5com.example.kpitrackerapp.fragments.DashboardFragment  Bundle 4com.example.kpitrackerapp.fragments.MessagesFragment  LayoutInflater 4com.example.kpitrackerapp.fragments.MessagesFragment  View 4com.example.kpitrackerapp.fragments.MessagesFragment  	ViewGroup 4com.example.kpitrackerapp.fragments.MessagesFragment  Bundle 7com.example.kpitrackerapp.fragments.PerformanceFragment  LayoutInflater 7com.example.kpitrackerapp.fragments.PerformanceFragment  View 7com.example.kpitrackerapp.fragments.PerformanceFragment  	ViewGroup 7com.example.kpitrackerapp.fragments.PerformanceFragment  String "com.example.kpitrackerapp.services  
RemoteMessage >com.example.kpitrackerapp.services.KPIFirebaseMessagingService  String >com.example.kpitrackerapp.services.KPIFirebaseMessagingService  IntArray com.example.kpitrackerapp.ui  
ReportAdapter com.example.kpitrackerapp.ui  Task com.example.kpitrackerapp.ui  Array 8com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity  Boolean 8com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity  Bundle 8com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity  IntArray 8com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity  Intent 8com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity  Array Bcom.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity.Companion  Boolean Bcom.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity.Companion  Bundle Bcom.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity.Companion  IntArray Bcom.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity.Companion  Intent Bcom.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity.Companion  Boolean /com.example.kpitrackerapp.ui.AddEditKpiActivity  Bundle /com.example.kpitrackerapp.ui.AddEditKpiActivity  Boolean 9com.example.kpitrackerapp.ui.AddEditKpiActivity.Companion  Bundle 9com.example.kpitrackerapp.ui.AddEditKpiActivity.Companion  Boolean 5com.example.kpitrackerapp.ui.AutoSendSettingsActivity  Bundle 5com.example.kpitrackerapp.ui.AutoSendSettingsActivity  Entry ,com.example.kpitrackerapp.ui.ChartMarkerView  	Highlight ,com.example.kpitrackerapp.ui.ChartMarkerView  MPPointF ,com.example.kpitrackerapp.ui.ChartMarkerView  Boolean )com.example.kpitrackerapp.ui.ChatActivity  Bundle )com.example.kpitrackerapp.ui.ChatActivity  Menu )com.example.kpitrackerapp.ui.ChatActivity  MenuItem )com.example.kpitrackerapp.ui.ChatActivity  Boolean -com.example.kpitrackerapp.ui.ChatListActivity  Bundle -com.example.kpitrackerapp.ui.ChatListActivity  Menu -com.example.kpitrackerapp.ui.ChatListActivity  MenuItem -com.example.kpitrackerapp.ui.ChatListActivity  Boolean 1com.example.kpitrackerapp.ui.ColoredReportAdapter  DiffUtil 1com.example.kpitrackerapp.ui.ColoredReportAdapter  
KpiReportData 1com.example.kpitrackerapp.ui.ColoredReportAdapter  	ViewGroup 1com.example.kpitrackerapp.ui.ColoredReportAdapter  Boolean Dcom.example.kpitrackerapp.ui.ColoredReportAdapter.ReportDiffCallback  
KpiReportData Dcom.example.kpitrackerapp.ui.ColoredReportAdapter.ReportDiffCallback  Boolean 1com.example.kpitrackerapp.ui.CompactReportAdapter  DiffUtil 1com.example.kpitrackerapp.ui.CompactReportAdapter  
KpiReportData 1com.example.kpitrackerapp.ui.CompactReportAdapter  	ViewGroup 1com.example.kpitrackerapp.ui.CompactReportAdapter  Boolean Dcom.example.kpitrackerapp.ui.CompactReportAdapter.ReportDiffCallback  
KpiReportData Dcom.example.kpitrackerapp.ui.CompactReportAdapter.ReportDiffCallback  Task 8com.example.kpitrackerapp.ui.EnhancedTaskActionsListener  Boolean 0com.example.kpitrackerapp.ui.EnhancedTaskAdapter  DiffUtil 0com.example.kpitrackerapp.ui.EnhancedTaskAdapter  Int 0com.example.kpitrackerapp.ui.EnhancedTaskAdapter  Task 0com.example.kpitrackerapp.ui.EnhancedTaskAdapter  TaskViewHolder 0com.example.kpitrackerapp.ui.EnhancedTaskAdapter  	ViewGroup 0com.example.kpitrackerapp.ui.EnhancedTaskAdapter  Boolean Acom.example.kpitrackerapp.ui.EnhancedTaskAdapter.TaskDiffCallback  Task Acom.example.kpitrackerapp.ui.EnhancedTaskAdapter.TaskDiffCallback  Boolean /com.example.kpitrackerapp.ui.ExcelReviewAdapter  DiffUtil /com.example.kpitrackerapp.ui.ExcelReviewAdapter  Int /com.example.kpitrackerapp.ui.ExcelReviewAdapter  
OcrResultItem /com.example.kpitrackerapp.ui.ExcelReviewAdapter  	ViewGroup /com.example.kpitrackerapp.ui.ExcelReviewAdapter  Boolean Acom.example.kpitrackerapp.ui.ExcelReviewAdapter.ExcelDiffCallback  
OcrResultItem Acom.example.kpitrackerapp.ui.ExcelReviewAdapter.ExcelDiffCallback  Boolean 5com.example.kpitrackerapp.ui.ExpireManagementActivity  Bundle 5com.example.kpitrackerapp.ui.ExpireManagementActivity  Boolean +com.example.kpitrackerapp.ui.KpiListAdapter  DiffUtil +com.example.kpitrackerapp.ui.KpiListAdapter  Int +com.example.kpitrackerapp.ui.KpiListAdapter  
KpiViewHolder +com.example.kpitrackerapp.ui.KpiListAdapter  KpiWithProgress +com.example.kpitrackerapp.ui.KpiListAdapter  	ViewGroup +com.example.kpitrackerapp.ui.KpiListAdapter  Boolean Gcom.example.kpitrackerapp.ui.KpiListAdapter.KpiWithProgressDiffCallback  KpiWithProgress Gcom.example.kpitrackerapp.ui.KpiListAdapter.KpiWithProgressDiffCallback  Boolean 4com.example.kpitrackerapp.ui.KpiProgressEntryAdapter  DiffUtil 4com.example.kpitrackerapp.ui.KpiProgressEntryAdapter  Int 4com.example.kpitrackerapp.ui.KpiProgressEntryAdapter  	ViewGroup 4com.example.kpitrackerapp.ui.KpiProgressEntryAdapter  Boolean Ncom.example.kpitrackerapp.ui.KpiProgressEntryAdapter.ProgressEntryDiffCallback  KpiProgressEntry Ncom.example.kpitrackerapp.ui.KpiProgressEntryAdapter.ProgressEntryDiffCallback  Boolean 2com.example.kpitrackerapp.ui.NotificationsActivity  Bundle 2com.example.kpitrackerapp.ui.NotificationsActivity  MenuItem 2com.example.kpitrackerapp.ui.NotificationsActivity  Bundle .com.example.kpitrackerapp.ui.OcrReviewActivity  Bundle 8com.example.kpitrackerapp.ui.OcrReviewActivity.Companion  Int -com.example.kpitrackerapp.ui.OcrReviewAdapter  	ViewGroup -com.example.kpitrackerapp.ui.OcrReviewAdapter  Boolean /com.example.kpitrackerapp.ui.RecentUsersAdapter  DiffUtil /com.example.kpitrackerapp.ui.RecentUsersAdapter  Int /com.example.kpitrackerapp.ui.RecentUsersAdapter  RecentUserViewHolder /com.example.kpitrackerapp.ui.RecentUsersAdapter  User /com.example.kpitrackerapp.ui.RecentUsersAdapter  	ViewGroup /com.example.kpitrackerapp.ui.RecentUsersAdapter  Boolean Fcom.example.kpitrackerapp.ui.RecentUsersAdapter.RecentUserDiffCallback  User Fcom.example.kpitrackerapp.ui.RecentUsersAdapter.RecentUserDiffCallback  Boolean *com.example.kpitrackerapp.ui.ReportAdapter  DiffUtil *com.example.kpitrackerapp.ui.ReportAdapter  Int *com.example.kpitrackerapp.ui.ReportAdapter  
KpiReportData *com.example.kpitrackerapp.ui.ReportAdapter  ReportViewHolder *com.example.kpitrackerapp.ui.ReportAdapter  	ViewGroup *com.example.kpitrackerapp.ui.ReportAdapter  Boolean =com.example.kpitrackerapp.ui.ReportAdapter.ReportDiffCallback  
KpiReportData =com.example.kpitrackerapp.ui.ReportAdapter.ReportDiffCallback  Int (com.example.kpitrackerapp.ui.TaskAdapter  	ViewGroup (com.example.kpitrackerapp.ui.TaskAdapter  Boolean -com.example.kpitrackerapp.ui.TaskDiffCallback  Task -com.example.kpitrackerapp.ui.TaskDiffCallback  Boolean 3com.example.kpitrackerapp.ui.TaskManagementActivity  Bundle 3com.example.kpitrackerapp.ui.TaskManagementActivity  Menu 3com.example.kpitrackerapp.ui.TaskManagementActivity  MenuItem 3com.example.kpitrackerapp.ui.TaskManagementActivity  Boolean 9com.example.kpitrackerapp.ui.TaskReminderSettingsActivity  Bundle 9com.example.kpitrackerapp.ui.TaskReminderSettingsActivity  Bundle /com.example.kpitrackerapp.ui.TaskReportActivity  Int .com.example.kpitrackerapp.ui.TaskReportAdapter  	ViewGroup .com.example.kpitrackerapp.ui.TaskReportAdapter  Boolean 1com.example.kpitrackerapp.ui.UnifiedReportAdapter  DiffUtil 1com.example.kpitrackerapp.ui.UnifiedReportAdapter  UnifiedReportRow 1com.example.kpitrackerapp.ui.UnifiedReportAdapter  	ViewGroup 1com.example.kpitrackerapp.ui.UnifiedReportAdapter  Boolean Gcom.example.kpitrackerapp.ui.UnifiedReportAdapter.ReportRowDiffCallback  UnifiedReportRow Gcom.example.kpitrackerapp.ui.UnifiedReportAdapter.ReportRowDiffCallback  Bundle -com.example.kpitrackerapp.ui.UserFilterDialog  Boolean /com.example.kpitrackerapp.ui.UserSummaryAdapter  DiffUtil /com.example.kpitrackerapp.ui.UserSummaryAdapter  Int /com.example.kpitrackerapp.ui.UserSummaryAdapter  UserSummaryItem /com.example.kpitrackerapp.ui.UserSummaryAdapter  	ViewGroup /com.example.kpitrackerapp.ui.UserSummaryAdapter  Boolean Gcom.example.kpitrackerapp.ui.UserSummaryAdapter.UserSummaryDiffCallback  UserSummaryItem Gcom.example.kpitrackerapp.ui.UserSummaryAdapter.UserSummaryDiffCallback  Boolean com.example.kpitrackerapp.utils  Float com.example.kpitrackerapp.utils  Boolean 1com.example.kpitrackerapp.utils.CardGestureHelper  MotionEvent 1com.example.kpitrackerapp.utils.CardGestureHelper  View 1com.example.kpitrackerapp.utils.CardGestureHelper  Boolean .com.example.kpitrackerapp.utils.DragDropHelper  Canvas .com.example.kpitrackerapp.utils.DragDropHelper  Float .com.example.kpitrackerapp.utils.DragDropHelper  Class $com.example.kpitrackerapp.viewmodels  Class 9com.example.kpitrackerapp.viewmodels.ChatViewModelFactory  	ViewModel 9com.example.kpitrackerapp.viewmodels.ChatViewModelFactory  Class 8com.example.kpitrackerapp.viewmodels.KpiViewModelFactory  	ViewModel 8com.example.kpitrackerapp.viewmodels.KpiViewModelFactory  Class 9com.example.kpitrackerapp.viewmodels.TaskViewModelFactory  	ViewModel 9com.example.kpitrackerapp.viewmodels.TaskViewModelFactory  Result 8com.example.kpitrackerapp.workers.AutoReportSenderWorker  Result Bcom.example.kpitrackerapp.workers.AutoReportSenderWorker.Companion  Result :com.example.kpitrackerapp.workers.ExpiryNotificationWorker  Result Dcom.example.kpitrackerapp.workers.ExpiryNotificationWorker.Companion  Result 5com.example.kpitrackerapp.workers.RecurringTaskWorker  Result ?com.example.kpitrackerapp.workers.RecurringTaskWorker.Companion  Result 4com.example.kpitrackerapp.workers.TaskReminderWorker  Result >com.example.kpitrackerapp.workers.TaskReminderWorker.Companion  Entry 2com.github.mikephil.charting.components.MarkerView  	Highlight 2com.github.mikephil.charting.components.MarkerView  MPPointF 2com.github.mikephil.charting.components.MarkerView  Entry !com.github.mikephil.charting.data  	Highlight &com.github.mikephil.charting.highlight  MPPointF "com.github.mikephil.charting.utils  FirebaseMessagingService com.google.firebase.messaging  
RemoteMessage com.google.firebase.messaging  
RemoteMessage 3com.google.firebase.messaging.EnhancedIntentService  String 3com.google.firebase.messaging.EnhancedIntentService  
RemoteMessage 6com.google.firebase.messaging.FirebaseMessagingService  String 6com.google.firebase.messaging.FirebaseMessagingService  Class 	java.lang  Task 	java.util  Class kotlin  Float kotlin  IntArray kotlin  Class kotlin.annotation  Class kotlin.collections  Class kotlin.comparisons  Class 	kotlin.io  Class 
kotlin.jvm  Class 
kotlin.ranges  Class kotlin.sequences  Class kotlin.text  AlarmClockManager android.app.Activity  ContextMenu android.app.Activity  Float android.app.Activity  KpiWithProgress android.app.Activity  AlarmClockManager android.content.Context  ContextMenu android.content.Context  Float android.content.Context  KpiWithProgress android.content.Context  AlarmClockManager android.content.ContextWrapper  ContextMenu android.content.ContextWrapper  Float android.content.ContextWrapper  KpiWithProgress android.content.ContextWrapper  ContextMenu android.view  ContextMenuInfo android.view.ContextMenu  AlarmClockManager  android.view.ContextThemeWrapper  ContextMenu  android.view.ContextThemeWrapper  Float  android.view.ContextThemeWrapper  KpiWithProgress  android.view.ContextThemeWrapper  AlarmClockManager #androidx.activity.ComponentActivity  ContextMenu #androidx.activity.ComponentActivity  Float #androidx.activity.ComponentActivity  KpiWithProgress #androidx.activity.ComponentActivity  AlarmClockManager (androidx.appcompat.app.AppCompatActivity  ContextMenu (androidx.appcompat.app.AppCompatActivity  Float (androidx.appcompat.app.AppCompatActivity  KpiWithProgress (androidx.appcompat.app.AppCompatActivity  AlarmClockManager #androidx.core.app.ComponentActivity  ContextMenu #androidx.core.app.ComponentActivity  Float #androidx.core.app.ComponentActivity  KpiWithProgress #androidx.core.app.ComponentActivity  Bundle $androidx.fragment.app.DialogFragment  Dialog $androidx.fragment.app.DialogFragment  Dialog androidx.fragment.app.Fragment  Int androidx.fragment.app.Fragment  UserSummaryItem androidx.fragment.app.Fragment  AlarmClockManager &androidx.fragment.app.FragmentActivity  ContextMenu &androidx.fragment.app.FragmentActivity  Float &androidx.fragment.app.FragmentActivity  KpiWithProgress &androidx.fragment.app.FragmentActivity  Int com.example.kpitrackerapp  Boolean &com.example.kpitrackerapp.MainActivity  Bundle &com.example.kpitrackerapp.MainActivity  ContextMenu &com.example.kpitrackerapp.MainActivity  Int &com.example.kpitrackerapp.MainActivity  Menu &com.example.kpitrackerapp.MainActivity  MenuItem &com.example.kpitrackerapp.MainActivity  Int #com.example.kpitrackerapp.fragments  Bundle 9com.example.kpitrackerapp.fragments.MainDashboardFragment  Int 9com.example.kpitrackerapp.fragments.MainDashboardFragment  LayoutInflater 9com.example.kpitrackerapp.fragments.MainDashboardFragment  UserSummaryItem 9com.example.kpitrackerapp.fragments.MainDashboardFragment  View 9com.example.kpitrackerapp.fragments.MainDashboardFragment  	ViewGroup 9com.example.kpitrackerapp.fragments.MainDashboardFragment  Float com.example.kpitrackerapp.ui  AlarmClockManager 8com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity  AlarmClockManager Bcom.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity.Companion  AlarmClockManager /com.example.kpitrackerapp.ui.AddEditKpiActivity  AlarmClockManager 9com.example.kpitrackerapp.ui.AddEditKpiActivity.Companion  Boolean 7com.example.kpitrackerapp.ui.AddEditKpiOriginalActivity  Bundle 7com.example.kpitrackerapp.ui.AddEditKpiOriginalActivity  Boolean Acom.example.kpitrackerapp.ui.AddEditKpiOriginalActivity.Companion  Bundle Acom.example.kpitrackerapp.ui.AddEditKpiOriginalActivity.Companion  Bundle :com.example.kpitrackerapp.ui.AddEditProgressDialogFragment  Dialog :com.example.kpitrackerapp.ui.AddEditProgressDialogFragment  Bundle Dcom.example.kpitrackerapp.ui.AddEditProgressDialogFragment.Companion  Dialog Dcom.example.kpitrackerapp.ui.AddEditProgressDialogFragment.Companion  Boolean /com.example.kpitrackerapp.ui.CreateUserActivity  Bundle /com.example.kpitrackerapp.ui.CreateUserActivity  Boolean 0com.example.kpitrackerapp.ui.ExcelImportActivity  Bundle 0com.example.kpitrackerapp.ui.ExcelImportActivity  MenuItem 0com.example.kpitrackerapp.ui.ExcelImportActivity  Boolean :com.example.kpitrackerapp.ui.ExcelImportActivity.Companion  Bundle :com.example.kpitrackerapp.ui.ExcelImportActivity.Companion  MenuItem :com.example.kpitrackerapp.ui.ExcelImportActivity.Companion  Bundle 0com.example.kpitrackerapp.ui.ExcelReviewActivity  MenuItem 0com.example.kpitrackerapp.ui.ExcelReviewActivity  Bundle :com.example.kpitrackerapp.ui.ExcelReviewActivity.Companion  MenuItem :com.example.kpitrackerapp.ui.ExcelReviewActivity.Companion  Bundle .com.example.kpitrackerapp.ui.KpiDetailActivity  Float .com.example.kpitrackerapp.ui.KpiDetailActivity  Menu .com.example.kpitrackerapp.ui.KpiDetailActivity  MenuItem .com.example.kpitrackerapp.ui.KpiDetailActivity  Bundle 8com.example.kpitrackerapp.ui.KpiDetailActivity.Companion  Float 8com.example.kpitrackerapp.ui.KpiDetailActivity.Companion  Menu 8com.example.kpitrackerapp.ui.KpiDetailActivity.Companion  MenuItem 8com.example.kpitrackerapp.ui.KpiDetailActivity.Companion  Float Ecom.example.kpitrackerapp.ui.KpiDetailActivity.DateAxisValueFormatter  String Ecom.example.kpitrackerapp.ui.KpiDetailActivity.DateAxisValueFormatter  Bundle *com.example.kpitrackerapp.ui.LoginActivity  Boolean 1com.example.kpitrackerapp.ui.ModernReportActivity  Bundle 1com.example.kpitrackerapp.ui.ModernReportActivity  Boolean ;com.example.kpitrackerapp.ui.ModernReportActivity.Companion  Bundle ;com.example.kpitrackerapp.ui.ModernReportActivity.Companion  Bundle (com.example.kpitrackerapp.ui.OcrActivity  Bundle 2com.example.kpitrackerapp.ui.OcrActivity.Companion  KpiWithProgress 1com.example.kpitrackerapp.ui.OnKpiActionsListener  View 1com.example.kpitrackerapp.ui.OnKpiActionsListener  Int 9com.example.kpitrackerapp.ui.OnUserSummaryActionsListener  UserSummaryItem 9com.example.kpitrackerapp.ui.OnUserSummaryActionsListener  View 9com.example.kpitrackerapp.ui.OnUserSummaryActionsListener  Boolean +com.example.kpitrackerapp.ui.ReportActivity  Bundle +com.example.kpitrackerapp.ui.ReportActivity  Boolean 5com.example.kpitrackerapp.ui.ReportActivity.Companion  Bundle 5com.example.kpitrackerapp.ui.ReportActivity.Companion  Boolean 7com.example.kpitrackerapp.ui.SearchEditProgressActivity  Bundle 7com.example.kpitrackerapp.ui.SearchEditProgressActivity  MenuItem 7com.example.kpitrackerapp.ui.SearchEditProgressActivity  Boolean Acom.example.kpitrackerapp.ui.SearchEditProgressActivity.Companion  Bundle Acom.example.kpitrackerapp.ui.SearchEditProgressActivity.Companion  MenuItem Acom.example.kpitrackerapp.ui.SearchEditProgressActivity.Companion  Bundle 0com.example.kpitrackerapp.ui.UserKpiListActivity  KpiWithProgress 0com.example.kpitrackerapp.ui.UserKpiListActivity  MenuItem 0com.example.kpitrackerapp.ui.UserKpiListActivity  View 0com.example.kpitrackerapp.ui.UserKpiListActivity  Bundle :com.example.kpitrackerapp.ui.UserKpiListActivity.Companion  KpiWithProgress :com.example.kpitrackerapp.ui.UserKpiListActivity.Companion  MenuItem :com.example.kpitrackerapp.ui.UserKpiListActivity.Companion  View :com.example.kpitrackerapp.ui.UserKpiListActivity.Companion  AlarmClockManager com.example.kpitrackerapp.utils  Float 5com.github.mikephil.charting.formatter.ValueFormatter  String 5com.github.mikephil.charting.formatter.ValueFormatter  ActivityAddTaskModernBinding android.app.Activity  ActivityAddTaskModernBinding android.content.Context  ActivityAddTaskModernBinding android.content.ContextWrapper  ActivityAddTaskModernBinding  android.view.ContextThemeWrapper  ActivityAddTaskModernBinding #androidx.activity.ComponentActivity  ActivityAddTaskModernBinding (androidx.appcompat.app.AppCompatActivity  ActivityAddTaskModernBinding #androidx.core.app.ComponentActivity  ActivityAddTaskModernBinding &androidx.fragment.app.FragmentActivity  ActivityAddTaskModernBinding com.example.kpitrackerapp.ui  ActivityAddTaskModernBinding 2com.example.kpitrackerapp.ui.ModernAddTaskActivity  AlarmClockManager 2com.example.kpitrackerapp.ui.ModernAddTaskActivity  String 2com.example.kpitrackerapp.ui.ModernAddTaskActivity  
TaskViewModel 2com.example.kpitrackerapp.ui.ModernAddTaskActivity  ActivityAddTaskModernBinding 	java.lang  ActivityAddTaskModernBinding 	java.util  ActivityAddTaskModernBinding kotlin  ActivityAddTaskModernBinding kotlin.annotation  ActivityAddTaskModernBinding kotlin.collections  ActivityAddTaskModernBinding kotlin.comparisons  ActivityAddTaskModernBinding 	kotlin.io  ActivityAddTaskModernBinding 
kotlin.jvm  ActivityAddTaskModernBinding 
kotlin.ranges  ActivityAddTaskModernBinding kotlin.sequences  ActivityAddTaskModernBinding kotlin.text  ActivityPomodoroTimerBinding android.app.Activity  CountDownTimer android.app.Activity  Long android.app.Activity  MediaPlayer android.app.Activity  ActivityPomodoroTimerBinding android.content.Context  CountDownTimer android.content.Context  Long android.content.Context  MediaPlayer android.content.Context  ActivityPomodoroTimerBinding android.content.ContextWrapper  CountDownTimer android.content.ContextWrapper  Long android.content.ContextWrapper  MediaPlayer android.content.ContextWrapper  MediaPlayer 
android.media  CountDownTimer 
android.os  ActivityPomodoroTimerBinding  android.view.ContextThemeWrapper  CountDownTimer  android.view.ContextThemeWrapper  Long  android.view.ContextThemeWrapper  MediaPlayer  android.view.ContextThemeWrapper  ActivityPomodoroTimerBinding #androidx.activity.ComponentActivity  CountDownTimer #androidx.activity.ComponentActivity  Long #androidx.activity.ComponentActivity  MediaPlayer #androidx.activity.ComponentActivity  ActivityPomodoroTimerBinding (androidx.appcompat.app.AppCompatActivity  CountDownTimer (androidx.appcompat.app.AppCompatActivity  Long (androidx.appcompat.app.AppCompatActivity  MediaPlayer (androidx.appcompat.app.AppCompatActivity  ActivityPomodoroTimerBinding #androidx.core.app.ComponentActivity  CountDownTimer #androidx.core.app.ComponentActivity  Long #androidx.core.app.ComponentActivity  MediaPlayer #androidx.core.app.ComponentActivity  ActivityPomodoroTimerBinding &androidx.fragment.app.FragmentActivity  CountDownTimer &androidx.fragment.app.FragmentActivity  Long &androidx.fragment.app.FragmentActivity  MediaPlayer &androidx.fragment.app.FragmentActivity  ActivityPomodoroTimerBinding com.example.kpitrackerapp.ui  Long com.example.kpitrackerapp.ui  Bundle 2com.example.kpitrackerapp.ui.ModernAddTaskActivity  ActivityPomodoroTimerBinding 2com.example.kpitrackerapp.ui.PomodoroTimerActivity  CountDownTimer 2com.example.kpitrackerapp.ui.PomodoroTimerActivity  Int 2com.example.kpitrackerapp.ui.PomodoroTimerActivity  Long 2com.example.kpitrackerapp.ui.PomodoroTimerActivity  MediaPlayer 2com.example.kpitrackerapp.ui.PomodoroTimerActivity  String 2com.example.kpitrackerapp.ui.PomodoroTimerActivity  ActivityPomodoroTimerBinding 	java.lang  ActivityPomodoroTimerBinding kotlin  ActivityPomodoroTimerBinding kotlin.annotation  ActivityPomodoroTimerBinding kotlin.collections  ActivityPomodoroTimerBinding kotlin.comparisons  ActivityPomodoroTimerBinding 	kotlin.io  ActivityPomodoroTimerBinding 
kotlin.jvm  ActivityPomodoroTimerBinding 
kotlin.ranges  ActivityPomodoroTimerBinding kotlin.sequences  ActivityPomodoroTimerBinding kotlin.text  Boolean 2com.example.kpitrackerapp.ui.PomodoroTimerActivity  Bundle 2com.example.kpitrackerapp.ui.PomodoroTimerActivity  SearchResultsAdapter "com.example.kpitrackerapp.adapters  Int 7com.example.kpitrackerapp.adapters.SearchResultsAdapter  SearchResultViewHolder 7com.example.kpitrackerapp.adapters.SearchResultsAdapter  	ViewGroup 7com.example.kpitrackerapp.adapters.SearchResultsAdapter  ActivityDateConverterBinding android.app.Activity  ActivityDateConverterBinding android.content.Context  ActivityDateConverterBinding android.content.ContextWrapper  ActivityDateConverterBinding  android.view.ContextThemeWrapper  ActivityDateConverterBinding #androidx.activity.ComponentActivity  ActivityDateConverterBinding (androidx.appcompat.app.AppCompatActivity  ActivityDateConverterBinding #androidx.core.app.ComponentActivity  ActivityDateConverterBinding &androidx.fragment.app.FragmentActivity  ActivityDateConverterBinding com.example.kpitrackerapp.ui  ActivityDateConverterBinding 2com.example.kpitrackerapp.ui.DateConverterActivity  ActivityDateConverterBinding 	java.lang  ActivityDateConverterBinding 	java.util  ActivityDateConverterBinding kotlin  ActivityDateConverterBinding kotlin.annotation  ActivityDateConverterBinding kotlin.collections  ActivityDateConverterBinding kotlin.comparisons  ActivityDateConverterBinding 	kotlin.io  ActivityDateConverterBinding 
kotlin.jvm  ActivityDateConverterBinding 
kotlin.ranges  ActivityDateConverterBinding kotlin.sequences  ActivityDateConverterBinding kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 