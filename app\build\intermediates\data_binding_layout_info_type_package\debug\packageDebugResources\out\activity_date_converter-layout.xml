<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_date_converter" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_date_converter.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_date_converter_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="721" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="10" startOffset="4" endLine="27" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="18" startOffset="8" endLine="25" endOffset="46"/></Target><Target id="@+id/toolsSpinner" view="Spinner"><Expressions/><location startLine="58" startOffset="16" endLine="65" endOffset="52"/></Target><Target id="@+id/shiftCalculatorSection" view="LinearLayout"><Expressions/><location startLine="70" startOffset="12" endLine="267" endOffset="26"/></Target><Target id="@+id/hoursInput" view="EditText"><Expressions/><location startLine="107" startOffset="20" endLine="117" endOffset="48"/></Target><Target id="@+id/startTimeInput" view="EditText"><Expressions/><location startLine="137" startOffset="20" endLine="147" endOffset="48"/></Target><Target id="@+id/amPmSpinner" view="Spinner"><Expressions/><location startLine="149" startOffset="20" endLine="154" endOffset="58"/></Target><Target id="@+id/calculateShiftButton" view="Button"><Expressions/><location startLine="159" startOffset="16" endLine="168" endOffset="56"/></Target><Target id="@+id/shiftResultSection" view="LinearLayout"><Expressions/><location startLine="171" startOffset="16" endLine="265" endOffset="30"/></Target><Target id="@+id/resultStartTime" view="TextView"><Expressions/><location startLine="204" startOffset="24" endLine="211" endOffset="61"/></Target><Target id="@+id/resultEndTime" view="TextView"><Expressions/><location startLine="229" startOffset="24" endLine="236" endOffset="61"/></Target><Target id="@+id/resultTotalHours" view="TextView"><Expressions/><location startLine="253" startOffset="24" endLine="261" endOffset="61"/></Target><Target id="@+id/conversionTypeGroup" view="RadioGroup"><Expressions/><location startLine="280" startOffset="12" endLine="309" endOffset="24"/></Target><Target id="@+id/radioMiladiToHijri" view="RadioButton"><Expressions/><location startLine="288" startOffset="16" endLine="297" endOffset="53"/></Target><Target id="@+id/radioHijriToMiladi" view="RadioButton"><Expressions/><location startLine="299" startOffset="16" endLine="307" endOffset="50"/></Target><Target id="@+id/inputModeLayout" view="LinearLayout"><Expressions/><location startLine="316" startOffset="12" endLine="412" endOffset="26"/></Target><Target id="@+id/dayInput" view="EditText"><Expressions/><location startLine="340" startOffset="20" endLine="349" endOffset="48"/></Target><Target id="@+id/monthInput" view="EditText"><Expressions/><location startLine="370" startOffset="20" endLine="379" endOffset="48"/></Target><Target id="@+id/yearInput" view="EditText"><Expressions/><location startLine="399" startOffset="20" endLine="408" endOffset="48"/></Target><Target id="@+id/convertButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="415" startOffset="12" endLine="425" endOffset="52"/></Target><Target id="@+id/resultMiladiDate" view="TextView"><Expressions/><location startLine="459" startOffset="24" endLine="468" endOffset="63"/></Target><Target id="@+id/resultHijriDate" view="TextView"><Expressions/><location startLine="493" startOffset="24" endLine="502" endOffset="63"/></Target><Target id="@+id/resultDayName" view="TextView"><Expressions/><location startLine="528" startOffset="24" endLine="537" endOffset="63"/></Target><Target id="@+id/resultHijriMonth" view="TextView"><Expressions/><location startLine="562" startOffset="24" endLine="571" endOffset="63"/></Target><Target id="@+id/resultMiladiMonth" view="TextView"><Expressions/><location startLine="597" startOffset="24" endLine="606" endOffset="63"/></Target><Target id="@+id/resultSyrianiMonth" view="TextView"><Expressions/><location startLine="631" startOffset="24" endLine="640" endOffset="63"/></Target><Target id="@+id/resultHijriMonthDays" view="TextView"><Expressions/><location startLine="666" startOffset="24" endLine="675" endOffset="63"/></Target><Target id="@+id/resultMiladiMonthDays" view="TextView"><Expressions/><location startLine="700" startOffset="24" endLine="709" endOffset="63"/></Target></Targets></Layout>