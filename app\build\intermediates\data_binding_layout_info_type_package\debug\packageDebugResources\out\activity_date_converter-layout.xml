<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_date_converter" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_date_converter.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_date_converter_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="523" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="10" startOffset="4" endLine="27" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="18" startOffset="8" endLine="25" endOffset="46"/></Target><Target id="@+id/conversionTypeGroup" view="RadioGroup"><Expressions/><location startLine="52" startOffset="12" endLine="78" endOffset="24"/></Target><Target id="@+id/radioMiladiToHijri" view="RadioButton"><Expressions/><location startLine="59" startOffset="16" endLine="67" endOffset="50"/></Target><Target id="@+id/radioHijriToMiladi" view="RadioButton"><Expressions/><location startLine="69" startOffset="16" endLine="76" endOffset="50"/></Target><Target id="@+id/dayCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="87" startOffset="16" endLine="127" endOffset="67"/></Target><Target id="@+id/dayText" view="TextView"><Expressions/><location startLine="116" startOffset="24" endLine="123" endOffset="57"/></Target><Target id="@+id/monthCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="129" startOffset="16" endLine="170" endOffset="67"/></Target><Target id="@+id/monthText" view="TextView"><Expressions/><location startLine="159" startOffset="24" endLine="166" endOffset="57"/></Target><Target id="@+id/yearCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="172" startOffset="16" endLine="212" endOffset="67"/></Target><Target id="@+id/yearText" view="TextView"><Expressions/><location startLine="201" startOffset="24" endLine="208" endOffset="57"/></Target><Target id="@+id/convertButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="217" startOffset="12" endLine="227" endOffset="52"/></Target><Target id="@+id/resultMiladiDate" view="TextView"><Expressions/><location startLine="261" startOffset="24" endLine="270" endOffset="63"/></Target><Target id="@+id/resultHijriDate" view="TextView"><Expressions/><location startLine="295" startOffset="24" endLine="304" endOffset="63"/></Target><Target id="@+id/resultDayName" view="TextView"><Expressions/><location startLine="330" startOffset="24" endLine="339" endOffset="63"/></Target><Target id="@+id/resultHijriMonth" view="TextView"><Expressions/><location startLine="364" startOffset="24" endLine="373" endOffset="63"/></Target><Target id="@+id/resultMiladiMonth" view="TextView"><Expressions/><location startLine="399" startOffset="24" endLine="408" endOffset="63"/></Target><Target id="@+id/resultSyrianiMonth" view="TextView"><Expressions/><location startLine="433" startOffset="24" endLine="442" endOffset="63"/></Target><Target id="@+id/resultHijriMonthDays" view="TextView"><Expressions/><location startLine="468" startOffset="24" endLine="477" endOffset="63"/></Target><Target id="@+id/resultMiladiMonthDays" view="TextView"><Expressions/><location startLine="502" startOffset="24" endLine="511" endOffset="63"/></Target></Targets></Layout>