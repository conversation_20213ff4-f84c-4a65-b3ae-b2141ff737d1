<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="am_pm_array">
        <item>ص</item>
        <item>م</item>
    </string-array>
    <string-array name="owner_type_options_add_edit">
        <item>@string/owner_type_user</item>
        <item>@string/owner_type_department</item>
        <item>@string/owner_type_company</item>
        <item>@string/owner_type_other</item>
    </string-array>
    <string-array name="reminder_options">
        <item>@string/reminder_option_no_reminder</item>
        <item>@string/reminder_option_on_due_date</item>
        <item>@string/reminder_option_1_day</item>
        <item>@string/reminder_option_2_days</item>
        <item>@string/reminder_option_3_days</item>
        <item>@string/reminder_option_1_week</item>
    </string-array>
    <string-array name="tools_array">
        <item>اختر أداة</item>
        <item>shift hour calculator</item>
        <item>محول التاريخ</item>
    </string-array>
    <color name="admin_color">#FF9C27B0</color>
    <color name="ai_suggestion_background">#FFF3E5F5</color>
    <color name="background">#FFF5F5F5</color>
    <color name="background_color">#FFF5F5F5</color>
    <color name="background_light">#FFF5F5F5</color>
    <color name="black">#FF000000</color>
    <color name="blue">#FF0000FF</color>
    <color name="blue_500">#FF2196F3</color>
    <color name="card_background_default">#FFFFFFFF</color>
    <color name="category_education">#FF9C27B0</color>
    <color name="category_family">#FFE91E63</color>
    <color name="category_finance">#FFF44336</color>
    <color name="category_health">#FFFF9800</color>
    <color name="category_personal">#FF4CAF50</color>
    <color name="category_shopping">#FFFF5722</color>
    <color name="category_travel">#FF00BCD4</color>
    <color name="category_work">#FF2196F3</color>
    <color name="chart_achieved_color">#FF42A5F5</color>
    <color name="chart_fill_purple_light">#406200EE</color>
    <color name="chart_line_purple">#FFAB47BC</color>
    <color name="chart_target_color">#FFEF5350</color>
    <color name="chat_background">#FFF5F5F5</color>
    <color name="chip_background_default">#FFE0E0E0</color>
    <color name="dark_gray">#A9A9A9</color>
    <color name="dark_green">#FF388E3C</color>
    <color name="dark_red">#FFD32F2F</color>
    <color name="dark_yellow">#FFFBC02D</color>
    <color name="date_header_background">#40000000</color>
    <color name="default_kpi_card_bottom_gradient">#FF3700B3</color>
    <color name="default_kpi_card_color">#FFFFFFFF</color>
    <color name="default_kpi_card_top_gradient">#FF6200EE</color>
    <color name="doctor_color_1">#673AB7</color>
    <color name="doctor_color_1_light">#EDE7F6</color>
    <color name="doctor_color_1_lighter">#F3F0F9</color>
    <color name="doctor_color_2">#2196F3</color>
    <color name="doctor_color_2_light">#E3F2FD</color>
    <color name="doctor_color_2_lighter">#EFF8FE</color>
    <color name="doctor_color_3">#4CAF50</color>
    <color name="doctor_color_3_light">#E8F5E9</color>
    <color name="doctor_color_3_lighter">#F1FAF1</color>
    <color name="doctor_color_4">#FF9800</color>
    <color name="doctor_color_4_light">#FFF3E0</color>
    <color name="doctor_color_4_lighter">#FFF8ED</color>
    <color name="doctor_color_5">#E91E63</color>
    <color name="doctor_color_5_light">#FCE4EC</color>
    <color name="doctor_color_5_lighter">#FDF2F6</color>
    <color name="edit_indicator">#FF4CAF50</color>
    <color name="energy_high">#FF4CAF50</color>
    <color name="energy_low">#FFFF9800</color>
    <color name="energy_medium">#FF2196F3</color>
    <color name="fab_color">#FF6200EE</color>
    <color name="gray_200">#FFEEEEEE</color>
    <color name="gray_300">#FFE0E0E0</color>
    <color name="gray_500">#FF9E9E9E</color>
    <color name="gray_600">#FF757575</color>
    <color name="gray_700">#FF616161</color>
    <color name="gray_800">#FF424242</color>
    <color name="green">#FF00FF00</color>
    <color name="green_500">#FF4CAF50</color>
    <color name="header_gradient_end">#FF3700B3</color>
    <color name="header_gradient_start">#FF6200EE</color>
    <color name="highlight_yellow">#FFFFFF00</color>
    <color name="icon_tint">#FF757575</color>
    <color name="importance_critical">#FFE91E63</color>
    <color name="importance_high">#FF9C27B0</color>
    <color name="importance_low">#FF9E9E9E</color>
    <color name="importance_medium">#FF2196F3</color>
    <color name="kpi_concern">#FFFF6F00</color>
    <color name="kpi_good">#FF4CAF50</color>
    <color name="kpi_share_background">#FF2196F3</color>
    <color name="kpi_warning">#FFFFC107</color>
    <color name="light_blue_200">#FF81D4FA</color>
    <color name="light_blue_50">#FFE1F5FE</color>
    <color name="light_blue_600">#FF039BE5</color>
    <color name="light_blue_900">#FF01579B</color>
    <color name="light_gray">#D3D3D3</color>
    <color name="light_green">#FFA5D6A7</color>
    <color name="light_red">#FFEF9A9A</color>
    <color name="light_yellow">#FFFFF59D</color>
    <color name="low_priority_color">#FF4CAF50</color>
    <color name="matrix_toggle_background">#FFF0F0F0</color>
    <color name="medium_priority_color">#FFFF9800</color>
    <color name="message_input_background">#FFFFFFFF</color>
    <color name="message_read_color">#FF4CAF50</color>
    <color name="not_urgent_important_bg">#FF1976D2</color>
    <color name="not_urgent_not_important_bg">#FF757575</color>
    <color name="notification_admin_bg">#FFF3E5F5</color>
    <color name="notification_kpi_bg">#FFE8F5E8</color>
    <color name="notification_reminder_bg">#FFFEF7E0</color>
    <color name="notification_system_bg">#FFE3F2FD</color>
    <color name="online_color">#FF4CAF50</color>
    <color name="orange">#FFFFA500</color>
    <color name="orange_500">#FFFF9800</color>
    <color name="overdue_chip_background">#FFFFE0E0</color>
    <color name="overdue_color">#FFF44336</color>
    <color name="owner_card_border">#FF6200EE</color>
    <color name="performance_average">#FFC107</color>
    <color name="performance_below_average">#FF9800</color>
    <color name="performance_excellent">#4CAF50</color>
    <color name="performance_good">#8BC34A</color>
    <color name="performance_poor">#F44336</color>
    <color name="primary_color">#FF6200EE</color>
    <color name="primary_dark">#FF3700B3</color>
    <color name="priority_high">#FFFF5722</color>
    <color name="priority_low">#FF4CAF50</color>
    <color name="priority_medium">#FFFF9800</color>
    <color name="priority_urgent">#FFF44336</color>
    <color name="progress_background">#FFE0E0E0</color>
    <color name="progress_color_default">#FF6200EE</color>
    <color name="progress_indicator_blue">#FF2196F3</color>
    <color name="progress_indicator_track">#FFBDBDBD</color>
    <color name="progress_share_background">#FF4CAF50</color>
    <color name="progress_text">#FF4CAF50</color>
    <color name="progress_tint">#FF4CAF50</color>
    <color name="progress_track_color">#FFBDBDBD</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="purple_accent">#FF9C27B0</color>
    <color name="purple_button_text">#FFFFFFFF</color>
    <color name="read_only_overlay">#40000000</color>
    <color name="received_message_background">#FFFFFFFF</color>
    <color name="red">#FFFF0000</color>
    <color name="red_500">#FFF44336</color>
    <color name="reply_background">#20000000</color>
    <color name="screen_background_light_blue">#FFE3F2FD</color>
    <color name="search_background">#FFFFFFFF</color>
    <color name="sent_message_background">#FF6200EE</color>
    <color name="soft_lavender_background">#FFE6E6FA</color>
    <color name="status_green">#FF4CAF50</color>
    <color name="status_orange">#FFFF9800</color>
    <color name="status_red">#FFF44336</color>
    <color name="success_color">#FF4CAF50</color>
    <color name="summary_card_bottom_yellow">#FFFFF59D</color>
    <color name="summary_card_image_background">#00FFFFFF</color>
    <color name="summary_card_image_tint">#FFFFFFFF</color>
    <color name="summary_card_top_red">#FFEF9A9A</color>
    <color name="summary_card_username_text">#FFFFFFFF</color>
    <color name="super_admin_color">#FFE91E63</color>
    <color name="surface">#FFFFFFFF</color>
    <color name="system_message_background">#40757575</color>
    <color name="task_color_blue">#FF2196F3</color>
    <color name="task_color_green">#FF4CAF50</color>
    <color name="task_color_orange">#FFFF9800</color>
    <color name="task_color_purple">#FF9C27B0</color>
    <color name="task_color_red">#FFF44336</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_hint">#FF9E9E9E</color>
    <color name="text_primary">#FF212121</color>
    <color name="text_secondary">#FF757575</color>
    <color name="today_color">#FF2196F3</color>
    <color name="unread_badge_color">#FFF44336</color>
    <color name="urgent_chip_background">#FFFFE0E0</color>
    <color name="urgent_color">#FFF44336</color>
    <color name="urgent_important_bg">#FFD32F2F</color>
    <color name="urgent_not_important_bg">#FFFF9800</color>
    <color name="user_color">#FF607D8B</color>
    <color name="view_only_indicator">#FFFF9800</color>
    <color name="whatsapp_green">#FF25D366</color>
    <color name="white">#FFFFFFFF</color>
    <color name="yellow">#FFFFFF00</color>
    <dimen name="card_border_width">1dp</dimen>
    <dimen name="card_corner_radius">16dp</dimen>
    <dimen name="card_elevation">4dp</dimen>
    <dimen name="card_elevation_pressed">12dp</dimen>
    <dimen name="color_swatch_size">36dp</dimen>
    <dimen name="color_swatch_stroke_width">2dp</dimen>
    <dimen name="fab_margin">16dp</dimen>
    <item name="sortChartButton" type="id"/>
    <item name="star_animator_tag" type="id"/>
    <string name="achieved_value_label">Achieved:</string>
    <string name="action_add_progress">Add Progress</string>
    <string name="action_change_user_card_color">Change User Card Color</string>
    <string name="action_clear_month_progress">Clear Month\'s Progress</string>
    <string name="action_clear_progress">Clear Progress</string>
    <string name="action_customize_colors">Customize Colors</string>
    <string name="action_delete">Delete</string>
    <string name="action_delete_kpi">Delete KPI</string>
    <string name="action_delete_user">Delete User</string>
    <string name="action_delete_user_kpi">Delete User KPI Assignment</string>
    <string name="action_edit">Edit</string>
    <string name="action_edit_kpi">Edit KPI</string>
    <string name="action_edit_user">Edit User</string>
    <string name="action_excel_import">Import from Excel</string>
    <string name="action_expiry_management">Expiry Management</string>
    <string name="action_search_edit_progress">Search/Edit Progress</string>
    <string name="action_share_user_card_image">Share User Card Image</string>
    <string name="action_view_details">View Details</string>
    <string name="action_view_report">Interactive Performance Report</string>
    <string name="add_kpi">Add KPI</string>
    <string name="add_kpi_title">Add KPI</string>
    <string name="add_new_user_button_label">Add New User</string>
    <string name="add_progress_button_text">Add Progress</string>
    <string name="add_progress_dialog_title">Add Progress</string>
    <string name="add_progress_entry_button">Add Progress Entry</string>
    <string name="add_progress_title">Add Progress</string>
    <string name="add_task_button_text">Add</string>
    <string name="add_task_title">Add New Task</string>
    <string name="add_user_button_text">Add User</string>
    <string name="aggregation_average">Calculate Average (for selected user)</string>
    <string name="aggregation_individual">Import Individually</string>
    <string name="aggregation_type_label">Aggregation Type:</string>
    <string name="all_kpis_title">All KPIs (Aggregated)</string>
    <string name="all_users_excel_filter">All Users (from file)</string>
    <string name="all_users_report_option">All Users (Aggregated)</string>
    <string name="annual_progress_label_short">Year: %1$d%%</string>
    <string name="app_name">KPI Tracker Pro</string>
    <string name="assign_to_kpi_label">Assign to KPI:</string>
    <string name="change_color">Change Color</string>
    <string name="clear_month_progress_confirmation_message">TODO</string>
    <string name="clear_month_progress_confirmation_title">TODO</string>
    <string name="clear_month_progress_select_month_message">TODO</string>
    <string name="clear_month_progress_success">TODO</string>
    <string name="clear_progress_confirmation_message">TODO</string>
    <string name="clear_progress_confirmation_title">TODO</string>
    <string name="clear_progress_success">TODO</string>
    <string name="color_picker_title_individual_kpi_card">Individual KPI Card Color</string>
    <string name="color_picker_title_summary_card_bottom">Summary Card Bottom Color</string>
    <string name="color_picker_title_summary_card_top">Summary Card Top Color</string>
    <string name="color_reset_success">TODO</string>
    <string name="color_set_success">TODO</string>
    <string name="colors_updated_successfully">Card colors updated.</string>
    <string name="column_mapping_title">Map Excel Columns</string>
    <string name="confirm_clear_month_progress_message">Are you sure you want to clear progress for %1$s for this KPI for this user?</string>
    <string name="confirm_clear_progress_message">Are you sure you want to clear all progress entries for this KPI for this user?</string>
    <string name="confirm_clear_progress_title">Confirm Clear Progress</string>
    <string name="confirm_delete_kpi_message">Are you sure you want to delete this KPI and all its progress?</string>
    <string name="confirm_delete_kpi_title">Confirm Delete</string>
    <string name="confirm_delete_ocr_item_message">Are you sure you want to delete this OCR item?</string>
    <string name="confirm_delete_progress_entry_message">Are you sure you want to delete this progress entry?</string>
    <string name="confirm_delete_progress_entry_title">Confirm Delete Entry</string>
    <string name="confirm_delete_task_message">Are you sure you want to delete task \'%1$s\'?</string>
    <string name="confirm_delete_task_title">Confirm Delete Task</string>
    <string name="confirm_delete_user_message">Are you sure you want to delete user \'%1$s\' and all their KPI assignments? This action cannot be undone.</string>
    <string name="confirm_delete_user_title">Confirm Delete User</string>
    <string name="confirm_import_button">Confirm Import</string>
    <string name="create_new_user_dialog_title">Add New User</string>
    <string name="current_label">Current:</string>
    <string name="current_progress_label">Current Progress:</string>
    <string name="days_since_last_update_label">Days Since Update:</string>
    <string name="default_web_client_id" translatable="false">123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com</string>
    <string name="delete_entry_button">Delete Entry</string>
    <string name="delete_entry_confirmation_message">TODO</string>
    <string name="delete_entry_confirmation_title">TODO</string>
    <string name="delete_kpi_confirmation_message">TODO</string>
    <string name="delete_kpi_confirmation_title">TODO</string>
    <string name="delete_kpi_menu_item">Delete KPI</string>
    <string name="delete_ocr_item_title">Delete OCR Item</string>
    <string name="delete_progress_entry_menu_item">Delete Entry</string>
    <string name="dialog_cancel">Cancel</string>
    <string name="dialog_color_picker_pick_button">Pick</string>
    <string name="dialog_color_picker_save_button">Save Colors</string>
    <string name="dialog_color_picker_title">TODO</string>
    <string name="dialog_confirm">TODO</string>
    <string name="dialog_kpi_action_change_color">Change Color</string>
    <string name="dialog_kpi_action_delete">Delete</string>
    <string name="dialog_kpi_action_duplicate_copy">Duplicate/Copy</string>
    <string name="dialog_kpi_action_edit">Edit KPI</string>
    <string name="dialog_kpi_action_reset_color">Reset Color</string>
    <string name="dialog_kpi_action_share_card">Share Card</string>
    <string name="dialog_kpi_action_title">KPI Actions</string>
    <string name="dialog_ok">OK</string>
    <string name="dialog_select_card_colors_title">Select Card Colors</string>
    <string name="duplicate_kpi_menu_item">Duplicate KPI</string>
    <string name="edit_kpi_menu_item">Edit KPI</string>
    <string name="edit_kpi_title">Edit KPI</string>
    <string name="edit_ocr_item_title">Edit OCR Item</string>
    <string name="edit_progress_dialog_title">TODO</string>
    <string name="edit_progress_entry_menu_item">Edit Entry</string>
    <string name="edit_progress_title">Edit Progress</string>
    <string name="edit_task_dialog_title">Edit Task</string>
    <string name="edit_user_title">Edit User</string>
    <string name="enter_new_user_name_hint">Enter New User Name</string>
    <string name="error_at_least_one_user">Please select at least one user.</string>
    <string name="error_average_for_all_users">Cannot calculate average for "All Users". Select a specific user or import individually.</string>
    <string name="error_clearing_month_progress">TODO</string>
    <string name="error_clearing_progress">Error clearing progress.</string>
    <string name="error_column_selection">Please select columns for Value and Date.</string>
    <string name="error_copying_image">Error copying image</string>
    <string name="error_date_required">Date is required</string>
    <string name="error_deleting_kpi">TODO</string>
    <string name="error_excel_processing">Error processing Excel file.</string>
    <string name="error_generating_report">Error generating report.</string>
    <string name="error_invalid_expiration_date">Invalid expiration date.</string>
    <string name="error_invalid_target">Invalid target value</string>
    <string name="error_invalid_value">Invalid value</string>
    <string name="error_kpi_not_found">KPI not found for editing.</string>
    <string name="error_kpi_or_user_not_found">TODO</string>
    <string name="error_name_required">Name is required</string>
    <string name="error_no_sheets_found">No sheets found in the Excel file.</string>
    <string name="error_ocr_processing">Error processing image with OCR.</string>
    <string name="error_reading_sheet_names">Error reading sheet names from Excel file.</string>
    <string name="error_saving_kpi">Error saving KPI</string>
    <string name="error_saving_progress">Error saving progress</string>
    <string name="error_saving_user">Error saving user.</string>
    <string name="error_select_kpi_for_ocr">Please select a KPI to assign the data to.</string>
    <string name="error_target_required">Target is required</string>
    <string name="error_updating_colors">Error updating colors.</string>
    <string name="error_updating_progress">TODO</string>
    <string name="error_user_name_required">User name is required.</string>
    <string name="error_value_required">Value is required</string>
    <string name="excel_data_import_failed">Failed to import Excel data.</string>
    <string name="excel_data_imported_success">Excel data imported successfully.</string>
    <string name="excel_import_instructions">Select an Excel file (.xlsx). Ensure it has columns for Value (numeric), Date (e.g., YYYY-MM-DD), and optionally User Identifier. Review and edit before importing.</string>
    <string name="excel_import_title">Excel Import</string>
    <string name="excel_review_title">Review Excel Data</string>
    <string name="existing_user_added_to_selection_message">Existing user added to selection.</string>
    <string name="expiry_notification_channel_description">Reminders for KPIs nearing their implicit monthly/quarterly/annual review or target dates.</string>
    <string name="expiry_notification_channel_name">KPI Expiry Reminders</string>
    <string name="expiry_return_notification_message">TODO</string>
    <string name="expiry_return_notification_title">TODO</string>
    <string name="filter_all">All</string>
    <string name="filter_all_months">All Months</string>
    <string name="filter_by_month_button_text">Filter by Month</string>
    <string name="filter_by_owner_hint">Filter by Owner</string>
    <string name="filter_by_user_excel_label">Filter by User (from Excel):</string>
    <string name="filter_expiry">Expiry</string>
    <string name="filter_report">Interactive Report</string>
    <string name="filter_task_follow_up">Task follow-up</string>
    <string name="gcm_defaultSenderId" translatable="false">123456789012</string>
    <string name="generate_report_button">Generate Report</string>
    <string name="google_api_key" translatable="false">AIzaSyDemoKeyForKPITrackerApp123456789</string>
    <string name="google_app_id" translatable="false">1:123456789012:android:abcdef1234567890</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyDemoKeyForKPITrackerApp123456789</string>
    <string name="google_storage_bucket" translatable="false">kpi-tracker-app-demo.appspot.com</string>
    <string name="header_row_label">Data starts at row:</string>
    <string name="hide_master_card">Hide master card</string>
    <string name="import_excel_data_button">Import Data</string>
    <string name="import_failed_with_errors">Import failed. %1$d errors occurred.</string>
    <string name="import_ocr_data_button">Import Data</string>
    <string name="import_partially_successful">Import partially successful. %1$d entries imported, %2$d errors.</string>
    <string name="import_successful_count">Imported %1$d out of %2$d entries.</string>
    <string name="kpi_actions_dialog_title">KPI Actions</string>
    <string name="kpi_annual_target_hint">Annual Target</string>
    <string name="kpi_annual_target_label_formatted">TODO</string>
    <string name="kpi_card_context_menu_title">KPI Card Actions</string>
    <string name="kpi_card_item_average_label">Average</string>
    <string name="kpi_card_item_current_label">Current</string>
    <string name="kpi_card_item_current_period_achievement_label">Period Achievement:</string>
    <string name="kpi_card_item_current_period_target_label">Period Target:</string>
    <string name="kpi_card_item_last_update_label">Last Update:</string>
    <string name="kpi_card_item_remaining_to_target_label">Remaining:</string>
    <string name="kpi_card_item_required_daily_label">Daily Req.:</string>
    <string name="kpi_card_item_target_label">Target:</string>
    <string name="kpi_daily_target_hint">Daily Target (Optional)</string>
    <string name="kpi_daily_target_label_formatted">TODO</string>
    <string name="kpi_deleted_success">TODO</string>
    <string name="kpi_description_hint">Description (Optional)</string>
    <string name="kpi_detail_tab_all_time">All Time</string>
    <string name="kpi_detail_tab_current">Current</string>
    <string name="kpi_detail_tab_monthly">Monthly</string>
    <string name="kpi_detail_tab_quarterly">Quarterly</string>
    <string name="kpi_detail_tab_yearly">Yearly</string>
    <string name="kpi_detail_title">KPI Details</string>
    <string name="kpi_duplicated_success">KPI duplicated successfully.</string>
    <string name="kpi_duplication_failed">Failed to duplicate KPI.</string>
    <string name="kpi_expiry_reminder_text_annual">Annual target for \'%1$s\' is approaching. Review progress.</string>
    <string name="kpi_expiry_reminder_text_monthly">Monthly target for \'%1$s\' is approaching. Review progress.</string>
    <string name="kpi_expiry_reminder_text_quarterly">Quarterly target for \'%1$s\' is approaching. Review progress.</string>
    <string name="kpi_expiry_reminder_title">KPI Reminder: %1$s</string>
    <string name="kpi_list_title">KPIs</string>
    <string name="kpi_monthly_target_hint">Monthly Target (Optional)</string>
    <string name="kpi_monthly_target_label_formatted">TODO</string>
    <string name="kpi_name_hint">KPI Name</string>
    <string name="kpi_name_not_editable_editing">KPI name cannot be changed during edit</string>
    <string name="kpi_quarterly_target_hint">Quarterly Target (Optional)</string>
    <string name="kpi_report_title">KPI Report</string>
    <string name="kpi_summary_item_format">%1$s: M %2$d%%, Y %3$d%%</string>
    <string name="kpi_target_hint">Annual Target Value</string>
    <string name="kpi_unit_label">Unit</string>
    <string name="kpis_assigned_label">KPIs Assigned: %d</string>
    <string name="label_bottom_color">Bottom Color:</string>
    <string name="label_individual_color">Card Background:</string>
    <string name="label_top_color">Top Color:</string>
    <string name="last_entry_date_label">TODO</string>
    <string name="main_activity_title">KPI Dashboard</string>
    <string name="manual_user_input_hint">Enter User Name</string>
    <string name="map_date_column_label">Date Column:</string>
    <string name="map_user_column_label">User Identifier Column (Optional):</string>
    <string name="map_value_column_label">Value Column:</string>
    <string name="master_card_no_data">No data available for master card</string>
    <string name="menu_generate_report">Generate Report</string>
    <string name="menu_import_excel">Import from Excel</string>
    <string name="menu_import_ocr">Import from Image (OCR)</string>
    <string name="menu_manage_users">Manage Users</string>
    <string name="menu_search_kpis">Search KPIs</string>
    <string name="menu_settings">Settings</string>
    <string name="month_year_format">MMMM yyyy</string>
    <string name="monthly_progress_label_short">Month: %1$d%%</string>
    <string name="my_tasks_title">My Tasks</string>
    <string name="near_expiry_notification_message">TODO</string>
    <string name="near_expiry_notification_title">TODO</string>
    <string name="no_data_for_report">No data available for the selected criteria.</string>
    <string name="no_data_for_selected_month">TODO</string>
    <string name="no_data_to_import">No data to import after review.</string>
    <string name="no_date_placeholder">No date</string>
    <string name="no_entries_found">TODO</string>
    <string name="no_entries_yet">TODO</string>
    <string name="no_kpis_assigned">No KPIs Assigned</string>
    <string name="no_kpis_assigned_for_master_card">No KPIs assigned to users yet.</string>
    <string name="no_kpis_assigned_to_user">No KPIs assigned to this user.</string>
    <string name="no_kpis_for_this_user">No KPIs found for this user.</string>
    <string name="no_kpis_to_display">No KPIs to display.</string>
    <string name="no_overall_summary_to_display">No overall KPI summary to display.</string>
    <string name="no_progress_entries">No progress entries yet.</string>
    <string name="no_target_set_short">No Target</string>
    <string name="no_text_found_ocr">No text found in image or OCR failed.</string>
    <string name="no_user_summaries_to_display">No user summaries to display.</string>
    <string name="no_users_exist">No users exist. Add users to assign KPIs.</string>
    <string name="no_users_selected_hint">No users selected</string>
    <string name="not_applicable_short">N/A</string>
    <string name="notification_channel_description">Notifications for KPI events like target achievement.</string>
    <string name="notification_channel_name">KPI Notifications</string>
    <string name="ocr_activity_title">OCR Activity</string>
    <string name="ocr_data_import_failed">Failed to import OCR data.</string>
    <string name="ocr_data_imported_success">OCR data imported successfully.</string>
    <string name="ocr_date_label">Date:</string>
    <string name="ocr_import_cancelled">TODO</string>
    <string name="ocr_import_failed">TODO</string>
    <string name="ocr_import_instructions">Select an image containing numerical data, dates, and optional user identifiers. Review and edit the extracted data before importing.</string>
    <string name="ocr_import_title">OCR Import</string>
    <string name="ocr_no_entries_found">TODO</string>
    <string name="ocr_review_title">Review OCR Results</string>
    <string name="ocr_user_label">User (Optional):</string>
    <string name="ocr_value_label">Value:</string>
    <string name="ocr_waiting_for_review">TODO</string>
    <string name="overall_kpi_summary_title">Overall KPI Summary</string>
    <string name="overall_summary">Overall Summary</string>
    <string name="overall_summary_card_title">Overall Summary</string>
    <string name="overall_summary_context_menu_title">Overall Summary Card Actions</string>
    <string name="owner_image_description">Owner Image</string>
    <string name="owner_name_hint">User Name</string>
    <string name="owner_type_company">Company</string>
    <string name="owner_type_department">Department</string>
    <string name="owner_type_hint">Owner Type</string>
    <string name="owner_type_other">Other</string>
    <string name="owner_type_user">User</string>
    <string name="percentage_value_label">Percentage:</string>
    <string name="performance_report_title">Performance Report</string>
    <string name="pref_key_expiry_reminder_notif">expiry_reminder_notification</string>
    <string name="pref_key_target_achieved_notif">target_achieved_notification</string>
    <string name="pref_notifications_title">Notifications</string>
    <string name="pref_summary_expiry_reminder_notif">Receive reminders for KPIs nearing review dates.</string>
    <string name="pref_summary_target_achieved_notif">Receive a notification when a KPI target is met.</string>
    <string name="pref_title_expiry_reminder_notif">Expiry Reminder Notifications</string>
    <string name="pref_title_target_achieved_notif">Target Achieved Notifications</string>
    <string name="preview_data_button">Preview Data</string>
    <string name="progress_cleared_toast">Progress cleared.</string>
    <string name="progress_date_hint">Date</string>
    <string name="progress_entry_deleted">TODO</string>
    <string name="progress_history_title">Progress History</string>
    <string name="progress_saved_success">TODO</string>
    <string name="progress_updated_success">TODO</string>
    <string name="progress_value_hint">Value</string>
    <string name="project_id" translatable="false">kpi-tracker-app-demo</string>
    <string name="remaining_days_label">Days Left:</string>
    <string name="remaining_percentage_label">Remaining %:</string>
    <string name="remaining_value_label">Remaining:</string>
    <string name="reminder_option_1_day">1 day before</string>
    <string name="reminder_option_1_week">1 week before</string>
    <string name="reminder_option_2_days">2 days before</string>
    <string name="reminder_option_3_days">3 days before</string>
    <string name="reminder_option_no_reminder">No reminder</string>
    <string name="reminder_option_on_due_date">On due date</string>
    <string name="report_achieved_label">Achieved</string>
    <string name="report_end_date_label">End Date:</string>
    <string name="report_for_kpi_user_format">Report for %1$s - %2$s</string>
    <string name="report_percentage_label">Percentage</string>
    <string name="report_period_annual">Annual (Range)</string>
    <string name="report_period_daily">Daily</string>
    <string name="report_period_monthly">Monthly</string>
    <string name="report_period_quarterly">Quarterly</string>
    <string name="report_start_date_label">Start Date:</string>
    <string name="report_target_label">Target</string>
    <string name="required_daily_rate_label">Required Daily:</string>
    <string name="reset_to_default">Reset to Default</string>
    <string name="reset_to_default_color">Default</string>
    <string name="reset_to_default_colors_button">Reset to Default</string>
    <string name="review_ocr_results_title">Review OCR Results</string>
    <string name="save_button">Save</string>
    <string name="save_kpi_button">Save KPI</string>
    <string name="save_kpi_button_text">Save KPI</string>
    <string name="save_progress_button_text">Save Progress</string>
    <string name="save_user_button_text">Save User</string>
    <string name="search_edit_progress_title">Search/Edit Progress</string>
    <string name="search_edit_prompt">Search or Edit Prompt</string>
    <string name="search_hint">Search KPIs...</string>
    <string name="search_kpis_title">Search KPIs</string>
    <string name="select_card_gradient_colors">TODO</string>
    <string name="select_date_prompt">TODO</string>
    <string name="select_excel_file">Select Excel File (.xlsx)</string>
    <string name="select_excel_file_button_text">TODO</string>
    <string name="select_image_for_ocr">Select Image for OCR</string>
    <string name="select_kpi_for_ocr_hint">Select KPI</string>
    <string name="select_kpi_for_report">Select KPI:</string>
    <string name="select_month_year_title">Select Month and Year</string>
    <string name="select_owner_image_button">Select Image</string>
    <string name="select_owner_image_button_text">Select Owner Image</string>
    <string name="select_sheet_label">Select Sheet:</string>
    <string name="select_user_for_report">Select User:</string>
    <string name="select_user_hint">Select User</string>
    <string name="select_users_button">Select Users</string>
    <string name="select_users_dialog_title">Select Users</string>
    <string name="selected_users_label">Selected Users:</string>
    <string name="settings_activity_title">Settings</string>
    <string name="show_master_card">Show master card</string>
    <string name="status_completed">Status: Completed</string>
    <string name="status_due_in_days">Status: Due in %1$d day(s)</string>
    <string name="status_due_today">Status: Due Today</string>
    <string name="status_overdue">Status: Overdue</string>
    <string name="summary_detail_annual_format">Annual: %d%%</string>
    <string name="summary_detail_monthly_format">Monthly: %d%%</string>
    <string name="target_achieved_notification_text">You achieved %1$.1f for %2$s (Target: %3$.1f)</string>
    <string name="target_achieved_notification_title">Target Achieved!</string>
    <string name="target_label">Target:</string>
    <string name="task_added_success">Task \'%1$s\' added.</string>
    <string name="task_deleted_success">Task \'%1$s\' deleted.</string>
    <string name="task_expiration_date_hint">Expiration Date</string>
    <string name="task_expiration_time_hint">Expiration Time (Optional)</string>
    <string name="task_item_expires_prefix">Expires: %1$s</string>
    <string name="task_name_hint">Task Name</string>
    <string name="task_notification_channel_description">Notifications for upcoming task deadlines.</string>
    <string name="task_notification_channel_name">Task Reminders</string>
    <string name="task_reminder_days_hint">Reminder (days before)</string>
    <string name="task_updated_success">Task \'%1$s\' updated.</string>
    <string name="title_activity_kpi_list">TODO</string>
    <string name="toggle_master_card">View master card</string>
    <string name="total_annual_achieved_label">Total Annual Achieved: %s</string>
    <string name="total_annual_target_label">Total Annual Target: %s</string>
    <string name="total_monthly_achieved_label">Total Monthly Achieved: %s</string>
    <string name="total_monthly_target_label">Total Monthly Target: %s</string>
    <string name="trend_chart_title">Progress Trend</string>
    <string name="unit_currency">Currency</string>
    <string name="unit_number">Number</string>
    <string name="unit_percentage">Percentage</string>
    <string name="unit_point">Point</string>
    <string name="user_already_selected_error">This user is already selected.</string>
    <string name="user_card_color_bottom_label">Card Bottom Color:</string>
    <string name="user_card_color_top_label">Card Top Color:</string>
    <string name="user_deleted_success">User deleted successfully.</string>
    <string name="user_image_label_manage">User Image (Optional)</string>
    <string name="user_kpi_list_title">User KPI List</string>
    <string name="user_kpi_list_title_prefix">KPIs for</string>
    <string name="user_management_title">Manage Users</string>
    <string name="user_name_cannot_be_empty_error">User name cannot be empty.</string>
    <string name="user_name_hint_manage">User Name</string>
    <string name="user_name_label">User Name</string>
    <string name="user_saved_success">User saved successfully.</string>
    <string name="user_summaries_title">User Summaries</string>
    <string name="user_summary_card_title_prefix">Summary for</string>
    <string name="user_summary_context_menu_title">User Card Actions</string>
    <string name="users_not_loaded_yet">Users not loaded yet, please try again shortly.</string>
    <string name="value_hint">Value</string>
    <string name="view_history_button_text">View History</string>
    <style name="CustomFilterChip" parent="Widget.MaterialComponents.Chip.Filter">
        <item name="android:textSize">12sp</item>
        <item name="chipIconSize">18dp</item>
        <item name="chipIconVisible">true</item>
        <item name="chipMinHeight">40dp</item>
        <item name="chipMinTouchTargetSize">40dp</item>
        <item name="chipStartPadding">8dp</item>
        <item name="chipEndPadding">8dp</item>
        <item name="android:textColor">@color/chip_text_color</item>
        <item name="chipBackgroundColor">@color/chip_background_color</item>
    </style>
    <style name="MasterCardButton" parent="Widget.MaterialComponents.Button">
        <item name="android:textSize">12sp</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:minHeight">40dp</item>
        <item name="cornerRadius">20dp</item>
        <item name="icon">@drawable/ic_master_card_24</item>
        <item name="iconGravity">textStart</item>
        <item name="iconPadding">8dp</item>
        <item name="iconSize">18dp</item>
    </style>
    <style name="Theme.KPITrackerApp" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        

        
        <item name="chipStyle">@style/Widget.App.Chip</item>
    </style>
    <style name="Theme.KPITrackerApp.NoActionBar" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        

        
        <item name="chipStyle">@style/Widget.App.Chip</item>
    </style>
    <style name="Widget.App.Chip" parent="Widget.MaterialComponents.Chip.Filter">
        <item name="android:textSize">14sp</item>
        <item name="chipBackgroundColor">@color/chip_background_selector</item>
        <item name="chipStrokeColor">@color/purple_500</item>
        <item name="chipStrokeWidth">1dp</item>
        <item name="chipCornerRadius">16dp</item>
        <item name="android:textColor">@color/chip_text_selector</item>
        <item name="chipIconTint">@color/chip_text_selector</item>
        <item name="closeIconTint">@color/chip_text_selector</item>
    </style>
    <style name="Widget.App.Chip.Advanced" parent="Widget.MaterialComponents.Chip.Choice">
        <item name="android:textSize">14sp</item>
        <item name="chipMinHeight">40dp</item>
        <item name="chipStartPadding">12dp</item>
        <item name="chipEndPadding">12dp</item>
        <item name="chipCornerRadius">20dp</item>
        <item name="chipBackgroundColor">@color/chip_background_selector</item>
        <item name="android:textColor">@color/chip_text_selector</item>
        <item name="chipStrokeWidth">1dp</item>
        <item name="chipStrokeColor">@color/purple_accent</item>
    </style>
</resources>