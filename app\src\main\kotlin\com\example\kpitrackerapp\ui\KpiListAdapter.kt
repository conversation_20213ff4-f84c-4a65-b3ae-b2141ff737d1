package com.example.kpitrackerapp.ui

import android.content.Context
import android.content.Intent
import android.content.res.ColorStateList
import android.graphics.Color
import android.animation.ObjectAnimator
import android.animation.PropertyValuesHolder
import android.animation.ValueAnimator
import android.util.Log
import android.graphics.drawable.GradientDrawable // Import GradientDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
// Removed: import android.view.animation.AnimationUtils
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.kpitrackerapp.R
import com.example.kpitrackerapp.databinding.KpiCardItemBinding
import com.example.kpitrackerapp.models.Kpi
import com.example.kpitrackerapp.models.KpiUnit
import com.example.kpitrackerapp.viewmodels.KpiWithProgress
import java.text.NumberFormat
import java.util.Locale
import kotlin.math.max // Corrected import for max function

// Interface for handling actions requested on a KPI item
interface OnKpiActionsListener {
    // Single method to show an action dialog
    fun onKpiActionRequested(kpiWithProgress: KpiWithProgress, itemView: View)
}

class KpiListAdapter(
    private val listener: OnKpiActionsListener, // Add listener parameter
    private val isAggregatedView: Boolean // Add flag for aggregated view mode
) : ListAdapter<KpiWithProgress, KpiListAdapter.KpiViewHolder>(KpiWithProgressDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): KpiViewHolder {
        val binding = KpiCardItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        // Pass the isAggregatedView flag to the ViewHolder as well
        return KpiViewHolder(binding, listener, isAggregatedView)
    }

    override fun onBindViewHolder(holder: KpiViewHolder, position: Int) {
        val currentKpiWithProgress = getItem(position)
        holder.bind(currentKpiWithProgress)
    }

    // Update ViewHolder to accept the listener and the aggregated view flag
    inner class KpiViewHolder(
        private val binding: KpiCardItemBinding,
        private val listener: OnKpiActionsListener,
        private val isAggregated: Boolean // Store the flag in the ViewHolder
    ) : RecyclerView.ViewHolder(binding.root) {

        init {
            // REMOVED: Share button listener is no longer needed

            // Set click listener on the item view (the card itself) for navigation (SHORT CLICK)
            itemView.setOnClickListener {
                val position = bindingAdapterPosition // Use bindingAdapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    val item = getItem(position)
                    val intent = Intent(itemView.context, KpiDetailActivity::class.java).apply {
                        putExtra(KpiDetailActivity.EXTRA_KPI_ID, item.kpi.id)
                        if (isAggregated) {
                            putExtra(KpiDetailActivity.EXTRA_IS_AGGREGATED_VIEW, true)
                            // Do NOT pass EXTRA_USER_ID in aggregated mode
                        } else {
                            putExtra(KpiDetailActivity.EXTRA_IS_AGGREGATED_VIEW, false)
                            putExtra(KpiDetailActivity.EXTRA_USER_ID, item.userId)
                        }
                    }
                    itemView.context.startActivity(intent)
                }
            }

            // Set long click listener on the item view to request actions dialog (LONG CLICK)
            itemView.setOnLongClickListener {
                val position = bindingAdapterPosition // Use bindingAdapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    val item = getItem(position)
                    // Call the single listener method to show the action dialog
                    listener.onKpiActionRequested(item, itemView)
                    true // Indicate that the long click was consumed
                } else {
                    false
                }
            }
        }

        fun bind(kpiWithProgress: KpiWithProgress) {
            val kpi = kpiWithProgress.kpi
            // Use the total accumulated progress for the "Current" display on the card
            val currentFormatted = formatValue(kpiWithProgress.currentProgressValue, kpi.unit) // Use total progress

            // The percentage indicator and target display are based on monthly values
            val progress = kpiWithProgress.monthlyPercentage ?: 0 // Use monthly percentage for indicator circle
            val targetFormatted = kpiWithProgress.monthlyTargetValue?.let { formatValue(it, kpi.unit) } ?: "N/A" // Display monthly target

            // Bind data to the new views in kpi_card_item.xml
            binding.kpiTitleTextView.text = kpi.name

            // --- Owner Chip Visibility ---
            if (isAggregated) {
                binding.ownerChip.visibility = View.GONE // Hide owner chip in aggregated view
            } else {
                binding.ownerChip.visibility = View.VISIBLE
                binding.ownerChip.text = kpi.ownerName ?: "N/A" // Show owner name in single user view
            }
            // --- End Owner Chip Visibility ---

            // binding.kpiSubtitleTextView.text = kpi.description // Optional: Use subtitle if needed
            binding.targetValueTextView.text = targetFormatted // Note: This still shows monthly target
            binding.currentValueTextView.text = currentFormatted // Note: This shows overall progress

            // Ensure the label is always "Current" as per the new request.
            // The calculation of 'currentFormatted' will be handled in the ViewModel for the "Loyalty" average.
            binding.currentTextView.text = itemView.context.getString(R.string.kpi_card_item_current_label)

            // --- Calculate and Set Star Rating (with animation) ---
            val monthlyPercent = kpiWithProgress.monthlyPercentage
            // Removed shouldAnimateStar flag
            val starRatingText = when {
                monthlyPercent == null -> "" // No stars if no monthly target/percentage
                monthlyPercent < 85 -> "🌟"
                monthlyPercent < 95 -> "🌟🌟"
                monthlyPercent <= 105 -> "🌟🌟🌟"
                monthlyPercent <= 115 -> "🌟🌟🌟🌟"
                else -> "🌟🌟🌟🌟🌟" // Above 115%
            }
            binding.starRatingTextView.text = starRatingText
            binding.starRatingTextView.visibility = if (monthlyPercent != null) View.VISIBLE else View.GONE

            // --- Apply ObjectAnimator animation ---
            // Cancel any existing animator on this view
            (binding.starRatingTextView.getTag(R.id.star_animator_tag) as? ObjectAnimator)?.cancel()
            binding.starRatingTextView.setTag(R.id.star_animator_tag, null) // Clear tag
            binding.starRatingTextView.scaleX = 1f // Reset scale
            binding.starRatingTextView.scaleY = 1f // Reset scale


            if (monthlyPercent != null) { // Apply animation whenever stars are shown
                val scaleX = PropertyValuesHolder.ofFloat(View.SCALE_X, 1.0f, 1.15f)
                val scaleY = PropertyValuesHolder.ofFloat(View.SCALE_Y, 1.0f, 1.15f)
                val animator = ObjectAnimator.ofPropertyValuesHolder(binding.starRatingTextView, scaleX, scaleY).apply {
                    duration = 500
                    repeatCount = ValueAnimator.INFINITE
                    repeatMode = ValueAnimator.REVERSE
                    // No need for interpolator here, default is fine or use AccelerateDecelerateInterpolator if needed
                }
                binding.starRatingTextView.setTag(R.id.star_animator_tag, animator) // Store animator in tag
                animator.start()
            }
            // --- End ObjectAnimator animation ---


            // --- Apply Single Card Background Color ---
            val context = itemView.context
            // Define a single default color resource (e.g., R.color.card_background_default)
            val defaultCardColor = ContextCompat.getColor(context, R.color.card_background_default)

            val cardColor = try {
                // Use cardColorHex (which is the primary/top color field now)
                kpi.cardColorHex?.takeIf { it.isNotBlank() }?.let { Color.parseColor(it) } ?: defaultCardColor
            } catch (e: IllegalArgumentException) {
                Log.w("KpiListAdapter", "Invalid hex color stored for KPI ${kpi.id}: ${kpi.cardColorHex}")
                defaultCardColor
            }

            // Set the solid background color on the MaterialCardView
            // MaterialCardView uses cardBackgroundColor attribute, but we can set it programmatically
            binding.root.setCardBackgroundColor(cardColor)
            // --- End Apply Single Card Background Color ---


            // Update CircularProgressIndicator and TextView using monthly percentage
            binding.progressIndicator.progress = progress.coerceIn(0, 100) // Cap at 100 for visual
            binding.percentageTextView.text = kpiWithProgress.monthlyPercentage?.let { "$it%" } ?: "--%" // Show "--%" if no monthly target

            // Update Current Achievement Progress Indicator
            if (kpiWithProgress.currentPeriodAchievement != null) {
                binding.currentAchievementProgressIndicator.progress = kpiWithProgress.currentPeriodAchievement.coerceIn(0, 100)
                binding.currentAchievementPercentageTextView.text = "${kpiWithProgress.currentPeriodAchievement}%"
                binding.currentAchievementProgressIndicator.visibility = View.VISIBLE
                binding.currentAchievementPercentageTextView.visibility = View.VISIBLE
                binding.currentAchievementLabelTextView.visibility = View.VISIBLE
            } else {
                binding.currentAchievementProgressIndicator.visibility = View.GONE
                binding.currentAchievementPercentageTextView.visibility = View.GONE
                binding.currentAchievementLabelTextView.visibility = View.GONE
            }

            // Reverted: Removed Remaining/Required/Projected logic

            // Set indicator color based on original logic
            val itemContext = itemView.context
             val indicatorColor = when {
                 progress >= 100 -> ContextCompat.getColor(itemContext, R.color.kpi_good)
                 progress >= 75 -> ContextCompat.getColor(itemContext, R.color.kpi_warning) // Original warning threshold
                 else -> ContextCompat.getColor(itemContext, R.color.kpi_concern)
             }
            binding.progressIndicator.setIndicatorColor(indicatorColor)
            // Set track color explicitly from our defined colors
            binding.progressIndicator.trackColor = ContextCompat.getColor(itemContext, R.color.progress_track_color)

            // --- Trend Indicator Logic Removed ---

            // --- Display Current Period Target and Achievement ---
            // Display Current Period Target
            if (kpiWithProgress.currentPeriodTarget != null) {
                val currentPeriodTargetFormatted = formatValueAsInteger(kpiWithProgress.currentPeriodTarget, kpi.unit)
                binding.currentPeriodTargetValueTextView.text = currentPeriodTargetFormatted
                binding.currentPeriodTargetIcon.visibility = View.VISIBLE
                binding.currentPeriodTargetTextView.visibility = View.VISIBLE
                binding.currentPeriodTargetValueTextView.visibility = View.VISIBLE
            } else {
                // Hide if there's no monthly target or current period target couldn't be calculated
                binding.currentPeriodTargetIcon.visibility = View.GONE
                binding.currentPeriodTargetTextView.visibility = View.GONE
                binding.currentPeriodTargetValueTextView.visibility = View.GONE
            }

            // Display Current Period Achievement
            if (kpiWithProgress.currentPeriodAchievement != null) {
                binding.currentPeriodAchievementValueTextView.text = "${kpiWithProgress.currentPeriodAchievement}%"
                binding.currentPeriodAchievementIcon.visibility = View.VISIBLE
                binding.currentPeriodAchievementTextView.visibility = View.VISIBLE
                binding.currentPeriodAchievementValueTextView.visibility = View.VISIBLE
            } else {
                // Hide if there's no current period achievement
                binding.currentPeriodAchievementIcon.visibility = View.GONE
                binding.currentPeriodAchievementTextView.visibility = View.GONE
                binding.currentPeriodAchievementValueTextView.visibility = View.GONE
            }

            // --- Display Remaining to Target and Required Daily (Using ViewModel's current month calculations) ---
            // Use remainingMonthlyValue calculated in ViewModel based on current month's progress
            if (kpiWithProgress.remainingMonthlyValue != null) {
                val remainingFormatted = formatValue(kpiWithProgress.remainingMonthlyValue, kpi.unit)
                binding.remainingValueTextView.text = remainingFormatted
                binding.remainingIcon.visibility = View.VISIBLE
                binding.remainingTextView.visibility = View.VISIBLE
                binding.remainingValueTextView.visibility = View.VISIBLE
            } else {
                // Hide if there's no monthly target or remaining value couldn't be calculated
                binding.remainingIcon.visibility = View.GONE
                binding.remainingTextView.visibility = View.GONE
                binding.remainingValueTextView.visibility = View.GONE
            }

            // Use requiredDailyRate and remainingDaysInMonth calculated in ViewModel based on current month
            if (kpiWithProgress.requiredDailyRate != null && kpiWithProgress.remainingDaysInMonth != null) {
                val dailyRateFormatted = formatValue(kpiWithProgress.requiredDailyRate, kpi.unit)
                binding.requiredDailyValueTextView.text = "$dailyRateFormatted / day (${kpiWithProgress.remainingDaysInMonth} days left)"
                binding.requiredDailyIcon.visibility = View.VISIBLE
                binding.requiredDailyTextView.visibility = View.VISIBLE
                binding.requiredDailyValueTextView.visibility = View.VISIBLE
            } else {
                 binding.requiredDailyIcon.visibility = View.GONE
                 binding.requiredDailyTextView.visibility = View.GONE
                 binding.requiredDailyValueTextView.visibility = View.GONE
            }

            // --- Display Last Update ---
            // Removed Projected End display logic
            if (kpiWithProgress.daysSinceLastUpdate != null) {
                val daysAgoText = when (kpiWithProgress.daysSinceLastUpdate) {
                    0L -> "Today"
                    1L -> "1 day ago"
                    else -> "${kpiWithProgress.daysSinceLastUpdate} days ago"
                }
                binding.lastUpdateValueTextView.text = daysAgoText
                binding.lastUpdateIcon.visibility = View.VISIBLE
                binding.lastUpdateTextView.visibility = View.VISIBLE
                binding.lastUpdateValueTextView.visibility = View.VISIBLE
            } else {
                binding.lastUpdateIcon.visibility = View.GONE
                binding.lastUpdateTextView.visibility = View.GONE
                binding.lastUpdateValueTextView.visibility = View.GONE
            }
            // --- End Display Last Update ---


            // TODO: Optionally set ownerChip background color based on owner or status
        }

        // Moved formatValue outside the ViewHolder for potential reuse or placement in a Util class
        // private fun formatValue(value: Double, unit: KpiUnit): String { ... }
    } // End of KpiViewHolder inner class

    // formatValue function should be part of KpiListAdapter
    private fun formatValue(value: Double, unit: KpiUnit): String {
            // Basic formatting, consider locale and precision needs
             val format = NumberFormat.getNumberInstance(Locale.getDefault())
             format.maximumFractionDigits = 2 // Adjust precision as needed
             return when (unit) {
                KpiUnit.NUMBER, KpiUnit.POINT -> format.format(value) // Keep POINT formatting
                KpiUnit.PERCENTAGE -> "${format.format(value)}%"
                KpiUnit.CURRENCY -> NumberFormat.getCurrencyInstance(Locale.getDefault()).format(value)
            }
        }

    // formatValueAsInteger function for displaying values without decimals and with "riyal" suffix
    private fun formatValueAsInteger(value: Double, unit: KpiUnit): String {
        val format = NumberFormat.getNumberInstance(Locale.getDefault())
        format.maximumFractionDigits = 0 // No decimal places
        return when (unit) {
            KpiUnit.NUMBER, KpiUnit.POINT -> format.format(value)
            KpiUnit.PERCENTAGE -> "${format.format(value)}%"
            KpiUnit.CURRENCY -> "${format.format(value)} riyal" // Custom format with "riyal" suffix
        }
    }

    // KpiWithProgressDiffCallback should be inside KpiListAdapter or standalone (but typically inner/static inner)
    class KpiWithProgressDiffCallback : DiffUtil.ItemCallback<KpiWithProgress>() {
        override fun areItemsTheSame(oldItem: KpiWithProgress, newItem: KpiWithProgress): Boolean {
            return oldItem.kpi.id == newItem.kpi.id
        }

        override fun areContentsTheSame(oldItem: KpiWithProgress, newItem: KpiWithProgress): Boolean {
            return oldItem == newItem
        }
    }
} // End of KpiListAdapter class
