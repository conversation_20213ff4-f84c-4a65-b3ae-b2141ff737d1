package com.example.kpitrackerapp.utils

import android.annotation.SuppressLint
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.example.kpitrackerapp.MainActivity
import com.example.kpitrackerapp.R

object AppNotificationManager {
    private const val PREFS_NAME = "notification_prefs"

    // Notification Channels
    private const val CHANNEL_ADMIN_MESSAGES = "admin_messages"
    private const val CHANNEL_KPI_ALERTS = "kpi_alerts"
    private const val CHANNEL_REMINDERS = "reminders"
    private const val CHANNEL_SYSTEM = "system"

    // Notification IDs
    private var notificationId = 1000

    fun initializeNotificationChannels(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // Admin Messages Channel
            val adminChannel = NotificationChannel(
                CHANNEL_ADMIN_MESSAGES,
                "Admin Messages",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Messages from administrators"
                enableLights(true)
                enableVibration(true)
            }

            // KPI Alerts Channel
            val kpiChannel = NotificationChannel(
                CHANNEL_KPI_ALERTS,
                "KPI Alerts",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "KPI target and performance alerts"
                enableLights(true)
                enableVibration(false)
            }

            // Reminders Channel
            val reminderChannel = NotificationChannel(
                CHANNEL_REMINDERS,
                "Reminders",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "Daily and weekly reminders"
                enableLights(false)
                enableVibration(true)
            }

            // System Channel
            val systemChannel = NotificationChannel(
                CHANNEL_SYSTEM,
                "System Notifications",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "System updates and maintenance"
                enableLights(false)
                enableVibration(false)
            }

            notificationManager.createNotificationChannels(listOf(
                adminChannel, kpiChannel, reminderChannel, systemChannel
            ))
        }
    }

    fun showAdminMessage(context: Context, title: String, message: String, senderId: String = "admin") {
        if (!isNotificationEnabled(context, "push_notifications")) return

        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("open_messages", true)
        }

        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, CHANNEL_ADMIN_MESSAGES)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(message)
            .setStyle(NotificationCompat.BigTextStyle().bigText(message))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .setColor(context.getColor(R.color.purple_500))
            .build()

        NotificationManagerCompat.from(context).notify(getNextNotificationId(), notification)

        // Save notification locally
        saveNotificationLocally(context, title, message, "admin_message", senderId)
    }

    fun showKpiAlert(context: Context, kpiName: String, currentValue: Double, targetValue: Double, percentage: Double) {
        if (!isNotificationEnabled(context, "target_alerts")) return

        val threshold = getNotificationPreference(context, "target_threshold", "80").toDoubleOrNull() ?: 80.0
        if (percentage < threshold) return

        val title = "🎯 KPI Alert: $kpiName"
        val message = "You've reached ${percentage.toInt()}% of your target! Current: $currentValue / Target: $targetValue"

        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }

        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, CHANNEL_KPI_ALERTS)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(message)
            .setStyle(NotificationCompat.BigTextStyle().bigText(message))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .setColor(context.getColor(R.color.purple_500))
            .build()

        NotificationManagerCompat.from(context).notify(getNextNotificationId(), notification)

        // Save notification locally
        saveNotificationLocally(context, title, message, "kpi_alert", "system")
    }

    @SuppressLint("MissingPermission")
    fun showReminder(context: Context, title: String, message: String) {
        if (!isNotificationEnabled(context, "reminder_notifications")) return

        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, CHANNEL_REMINDERS)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(message)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .build()

        NotificationManagerCompat.from(context).notify(getNextNotificationId(), notification)

        // Save notification locally
        saveNotificationLocally(context, title, message, "reminder", "system")
    }

    @SuppressLint("MissingPermission")
    fun showSystemNotification(context: Context, title: String, message: String) {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, CHANNEL_SYSTEM)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(message)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .build()

        NotificationManagerCompat.from(context).notify(getNextNotificationId(), notification)

        // Save notification locally
        saveNotificationLocally(context, title, message, "system", "system")
    }

    private fun saveNotificationLocally(context: Context, title: String, message: String, type: String, senderId: String) {
        val currentUserId = SessionManager.getCurrentUserId() ?: return
        val prefs = context.getSharedPreferences("user_notifications_$currentUserId", Context.MODE_PRIVATE)
        val notificationId = System.currentTimeMillis().toString()

        prefs.edit()
            .putString("notification_$notificationId", message)
            .putString("notification_${notificationId}_title", title)
            .putLong("notification_${notificationId}_time", System.currentTimeMillis())
            .putBoolean("notification_${notificationId}_read", false)
            .putString("notification_${notificationId}_type", type)
            .putString("notification_${notificationId}_sender", senderId)
            .apply()
    }

    fun getUnreadNotificationsCount(context: Context): Int {
        val currentUserId = SessionManager.getCurrentUserId() ?: return 0
        val prefs = context.getSharedPreferences("user_notifications_$currentUserId", Context.MODE_PRIVATE)

        return prefs.all.count { (key, value) ->
            key.endsWith("_read") && value == false
        }
    }

    fun getAllNotifications(context: Context): List<NotificationItem> {
        val currentUserId = SessionManager.getCurrentUserId() ?: return emptyList()
        val prefs = context.getSharedPreferences("user_notifications_$currentUserId", Context.MODE_PRIVATE)

        val notifications = mutableListOf<NotificationItem>()
        val notificationIds = prefs.all.keys
            .filter { it.startsWith("notification_") && !it.contains("_") }
            .map { it.removePrefix("notification_") }

        notificationIds.forEach { id ->
            val title = prefs.getString("notification_${id}_title", "Notification") ?: "Notification"
            val message = prefs.getString("notification_$id", "") ?: ""
            val time = prefs.getLong("notification_${id}_time", 0)
            val isRead = prefs.getBoolean("notification_${id}_read", false)
            val type = prefs.getString("notification_${id}_type", "message") ?: "message"
            val sender = prefs.getString("notification_${id}_sender", "system") ?: "system"

            notifications.add(NotificationItem(id, title, message, time, isRead, type, sender))
        }

        return notifications.sortedByDescending { it.time }
    }

    fun markNotificationAsRead(context: Context, notificationId: String) {
        val currentUserId = SessionManager.getCurrentUserId() ?: return
        val prefs = context.getSharedPreferences("user_notifications_$currentUserId", Context.MODE_PRIVATE)
        prefs.edit().putBoolean("notification_${notificationId}_read", true).apply()
    }

    fun clearAllNotifications(context: Context) {
        val currentUserId = SessionManager.getCurrentUserId() ?: return
        val prefs = context.getSharedPreferences("user_notifications_$currentUserId", Context.MODE_PRIVATE)
        prefs.edit().clear().apply()

        // Clear system notifications
        NotificationManagerCompat.from(context).cancelAll()
    }

    private fun getNextNotificationId(): Int {
        return ++notificationId
    }

    private fun isNotificationEnabled(context: Context, type: String): Boolean {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return prefs.getBoolean(type, true)
    }

    private fun getNotificationPreference(context: Context, key: String, defaultValue: String): String {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return prefs.getString(key, defaultValue) ?: defaultValue
    }

    data class NotificationItem(
        val id: String,
        val title: String,
        val message: String,
        val time: Long,
        val isRead: Boolean,
        val type: String,
        val sender: String
    )
}
