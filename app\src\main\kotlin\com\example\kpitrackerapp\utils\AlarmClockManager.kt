package com.example.kpitrackerapp.utils

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.provider.AlarmClock
import android.util.Log
import android.widget.Toast
import com.example.kpitrackerapp.models.Task
import java.text.SimpleDateFormat
import java.util.*

/**
 * مدير المنبهات - يدير إضافة المهام تلقائياً إلى منبه الهاتف
 */
class AlarmClockManager(private val context: Context) {

    companion object {
        private const val TAG = "AlarmClockManager"
    }

    /**
     * إضافة مهمة جديدة كمنبه في ساعة الهاتف
     */
    fun addTaskToAlarmClock(task: Task, reminderTime: Calendar? = null): Boolean {
        return try {
            val alarmTime = reminderTime ?: getDefaultReminderTime(task)

            Log.d(TAG, "محاولة إضافة منبه للمهمة: ${task.name} في الوقت: ${formatTime(alarmTime)}")

            // محاولة عدة طرق لإضافة المنبه
            val success = tryMultipleAlarmMethods(task, alarmTime)

            if (success) {
                showSuccessMessage(task, alarmTime)
                Log.d(TAG, "تم إضافة المنبه بنجاح للمهمة: ${task.name}")
            } else {
                showErrorMessage("فشل في إضافة المنبه. جرب إضافة المنبه يدوياً.")
                Log.w(TAG, "فشل في إضافة المنبه للمهمة: ${task.name}")
            }

            success

        } catch (e: Exception) {
            Log.e(TAG, "خطأ في إضافة المنبه للمهمة: ${task.name}", e)
            showErrorMessage("حدث خطأ في إضافة المنبه: ${e.message}")
            return false
        }
    }

    /**
     * محاولة عدة طرق لإضافة المنبه
     */
    private fun tryMultipleAlarmMethods(task: Task, alarmTime: Calendar): Boolean {
        // الطريقة الأولى: استخدام ACTION_SET_ALARM العادي
        if (tryStandardAlarmMethod(task, alarmTime)) {
            Log.d(TAG, "نجحت الطريقة العادية لإضافة المنبه")
            return true
        }

        // الطريقة الثانية: استخدام ACTION_SET_ALARM مع SKIP_UI = true
        if (tryDirectAlarmMethod(task, alarmTime)) {
            Log.d(TAG, "نجحت الطريقة المباشرة لإضافة المنبه")
            return true
        }

        // الطريقة الثالثة: محاولة فتح تطبيق الساعة فقط
        if (tryOpenClockApp(task, alarmTime)) {
            Log.d(TAG, "نجح فتح تطبيق الساعة")
            return true
        }

        return false
    }

    /**
     * الطريقة العادية لإضافة المنبه
     */
    private fun tryStandardAlarmMethod(task: Task, alarmTime: Calendar): Boolean {
        return try {
            val intent = Intent(AlarmClock.ACTION_SET_ALARM).apply {
                putExtra(AlarmClock.EXTRA_MESSAGE, "📋 ${task.name}")
                putExtra(AlarmClock.EXTRA_HOUR, alarmTime.get(Calendar.HOUR_OF_DAY))
                putExtra(AlarmClock.EXTRA_MINUTES, alarmTime.get(Calendar.MINUTE))
                putExtra(AlarmClock.EXTRA_SKIP_UI, false)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }

            if (intent.resolveActivity(context.packageManager) != null) {
                context.startActivity(intent)
                return true
            }
            false
        } catch (e: Exception) {
            Log.e(TAG, "فشلت الطريقة العادية: ${e.message}")
            false
        }
    }

    /**
     * الطريقة المباشرة لإضافة المنبه
     */
    private fun tryDirectAlarmMethod(task: Task, alarmTime: Calendar): Boolean {
        return try {
            val intent = Intent(AlarmClock.ACTION_SET_ALARM).apply {
                putExtra(AlarmClock.EXTRA_MESSAGE, "📋 ${task.name}")
                putExtra(AlarmClock.EXTRA_HOUR, alarmTime.get(Calendar.HOUR_OF_DAY))
                putExtra(AlarmClock.EXTRA_MINUTES, alarmTime.get(Calendar.MINUTE))
                putExtra(AlarmClock.EXTRA_SKIP_UI, true)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }

            if (intent.resolveActivity(context.packageManager) != null) {
                context.startActivity(intent)
                return true
            }
            false
        } catch (e: Exception) {
            Log.e(TAG, "فشلت الطريقة المباشرة: ${e.message}")
            false
        }
    }

    /**
     * محاولة فتح تطبيق الساعة
     */
    private fun tryOpenClockApp(task: Task, alarmTime: Calendar): Boolean {
        return try {
            // محاولة فتح تطبيق الساعة الافتراضي
            val clockPackages = listOf(
                "com.google.android.deskclock",
                "com.samsung.android.app.clockpackage",
                "com.oneplus.deskclock",
                "com.android.deskclock",
                "com.htc.android.worldclock",
                "com.lge.clock"
            )

            for (packageName in clockPackages) {
                if (isPackageInstalled(packageName)) {
                    val intent = context.packageManager.getLaunchIntentForPackage(packageName)
                    if (intent != null) {
                        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                        context.startActivity(intent)

                        // إظهار رسالة للمستخدم
                        Toast.makeText(context,
                            "تم فتح تطبيق الساعة. أضف منبه للمهمة: ${task.name} في الساعة ${formatTime(alarmTime)}",
                            Toast.LENGTH_LONG).show()
                        return true
                    }
                }
            }
            false
        } catch (e: Exception) {
            Log.e(TAG, "فشل في فتح تطبيق الساعة: ${e.message}")
            false
        }
    }

    /**
     * التحقق من وجود تطبيق معين
     */
    private fun isPackageInstalled(packageName: String): Boolean {
        return try {
            context.packageManager.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }

    /**
     * إضافة منبه مخصص بوقت محدد
     */
    fun addCustomAlarm(task: Task, hour: Int, minute: Int): Boolean {
        val calendar = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, hour)
            set(Calendar.MINUTE, minute)
            set(Calendar.SECOND, 0)

            // إذا كان الوقت قد مضى اليوم، اجعله غداً
            if (timeInMillis <= System.currentTimeMillis()) {
                add(Calendar.DAY_OF_YEAR, 1)
            }
        }

        return addTaskToAlarmClock(task, calendar)
    }

    /**
     * الحصول على وقت التذكير الافتراضي للمهمة
     */
    private fun getDefaultReminderTime(task: Task): Calendar {
        val calendar = Calendar.getInstance()

        // حساب وقت التذكير بناءً على تاريخ انتهاء المهمة
        val expirationCalendar = Calendar.getInstance().apply {
            time = task.expirationDate
        }

        // إذا كانت المهمة تنتهي اليوم، اجعل التذكير بعد ساعة
        if (isSameDay(calendar, expirationCalendar)) {
            calendar.add(Calendar.HOUR_OF_DAY, 1)
        } else {
            // إذا كانت المهمة تنتهي في المستقبل، اجعل التذكير في الصباح قبل يوم من انتهائها
            calendar.time = task.expirationDate
            calendar.add(Calendar.DAY_OF_YEAR, -1)
            calendar.set(Calendar.HOUR_OF_DAY, 9) // 9 صباحاً
            calendar.set(Calendar.MINUTE, 0)
        }

        calendar.set(Calendar.SECOND, 0)
        return calendar
    }

    /**
     * بناء وصف المنبه
     */
    private fun buildAlarmDescription(task: Task, alarmTime: Calendar): String {
        val taskName = task.name
        val dueDate = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault()).format(task.expirationDate)
        val alarmTimeStr = formatTime(alarmTime)

        return "📋 $taskName\n⏰ تذكير: $alarmTimeStr\n📅 تنتهي: $dueDate"
    }

    /**
     * تنسيق الوقت للعرض
     */
    private fun formatTime(calendar: Calendar): String {
        val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
        return timeFormat.format(calendar.time)
    }

    /**
     * التحقق من كون التاريخين في نفس اليوم
     */
    private fun isSameDay(cal1: Calendar, cal2: Calendar): Boolean {
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR)
    }

    /**
     * إظهار رسالة نجاح
     */
    private fun showSuccessMessage(task: Task, alarmTime: Calendar) {
        val timeStr = formatTime(alarmTime)
        val message = "⏰ تم إضافة منبه للمهمة '${task.name}' في الساعة $timeStr"
        Toast.makeText(context, message, Toast.LENGTH_LONG).show()
    }

    /**
     * إظهار رسالة خطأ
     */
    private fun showErrorMessage(message: String) {
        Toast.makeText(context, "❌ $message", Toast.LENGTH_LONG).show()
    }

    /**
     * إضافة منبه سريع (بعد 30 دقيقة من الآن)
     */
    fun addQuickReminder(task: Task): Boolean {
        val calendar = Calendar.getInstance().apply {
            add(Calendar.MINUTE, 30) // بعد 30 دقيقة
        }
        return addTaskToAlarmClock(task, calendar)
    }

    /**
     * إضافة منبه يومي متكرر للمهام المتكررة
     */
    fun addRecurringAlarm(task: Task, hour: Int, minute: Int): Boolean {
        return try {
            val calendar = Calendar.getInstance().apply {
                set(Calendar.HOUR_OF_DAY, hour)
                set(Calendar.MINUTE, minute)
            }

            Log.d(TAG, "محاولة إضافة منبه متكرر للمهمة: ${task.name}")

            // محاولة إضافة منبه متكرر
            val intent = Intent(AlarmClock.ACTION_SET_ALARM).apply {
                putExtra(AlarmClock.EXTRA_MESSAGE, "📋 ${task.name} (يومي)")
                putExtra(AlarmClock.EXTRA_HOUR, hour)
                putExtra(AlarmClock.EXTRA_MINUTES, minute)
                putExtra(AlarmClock.EXTRA_SKIP_UI, false)

                // تعيين المنبه ليتكرر يومياً
                putExtra(AlarmClock.EXTRA_DAYS, arrayListOf(
                    Calendar.MONDAY,
                    Calendar.TUESDAY,
                    Calendar.WEDNESDAY,
                    Calendar.THURSDAY,
                    Calendar.FRIDAY,
                    Calendar.SATURDAY,
                    Calendar.SUNDAY
                ))

                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }

            if (intent.resolveActivity(context.packageManager) != null) {
                context.startActivity(intent)
                showSuccessMessage(task, calendar)
                true
            } else {
                // إذا فشل المنبه المتكرر، جرب منبه عادي
                Log.w(TAG, "فشل المنبه المتكرر، محاولة منبه عادي")
                return addTaskToAlarmClock(task, calendar)
            }

        } catch (e: Exception) {
            Log.e(TAG, "خطأ في إضافة المنبه المتكرر", e)
            // إذا فشل المنبه المتكرر، جرب منبه عادي
            val calendar = Calendar.getInstance().apply {
                set(Calendar.HOUR_OF_DAY, hour)
                set(Calendar.MINUTE, minute)
            }
            return addTaskToAlarmClock(task, calendar)
        }
    }

    /**
     * اختبار إمكانية إضافة المنبهات
     */
    fun testAlarmIntegration(): String {
        val testResults = mutableListOf<String>()

        // اختبار ACTION_SET_ALARM
        val alarmIntent = Intent(AlarmClock.ACTION_SET_ALARM)
        if (alarmIntent.resolveActivity(context.packageManager) != null) {
            testResults.add("✅ ACTION_SET_ALARM متاح")
        } else {
            testResults.add("❌ ACTION_SET_ALARM غير متاح")
        }

        // اختبار تطبيقات الساعة المثبتة
        val clockPackages = listOf(
            "com.google.android.deskclock" to "Google Clock",
            "com.samsung.android.app.clockpackage" to "Samsung Clock",
            "com.oneplus.deskclock" to "OnePlus Clock",
            "com.android.deskclock" to "Android Clock",
            "com.htc.android.worldclock" to "HTC Clock",
            "com.lge.clock" to "LG Clock"
        )

        val installedClocks = mutableListOf<String>()
        for ((packageName, appName) in clockPackages) {
            if (isPackageInstalled(packageName)) {
                installedClocks.add("✅ $appName")
            }
        }

        if (installedClocks.isNotEmpty()) {
            testResults.add("تطبيقات الساعة المثبتة:")
            testResults.addAll(installedClocks)
        } else {
            testResults.add("❌ لا توجد تطبيقات ساعة معروفة مثبتة")
        }

        return testResults.joinToString("\n")
    }

    /**
     * إضافة منبه تجريبي للاختبار
     */
    fun addTestAlarm(): Boolean {
        val testTask = Task(
            id = 0,
            name = "اختبار المنبه",
            description = "هذا منبه تجريبي",
            expirationDate = Date(),
            creationDate = Date(),
            completionDate = null,
            isCompleted = false,
            reminderDaysBefore = null
        )

        val testTime = Calendar.getInstance().apply {
            add(Calendar.MINUTE, 2) // بعد دقيقتين
        }

        return addTaskToAlarmClock(testTask, testTime)
    }
}
