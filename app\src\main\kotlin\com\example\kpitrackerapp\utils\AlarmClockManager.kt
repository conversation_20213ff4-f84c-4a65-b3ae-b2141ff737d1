package com.example.kpitrackerapp.utils

import android.content.Context
import android.content.Intent
import android.provider.AlarmClock
import android.util.Log
import android.widget.Toast
import com.example.kpitrackerapp.models.Task
import java.text.SimpleDateFormat
import java.util.*

/**
 * مدير المنبهات - يدير إضافة المهام تلقائياً إلى منبه الهاتف
 */
class AlarmClockManager(private val context: Context) {

    companion object {
        private const val TAG = "AlarmClockManager"
    }

    /**
     * إضافة مهمة جديدة كمنبه في ساعة الهاتف
     */
    fun addTaskToAlarmClock(task: Task, reminderTime: Calendar? = null): Boolean {
        return try {
            val alarmTime = reminderTime ?: getDefaultReminderTime(task)

            // إنشاء Intent لإضافة منبه
            val intent = Intent(AlarmClock.ACTION_SET_ALARM).apply {
                // اسم المنبه (اسم المهمة)
                putExtra(AlarmClock.EXTRA_MESSAGE, "📋 ${task.name}")

                // وقت المنبه
                putExtra(AlarmClock.EXTRA_HOUR, alarmTime.get(Calendar.HOUR_OF_DAY))
                putExtra(AlarmClock.EXTRA_MINUTES, alarmTime.get(Calendar.MINUTE))

                // عدم تخطي واجهة المستخدم لضمان إضافة المنبه
                putExtra(AlarmClock.EXTRA_SKIP_UI, false)

                // تعيين الـ flags المطلوبة
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }

            // التحقق من وجود تطبيق ساعة يدعم هذه الميزة
            if (intent.resolveActivity(context.packageManager) != null) {
                context.startActivity(intent)

                Log.d(TAG, "تم إضافة المهمة '${task.name}' كمنبه في الساعة ${formatTime(alarmTime)}")

                // إظهار رسالة تأكيد للمستخدم
                showSuccessMessage(task, alarmTime)

                true
            } else {
                Log.w(TAG, "لا يوجد تطبيق ساعة يدعم إضافة المنبهات")
                showErrorMessage("لا يوجد تطبيق ساعة متوافق على هذا الجهاز")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "خطأ في إضافة المنبه للمهمة: ${task.name}", e)
            showErrorMessage("حدث خطأ في إضافة المنبه: ${e.message}")
            return false
        }
    }

    /**
     * إضافة منبه مخصص بوقت محدد
     */
    fun addCustomAlarm(task: Task, hour: Int, minute: Int): Boolean {
        val calendar = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, hour)
            set(Calendar.MINUTE, minute)
            set(Calendar.SECOND, 0)

            // إذا كان الوقت قد مضى اليوم، اجعله غداً
            if (timeInMillis <= System.currentTimeMillis()) {
                add(Calendar.DAY_OF_YEAR, 1)
            }
        }

        return addTaskToAlarmClock(task, calendar)
    }

    /**
     * الحصول على وقت التذكير الافتراضي للمهمة
     */
    private fun getDefaultReminderTime(task: Task): Calendar {
        val calendar = Calendar.getInstance()

        // حساب وقت التذكير بناءً على تاريخ انتهاء المهمة
        val expirationCalendar = Calendar.getInstance().apply {
            time = task.expirationDate
        }

        // إذا كانت المهمة تنتهي اليوم، اجعل التذكير بعد ساعة
        if (isSameDay(calendar, expirationCalendar)) {
            calendar.add(Calendar.HOUR_OF_DAY, 1)
        } else {
            // إذا كانت المهمة تنتهي في المستقبل، اجعل التذكير في الصباح قبل يوم من انتهائها
            calendar.time = task.expirationDate
            calendar.add(Calendar.DAY_OF_YEAR, -1)
            calendar.set(Calendar.HOUR_OF_DAY, 9) // 9 صباحاً
            calendar.set(Calendar.MINUTE, 0)
        }

        calendar.set(Calendar.SECOND, 0)
        return calendar
    }

    /**
     * بناء وصف المنبه
     */
    private fun buildAlarmDescription(task: Task, alarmTime: Calendar): String {
        val taskName = task.name
        val dueDate = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault()).format(task.expirationDate)
        val alarmTimeStr = formatTime(alarmTime)

        return "📋 $taskName\n⏰ تذكير: $alarmTimeStr\n📅 تنتهي: $dueDate"
    }

    /**
     * تنسيق الوقت للعرض
     */
    private fun formatTime(calendar: Calendar): String {
        val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
        return timeFormat.format(calendar.time)
    }

    /**
     * التحقق من كون التاريخين في نفس اليوم
     */
    private fun isSameDay(cal1: Calendar, cal2: Calendar): Boolean {
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR)
    }

    /**
     * إظهار رسالة نجاح
     */
    private fun showSuccessMessage(task: Task, alarmTime: Calendar) {
        val timeStr = formatTime(alarmTime)
        val message = "⏰ تم إضافة منبه للمهمة '${task.name}' في الساعة $timeStr"
        Toast.makeText(context, message, Toast.LENGTH_LONG).show()
    }

    /**
     * إظهار رسالة خطأ
     */
    private fun showErrorMessage(message: String) {
        Toast.makeText(context, "❌ $message", Toast.LENGTH_LONG).show()
    }

    /**
     * إضافة منبه سريع (بعد 30 دقيقة من الآن)
     */
    fun addQuickReminder(task: Task): Boolean {
        val calendar = Calendar.getInstance().apply {
            add(Calendar.MINUTE, 30) // بعد 30 دقيقة
        }
        return addTaskToAlarmClock(task, calendar)
    }

    /**
     * إضافة منبه يومي متكرر للمهام المتكررة
     */
    fun addRecurringAlarm(task: Task, hour: Int, minute: Int): Boolean {
        return try {
            val intent = Intent(AlarmClock.ACTION_SET_ALARM).apply {
                putExtra(AlarmClock.EXTRA_MESSAGE, "📋 ${task.name} (يومي)")
                putExtra(AlarmClock.EXTRA_HOUR, hour)
                putExtra(AlarmClock.EXTRA_MINUTES, minute)
                putExtra(AlarmClock.EXTRA_SKIP_UI, false)

                // تعيين المنبه ليتكرر يومياً
                putExtra(AlarmClock.EXTRA_DAYS, arrayListOf(
                    Calendar.MONDAY,
                    Calendar.TUESDAY,
                    Calendar.WEDNESDAY,
                    Calendar.THURSDAY,
                    Calendar.FRIDAY,
                    Calendar.SATURDAY,
                    Calendar.SUNDAY
                ))

                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }

            if (intent.resolveActivity(context.packageManager) != null) {
                context.startActivity(intent)
                showSuccessMessage(task, Calendar.getInstance().apply {
                    set(Calendar.HOUR_OF_DAY, hour)
                    set(Calendar.MINUTE, minute)
                })
                true
            } else {
                showErrorMessage("لا يوجد تطبيق ساعة متوافق")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "خطأ في إضافة المنبه المتكرر", e)
            showErrorMessage("حدث خطأ في إضافة المنبه المتكرر")
            return false
        }
    }
}
