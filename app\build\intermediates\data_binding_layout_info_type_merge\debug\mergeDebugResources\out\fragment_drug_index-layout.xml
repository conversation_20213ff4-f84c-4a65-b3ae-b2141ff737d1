<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_drug_index" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\fragment_drug_index.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_drug_index_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="170" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="15" startOffset="8" endLine="20" endOffset="61"/></Target><Target id="@+id/searchEditText" view="EditText"><Expressions/><location startLine="34" startOffset="12" endLine="41" endOffset="52"/></Target><Target id="@+id/drugNameTextView" view="TextView"><Expressions/><location startLine="43" startOffset="12" endLine="50" endOffset="43"/></Target><Target id="@+id/usesTextView" view="TextView"><Expressions/><location startLine="72" startOffset="20" endLine="77" endOffset="42"/></Target><Target id="@+id/interactionsTextView" view="TextView"><Expressions/><location startLine="101" startOffset="20" endLine="106" endOffset="42"/></Target><Target id="@+id/sideEffectsTextView" view="TextView"><Expressions/><location startLine="130" startOffset="20" endLine="135" endOffset="42"/></Target><Target id="@+id/usageInstructionsTextView" view="TextView"><Expressions/><location startLine="159" startOffset="20" endLine="164" endOffset="42"/></Target></Targets></Layout>