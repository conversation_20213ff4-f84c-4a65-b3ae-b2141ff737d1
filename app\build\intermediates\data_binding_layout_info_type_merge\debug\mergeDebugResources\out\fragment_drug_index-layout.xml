<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_drug_index" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\fragment_drug_index.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_drug_index_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="168" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="18" endOffset="53"/></Target><Target id="@+id/searchEditText" view="EditText"><Expressions/><location startLine="32" startOffset="12" endLine="39" endOffset="52"/></Target><Target id="@+id/drugNameTextView" view="TextView"><Expressions/><location startLine="41" startOffset="12" endLine="48" endOffset="43"/></Target><Target id="@+id/usesTextView" view="TextView"><Expressions/><location startLine="70" startOffset="20" endLine="75" endOffset="42"/></Target><Target id="@+id/interactionsTextView" view="TextView"><Expressions/><location startLine="99" startOffset="20" endLine="104" endOffset="42"/></Target><Target id="@+id/sideEffectsTextView" view="TextView"><Expressions/><location startLine="128" startOffset="20" endLine="133" endOffset="42"/></Target><Target id="@+id/usageInstructionsTextView" view="TextView"><Expressions/><location startLine="157" startOffset="20" endLine="162" endOffset="42"/></Target></Targets></Layout>