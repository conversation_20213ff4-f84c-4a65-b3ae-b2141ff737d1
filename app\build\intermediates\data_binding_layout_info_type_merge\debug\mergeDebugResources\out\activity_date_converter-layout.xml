<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_date_converter" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_date_converter.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_date_converter_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="638" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="10" startOffset="4" endLine="27" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="18" startOffset="8" endLine="25" endOffset="46"/></Target><Target id="@+id/conversionTypeGroup" view="RadioGroup"><Expressions/><location startLine="52" startOffset="12" endLine="78" endOffset="24"/></Target><Target id="@+id/radioMiladiToHijri" view="RadioButton"><Expressions/><location startLine="59" startOffset="16" endLine="67" endOffset="50"/></Target><Target id="@+id/radioHijriToMiladi" view="RadioButton"><Expressions/><location startLine="69" startOffset="16" endLine="76" endOffset="50"/></Target><Target id="@+id/buttonPickerMode" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="88" startOffset="16" endLine="97" endOffset="45"/></Target><Target id="@+id/buttonInputMode" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="99" startOffset="16" endLine="108" endOffset="45"/></Target><Target id="@+id/pickerModeLayout" view="LinearLayout"><Expressions/><location startLine="113" startOffset="12" endLine="248" endOffset="26"/></Target><Target id="@+id/dayCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="121" startOffset="16" endLine="161" endOffset="67"/></Target><Target id="@+id/dayText" view="TextView"><Expressions/><location startLine="150" startOffset="24" endLine="157" endOffset="57"/></Target><Target id="@+id/monthCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="163" startOffset="16" endLine="204" endOffset="67"/></Target><Target id="@+id/monthText" view="TextView"><Expressions/><location startLine="193" startOffset="24" endLine="200" endOffset="57"/></Target><Target id="@+id/yearCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="206" startOffset="16" endLine="246" endOffset="67"/></Target><Target id="@+id/yearText" view="TextView"><Expressions/><location startLine="235" startOffset="24" endLine="242" endOffset="57"/></Target><Target id="@+id/inputModeLayout" view="LinearLayout"><Expressions/><location startLine="251" startOffset="12" endLine="329" endOffset="26"/></Target><Target id="@+id/dayInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="271" startOffset="20" endLine="278" endOffset="49"/></Target><Target id="@+id/monthInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="295" startOffset="20" endLine="302" endOffset="49"/></Target><Target id="@+id/yearInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="318" startOffset="20" endLine="325" endOffset="49"/></Target><Target id="@+id/convertButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="332" startOffset="12" endLine="342" endOffset="52"/></Target><Target id="@+id/resultMiladiDate" view="TextView"><Expressions/><location startLine="376" startOffset="24" endLine="385" endOffset="63"/></Target><Target id="@+id/resultHijriDate" view="TextView"><Expressions/><location startLine="410" startOffset="24" endLine="419" endOffset="63"/></Target><Target id="@+id/resultDayName" view="TextView"><Expressions/><location startLine="445" startOffset="24" endLine="454" endOffset="63"/></Target><Target id="@+id/resultHijriMonth" view="TextView"><Expressions/><location startLine="479" startOffset="24" endLine="488" endOffset="63"/></Target><Target id="@+id/resultMiladiMonth" view="TextView"><Expressions/><location startLine="514" startOffset="24" endLine="523" endOffset="63"/></Target><Target id="@+id/resultSyrianiMonth" view="TextView"><Expressions/><location startLine="548" startOffset="24" endLine="557" endOffset="63"/></Target><Target id="@+id/resultHijriMonthDays" view="TextView"><Expressions/><location startLine="583" startOffset="24" endLine="592" endOffset="63"/></Target><Target id="@+id/resultMiladiMonthDays" view="TextView"><Expressions/><location startLine="617" startOffset="24" endLine="626" endOffset="63"/></Target></Targets></Layout>