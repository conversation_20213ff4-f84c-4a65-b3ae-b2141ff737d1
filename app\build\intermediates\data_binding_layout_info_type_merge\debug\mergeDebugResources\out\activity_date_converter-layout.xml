<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_date_converter" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_date_converter.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_date_converter_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="493" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="10" startOffset="4" endLine="27" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="18" startOffset="8" endLine="25" endOffset="46"/></Target><Target id="@+id/conversionTypeGroup" view="RadioGroup"><Expressions/><location startLine="52" startOffset="12" endLine="81" endOffset="24"/></Target><Target id="@+id/radioMiladiToHijri" view="RadioButton"><Expressions/><location startLine="60" startOffset="16" endLine="69" endOffset="53"/></Target><Target id="@+id/radioHijriToMiladi" view="RadioButton"><Expressions/><location startLine="71" startOffset="16" endLine="79" endOffset="50"/></Target><Target id="@+id/inputModeLayout" view="LinearLayout"><Expressions/><location startLine="88" startOffset="12" endLine="184" endOffset="26"/></Target><Target id="@+id/dayInput" view="EditText"><Expressions/><location startLine="112" startOffset="20" endLine="121" endOffset="48"/></Target><Target id="@+id/monthInput" view="EditText"><Expressions/><location startLine="142" startOffset="20" endLine="151" endOffset="48"/></Target><Target id="@+id/yearInput" view="EditText"><Expressions/><location startLine="171" startOffset="20" endLine="180" endOffset="48"/></Target><Target id="@+id/convertButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="187" startOffset="12" endLine="197" endOffset="52"/></Target><Target id="@+id/resultMiladiDate" view="TextView"><Expressions/><location startLine="231" startOffset="24" endLine="240" endOffset="63"/></Target><Target id="@+id/resultHijriDate" view="TextView"><Expressions/><location startLine="265" startOffset="24" endLine="274" endOffset="63"/></Target><Target id="@+id/resultDayName" view="TextView"><Expressions/><location startLine="300" startOffset="24" endLine="309" endOffset="63"/></Target><Target id="@+id/resultHijriMonth" view="TextView"><Expressions/><location startLine="334" startOffset="24" endLine="343" endOffset="63"/></Target><Target id="@+id/resultMiladiMonth" view="TextView"><Expressions/><location startLine="369" startOffset="24" endLine="378" endOffset="63"/></Target><Target id="@+id/resultSyrianiMonth" view="TextView"><Expressions/><location startLine="403" startOffset="24" endLine="412" endOffset="63"/></Target><Target id="@+id/resultHijriMonthDays" view="TextView"><Expressions/><location startLine="438" startOffset="24" endLine="447" endOffset="63"/></Target><Target id="@+id/resultMiladiMonthDays" view="TextView"><Expressions/><location startLine="472" startOffset="24" endLine="481" endOffset="63"/></Target></Targets></Layout>