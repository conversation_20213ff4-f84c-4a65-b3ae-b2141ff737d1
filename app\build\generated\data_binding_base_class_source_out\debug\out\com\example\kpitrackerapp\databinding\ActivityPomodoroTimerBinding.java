// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityPomodoroTimerBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton addTimeButton;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView progressText;

  @NonNull
  public final MaterialButton skipBreakButton;

  @NonNull
  public final MaterialButton startPauseButton;

  @NonNull
  public final MaterialButton stopButton;

  @NonNull
  public final TextView taskNameText;

  @NonNull
  public final TextView timerDisplay;

  @NonNull
  public final TextView timerDurationText;

  @NonNull
  public final TextView timerStatusText;

  private ActivityPomodoroTimerBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton addTimeButton, @NonNull ProgressBar progressBar,
      @NonNull TextView progressText, @NonNull MaterialButton skipBreakButton,
      @NonNull MaterialButton startPauseButton, @NonNull MaterialButton stopButton,
      @NonNull TextView taskNameText, @NonNull TextView timerDisplay,
      @NonNull TextView timerDurationText, @NonNull TextView timerStatusText) {
    this.rootView = rootView;
    this.addTimeButton = addTimeButton;
    this.progressBar = progressBar;
    this.progressText = progressText;
    this.skipBreakButton = skipBreakButton;
    this.startPauseButton = startPauseButton;
    this.stopButton = stopButton;
    this.taskNameText = taskNameText;
    this.timerDisplay = timerDisplay;
    this.timerDurationText = timerDurationText;
    this.timerStatusText = timerStatusText;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityPomodoroTimerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityPomodoroTimerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_pomodoro_timer, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityPomodoroTimerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addTimeButton;
      MaterialButton addTimeButton = ViewBindings.findChildViewById(rootView, id);
      if (addTimeButton == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.progressText;
      TextView progressText = ViewBindings.findChildViewById(rootView, id);
      if (progressText == null) {
        break missingId;
      }

      id = R.id.skipBreakButton;
      MaterialButton skipBreakButton = ViewBindings.findChildViewById(rootView, id);
      if (skipBreakButton == null) {
        break missingId;
      }

      id = R.id.startPauseButton;
      MaterialButton startPauseButton = ViewBindings.findChildViewById(rootView, id);
      if (startPauseButton == null) {
        break missingId;
      }

      id = R.id.stopButton;
      MaterialButton stopButton = ViewBindings.findChildViewById(rootView, id);
      if (stopButton == null) {
        break missingId;
      }

      id = R.id.taskNameText;
      TextView taskNameText = ViewBindings.findChildViewById(rootView, id);
      if (taskNameText == null) {
        break missingId;
      }

      id = R.id.timerDisplay;
      TextView timerDisplay = ViewBindings.findChildViewById(rootView, id);
      if (timerDisplay == null) {
        break missingId;
      }

      id = R.id.timerDurationText;
      TextView timerDurationText = ViewBindings.findChildViewById(rootView, id);
      if (timerDurationText == null) {
        break missingId;
      }

      id = R.id.timerStatusText;
      TextView timerStatusText = ViewBindings.findChildViewById(rootView, id);
      if (timerStatusText == null) {
        break missingId;
      }

      return new ActivityPomodoroTimerBinding((CoordinatorLayout) rootView, addTimeButton,
          progressBar, progressText, skipBreakButton, startPauseButton, stopButton, taskNameText,
          timerDisplay, timerDurationText, timerStatusText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
