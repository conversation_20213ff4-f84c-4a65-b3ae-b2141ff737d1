{"logs": [{"outputFile": "com.example.kpitrackerapp-mergeDebugResources-54:/values-az/values-az.xml", "map": [{"source": "C:\\Gradle\\gradle-8.4\\caches\\8.11.1\\transforms\\36b4f88c4f8ab3af336856b4860e45b2\\transformed\\material-1.11.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,353,438,518,603,682,778,894,974,1038,1132,1200,1259,1354,1417,1481,1540,1607,1670,1724,1839,1897,1959,2013,2084,2216,2300,2380,2514,2590,2666,2795,2879,2958,3015,3066,3132,3202,3280,3363,3443,3513,3589,3667,3738,3836,3922,4005,4098,4191,4264,4336,4430,4484,4568,4635,4719,4807,4871,4936,5000,5070,5172,5276,5372,5473,5534,5589", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,87,84,79,84,78,95,115,79,63,93,67,58,94,62,63,58,66,62,53,114,57,61,53,70,131,83,79,133,75,75,128,83,78,56,50,65,69,77,82,79,69,75,77,70,97,85,82,92,92,72,71,93,53,83,66,83,87,63,64,63,69,101,103,95,100,60,54,79", "endOffsets": "260,348,433,513,598,677,773,889,969,1033,1127,1195,1254,1349,1412,1476,1535,1602,1665,1719,1834,1892,1954,2008,2079,2211,2295,2375,2509,2585,2661,2790,2874,2953,3010,3061,3127,3197,3275,3358,3438,3508,3584,3662,3733,3831,3917,4000,4093,4186,4259,4331,4425,4479,4563,4630,4714,4802,4866,4931,4995,5065,5167,5271,5367,5468,5529,5584,5664"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3023,3111,3196,3276,3361,4167,4263,4379,6705,6769,6863,6931,6990,7085,7148,7212,7271,7338,7401,7455,7570,7628,7690,7744,7815,7947,8031,8111,8245,8321,8397,8526,8610,8689,8746,8797,8863,8933,9011,9094,9174,9244,9320,9398,9469,9567,9653,9736,9829,9922,9995,10067,10161,10215,10299,10366,10450,10538,10602,10667,10731,10801,10903,11007,11103,11204,11265,11320", "endLines": "5,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "endColumns": "12,87,84,79,84,78,95,115,79,63,93,67,58,94,62,63,58,66,62,53,114,57,61,53,70,131,83,79,133,75,75,128,83,78,56,50,65,69,77,82,79,69,75,77,70,97,85,82,92,92,72,71,93,53,83,66,83,87,63,64,63,69,101,103,95,100,60,54,79", "endOffsets": "310,3106,3191,3271,3356,3435,4258,4374,4454,6764,6858,6926,6985,7080,7143,7207,7266,7333,7396,7450,7565,7623,7685,7739,7810,7942,8026,8106,8240,8316,8392,8521,8605,8684,8741,8792,8858,8928,9006,9089,9169,9239,9315,9393,9464,9562,9648,9731,9824,9917,9990,10062,10156,10210,10294,10361,10445,10533,10597,10662,10726,10796,10898,11002,11098,11199,11260,11315,11395"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\8.11.1\\transforms\\44fac508c06d1fa71634b2a2e87bd97f\\transformed\\jetified-play-services-basement-18.1.0\\res\\values-az\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "157", "endOffsets": "352"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5471", "endColumns": "161", "endOffsets": "5628"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\8.11.1\\transforms\\d69c07a7d8d82aa3a4b3977e5b803b90\\transformed\\core-1.12.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,127", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3440,3541,3643,3746,3850,3951,4056,11484", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "3536,3638,3741,3845,3946,4051,4162,11580"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\8.11.1\\transforms\\32f7978bc74fb055e711be763c16e3bd\\transformed\\jetified-play-services-base-18.1.0\\res\\values-az\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,449,578,684,826,955,1071,1173,1335,1441,1586,1719,1859,2011,2071,2132", "endColumns": "104,150,128,105,141,128,115,101,161,105,144,132,139,151,59,60,76", "endOffsets": "297,448,577,683,825,954,1070,1172,1334,1440,1585,1718,1858,2010,2070,2131,2208"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4459,4568,4723,4856,4966,5112,5245,5365,5633,5799,5909,6058,6195,6339,6495,6559,6624", "endColumns": "108,154,132,109,145,132,119,105,165,109,148,136,143,155,63,64,80", "endOffsets": "4563,4718,4851,4961,5107,5240,5360,5466,5794,5904,6053,6190,6334,6490,6554,6619,6700"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\8.11.1\\transforms\\1fb6da15b525de990376081401abcb58\\transformed\\appcompat-1.6.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,895,986,1079,1173,1272,1372,1465,1560,1654,1745,1837,1922,2027,2133,2233,2342,2447,2549,2707,2813", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "210,311,421,509,616,730,812,890,981,1074,1168,1267,1367,1460,1555,1649,1740,1832,1917,2022,2128,2228,2337,2442,2544,2702,2808,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,425,526,636,724,831,945,1027,1105,1196,1289,1383,1482,1582,1675,1770,1864,1955,2047,2132,2237,2343,2443,2552,2657,2759,2917,11400", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "420,521,631,719,826,940,1022,1100,1191,1284,1378,1477,1577,1670,1765,1859,1950,2042,2127,2232,2338,2438,2547,2652,2754,2912,3018,11479"}}]}]}