<lint-module
    format="1"
    dir="D:\copy from kpi tracker app\kpi-tracker-app void test 2\app"
    name=":app"
    type="APP"
    maven="KPITrackerApp:app:unspecified"
    agpVersion="8.9.1"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-34\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\35.0.0\core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="debug"/>
</lint-module>
