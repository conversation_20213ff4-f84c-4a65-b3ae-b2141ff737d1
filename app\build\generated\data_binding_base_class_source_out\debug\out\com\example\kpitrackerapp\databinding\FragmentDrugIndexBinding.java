// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentDrugIndexBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView drugNameTextView;

  @NonNull
  public final TextView interactionsTextView;

  @NonNull
  public final EditText searchEditText;

  @NonNull
  public final ListView searchResultsListView;

  @NonNull
  public final TextView sideEffectsTextView;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView usageInstructionsTextView;

  @NonNull
  public final TextView usesTextView;

  private FragmentDrugIndexBinding(@NonNull LinearLayout rootView,
      @NonNull TextView drugNameTextView, @NonNull TextView interactionsTextView,
      @NonNull EditText searchEditText, @NonNull ListView searchResultsListView,
      @NonNull TextView sideEffectsTextView, @NonNull Toolbar toolbar,
      @NonNull TextView usageInstructionsTextView, @NonNull TextView usesTextView) {
    this.rootView = rootView;
    this.drugNameTextView = drugNameTextView;
    this.interactionsTextView = interactionsTextView;
    this.searchEditText = searchEditText;
    this.searchResultsListView = searchResultsListView;
    this.sideEffectsTextView = sideEffectsTextView;
    this.toolbar = toolbar;
    this.usageInstructionsTextView = usageInstructionsTextView;
    this.usesTextView = usesTextView;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDrugIndexBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDrugIndexBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_drug_index, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDrugIndexBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.drugNameTextView;
      TextView drugNameTextView = ViewBindings.findChildViewById(rootView, id);
      if (drugNameTextView == null) {
        break missingId;
      }

      id = R.id.interactionsTextView;
      TextView interactionsTextView = ViewBindings.findChildViewById(rootView, id);
      if (interactionsTextView == null) {
        break missingId;
      }

      id = R.id.searchEditText;
      EditText searchEditText = ViewBindings.findChildViewById(rootView, id);
      if (searchEditText == null) {
        break missingId;
      }

      id = R.id.searchResultsListView;
      ListView searchResultsListView = ViewBindings.findChildViewById(rootView, id);
      if (searchResultsListView == null) {
        break missingId;
      }

      id = R.id.sideEffectsTextView;
      TextView sideEffectsTextView = ViewBindings.findChildViewById(rootView, id);
      if (sideEffectsTextView == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.usageInstructionsTextView;
      TextView usageInstructionsTextView = ViewBindings.findChildViewById(rootView, id);
      if (usageInstructionsTextView == null) {
        break missingId;
      }

      id = R.id.usesTextView;
      TextView usesTextView = ViewBindings.findChildViewById(rootView, id);
      if (usesTextView == null) {
        break missingId;
      }

      return new FragmentDrugIndexBinding((LinearLayout) rootView, drugNameTextView,
          interactionsTextView, searchEditText, searchResultsListView, sideEffectsTextView, toolbar,
          usageInstructionsTextView, usesTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
