// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAddTaskModernBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final TextInputEditText dueDateEditText;

  @NonNull
  public final TextInputLayout dueDateInputLayout;

  @NonNull
  public final TextInputEditText dueTimeEditText;

  @NonNull
  public final TextInputLayout dueTimeInputLayout;

  @NonNull
  public final ExtendedFloatingActionButton fabSave;

  @NonNull
  public final MaterialCardView priorityHigh;

  @NonNull
  public final MaterialCardView priorityLow;

  @NonNull
  public final MaterialCardView priorityMedium;

  @NonNull
  public final TextInputEditText taskDescriptionEditText;

  @NonNull
  public final TextInputLayout taskDescriptionInputLayout;

  @NonNull
  public final TextInputEditText taskNameEditText;

  @NonNull
  public final TextInputLayout taskNameInputLayout;

  @NonNull
  public final MaterialCardView templateCall;

  @NonNull
  public final MaterialCardView templateMeeting;

  @NonNull
  public final MaterialCardView templateStudy;

  @NonNull
  public final MaterialToolbar toolbar;

  private ActivityAddTaskModernBinding(@NonNull CoordinatorLayout rootView,
      @NonNull AppBarLayout appBarLayout, @NonNull TextInputEditText dueDateEditText,
      @NonNull TextInputLayout dueDateInputLayout, @NonNull TextInputEditText dueTimeEditText,
      @NonNull TextInputLayout dueTimeInputLayout, @NonNull ExtendedFloatingActionButton fabSave,
      @NonNull MaterialCardView priorityHigh, @NonNull MaterialCardView priorityLow,
      @NonNull MaterialCardView priorityMedium, @NonNull TextInputEditText taskDescriptionEditText,
      @NonNull TextInputLayout taskDescriptionInputLayout,
      @NonNull TextInputEditText taskNameEditText, @NonNull TextInputLayout taskNameInputLayout,
      @NonNull MaterialCardView templateCall, @NonNull MaterialCardView templateMeeting,
      @NonNull MaterialCardView templateStudy, @NonNull MaterialToolbar toolbar) {
    this.rootView = rootView;
    this.appBarLayout = appBarLayout;
    this.dueDateEditText = dueDateEditText;
    this.dueDateInputLayout = dueDateInputLayout;
    this.dueTimeEditText = dueTimeEditText;
    this.dueTimeInputLayout = dueTimeInputLayout;
    this.fabSave = fabSave;
    this.priorityHigh = priorityHigh;
    this.priorityLow = priorityLow;
    this.priorityMedium = priorityMedium;
    this.taskDescriptionEditText = taskDescriptionEditText;
    this.taskDescriptionInputLayout = taskDescriptionInputLayout;
    this.taskNameEditText = taskNameEditText;
    this.taskNameInputLayout = taskNameInputLayout;
    this.templateCall = templateCall;
    this.templateMeeting = templateMeeting;
    this.templateStudy = templateStudy;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAddTaskModernBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAddTaskModernBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_add_task_modern, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAddTaskModernBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appBarLayout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.dueDateEditText;
      TextInputEditText dueDateEditText = ViewBindings.findChildViewById(rootView, id);
      if (dueDateEditText == null) {
        break missingId;
      }

      id = R.id.dueDateInputLayout;
      TextInputLayout dueDateInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (dueDateInputLayout == null) {
        break missingId;
      }

      id = R.id.dueTimeEditText;
      TextInputEditText dueTimeEditText = ViewBindings.findChildViewById(rootView, id);
      if (dueTimeEditText == null) {
        break missingId;
      }

      id = R.id.dueTimeInputLayout;
      TextInputLayout dueTimeInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (dueTimeInputLayout == null) {
        break missingId;
      }

      id = R.id.fabSave;
      ExtendedFloatingActionButton fabSave = ViewBindings.findChildViewById(rootView, id);
      if (fabSave == null) {
        break missingId;
      }

      id = R.id.priorityHigh;
      MaterialCardView priorityHigh = ViewBindings.findChildViewById(rootView, id);
      if (priorityHigh == null) {
        break missingId;
      }

      id = R.id.priorityLow;
      MaterialCardView priorityLow = ViewBindings.findChildViewById(rootView, id);
      if (priorityLow == null) {
        break missingId;
      }

      id = R.id.priorityMedium;
      MaterialCardView priorityMedium = ViewBindings.findChildViewById(rootView, id);
      if (priorityMedium == null) {
        break missingId;
      }

      id = R.id.taskDescriptionEditText;
      TextInputEditText taskDescriptionEditText = ViewBindings.findChildViewById(rootView, id);
      if (taskDescriptionEditText == null) {
        break missingId;
      }

      id = R.id.taskDescriptionInputLayout;
      TextInputLayout taskDescriptionInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskDescriptionInputLayout == null) {
        break missingId;
      }

      id = R.id.taskNameEditText;
      TextInputEditText taskNameEditText = ViewBindings.findChildViewById(rootView, id);
      if (taskNameEditText == null) {
        break missingId;
      }

      id = R.id.taskNameInputLayout;
      TextInputLayout taskNameInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskNameInputLayout == null) {
        break missingId;
      }

      id = R.id.templateCall;
      MaterialCardView templateCall = ViewBindings.findChildViewById(rootView, id);
      if (templateCall == null) {
        break missingId;
      }

      id = R.id.templateMeeting;
      MaterialCardView templateMeeting = ViewBindings.findChildViewById(rootView, id);
      if (templateMeeting == null) {
        break missingId;
      }

      id = R.id.templateStudy;
      MaterialCardView templateStudy = ViewBindings.findChildViewById(rootView, id);
      if (templateStudy == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityAddTaskModernBinding((CoordinatorLayout) rootView, appBarLayout,
          dueDateEditText, dueDateInputLayout, dueTimeEditText, dueTimeInputLayout, fabSave,
          priorityHigh, priorityLow, priorityMedium, taskDescriptionEditText,
          taskDescriptionInputLayout, taskNameEditText, taskNameInputLayout, templateCall,
          templateMeeting, templateStudy, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
