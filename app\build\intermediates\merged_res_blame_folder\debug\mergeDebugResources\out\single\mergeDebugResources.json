[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_item_search_result.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_search_result.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_chart_fill_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\chart_fill_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_rounded_background_light.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\rounded_background_light.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_share_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_share_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\xml\\network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_circle_green.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\circle_green.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_admin_panel_settings_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_admin_panel_settings_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_auto_send_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_auto_send_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_event_busy_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_event_busy_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_divider_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\divider_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\menu_overall_summary_context_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\menu\\overall_summary_context_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_add_edit_task_enhanced.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_add_edit_task_enhanced.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\menu_chart_share_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\menu\\chart_share_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_admin_dashboard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_admin_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\anim_bounce_animation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\anim\\bounce_animation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_excel_review.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_excel_review.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_content_copy_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_content_copy_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_filter_report_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_filter_report_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_calendar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_calendar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_item_task_enhanced.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_task_enhanced.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\menu_main_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\menu\\main_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_fragment_account.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\fragment_account.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_ocr_review.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_ocr_review.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_user_kpi_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_user_kpi_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_camera_alt_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_camera_alt_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\anim_shake_animation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\anim\\shake_animation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_pomodoro_timer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_pomodoro_timer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_drag_handle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_drag_handle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_description_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_description_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_alternate_email_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_alternate_email_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_color_selector_green.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\color_selector_green.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_email_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_email_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\anim_star_pulse.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\anim\\star_pulse.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_dialog_select_card_colors.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\dialog_select_card_colors.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_user_summary_card_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\user_summary_card_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_sort_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_sort_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_priority_indicator_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\priority_indicator_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_excel_import.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_excel_import.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_sort_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_sort_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_switch_account_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_switch_account_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_color_selector_orange.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\color_selector_orange.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_item_conversation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_conversation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\anim_fade_scale_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\anim\\fade_scale_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\color_bottom_nav_color_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\color\\bottom_nav_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_search_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_search_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_fragment_dashboard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\fragment_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_filter_all_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_filter_all_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_rounded_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\rounded_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_phone.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_phone.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_dialog_kpi_actions.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\dialog_kpi_actions.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_ocr.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_ocr.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_item_admin_dashboard_action.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_admin_dashboard_action.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_overall_summary_card_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\overall_summary_card_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_task_report.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_task_report.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_keyboard_arrow_up_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_keyboard_arrow_up_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_fragment_main_dashboard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\fragment_main_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_kpi_list_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\kpi_list_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_master_card_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_master_card_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_item_chat_message_sent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_chat_message_sent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_dialog_custom_pomodoro.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\dialog_custom_pomodoro.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_task_management.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_task_management.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_time.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_time.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_document_scanner_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_document_scanner_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_item_chat_date_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_chat_date_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_mic_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_mic_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_unread_badge_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\unread_badge_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_report_table_row.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\report_table_row.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_color_selector_purple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\color_selector_purple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_color_swatch_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\color_swatch_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_item_chat_system_message.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_chat_system_message.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_task_reminder_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_task_reminder_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_dashboard_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_dashboard_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ripple_effect.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ripple_effect.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\menu_sort_options_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\menu\\sort_options_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_delete_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_delete_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_business_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_business_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_blue_gradient_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\blue_gradient_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_status_dot_orange.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\status_dot_orange.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_performance_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_performance_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_timer_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_timer_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_item_task_report.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_task_report.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\color_chip_background_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\color\\chip_background_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\xml_file_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\xml\\file_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_overall_summary_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\overall_summary_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_person_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_person_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_sort_alpha.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_sort_alpha.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_report_table_row_tabular.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\report_table_row_tabular.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\mipmap-xxxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\mipmap-xxxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_dialog_edit_task.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\dialog_edit_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_call_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_call_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_status_dot_grey.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\status_dot_grey.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_item_notification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_fragment_messages.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\fragment_messages.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\menu_user_summary_context_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\menu\\user_summary_context_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_search_edit_progress.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_search_edit_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_refresh_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_refresh_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\menu_admin_bottom_navigation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\menu\\admin_bottom_navigation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_notifications.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_notifications.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_item_task.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_dialog_search_messages.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\dialog_search_messages.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_access_time_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_access_time_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_card_top_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\card_top_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_logout_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_logout_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_ocr_review_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\ocr_review_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_header_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\header_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_add_time.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_add_time.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_user_checkbox_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\user_checkbox_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_card_drag_shadow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\card_drag_shadow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_check.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_check.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_create_user.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_create_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_gradient_admin_welcome.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\gradient_admin_welcome.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_unified_report_table_row_binding.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\unified_report_table_row_binding.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_text_view_border.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\text_view_border.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_circle_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\circle_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_item_admin_dashboard_user.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_admin_dashboard_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\menu_admin_dashboard_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\menu\\admin_dashboard_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\menu_main_bottom_navigation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\menu\\main_bottom_navigation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_report.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_report.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_format_color_reset_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_format_color_reset_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_add_edit_kpi.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_add_edit_kpi.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\menu_task_item_actions_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\menu\\task_item_actions_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_send_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_send_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_add_edit_kpi_original.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_add_edit_kpi_original.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_item_chat_message_received.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_chat_message_received.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_dialog_custom_reminder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\dialog_custom_reminder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\color_chip_text_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\color\\chip_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\menu_chat_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\menu\\chat_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_arrow_back_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_arrow_back_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_kpi_card_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\kpi_card_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_messages_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_messages_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_report_table_row_colored.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\report_table_row_colored.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_custom_progress_bar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\custom_progress_bar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_delete.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_delete.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_email.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_email.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_status_dot_red.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\status_dot_red.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_circle_indicator.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\circle_indicator.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_location_on_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_location_on_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_notification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_attachment_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_attachment_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_date_header_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\date_header_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_videocam_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_videocam_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_calendar_today_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_calendar_today_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_account_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_account_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_security_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_security_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_kpi_detail_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\kpi_detail_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_whatsapp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_whatsapp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_arrow_upward_16.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_arrow_upward_16.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_item_admin_dashboard_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_admin_dashboard_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_chat.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_chat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\menu_kpi_detail_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\menu\\kpi_detail_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_kpi_summary_detail_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\kpi_summary_detail_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_expire_management.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_expire_management.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_more_vert_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_more_vert_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_attach_file_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_attach_file_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_priority_high_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_priority_high_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_arrow_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_arrow_back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_notifications.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_notifications.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_horizontal_rule_16.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_horizontal_rule_16.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\anim_card_release_scale.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\anim\\card_release_scale.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\menu_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\menu\\main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_add_task_modern.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_add_task_modern.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_notifications_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_notifications_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_chat_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_chat_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_settings_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_settings_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_task_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_task_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_item_admin_dashboard_stat.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_admin_dashboard_stat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_search_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\search_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_view_list_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_view_list_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\menu_chart_options_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\menu\\chart_options_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_progress_entry_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\progress_entry_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_card_bottom_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\card_bottom_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_unified_report_table.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\unified_report_table.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_skip.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_skip.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_header_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\header_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_filter_expiry_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_filter_expiry_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_notification_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_notification_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_assessment_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_assessment_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_compact_report_table_row.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\compact_report_table_row.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_spinner_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\spinner_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\color_chip_background_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\color\\chip_background_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_add_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_add_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_filter_task_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_filter_task_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_color_selector_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\color_selector_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_item_recent_user.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_recent_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_dialog_progress_update.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\dialog_progress_update.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\anim_card_press_scale.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\anim\\card_press_scale.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_excel_review_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\excel_review_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_item_user_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_user_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_chart_marker_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\chart_marker_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_arrow_downward_16.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_arrow_downward_16.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_fragment_performance.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\fragment_performance.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_modern_report.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_modern_report.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_my_location_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_my_location_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\anim_slide_in_bottom.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\anim\\slide_in_bottom.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_status_dot_green.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\status_dot_green.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_sort_descending.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_sort_descending.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_color_selector_red.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\color_selector_red.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_system_message_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\system_message_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\menu_task_management_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\menu\\task_management_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_chart_fill_gradient_purple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\chart_fill_gradient_purple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_volume_off_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_volume_off_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\menu_chat_list_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\menu\\chat_list_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_circle_red.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\circle_red.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_item_subtask_mini.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_subtask_mini.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_kpi_detail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_kpi_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_edit_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_edit_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_circle_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\circle_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\color_chip_text_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\color\\chip_text_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_user_filter_dialog.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\user_filter_dialog.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_reply_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\reply_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_circular_progress_bar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\circular_progress_bar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_online_indicator.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\online_indicator.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_unified_report_table_row.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\unified_report_table_row.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_filter_list_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_filter_list_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_dialog_add_edit_progress.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\dialog_add_edit_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_category_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_category_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_chat_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_chat_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_activity_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\activity_login.xml"}, {"merged": "com.example.kpitrackerapp-debug-56:/raw_notification_sound.mp3.flat", "source": "com.example.kpitrackerapp-main-58:/raw/notification_sound.mp3"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_sort_ascending.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_sort_ascending.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_compact_report_table.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\compact_report_table.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\layout_item_admin_dashboard_activity.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\layout\\item_admin_dashboard_activity.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-debug-56:\\drawable_ic_baseline_keyboard_arrow_down_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.kpitrackerapp-main-58:\\drawable\\ic_baseline_keyboard_arrow_down_24.xml"}]