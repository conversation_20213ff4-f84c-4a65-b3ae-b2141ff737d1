http://schemas.android.com/apk/res-auto;;${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/bounce_animation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/slide_in_bottom.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/star_pulse.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/fade_scale_in.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/shake_animation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/card_release_scale.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/card_press_scale.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/arrays.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/chip_text_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/bottom_nav_color_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/chip_background_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/chip_text_color.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/chip_background_color.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_event_busy_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_arrow_upward_16.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_filter_list_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_view_list_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/color_selector_orange.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_admin_panel_settings_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_content_copy_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_security_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/online_indicator.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_task_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/status_dot_orange.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_time.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/system_message_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sort_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_format_color_reset_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_filter_report_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/blue_gradient_button_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/search_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notification.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_more_vert_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ripple_effect.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_calculator.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/input_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_calendar.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_arrow_downward_16.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_videocam_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_skip.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/card_drag_shadow.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_performance_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notifications.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/unread_badge_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_keyboard_arrow_down_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/chart_fill_gradient_purple.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/color_selector_blue.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_notifications_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/button_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_share_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_send_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_blue.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_arrow_back_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_red.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_assessment_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_business_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_email_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_my_location_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_email.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_background_light.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_call_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_drag_handle.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/status_dot_grey.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_check.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/overall_summary_gradient.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_description_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_delete.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_back.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/spinner_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_add_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_timer_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sort_ascending.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_alternate_email_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_chat_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sort_descending.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_switch_account_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_attach_file_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_calendar_today_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_filter_task_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_green.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/card_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_keyboard_arrow_up_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/gradient_admin_welcome.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_mic_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/chart_fill_gradient.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_indicator.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_master_card_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/date_header_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_account_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_delete_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/status_dot_green.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_horizontal_rule_16.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/header_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_priority_high_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_refresh_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add_time.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_filter_expiry_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/color_selector_purple.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_access_time_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/result_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_document_scanner_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_logout_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_filter_all_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_phone.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/card_top_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_category_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notification_icon.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/priority_indicator_gradient.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/card_bottom_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/color_swatch_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_edit_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_camera_alt_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_sort_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/reply_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_volume_off_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_messages_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_person_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/color_selector_red.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/color_selector_green.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/text_view_border.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_dashboard_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circular_progress_bar.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_search_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_settings_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sort_alpha.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/status_dot_red.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_location_on_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/header_gradient.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/custom_progress_bar.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_whatsapp.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_search.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_attachment_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_admin_dashboard_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/kpi_card_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/unified_report_table_row.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/unified_report_table_row_binding.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_edit_kpi.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_edit_task_enhanced.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_task_modern.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_admin_dashboard_action.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_kpi_detail.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/chart_share_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_pomodoro_timer.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/excel_review_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/kpi_detail_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_main_dashboard.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_recent_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_edit_kpi_original.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_task_management.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_drug_index.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/task_management_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_kpi_actions.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/kpi_detail_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_date_converter.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_ocr_review.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/overall_summary_card_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_dashboard.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_performance.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/overall_summary_context_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_custom_reminder.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_expire_management.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_excel_import.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_task_enhanced.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_modern_report.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/compact_report_table_row.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/unified_report_table.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/main.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/main_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_edit_task.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/user_filter_dialog.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/ocr_review_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_report.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/sort_options_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_select_card_colors.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_edit_progress.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/admin_bottom_navigation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/main_bottom_navigation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_chat_message_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_chat_message_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_user_list.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_chat.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_excel_review.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_task_reminder_settings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_task.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_admin_dashboard.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_auto_send_settings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_chat_list.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_search_result.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/report_table_row.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/report_table_row_colored.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/report_table_row_tabular.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_create_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/kpi_summary_detail_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/chat_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_ocr.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_user_kpi_list.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_subtask_mini.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_account.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_conversation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_search_edit_progress.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_admin_dashboard_stat.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_task_report.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/user_summary_context_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/chat_list_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/ids.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/progress_entry_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/user_summary_card_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_notification.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/kpi_list_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/task_item_actions_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/chart_marker_view.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/user_checkbox_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/date_converter_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_search_messages.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_chat_system_message.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_progress_update.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/admin_dashboard_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_task_report.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_messages.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/compact_report_table.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_admin_dashboard_header.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_admin_dashboard_activity.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_custom_pomodoro.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/chart_options_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_notifications.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_chat_date_header.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/divider_view.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/raw/notification_sound.mp3,${\:app*buildDir}/generated/res/processReleaseGoogleServices/values/values.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/styles.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/file_paths.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+anim:bounce_animation,0,F;slide_in_bottom,1,F;star_pulse,2,F;fade_scale_in,3,F;shake_animation,4,F;card_release_scale,5,F;card_press_scale,6,F;+array:reminder_options,7,V4019c6b95,1301a36d17,;@string/reminder_option_no_reminder,@string/reminder_option_on_due_date,@string/reminder_option_1_day,@string/reminder_option_2_days,@string/reminder_option_3_days,@string/reminder_option_1_week,;owner_type_options_add_edit,7,V4004f1416,130054151e,;@string/owner_type_user,@string/owner_type_department,@string/owner_type_company,@string/owner_type_other,;tools_array,8,V400030058,13000700f9,;اختر أداة,shift hour calculator,محول التاريخ,;am_pm_array,8,V4000a0120,13000d0185,;AM,PM,;+color:progress_indicator_track,9,V400290a33,3c00290a6b,;"#FFBDBDBD";text_primary,9,V4006c1b1e,30006c1b4a,;"#FF212121";progress_color_default,9,V400300c52,3a00300c88,;"#FF6200EE";status_red,9,V400240892,2e002408bc,;"#FFF44336";light_red,9,V400360e8d,2d00360eb6,;"#FFEF9A9A";default_kpi_card_top_gradient,9,V400190557,4100190594,;"#FF6200EE";kpi_concern,9,V400200766,2f00200791,;"#FFFF6F00";message_read_color,9,V4007a1dde,36007a1e10,;"#FF4CAF50";chip_text_selector,10,F;notification_reminder_bg,9,V400861fe0,3c00862018,;"#FFFEF7E0";energy_medium,9,V400c42a11,3100c42a3e,;"#FF2196F3";kpi_good,9,V4002107b6,2c002107de,;"#FF4CAF50";soft_lavender_background,9,V4002508d8,3c00250910,;"#FFE6E6FA";dark_gray,9,V4000a01a9,2b000a01d0,;"#A9A9A9";purple_700,9,V400040099,2e000400c3,;"#FF3700B3";doctor_color_3_lighter,9,V400511567,380051159b,;"#F1FAF1";primary_dark,9,V4006e1b84,30006e1bb0,;"#FF3700B3";success_color,9,V400a1244e,3100a1247b,;"#FF4CAF50";status_orange,9,V400230846,3100230873,;"#FFFF9800";icon_tint,9,V400bf2957,2d00bf2980,;"#FF757575";chart_target_color,9,V400160461,3600160493,;"#FFEF5350";chart_achieved_color,9,V4001704b1,38001704e5,;"#FF42A5F5";default_kpi_card_color,9,V400180504,3a0018053a,;"#FFFFFFFF";progress_indicator_blue,9,V4002a0a92,3b002a0ac9,;"#FF2196F3";system_message_background,9,V400781d69,3d00781da2,;"#40757575";surface,9,V40089208a,2b008920b1,;"#FFFFFFFF";doctor_color_5_light,9,V4004c143e,36004c1470,;"#FCE4EC";gray_800,9,V40062198c,2c006219b4,;"#FF424242";blue_500,9,V400d42ca9,2c00d42cd1,;"#FF2196F3";background_light,9,V4006b1ae8,34006b1b18,;"#FFF5F5F5";default_kpi_card_bottom_gradient,9,V4001a05b6,44001a05f6,;"#FF3700B3";search_background,9,V400c02986,3500c029b7,;"#FFFFFFFF";performance_poor,9,V4005a1824,32005a1852,;"#F44336";dark_yellow,9,V4003b100e,2f003b1039,;"#FFFBC02D";urgent_chip_background,9,V400b026ac,3a00b026e2,;"#FFFFE0E0";category_education,9,V400b727e4,3600b72816,;"#FF9C27B0";task_color_orange,9,V400cc2b77,3500cc2ba8,;"#FFFF9800";medium_priority_color,9,V400d02c0b,3900d02c40,;"#FFFF9800";progress_tint,9,V400ab25f1,3100ab261e,;"#FF4CAF50";light_blue_200,9,V400110310,320011033e,;"#FF81D4FA";screen_background_light_blue,9,V4002809d4,4000280a10,;"#FFE3F2FD";priority_urgent,9,V400962298,33009622c7,;"#FFF44336";yellow,9,V4000e026c,2a000e0292,;"#FFFFFF00";light_blue_600,9,V400120344,3200120372,;"#FF039BE5";energy_low,9,V400c329e1,2e00c32a0b,;"#FFFF9800";importance_low,9,V4009922ef,320099231d,;"#FF9E9E9E";urgent_important_bg,9,V400a524dd,3700a52510,;"#FFD32F2F";fab_color,9,V4002d0b31,2d002d0b5a,;"#FF6200EE";doctor_color_4_lighter,9,V4005215b8,38005215ec,;"#FFF8ED";bottom_nav_color_selector,11,F;kpi_warning,9,V4003e1109,2f003e1134,;"#FFFFC107";summary_card_bottom_yellow,9,V400150402,3e0015043c,;"#FFFFF59D";gray_500,9,V4005f1902,2c005f192a,;"#FF9E9E9E";not_urgent_important_bg,9,V400a62516,3b00a6254d,;"#FF1976D2";purple_accent,9,V400260931,310026095e,;"#FF9C27B0";light_gray,9,V40009017b,2c000901a3,;"#D3D3D3";black,9,V400070125,290007014a,;"#FF000000";low_priority_color,9,V400d12c46,3600d12c78,;"#FF4CAF50";orange_500,9,V400d72d33,2e00d72d5d,;"#FFFF9800";priority_medium,9,V400942230,330094225f,;"#FFFF9800";kpi_share_background,9,V4007b1e16,38007b1e4a,;"#FF2196F3";notification_system_bg,9,V40087201e,3a00872054,;"#FFE3F2FD";energy_high,9,V400c52a44,2f00c52a6f,;"#FF4CAF50";light_yellow,9,V4003a0fbd,30003a0fe9,;"#FFFFF59D";progress_text,9,V400ad265d,3100ad268a,;"#FF4CAF50";card_background_default,9,V4003d10b4,3b003d10eb,;"#FFFFFFFF";blue,9,V4000d0242,28000d0266,;"#FF0000FF";background_color,9,V4008e214b,34008e217b,;"#FFF5F5F5";ai_suggestion_background,9,V400c82a9a,3c00c82ad2,;"#FFF3E5F5";chip_background_selector,12,F;doctor_color_1,9,V40041118b,30004111b7,;"#673AB7";doctor_color_2,9,V4004211cd,30004211f9,;"#2196F3";light_green,9,V400380f21,2f00380f4c,;"#FFA5D6A7";doctor_color_3,9,V40043120d,**********,;"#4CAF50";doctor_color_4,9,V40044124e,300044127a,;"#FF9800";doctor_color_5,9,V400451290,30004512bc,;"#E91E63";performance_average,9,V400581754,**********,;"#FFC107";text_hint,9,V4008a20b7,2d008a20e0,;"#FF9E9E9E";read_only_overlay,9,V400661a1d,3500661a4e,;"#40000000";whatsapp_green,9,V4008d2117,32008d2145,;"#FF25D366";priority_high,9,V400952265,**********,;"#FFFF5722";category_health,9,V400b627af,3300b627de,;"#FFFF9800";summary_card_image_background,9,V400320d46,4100320d83,;"#00FFFFFF";text_secondary,9,V4006d1b50,32006d1b7e,;"#FF757575";doctor_color_4_light,9,V4004b13f0,36004b1422,;"#FFF3E0";user_color,9,V400811f18,2e00811f42,;"#FF607D8B";performance_excellent,9,V400561687,37005616ba,;"#4CAF50";dark_red,9,V400370ed8,2c00370f00,;"#FFD32F2F";category_shopping,9,V400ba2887,3500ba28b8,;"#FFFF5722";header_gradient_start,9,V4001e06b8,39001e06ed,;"#FF6200EE";category_work,9,V400b42745,3100b42772,;"#FF2196F3";urgent_color,9,V400a0241c,3000a02448,;"#FFF44336";category_travel,9,V400bb28be,3300bb28ed,;"#FF00BCD4";message_input_background,9,V400741c85,3c00741cbd,;"#FFFFFFFF";doctor_color_5_lighter,9,V40053160a,380053163e,;"#FDF2F6";light_blue_900,9,V400130378,32001303a6,;"#FF01579B";green_500,9,V400d52cd7,2d00d52d00,;"#FF4CAF50";red,9,V4000b01d6,27000b01f9,;"#FFFF0000";doctor_color_1_light,9,V400481309,360048133b,;"#EDE7F6";white,9,V400080150,**********,;"#FFFFFFFF";teal_700,9,V4000600f7,2c0006011f,;"#FF018786";chip_text_color,13,F;performance_good,9,V4005716ed,320057171b,;"#8BC34A";unread_badge_color,9,V400761cf5,3600761d27,;"#FFF44336";not_urgent_not_important_bg,9,V400a82590,3f00a825cb,;"#FF757575";task_color_red,9,V400c92ad8,3200c92b06,;"#FFF44336";purple_500,9,V400030069,2e00030093,;"#FF6200EE";owner_card_border,9,V4006519e6,3500651a17,;"#FF6200EE";category_personal,9,V400b52778,3500b527a9,;"#FF4CAF50";gray_600,9,V400601930,2c00601958,;"#FF757575";summary_card_username_text,9,V400330ddb,3e00330e15,;"#FFFFFFFF";notification_kpi_bg,9,V400851fa7,3700851fda,;"#FFE8F5E8";doctor_color_1_lighter,9,V4004f14c5,38004f14f9,;"#F3F0F9";performance_below_average,9,V4005917bc,3b005917f3,;"#FF9800";chip_background_default,9,V4002e0b89,3b002e0bc0,;"#FFE0E0E0";gray_200,9,V4005d18a6,2c005d18ce,;"#FFEEEEEE";primary_color,9,V4008f2181,31008f21ae,;"#FF6200EE";chip_background_color,14,F;purple_button_text,9,V400270984,36002709b6,;"#FFFFFFFF";progress_share_background,9,V4007c1e50,3d007c1e89,;"#FF4CAF50";progress_background,9,V400ac2624,3700ac2657,;"#FFE0E0E0";summary_card_top_red,9,V4001403ac,38001403e0,;"#FFEF9A9A";overdue_chip_background,9,V400b126e8,3b00b1271f,;"#FFFFE0E0";task_color_purple,9,V400cd2bae,3500cd2bdf,;"#FF9C27B0";date_header_background,9,V400771d2d,3a00771d63,;"#40000000";summary_card_image_tint,9,V400310cb7,3b00310cee,;"#FFFFFFFF";chart_line_purple,9,V4003c105d,35003c108e,;"#FFAB47BC";background,9,V40088205a,2e00882084,;"#FFF5F5F5";task_color_green,9,V400cb2b41,3400cb2b71,;"#FF4CAF50";sent_message_background,9,V400721c07,3b00721c3e,;"#FF6200EE";category_finance,9,V400b8281c,3400b8284c,;"#FFF44336";purple_200,9,V400020039,2e00020063,;"#FFBB86FC";online_color,9,V400751cc3,3000751cef,;"#FF4CAF50";red_500,9,V400d62d06,2b00d62d2d,;"#FFF44336";teal_200,9,V4000500c9,2c000500f1,;"#FF03DAC5";doctor_color_2_light,9,V400491357,3600491389,;"#E3F2FD";progress_track_color,9,V4002f0bef,38002f0c23,;"#FFBDBDBD";doctor_color_2_lighter,9,V400501517,380050154b,;"#EFF8FE";header_gradient_end,9,V4001f0710,37001f0743,;"#FF3700B3";importance_medium,9,V4009a2323,35009a2354,;"#FF2196F3";overdue_color,9,V400a22481,3100a224ae,;"#FFF44336";highlight_yellow,9,V400da2d8f,3400da2dbf,;"#FFFFFF00";importance_critical,9,V4009c238f,37009c23c2,;"#FFE91E63";matrix_toggle_background,9,V400be2919,3c00be2951,;"#FFF0F0F0";status_green,9,V4002207fc,3000220828,;"#FF4CAF50";gray_700,9,V40061195e,2c00611986,;"#FF616161";reply_background,9,V400791da8,3400791dd8,;"#20000000";category_family,9,V400b92852,3300b92881,;"#FFE91E63";green,9,V4000c01ff,29000c0224,;"#FF00FF00";notification_admin_bg,9,V400841f6c,3900841fa1,;"#FFF3E5F5";dark_green,9,V400390f70,2e00390f9a,;"#FF388E3C";view_only_indicator,9,V400681a88,3700681abb,;"#FFFF9800";doctor_color_3_light,9,V4004a13a3,36004a13d5,;"#E8F5E9";edit_indicator,9,V400671a54,3200671a82,;"#FF4CAF50";light_blue_50,9,V4001002dd,310010030a,;"#FFE1F5FE";super_admin_color,9,V4007f1eb0,35007f1ee1,;"#FFE91E63";gray_300,9,V4005e18d4,2c005e18fc,;"#FFE0E0E0";priority_low,9,V4009321fe,300093222a,;"#FF4CAF50";importance_high,9,V4009b235a,33009b2389,;"#FF9C27B0";orange,9,V4000f0298,2a000f02be,;"#FFFFA500";chart_fill_purple_light,9,V4001d0651,3b001d0688,;"#406200EE";received_message_background,9,V400731c44,3f00731c7f,;"#FFFFFFFF";admin_color,9,V400801ee7,2f00801f12,;"#FF9C27B0";task_color_blue,9,V400ca2b0c,3300ca2b3b,;"#FF2196F3";chat_background,9,V400711bd2,3300711c01,;"#FFF5F5F5";today_color,9,V4009f23eb,2f009f2416,;"#FF2196F3";urgent_not_important_bg,9,V400a72553,3b00a7258a,;"#FFFF9800";+dimen:color_swatch_size,15,V40005008c,30000500b8,;"36dp";card_elevation_pressed,15,V4000b01a2,35000b01d3,;"12dp";card_corner_radius,15,V4000c01d9,31000c0206,;"16dp";card_elevation,15,V4000a0174,2c000a019c,;"4dp";fab_margin,15,V400020039,290002005e,;"16dp";color_swatch_stroke_width,15,V4000600be,37000600f1,;"2dp";card_border_width,15,V400090117,2f00090142,;"1dp";+drawable:ic_baseline_event_busy_24,16,F;ic_baseline_arrow_upward_16,17,F;ic_baseline_filter_list_24,18,F;ic_baseline_view_list_24,19,F;color_selector_orange,20,F;ic_baseline_admin_panel_settings_24,21,F;ic_baseline_content_copy_24,22,F;ic_baseline_security_24,23,F;online_indicator,24,F;ic_baseline_task_24,25,F;status_dot_orange,26,F;ic_time,27,F;system_message_background,28,F;ic_sort_24,29,F;ic_baseline_format_color_reset_24,30,F;ic_filter_report_24,31,F;blue_gradient_button_background,32,F;search_background,33,F;ic_notification,34,F;ic_baseline_more_vert_24,35,F;circle_background,36,F;ripple_effect,37,F;ic_calculator,38,F;input_background,39,F;ic_calendar,40,F;ic_baseline_arrow_downward_16,41,F;ic_baseline_videocam_24,42,F;ic_skip,43,F;card_drag_shadow,44,F;ic_performance_24,45,F;ic_notifications,46,F;unread_badge_background,47,F;ic_baseline_keyboard_arrow_down_24,48,F;chart_fill_gradient_purple,49,F;color_selector_blue,50,F;ic_baseline_notifications_24,51,F;button_background,52,F;ic_share_24,53,F;ic_baseline_send_24,54,F;circle_blue,55,F;ic_baseline_arrow_back_24,56,F;circle_red,57,F;ic_baseline_assessment_24,58,F;ic_baseline_business_24,59,F;ic_baseline_email_24,60,F;ic_baseline_my_location_24,61,F;ic_email,62,F;rounded_background_light,63,F;ic_baseline_call_24,64,F;ic_drag_handle,65,F;status_dot_grey,66,F;ic_check,67,F;overall_summary_gradient,68,F;ic_baseline_description_24,69,F;ic_delete,70,F;ic_arrow_back,71,F;spinner_background,72,F;ic_baseline_add_24,73,F;ic_baseline_timer_24,74,F;ic_sort_ascending,75,F;ic_baseline_alternate_email_24,76,F;ic_baseline_chat_24,77,F;ic_sort_descending,78,F;ic_baseline_switch_account_24,79,F;ic_baseline_attach_file_24,80,F;ic_baseline_calendar_today_24,81,F;ic_filter_task_24,82,F;circle_green,83,F;card_background,84,F;ic_baseline_keyboard_arrow_up_24,85,F;gradient_admin_welcome,86,F;ic_baseline_mic_24,87,F;chart_fill_gradient,88,F;circle_indicator,89,F;ic_master_card_24,90,F;date_header_background,91,F;ic_account_24,92,F;ic_baseline_delete_24,93,F;status_dot_green,94,F;ic_baseline_horizontal_rule_16,95,F;header_background,96,F;ic_baseline_priority_high_24,97,F;ic_baseline_refresh_24,98,F;rounded_background,99,F;ic_add_time,100,F;ic_filter_expiry_24,101,F;color_selector_purple,102,F;ic_baseline_access_time_24,103,F;result_background,104,F;ic_baseline_document_scanner_24,105,F;ic_baseline_logout_24,106,F;ic_filter_all_24,107,F;ic_phone,108,F;card_top_background,109,F;ic_baseline_category_24,110,F;ic_notification_icon,111,F;priority_indicator_gradient,112,F;card_bottom_background,113,F;color_swatch_background,114,F;ic_baseline_edit_24,115,F;ic_baseline_camera_alt_24,116,F;ic_baseline_sort_24,117,F;reply_background,118,F;ic_baseline_volume_off_24,119,F;ic_messages_24,120,F;ic_baseline_person_24,121,F;color_selector_red,122,F;color_selector_green,123,F;text_view_border,124,F;ic_dashboard_24,125,F;circular_progress_bar,126,F;ic_baseline_search_24,127,F;ic_baseline_settings_24,128,F;ic_sort_alpha,129,F;status_dot_red,130,F;ic_baseline_location_on_24,131,F;header_gradient,132,F;custom_progress_bar,133,F;ic_whatsapp,134,F;ic_search,135,F;ic_baseline_attachment_24,136,F;+id:userBadge,137,F;percentageTextView,138,F;percentageTextView,139,F;percentageTextView,140,F;taskDescriptionInputLayout,141,F;taskDescriptionInputLayout,142,F;taskDescriptionInputLayout,143,F;actionTitle,144,F;monthlyProgressPercentageTextView,145,F;action_share_as_pdf,146,F;timerDisplay,147,F;tvExcelReviewDate,148,F;tvExcelReviewDate,148,F;tvExcelReviewDate,148,F;chipContextOffice,141,F;kpiAchievedPercentageTextView,149,F;kpiRecyclerView,150,F;userAvatarImageView,151,F;kpiNameEditText,152,F;tvUrgentTasks,153,F;searchEditText,154,F;action_view_task_report,155,F;action_edit_kpi,156,F;action_edit_kpi,157,F;taskEstimatedTimeEditText,141,F;shiftCalculatorSection,158,F;btnAddAnother,141,F;btn_confirm_ocr_import,159,F;btn_confirm_ocr_import,159,F;tvExcelReviewUser,148,F;tvExcelReviewUser,148,F;overallSummaryCardView,160,F;dashboardRecyclerView,161,F;dailyProgressIndicatorContainer,145,F;kpiUnitLabelTextView,152,F;cardExpiryManagement,162,F;tvCompletionRate,153,F;context_overall_change_color,163,F;chipGroupTaskIcon,141,F;datePicker,164,F;layoutSettings,141,F;action_clear_month_progress,157,F;expiryReturnCard,165,F;kpiMonthlyPercentageTextView,149,F;scrollViewFilters,153,F;tvExcelImportStatus,166,F;tvExcelImportStatus,166,F;templateCall,141,F;templateCall,143,F;timePicker,164,F;unitPercentageRadioButton,152,F;masterCardBarrier,167,F;bottom_sheet_title,156,F;priorityIndicator,168,F;rvNotUrgentImportant,153,F;userFilterLayout,169,F;dailyAchievedTextView,170,F;unifiedReportRecyclerView,171,F;action_logout,172,F;action_logout,173,F;actionCard,144,F;toolbarTaskManagement,153,F;etEditTaskExpirationDate,174,F;radioSelectUsers,175,F;reportContentContainer,169,F;taskReminderInputLayout,141,F;taskReminderInputLayout,142,F;btn_delete_review_item,176,F;btn_delete_review_item,176,F;adminRoleAutoComplete,177,F;annualProgressPercentageText,145,F;kpiSelectorAutoCompleteTextView,169,F;kpiSelectorAutoCompleteTextView,178,F;sort_ascending,179,F;buttonSaveColors,180,F;colorBlue,141,F;dialogLastEntryDateTextView,181,F;nav_messages,182,F;nav_messages,183,F;messageCard,184,F;messageCard,185,F;userEmail,186,F;action_duplicate_copy,156,F;messageEditText,187,F;rbCalculateAverage,188,F;radioNoReminder,189,F;btnFilter,153,F;statusIndicatorDot,190,F;statusIndicatorDot,168,F;taskEnergyLevelInputLayout,142,F;nearExpiryCard,165,F;addTimeButton,147,F;dayInput,158,F;rememberMeCheckBox,177,F;fragmentContainer,191,F;fragmentContainer,167,F;switchEmailEnabled,192,F;tvTaskIcon,168,F;fabNewChat,193,F;messageTime,194,F;radioDaily,169,F;radioDaily,178,F;reportMonthlyTarget,195,F;reportMonthlyTarget,196,F;reportMonthlyTarget,197,F;goButton,169,F;goButton,178,F;currentTextView,138,F;currentTextView,138,F;currentTextView,138,F;spinnerUserFilter,188,F;spinnerUserFilter,188,F;progressOverall,153,F;tvExcelReviewValue,148,F;tvExcelReviewValue,148,F;tvExcelReviewValue,148,F;monthFilterSpinner,145,F;summaryBarChart,169,F;summaryBarChart,178,F;sliderProgress,141,F;fullNameEditText,198,F;btnSelectExcelFile,166,F;btnSelectExcelFile,166,F;kpiDetailAnnualPercentTextView,199,F;action_call,200,F;ownerChip,138,F;ownerChip,138,F;loginFormCard,177,F;loginFormCard,177,F;appBarLayoutTaskManagement,153,F;chipGroupEnergyLevel,141,F;monthInput,158,F;tvOcrResult,201,F;reportQuarterlyRow,195,F;reportQuarterlyRow,196,F;reportQuarterlyRow,197,F;kpiTargetInputLayout,152,F;compactReportTable,169,F;compactReportTable,178,F;dialogTitleTextView,181,F;buttonPickBottomColor,180,F;reportKpiNameTextView,195,F;colorRed,141,F;rgAggregationOptions,188,F;rgAggregationOptions,188,F;fabAddKpiUserList,202,F;dailyPercentageTextView,170,F;buttonCancelColorSelection,180,F;taskEstimatedTimeInputLayout,141,F;kpiDescriptionEditText,152,F;taskNameText,147,F;etEditTaskName,174,F;chatButton,186,F;recentUsersRecyclerView,177,F;trendChartTitle,169,F;tvSubtaskName,203,F;kpiDailyTargetEditText,152,F;userFilterAutoCompleteTextView,169,F;cardQuickStats,153,F;tvTotalTasks,153,F;priceCardView,154,F;currentValueTextView,138,F;cardDrugIndex,162,F;textUserEmail,204,F;btnShareBarChart,169,F;et_review_value,176,F;et_review_value,176,F;tvEnergyLevel,168,F;timerStatusText,147,F;cancelButton,142,F;cancelButton,198,F;cancelButton,175,F;priorityLow,143,F;taskCategorySelector,141,F;selectDateButton,165,F;taskImportanceInputLayout,142,F;resultMiladiMonth,158,F;colorOrange,141,F;chipCategory,168,F;btnScheduleTask,168,F;scrollView,145,F;scrollView,169,F;remainingIcon,138,F;remainingIcon,138,F;remainingIcon,138,F;remainingIcon,138,F;remainingIcon,138,F;userName,137,F;userName,205,F;userName,186,F;rvNotUrgentNotImportant,153,F;annualProgressPercentageTextView,145,F;taskTagsInputLayout,142,F;taskLocationEditText,141,F;cardDateConverter,162,F;deleteButton,206,F;welcomeTextView,177,F;welcomeTextView,177,F;statCard,207,F;kpiQuarterlyTargetInputLayout,152,F;appBarLayoutTaskReport,208,F;btnTaskSuggestions,168,F;switchRecurring,141,F;priorityMedium,143,F;senderName,194,F;radioMiladiToHijri,158,F;userFilterRadioGroup,175,F;departmentEditText,198,F;tvTaskDetails,168,F;action_change_user_card_color,209,F;switchBreakDown,141,F;addPhotoButton,198,F;addPhotoButton,198,F;tvNotUrgentImportantCount,153,F;topColorPreview,180,F;chipIconWork,141,F;searchPromptTextView,206,F;appBarLayoutUserKpi,202,F;ivAttachment,168,F;tv_review_date,176,F;unreadBadge,205,F;action_search,210,F;action_search,200,F;averagePerDayTextView,145,F;endDateEditText,169,F;endDateEditText,178,F;chipIconSport,141,F;switchWhatsappReminders,189,F;sortChartButton,211,V40003006a,2d00030093,;"";reportDailyRow,195,F;reportDailyRow,196,F;reportDailyRow,197,F;entryDateTextView,212,F;requiredDailyValueTextView,138,F;usageInstructionsTextView,154,F;inputModeLayout,158,F;topContentContainer,160,F;topContentContainer,213,F;switchReminderEmail,192,F;taskReminderAutoCompleteTextView,141,F;taskReminderAutoCompleteTextView,142,F;detailKpiTargetValueTextView,145,F;remainingTextView,138,F;remainingTextView,138,F;remainingTextView,138,F;kpiUnitRadioGroup,152,F;quickAdd10Button,145,F;progressIndicatorsLayout,145,F;chipGroupFilters,153,F;btnSaveDraft,141,F;reportContentCard,169,F;profilePictureCard,198,F;profilePictureCard,198,F;quickAdd50Button,145,F;dialogTitle,175,F;topBackgroundView,160,F;topBackgroundView,213,F;topBackgroundView,213,F;btnViewMode,153,F;resultStartTime,158,F;detailKpiNameTextView,145,F;chipContextTravel,141,F;ownerTypeAutoCompleteTextView,152,F;timerDurationText,147,F;kpiLineChart,145,F;userAvatar,184,F;star_animator_tag,211,V400020039,2f00020064,;"";dateSpinner,206,F;messageTextView,214,F;trendChart,169,F;trendChart,178,F;action_search_edit_progress,157,F;radio3Days,189,F;targetIcon,138,F;targetIcon,138,F;targetIcon,138,F;targetIcon,138,F;targetIcon,138,F;targetIcon,138,F;btnLocation,141,F;monthlyProgressIndicatorContainer,145,F;layoutActionButtons,168,F;kpiTargetLabelTextView,215,F;action_add_to_calendar,216,F;taskPriorityInputLayout,142,F;textUserName,204,F;remainingValueTextView,138,F;selectOwnerImageButton,152,F;tvUserName,217,F;layoutDetails,168,F;kpiTargetEditText,152,F;tvTaskStatusText,190,F;tvFocusTime,168,F;resultEndTime,158,F;etEditTaskExpirationTime,174,F;chipUrgent,153,F;selectedDrugDetailsTextView,154,F;resultHijriMonth,158,F;startTimeInput,158,F;rvExcelReviewItems,188,F;userCard,137,F;userCard,186,F;convertButton,158,F;resultDayName,158,F;chipEnergyMedium,141,F;cbTaskCompleted,174,F;currentPeriodTargetTextView,138,F;currentPeriodTargetTextView,138,F;currentPeriodTargetTextView,138,F;radioAnnual,169,F;radioAnnual,178,F;fabAddKpi,150,F;userCheckbox,218,F;unreadIndicator,214,F;action_admin_dashboard,172,F;action_admin_dashboard,173,F;bottomColorPreview,180,F;taskTagsEditText,142,F;action_auto_send_settings,172,F;buttonTestEmail,189,F;menu_shift_calculator,219,F;buttonScheduleNow,192,F;startDateEditText,169,F;startDateEditText,178,F;colorPurple,141,F;resultMiladiMonthDays,158,F;summaryChartFilterRadioGroup,169,F;summaryChartFilterRadioGroup,178,F;switchReminderWhatsapp,192,F;reportQuarterlyPercentage,195,F;reportQuarterlyPercentage,196,F;reportQuarterlyPercentage,197,F;typeIconTextView,214,F;muteIcon,205,F;currentPeriodTargetValueTextView,138,F;adminModeCheckBox,177,F;actionIcon,144,F;editedText,184,F;editedText,185,F;templateMeeting,141,F;templateMeeting,143,F;kpiCurrentLabelTextView,215,F;taskNotesEditText,142,F;tvExcelReviewTitle,188,F;tvExcelReviewTitle,188,F;currentPeriodAchievementValueTextView,138,F;btnConfirmExcelImport,188,F;btnConfirmExcelImport,188,F;hoursInput,158,F;annualProgressLayout,145,F;ivCompleteTask,168,F;chipGroupTags,168,F;action_test_alarm,172,F;action_chat,172,F;action_chat,173,F;startDateInputLayout,169,F;startDateInputLayout,169,F;startDateInputLayout,169,F;startDateInputLayout,178,F;startDateInputLayout,178,F;startDateInputLayout,178,F;replyMessageText,184,F;replyMessageText,185,F;switchMatrixView,153,F;searchInput,220,F;userNameTextView,151,F;btnSearch,153,F;statValue,207,F;taskCategoryText,141,F;cardInteractiveReport,162,F;btnTemplate,141,F;lastMessage,205,F;tabLayout,193,F;kpiQuarterlyTargetEditText,152,F;priorityHigh,143,F;tvDueDate,168,F;startPauseButton,147,F;radioHijriToMiladi,158,F;systemText,221,F;tvTaskDescription,168,F;reportUserNameTextView,195,F;reportUserNameTextView,196,F;reportUserNameTextView,197,F;userKpiRecyclerView,202,F;progressIndicator,138,F;progressIndicator,138,F;progressIndicator,138,F;progressIndicator,138,F;progressIndicator,138,F;progressIndicator,138,F;progressIndicator,138,F;progressIndicator,138,F;timeText,184,F;timeText,185,F;timeText,205,F;starRatingTextView,138,F;starRatingTextView,138,F;appLogoImageView,177,F;appLogoImageView,177,F;chipOverdue,153,F;seekBarProgress,222,F;chipEnergyHigh,141,F;appBarLayout,141,F;appBarLayout,142,F;appBarLayout,143,F;appBarLayout,191,F;appBarLayout,187,F;appBarLayout,193,F;appBarLayout,158,F;appBarLayout,167,F;appBarLayout,169,F;appBarLayout,178,F;selectedDrugNameTextView,154,F;taskEnergyLevelAutoCompleteTextView,142,F;searchView,153,F;reportDailyPercentage,195,F;reportDailyPercentage,196,F;reportDailyPercentage,197,F;dueTimeInputLayout,143,F;chipCompleted,153,F;rbImportIndividual,188,F;action_settings,223,F;action_settings,210,F;chipAll,153,F;dailyProgressLayout,145,F;overallSummaryCardInclude,167,F;currentAchievementProgressIndicator,138,F;currentAchievementProgressIndicator,138,F;currentAchievementProgressIndicator,138,F;currentAchievementProgressIndicator,138,F;currentAchievementProgressIndicator,138,F;currentAchievementProgressIndicator,138,F;currentAchievementProgressIndicator,138,F;currentAchievementProgressIndicator,138,F;resultSyrianiMonth,158,F;guideline_summary_detail_70,199,F;guideline_summary_detail_70,199,F;kpiDailyTargetInputLayout,152,F;tvReportTaskName,224,F;editTextEmail,192,F;editTextEmail,189,F;conversionTypeGroup,158,F;progressBar,147,F;createButton,198,F;reportMonthlyAchieved,195,F;reportMonthlyAchieved,196,F;reportMonthlyAchieved,197,F;saveButton,206,F;sort_descending,179,F;radio1Week,189,F;cardTaskFollowUp,162,F;detailKpiProgressLabelTextView,145,F;rvUrgentNotImportant,153,F;buttonTestWeeklyReport,192,F;detailKpiMonthlyTargetValueTextView,145,F;monthlyProgressPercentageText,145,F;valueEditText,206,F;valueEditText,181,F;kpiDetailMonthlyPercentTextView,199,F;templateStudy,141,F;templateStudy,143,F;headerTextView,198,F;headerTextView,198,F;action_ocr,172,F;action_ocr,173,F;action_modern_task,172,F;switchRecurringTask,142,F;chipIconIdea,141,F;dateConverterSection,158,F;action_save_to_device,146,F;action_video_call,200,F;monthlyTargetTextView,170,F;chipToday,153,F;action_reset_color,156,F;drugNameTextView,154,F;cardChatList,225,F;layoutEisenhowerMatrix,153,F;currentPeriodAchievementIcon,138,F;currentPeriodAchievementIcon,138,F;currentPeriodAchievementIcon,138,F;currentPeriodAchievementIcon,138,F;currentPeriodAchievementIcon,138,F;bottomBackgroundView,213,F;tvAdvancedDetailsHeader,141,F;tvReportTaskExpirationDate,224,F;monthlyPercentageTextView,170,F;detailKpiDailyTargetValueTextView,145,F;taskExpirationTimeInputLayout,141,F;taskExpirationTimeInputLayout,142,F;roleIcon,186,F;action_chat_info,200,F;ivTaskActions,190,F;ivTaskActions,190,F;ivTaskActions,190,F;ivTaskActions,190,F;ivTaskActions,168,F;usernameInputLayout,198,F;usernameInputLayout,177,F;quickAccessTextView,177,F;quickAccessTextView,177,F;rvTasksDueSoon,208,F;textEmailStatus,192,F;rvAllTasksReport,208,F;messageText,184,F;messageText,185,F;messageText,194,F;taskNameEditText,141,F;taskNameEditText,142,F;taskNameEditText,143,F;ownerNameInputLayout,152,F;spinnerMonthFilter,188,F;spinnerMonthFilter,188,F;layoutAdvancedDetails,141,F;orTextView,177,F;onlineIndicator,205,F;onlineIndicator,186,F;messagesRecyclerView,187,F;userSummaryCardView,213,F;emptyStateTextView,150,F;action_switch_user,172,F;action_switch_user,173,F;currentPeriodAchievementTextView,138,F;currentPeriodAchievementTextView,138,F;currentPeriodAchievementTextView,138,F;compactReportRecyclerView,226,F;reportQuarterlyAchieved,195,F;reportQuarterlyAchieved,196,F;reportQuarterlyAchieved,197,F;action_notifications,172,F;reportAnnualTarget,195,F;reportAnnualTarget,196,F;reportAnnualTarget,197,F;action_reminder_settings,155,F;createUserButton,177,F;monthlyProgressLayout,145,F;radio1Day,189,F;progressText,147,F;headerTitle,227,F;action_archived,210,F;dateButton,181,F;currentIcon,138,F;currentIcon,138,F;currentIcon,138,F;currentIcon,138,F;currentIcon,138,F;currentIcon,138,F;radioAllUsers,175,F;taskDescriptionEditText,141,F;taskDescriptionEditText,142,F;taskDescriptionEditText,143,F;switchEmailReminders,189,F;addProgressButtonLayout,145,F;cbTaskComplete,168,F;reportAnnualRow,195,F;reportAnnualRow,196,F;reportAnnualRow,197,F;tilEditTaskReminderDays,174,F;scrollViewOcrResult,201,F;tvComparison,217,F;lineChartTitle,145,F;kpiDetailsContainer,160,F;kpiDetailsContainer,213,F;layoutFocusTime,168,F;btnSelectImage,201,F;tvTimeRemaining,168,F;buttonCancelSchedules,192,F;annualCircularProgressIndicator,145,F;textWhatsappStatus,192,F;tvTaskName,190,F;tvTaskName,190,F;tvTaskName,190,F;tvTaskName,168,F;kpiDescriptionInputLayout,152,F;emptyStateTextViewUserKpi,202,F;statIcon,207,F;tvUrgentImportantCount,153,F;activityUser,228,F;periodTextView,139,F;periodTextView,140,F;priceTextView,154,F;kpiNameTextView,149,F;kpiNameTextView,215,F;reportAnnualAchieved,195,F;reportAnnualAchieved,196,F;reportAnnualAchieved,197,F;taskNameInputLayout,141,F;taskNameInputLayout,142,F;taskNameInputLayout,143,F;calculateShiftButton,158,F;layoutEnergyFocus,168,F;senderText,184,F;btnCreateMasterCard,167,F;taskEstimatedHoursInputLayout,142,F;action_share_user_card_image,209,F;kpiSubtitleTextView,138,F;kpiSubtitleTextView,138,F;reportAnnualPercentage,195,F;reportAnnualPercentage,196,F;reportAnnualPercentage,197,F;action_delete_task,216,F;appTitleTextView,177,F;appTitleTextView,177,F;userPerformance,137,F;action_clear_progress,157,F;addTaskButton,141,F;addTaskButton,142,F;taskNotesInputLayout,142,F;buttonResetColors,180,F;buttonSave,192,F;buttonSave,189,F;shiftResultSection,158,F;buttonCheckApps,192,F;buttonCheckApps,189,F;loginButton,177,F;tvValue,217,F;fabAddTask,153,F;switchNotifications,141,F;tvNotUrgentNotImportantCount,153,F;barChartContainer,169,F;reportMonthlyPercentage,195,F;reportMonthlyPercentage,196,F;reportMonthlyPercentage,197,F;dailyProgressPercentageText,145,F;taskExpirationDateEditText,141,F;taskExpirationDateEditText,142,F;unitNumberRadioButton,152,F;statTitle,207,F;action_change_color,156,F;tvSettingsHeader,141,F;userSelectionSpinner,177,F;nav_dashboard,182,F;nav_dashboard,183,F;btnVoiceNote,141,F;tvProgressValue,141,F;tvProgressValue,222,F;detailKpiCurrentValueTextView,145,F;sideEffectsTextView,154,F;drugAutoComplete,154,F;cbSubtaskComplete,203,F;ownerTypeInputLayout,152,F;minutesInput,229,F;kpiNameLabel,169,F;kpiNameLabel,178,F;nearExpiryMessageTextView,165,F;adminRoleInputLayout,177,F;tilEditTaskExpirationDate,174,F;action_advanced_task,172,F;reportTableCard,178,F;targetValueTextView,138,F;unitPointRadioButton,152,F;emailInputLayout,198,F;reportDailyTarget,195,F;reportDailyTarget,196,F;reportDailyTarget,197,F;buttonTestWhatsapp,189,F;action_excel_import,157,F;kpiProgressPercentageTextView,215,F;templateExercise,141,F;tvTodayTasks,153,F;kpiTitleTextView,138,F;kpiTitleTextView,138,F;kpiTitleTextView,138,F;kpiTitleTextView,138,F;kpiTitleTextView,138,F;kpiTitleTextView,138,F;kpiTitleTextView,138,F;kpiTitleTextView,138,F;currentPeriodTargetIcon,138,F;currentPeriodTargetIcon,138,F;currentPeriodTargetIcon,138,F;currentPeriodTargetIcon,138,F;currentPeriodTargetIcon,138,F;searchResults,220,F;kpiNameInputLayout,152,F;resultHijriMonthDays,158,F;progress_circular_layout,215,F;resultHijriDate,158,F;lastUpdateIcon,138,F;lastUpdateIcon,138,F;lastUpdateIcon,138,F;lastUpdateIcon,138,F;action_expiry_management,172,F;action_expiry_management,173,F;etEditTaskReminderDays,174,F;chipIconHealth,141,F;btnSortTasks,153,F;action_share,230,F;kpiMonthlyTargetEditText,152,F;replyContainer,184,F;replyContainer,185,F;monthlyCircularProgressIndicator,145,F;cardMatrixToggle,153,F;tvUrgentNotImportantCount,153,F;tvTasksDueSoonTitle,208,F;action_delete_kpi,156,F;action_delete_kpi,157,F;taskExpirationDateInputLayout,141,F;taskExpirationDateInputLayout,142,F;colorGreen,141,F;achievedTextView,139,F;achievedTextView,140,F;departmentInputLayout,198,F;dailyProgressPercentageTextView,145,F;statusText,185,F;rvSubtasks,168,F;userRole,186,F;buttonLogout,204,F;cardProfile,204,F;dueDateEditText,143,F;rvTasks,153,F;fab,231,F;annualPercentageTextView,170,F;actionDescription,144,F;titleTextView,214,F;loadingProgressBar,198,F;loadingProgressBar,177,F;textUserRole,204,F;tvAssignedUser,168,F;switchMonthlyReports,192,F;kpiMonthlyTargetInputLayout,152,F;progressBarExcelImport,166,F;conversationCard,205,F;nav_account,182,F;nav_account,183,F;btnShareTrendChart,169,F;dragHandleImageView,213,F;dragHandleImageView,213,F;chipGroupContext,141,F;rv_ocr_review,159,F;action_share_as_image,146,F;action_ocr_import,157,F;chipPriority,168,F;dueTimeEditText,143,F;rvUrgentImportant,153,F;dateText,232,F;kpiDetailNameTextView,199,F;kpiDetailNameTextView,199,F;kpiDetailNameTextView,199,F;kpiDetailNameTextView,199,F;kpiDetailNameTextView,199,F;kpiDetailNameTextView,199,F;ivSelectedImage,201,F;buttonPickTopColor,180,F;layoutEmptyState,153,F;action_delete_user_kpi,209,F;entryValueTextView,212,F;taskPriorityText,141,F;taskCategoryInputLayout,142,F;skipBreakButton,147,F;layoutAssignedUser,168,F;saveKpiButton,152,F;sendButton,187,F;kpiAnnualPercentageTextView,149,F;chipContextOnline,141,F;resultTotalHours,158,F;reportConstraintLayout,169,F;taskPriorityAutoCompleteTextView,142,F;tvProgressPercent,168,F;tvOcrResultLabel,201,F;taskExpirationTimeEditText,141,F;taskExpirationTimeEditText,142,F;emailEditText,198,F;quickStatsCard,145,F;btnTimer,141,F;targetsTitleTextView,145,F;kpiCurrentValueTextView,215,F;currentAchievementPercentageTextView,138,F;userCheckboxRecyclerView,175,F;chipIconStudy,141,F;switchWeeklyReports,192,F;switchAutoReminders,192,F;toolbarUserKpi,202,F;tvEnergyIcon,168,F;action_refresh,223,F;btnCopyText,201,F;userImage,205,F;userImage,186,F;dueDateInputLayout,143,F;tvAllTasksTitle,208,F;bottomAppBar,141,F;ownerImageView,152,F;ownerImageView,213,F;ownerImageView,213,F;ownerImageView,213,F;ownerImageView,213,F;ownerImageView,213,F;ownerImageView,213,F;buttonLayout,198,F;ownerNameSelectionLayout,152,F;profilePictureImageView,198,F;requiredDailyIcon,138,F;requiredDailyIcon,138,F;requiredDailyIcon,138,F;requiredDailyIcon,138,F;requiredDailyIcon,138,F;activityTime,228,F;kpiCircularProgressIndicator,215,F;targetTextView,138,F;targetTextView,138,F;targetTextView,138,F;targetTextView,139,F;targetTextView,140,F;toolbar,141,F;toolbar,142,F;toolbar,143,F;toolbar,191,F;toolbar,192,F;toolbar,187,F;toolbar,193,F;toolbar,158,F;toolbar,167,F;toolbar,169,F;toolbar,231,F;toolbar,178,F;toolbar,189,F;toolbar,154,F;nav_performance,182,F;nav_performance,183,F;formLayout,198,F;formLayout,198,F;reportMonthlyRow,195,F;reportMonthlyRow,196,F;reportMonthlyRow,197,F;annualAchievedTextView,170,F;tvPercentage,217,F;stopButton,147,F;valueInputLayout,206,F;tilEditTaskName,174,F;trendChartContainer,169,F;barChartTitle,169,F;amPmSpinner,158,F;interactionsTextView,154,F;activityAction,228,F;action_edit_task,216,F;kpiTargetValueTextView,215,F;reportQuarterlyTarget,195,F;reportQuarterlyTarget,196,F;reportQuarterlyTarget,197,F;switchWhatsappEnabled,192,F;radioMonthly,169,F;radioMonthly,178,F;bottomNavigation,191,F;bottomNavigation,167,F;currentAchievementLabelTextView,138,F;expiryReturnMessageTextView,165,F;action_share_card,156,F;action_export_user_data_csv,209,F;addProgressButton,145,F;reportDailyAchieved,195,F;reportDailyAchieved,196,F;reportDailyAchieved,197,F;applyButton,175,F;recyclerView,193,F;recyclerView,231,F;progressTask,168,F;annualProgressIndicatorContainer,145,F;cardAiSuggestions,141,F;tilEditTaskExpirationTime,174,F;yearInput,158,F;toolbarTaskReport,208,F;btnCamera,141,F;taskEstimatedHoursEditText,142,F;resultMiladiDate,158,F;ownerNameEditText,152,F;taskPrioritySelector,141,F;kpiOwnerTextView,215,F;activityCard,228,F;btnStartTask,168,F;tvTarget,217,F;requiredDailyTextView,138,F;requiredDailyTextView,138,F;requiredDailyTextView,138,F;lastUpdateValueTextView,138,F;editTextPhone,192,F;editTextPhone,189,F;tvTaskExpirationDate,190,F;layoutQuickActions,168,F;lastUpdateTextView,138,F;lastUpdateTextView,138,F;lastUpdateTextView,138,F;fullNameInputLayout,198,F;sort_alphabetical,179,F;timeTextView,214,F;action_view_modern_report,172,F;action_view_modern_report,173,F;kpiSelectorLayout,169,F;kpiSelectorLayout,178,F;doctorNameTextView,170,F;doctorNameTextView,139,F;doctorNameTextView,140,F;taskImportanceAutoCompleteTextView,142,F;dateHeader,185,F;unitCurrencyRadioButton,152,F;switchLocalNotifications,189,F;manufacturerTextView,154,F;dailyTargetTextView,170,F;dailyCircularProgressIndicator,145,F;endDateInputLayout,169,F;endDateInputLayout,169,F;endDateInputLayout,178,F;endDateInputLayout,178,F;progressSummaryTextView,145,F;kpiProgressBar,149,F;ownerNameTextView,213,F;taskLocationInputLayout,141,F;monthlyAchievedTextView,170,F;annualTargetTextView,170,F;attachButton,187,F;chipThisWeek,153,F;btnSaveTaskReport,208,F;usernameEditText,198,F;usernameEditText,177,F;taskCategoryAutoCompleteTextView,142,F;lastEntryDateTextView,145,F;fabSave,143,F;emptyStateText,193,F;emptyStateText,231,F;thisWeekProgressTextView,145,F;chipContextHome,141,F;buttonTestMonthlyReport,192,F;targetsCard,145,F;usesTextView,154,F;chipEnergyLow,141,F;replyToText,184,F;replyToText,185,F;+layout:item_task_report,224,F;activity_modern_report,169,F;activity_add_edit_task_enhanced,142,F;fragment_drug_index,154,F;dialog_kpi_actions,156,F;activity_kpi_detail,145,F;compact_report_table_row,170,F;item_admin_dashboard_action,144,F;report_table_row_tabular,197,F;activity_excel_review,188,F;fragment_messages,225,F;compact_report_table,226,F;item_chat_message_sent,185,F;kpi_detail_item,149,F;activity_user_kpi_list,202,F;activity_chat_list,193,F;item_task_enhanced,168,F;activity_ocr_review,159,F;item_user_list,186,F;item_subtask_mini,203,F;activity_pomodoro_timer,147,F;activity_date_converter,158,F;item_conversation,205,F;activity_add_edit_kpi,141,F;dialog_add_edit_progress,181,F;activity_login,177,F;report_table_row_colored,196,F;kpi_summary_detail_item,199,F;kpi_list_item,215,F;dialog_edit_task,174,F;unified_report_table_row_binding,140,F;activity_chat,187,F;dialog_progress_update,222,F;activity_report,178,F;item_chat_system_message,221,F;fragment_performance,162,F;overall_summary_card_item,160,F;activity_add_task_modern,143,F;report_table_row,195,F;item_notification,214,F;activity_excel_import,166,F;activity_task_management,153,F;ocr_review_item,176,F;user_checkbox_item,218,F;fragment_dashboard,161,F;item_task,190,F;unified_report_table,171,F;activity_auto_send_settings,192,F;activity_main,167,F;user_filter_dialog,175,F;item_chat_message_received,184,F;dialog_search_messages,220,F;activity_add_edit_kpi_original,152,F;activity_create_user,198,F;fragment_account,204,F;fragment_main_dashboard,150,F;activity_notifications,231,F;item_admin_dashboard_header,227,F;activity_expire_management,165,F;unified_report_table_row,139,F;activity_task_report,208,F;item_recent_user,151,F;chart_marker_view,217,F;divider_view,233,F;user_summary_card_item,213,F;activity_admin_dashboard,191,F;activity_task_reminder_settings,189,F;dialog_custom_reminder,164,F;kpi_card_item,138,F;excel_review_item,148,F;item_admin_dashboard_activity,228,F;dialog_select_card_colors,180,F;dialog_custom_pomodoro,229,F;item_admin_dashboard_user,137,F;activity_search_edit_progress,206,F;activity_ocr,201,F;item_admin_dashboard_stat,207,F;item_chat_date_header,232,F;item_search_result,194,F;progress_entry_item,212,F;+menu:kpi_detail_menu,157,F;task_item_actions_menu,216,F;chat_list_menu,210,F;main_menu,173,F;task_management_menu,155,F;overall_summary_context_menu,163,F;main,172,F;main_bottom_navigation,183,F;chat_menu,200,F;sort_options_menu,179,F;date_converter_menu,219,F;admin_dashboard_menu,223,F;chart_options_menu,230,F;admin_bottom_navigation,182,F;user_summary_context_menu,209,F;chart_share_menu,146,F;+mipmap:ic_launcher,234,F;+raw:notification_sound,235,F;+string:import_successful_count,7,V400b02ef9,5600b02f4b,;"Imported %1$d out of %2$d entries.";add_kpi_title,7,V40002004b,3100020078,;"Add KPI";reset_to_default_color,7,V40059163a,3a00591670,;"Default";kpi_duplicated_success,7,V400f23fab,4f00f23ff6,;"KPI duplicated successfully.";remaining_percentage_label,7,V400d03720,4300d0375f,;"Remaining %\:";import_excel_data_button,7,V4009b27d7,40009b2813,;"Import Data";add_progress_entry_button,7,V4011c4bef,48011c4c33,;"Add Progress Entry";view_history_button_text,7,V40023084f,410023088c,;"View History";selected_users_label,7,V401174ab4,4001174af0,;"Selected Users\:";percentage_value_label,7,V400ce36a2,3e00ce36dc,;"Percentage\:";kpi_expiry_reminder_text_quarterly,7,V400f94203,7d00f9427c,;"Quarterly target for '%1$s' is approaching. Review progress.";no_text_found_ocr,7,V4008d2388,53008d23d7,;"No text found in image or OCR failed.";generate_report_button,7,V400ba31af,4200ba31ed,;"Generate Report";delete_kpi_confirmation_title,7,V40176629d,3e017662d7,;"TODO";monthly_progress_label_short,7,V40061183d,460061187f,;"Month\: %1$d%%";edit_task_dialog_title,7,V40140552c,3c01405564,;"Edit Task";progress_cleared_toast,7,V4008220fb,440082213b,;"Progress cleared.";dialog_kpi_action_title,7,V4014355b5,3f014355f0,;"KPI Actions";kpi_monthly_target_hint,7,V4000801c4,4d0008020d,;"Monthly Target (Optional)";action_edit_kpi,7,V4003a0e7c,34003a0eac,;"Edit KPI";kpi_expiry_reminder_text_monthly,7,V400f84188,7900f841fd,;"Monthly target for '%1$s' is approaching. Review progress.";change_color,7,V4015e5cdf,35015e5d10,;"Change Color";excel_review_title,7,V4009a2795,40009a27d1,;"Review Excel Data";confirm_clear_progress_title,7,V4007e1f3e,4f007e1f89,;"Confirm Clear Progress";edit_ocr_item_title,7,V40094261f,3d00942658,;"Edit OCR Item";kpi_card_item_required_daily_label,7,V401535a25,4a01535a6b,;"Daily Req.\:";error_deleting_kpi,7,V401796355,3301796384,;"TODO";action_clear_month_progress,7,V40080201b,4f00802066,;"Clear Month's Progress";action_edit,7,V401595b9f,2c01595bc7,;"Edit";kpis_assigned_label,7,V4006a1aba,41006a1af7,;"KPIs Assigned\: %d";error_average_for_all_users,7,V400af2e67,9000af2ef3,;"Cannot calculate average for All Users. Select a specific user or import individually.";user_summary_context_menu_title,7,V401054628,4d01054671,;"User Card Actions";duplicate_kpi_menu_item,7,V400f13f68,4100f13fa5,;"Duplicate KPI";unit_point,7,V4000e032b,2c000e0353,;"Point";user_name_label,7,V400421046,3500421077,;"User Name";expiry_notification_channel_description,7,V400f6409e,9e00f64138,;"Reminders for KPIs nearing their implicit monthly/quarterly/annual review or target dates.";user_card_color_top_label,7,V400ed3e7c,4500ed3ebd,;"Card Top Color\:";overall_summary,7,V401565b21,3b01565b58,;"Overall Summary";user_summaries_title,7,V400771d7d,3f00771db8,;"User Summaries";color_picker_title_individual_kpi_card,7,V4005815dc,5c00581634,;"Individual KPI Card Color";menu_search_kpis,7,V400d838f4,3800d83928,;"Search KPIs";import_partially_successful,7,V400b22fad,7800b23021,;"Import partially successful. %1$d entries imported\, %2$d errors.";near_expiry_notification_message,7,V4019068e3,4101906920,;"TODO";summary_detail_annual_format,7,V401655e76,4501655eb7,;"Annual\: %d%%";confirm_clear_month_progress_message,7,V40081206c,8d008120f5,;"Are you sure you want to clear progress for %1$s for this KPI for this user?";map_value_column_label,7,V400a12a37,4000a12a73,;"Value Column\:";overall_kpi_summary_title,7,V400761d32,4900761d77,;"Overall KPI Summary";dialog_kpi_action_delete,7,V401495755,3b0149578c,;"Delete";error_clearing_progress,7,V400832141,4c00832189,;"Error clearing progress.";ocr_value_label,7,V400892292,32008922c0,;"Value\:";no_date_placeholder,7,V4013451ab,37013451de,;"No date";owner_type_hint,7,V400340cf2,3600340d24,;"Owner Type";owner_type_company,7,V4003e0f5c,36003e0f8e,;"Company";kpi_monthly_target_label_formatted,7,V401746215,4301746254,;"TODO";kpi_detail_tab_monthly,7,V400c93572,3a00c935a8,;"Monthly";pref_notifications_title,7,V400fb42fb,4200fb4339,;"Notifications";edit_progress_title,7,V40017055c,3d00170595,;"Edit Progress";target_achieved_notification_title,7,V4005d1735,4f005d1780,;"Target Achieved!";clear_month_progress_confirmation_message,7,V401806514,4a0180655a,;"TODO";filter_by_owner_hint,7,V400320c80,4000320cbc,;"Filter by Owner";edit_kpi_menu_item,7,V4002c0b32,37002c0b65,;"Edit KPI";no_entries_yet,7,V4016c602d,2f016c6058,;"TODO";error_ocr_processing,7,V4008c2335,51008c2382,;"Error processing image with OCR.";summary_detail_monthly_format,7,V401645e2d,4701645e70,;"Monthly\: %d%%";error_excel_processing,7,V4009c2819,4f009c2864,;"Error processing Excel file.";kpi_annual_target_label_formatted,7,V4017361d1,420173620f,;"TODO";select_excel_file_button_text,7,V401726191,3e017261cb,;"TODO";kpi_card_item_target_label,7,V4014d586a,3e014d58a4,;"Target\:";save_user_button_text,7,V400e33b80,3b00e33bb7,;"Save User";kpi_description_hint,7,V4000500e7,470005012a,;"Description (Optional)";error_select_kpi_for_ocr,7,V400922503,5f0092255e,;"Please select a KPI to assign the data to.";filter_expiry,7,V4011e4c65,30011e4c91,;"Expiry";kpi_detail_tab_current,7,V400c83536,3a00c8356c,;"Current";confirm_delete_user_message,7,V400e73c97,a200e73d35,;"Are you sure you want to delete user '%1$s' and all their KPI assignments? This action cannot be undone.";near_expiry_notification_title,7,V4018f68a2,3f018f68dd,;"TODO";reminder_option_2_days,7,V401986acd,4001986b09,;"2 days before";save_kpi_button,7,V4011b4bb9,34011b4be9,;"Save KPI";kpi_card_item_remaining_to_target_label,7,V4015259d5,4e01525a1f,;"Remaining\:";month_year_format,7,V400701c25,3700701c58,;"MMMM yyyy";all_users_excel_filter,7,V400ab2d38,4800ab2d7c,;"All Users (from file)";confirm_delete_task_title,7,V4013c53e9,49013c542e,;"Confirm Delete Task";app_name,7,V400010015,3400010045,;"KPI Tracker Pro";clear_month_progress_select_month_message,7,V4017e647e,4a017e64c4,;"TODO";kpi_card_item_current_period_target_label,7,V40150591f,540150596f,;"Period Target\:";kpi_target_hint,7,V400060130,3f0006016b,;"Annual Target Value";current_progress_label,7,V400200794,44002007d4,;"Current Progress\:";ocr_review_title,7,V400872211,3f0087224c,;"Review OCR Results";user_name_hint_manage,7,V400e13af8,3b00e13b2f,;"User Name";delete_ocr_item_title,7,V40095265e,410095269b,;"Delete OCR Item";action_share_user_card_image,7,V401605d68,4e01605db2,;"Share User Card Image";no_kpis_assigned,7,V4012a4f25,3d012a4f5e,;"No KPIs Assigned";user_image_label_manage,7,V400e23b35,4900e23b7a,;"User Image (Optional)";gcm_defaultSenderId,236,V4000300c7,5100030114,;"123456789012";select_excel_file,7,V40099274c,470099278f,;"Select Excel File (.xlsx)";task_item_expires_prefix,7,V401335167,42013351a5,;"Expires\: %1$s";action_clear_progress,7,V4007d1efc,40007d1f38,;"Clear Progress";error_invalid_target,7,V40012041c,450012045d,;"Invalid target value";unit_percentage,7,V4000c02bf,36000c02f1,;"Percentage";owner_type_user,7,V4003c0eec,30003c0f18,;"User";action_delete,7,V4015a5bcd,30015a5bf9,;"Delete";add_kpi,7,V401244dd6,2b01244dfd,;"Add KPI";error_kpi_or_user_not_found,7,V40184661c,3c01846654,;"TODO";error_no_sheets_found,7,V400a82c4b,5400a82c9b,;"No sheets found in the Excel file.";header_row_label,7,V400a92ca1,4000a92cdd,;"Data starts at row\:";enter_new_user_name_hint,7,V400481203,4800481247,;"Enter New User Name";add_user_button_text,7,V400e03abd,3900e03af2,;"Add User";dialog_cancel,7,V4002f0bdb,30002f0c07,;"Cancel";unit_currency,7,V4000d02f7,32000d0325,;"Currency";no_progress_entries,7,V400240892,48002408d6,;"No progress entries yet.";action_add_progress,7,V400380e00,3c00380e38,;"Add Progress";status_overdue,7,V401365224,3a0136525a,;"Status\: Overdue";edit_user_title,7,V400ea3dd1,3500ea3e02,;"Edit User";task_notification_channel_description,7,V4013b537b,6c013b53e3,;"Notifications for upcoming task deadlines.";delete_entry_confirmation_title,7,V4016f60d2,40016f610e,;"TODO";select_owner_image_button,7,V4011a4b75,42011a4bb3,;"Select Image";project_id,236,V4000802c6,5000080312,;"kpi-tracker-app-demo";report_start_date_label,7,V400b83131,3f00b8316c,;"Start Date\:";select_users_dialog_title,7,V40043107d,42004310bb,;"Select Users";task_deleted_success,7,V4013e549c,47013e54df,;"Task '%1$s' deleted.";filter_by_user_excel_label,7,V400aa2ce3,5300aa2d32,;"Filter by User (from Excel)\:";report_period_monthly,7,V400bc322a,3900bc325f,;"Monthly";menu_import_ocr,7,V400da396f,4300da39ae,;"Import from Image (OCR)";menu_settings,7,V400dc39f6,3200dc3a24,;"Settings";users_not_loaded_yet,7,V40046115d,60004611b9,;"Users not loaded yet\, please try again shortly.";error_user_name_required,7,V400e43bbd,4b00e43c04,;"User name is required.";reset_to_default,7,V401585b60,3d01585b99,;"Reset to Default";report_period_daily,7,V400bb31f3,3500bb3224,;"Daily";menu_generate_report,7,V400db39b4,4000db39f0,;"Generate Report";edit_kpi_title,7,V40003007e,33000300ad,;"Edit KPI";no_overall_summary_to_display,7,V4007b1e9c,5c007b1ef4,;"No overall KPI summary to display.";search_edit_prompt,7,V401264e45,4401264e85,;"Search or Edit Prompt";reset_to_default_colors_button,7,V4014b57d4,4b014b581b,;"Reset to Default";action_edit_user,7,V400eb3e08,3600eb3e3a,;"Edit User";color_set_success,7,V401685f46,3201685f74,;"TODO";add_progress_button_text,7,V40022080c,4100220849,;"Add Progress";no_entries_found,7,V401916926,3101916953,;"TODO";dialog_kpi_action_change_color,7,V4014756c5,4701475708,;"Change Color";add_task_button_text,7,V4013150fc,340131512c,;"Add";notification_channel_description,7,V4005c16c1,72005c172f,;"Notifications for KPI events like target achievement.";dialog_select_card_colors_title,7,V401084707,4e01084751,;"Select Card Colors";excel_import_instructions,7,V4009f2918,d9009f29ed,;"Select an Excel file (.xlsx). Ensure it has columns for Value (numeric)\, Date (e.g.\, YYYY-MM-DD)\, and optionally User Identifier. Review and edit before importing.";reminder_option_no_reminder,7,V401956a03,4301956a42,;"No reminder";ocr_import_instructions,7,V400932564,b900932619,;"Select an image containing numerical data\, dates\, and optional user identifiers. Review and edit the extracted data before importing.";kpi_report_title,7,V400b53078,3700b530ab,;"KPI Report";confirm_delete_kpi_message,7,V4002809a3,7500280a14,;"Are you sure you want to delete this KPI and all its progress?";no_kpis_to_display,7,V400791e00,4200791e3e,;"No KPIs to display.";aggregation_average,7,V400ae2e10,5500ae2e61,;"Calculate Average (for selected user)";progress_value_hint,7,V40018059b,35001805cc,;"Value";no_data_for_selected_month,7,V4018365df,3b01836616,;"TODO";column_mapping_title,7,V400a029f3,4200a02a31,;"Map Excel Columns";colors_updated_successfully,7,V4010c4833,4c010c487b,;"Card colors updated.";action_excel_import,7,V4015c5c4d,41015c5c8a,;"Import from Excel";kpi_daily_target_label_formatted,7,V40175625a,4101756297,;"TODO";action_delete_user,7,V400ec3e40,3a00ec3e76,;"Delete User";error_value_required,7,V4001b064c,42001b068a,;"Value is required";task_expiration_date_hint,7,V4012e5017,45012e5058,;"Expiration Date";owner_type_department,7,V4003d0f1e,3c003d0f56,;"Department";report_end_date_label,7,V400b93172,3b00b931a9,;"End Date\:";settings_activity_title,7,V40102458b,3c010245c3,;"Settings";kpi_actions_dialog_title,7,V400370dbe,4000370dfa,;"KPI Actions";expiry_notification_channel_name,7,V400f5404b,5100f54098,;"KPI Expiry Reminders";user_saved_success,7,V400e93d88,4700e93dcb,;"User saved successfully.";task_name_hint,7,V4012d4fe1,34012d5011,;"Task Name";select_card_gradient_colors,7,V401695f7a,3c01695fb2,;"TODO";task_added_success,7,V4013952eb,430139532a,;"Task '%1$s' added.";task_notification_channel_name,7,V4013a5330,49013a5375,;"Task Reminders";no_users_selected_hint,7,V4004410c1,4400441101,;"No users selected";status_completed,7,V4013551e4,3e0135521e,;"Status\: Completed";label_bottom_color,7,V4010a478f,3c010a47c7,;"Bottom Color\:";label_top_color,7,V401094757,3601094789,;"Top Color\:";action_expiry_management,7,V401104916,4601104958,;"Expiry Management";overall_summary_context_menu_title,7,V4010445cb,5b01044622,;"Overall Summary Card Actions";progress_entry_deleted,7,V401716158,370171618b,;"TODO";select_user_hint,7,V400400fc8,3800400ffc,;"Select User";kpi_detail_tab_quarterly,7,V400ca35ae,3e00ca35e8,;"Quarterly";existing_user_added_to_selection_message,7,V4004b12ec,66004b134e,;"Existing user added to selection.";overall_summary_card_title,7,V4006017f5,4600601837,;"Overall Summary";review_ocr_results_title,7,V4011349e8,4701134a2b,;"Review OCR Results";edit_progress_entry_menu_item,7,V4002d0b6b,44002d0bab,;"Edit Entry";confirm_delete_progress_entry_title,7,V4002a0a64,54002a0ab4,;"Confirm Delete Entry";action_delete_kpi,7,V4003b0eb2,38003b0ee6,;"Delete KPI";kpi_card_item_current_label,7,V4014f58de,3f014f5919,;"Current";pref_key_target_achieved_notif,7,V400fc433f,5700fc4392,;"target_achieved_notification";pref_summary_target_achieved_notif,7,V400fe43f4,6f00fe445f,;"Receive a notification when a KPI target is met.";clear_progress_confirmation_message,7,V4017b63ce,44017b640e,;"TODO";select_user_for_report,7,V400b730f0,3f00b7312b,;"Select User\:";kpi_daily_target_hint,7,V400090213,4900090258,;"Daily Target (Optional)";action_customize_colors,7,V4010746c1,4401074701,;"Customize Colors";filter_by_month_button_text,7,V4006f1bdc,47006f1c1f,;"Filter by Month";dialog_color_picker_save_button,7,V4014c5821,47014c5864,;"Save Colors";no_kpis_assigned_to_user,7,V4006b1afd,53006b1b4c,;"No KPIs assigned to this user.";remaining_value_label,7,V400cf36e2,3c00cf371a,;"Remaining\:";delete_kpi_menu_item,7,V400260921,3b00260958,;"Delete KPI";confirm_delete_kpi_title,7,V40027095e,430027099d,;"Confirm Delete";map_user_column_label,7,V400a32ab9,5400a32b09,;"User Identifier Column (Optional)\:";ocr_data_imported_success,7,V4008e23dd,55008e242e,;"OCR data imported successfully.";user_name_cannot_be_empty_error,7,V4004c1354,56004c13a6,;"User name cannot be empty.";kpi_detail_tab_yearly,7,V400cb35ee,3800cb3622,;"Yearly";user_card_color_bottom_label,7,V400ee3ec3,4b00ee3f0a,;"Card Bottom Color\:";all_kpis_title,7,V400781dbe,4000781dfa,;"All KPIs (Aggregated)";report_period_annual,7,V400be32a4,3f00be32df,;"Annual (Range)";reminder_option_3_days,7,V401996b0f,4001996b4b,;"3 days before";add_progress_dialog_title,7,V4012b4f64,42012b4fa2,;"Add Progress";error_clearing_month_progress,7,V40182659f,3e018265d9,;"TODO";dialog_confirm,7,V4017c6414,2f017c643f,;"TODO";dialog_color_picker_pick_button,7,V4014a5792,40014a57ce,;"Pick";task_expiration_time_hint,7,V4013050aa,50013050f6,;"Expiration Time (Optional)";delete_entry_button,7,V401294ee7,3c01294f1f,;"Delete Entry";kpi_list_title,7,V4001504ee,2f00150519,;"KPIs";kpi_annual_target_hint,7,V401164a72,4001164aae,;"Annual Target";report_period_quarterly,7,V400bd3265,3d00bd329e,;"Quarterly";filter_report,7,V4011f4c97,3c011f4ccf,;"Interactive Report";dialog_color_picker_title,7,V4018c67de,3a018c6814,;"TODO";target_label,7,V4002107da,3000210806,;"Target\:";kpi_card_item_current_period_achievement_label,7,V401515975,5e015159cf,;"Period Achievement\:";save_kpi_button_text,7,V4000f0359,39000f038e,;"Save KPI";add_new_user_button_label,7,V4004711bf,42004711fd,;"Add New User";owner_name_hint,7,V4004d13ac,35004d13dd,;"User Name";achieved_value_label,7,V400cd3666,3a00cd369c,;"Achieved\:";hide_master_card,7,V401234d97,3d01234dd0,;"Hide master card";progress_history_title,7,V4002508dc,430025091b,;"Progress History";no_data_for_report,7,V400c333d7,5b00c3342e,;"No data available for the selected criteria.";report_for_kpi_user_format,7,V400c53482,4d00c534cb,;"Report for %1$s - %2$s";aggregation_individual,7,V400ad2dc8,4600ad2e0a,;"Import Individually";current_label,7,V4014e58aa,32014e58d8,;"Current\:";action_change_user_card_color,7,V4015f5d16,50015f5d62,;"Change User Card Color";no_data_to_import,7,V400b33027,4d00b33070,;"No data to import after review.";clear_month_progress_confirmation_title,7,V4017f64ca,48017f650e,;"TODO";user_kpi_list_title,7,V401144a31,3d01144a6a,;"User KPI List";ocr_data_import_failed,7,V4008f2434,4d008f247d,;"Failed to import OCR data.";excel_data_imported_success,7,V4009d286a,59009d28bf,;"Excel data imported successfully.";error_saving_progress,7,V4001e0711,47001e0754,;"Error saving progress";not_applicable_short,7,V400d43836,3400d43866,;"N/A";color_picker_title_summary_card_top,7,V400561526,5600561578,;"Summary Card Top Color";action_delete_user_kpi,7,V401615db8,4d01615e01,;"Delete User KPI Assignment";filter_all,7,V4011d4c39,2a011d4c5f,;"All";kpi_detail_title,7,V4001f075a,38001f078e,;"KPI Details";kpi_quarterly_target_hint,7,V400070171,51000701be,;"Quarterly Target (Optional)";no_kpis_for_this_user,7,V400731ca1,4e00731ceb,;"No KPIs found for this user.";confirm_delete_user_title,7,V400e63c4c,4900e63c91,;"Confirm Delete User";user_summary_card_title_prefix,7,V400691a72,4600691ab4,;"Summary for";task_updated_success,7,V40141556a,47014155ad,;"Task '%1$s' updated.";error_invalid_expiration_date,7,V401a56d1f,5201a56d6d,;"Invalid expiration date.";pref_title_target_achieved_notif,7,V400fd4398,5a00fd43ee,;"Target Achieved Notifications";clear_month_progress_success,7,V401816560,3d01816599,;"TODO";assign_to_kpi_label,7,V400902483,3e009024bd,;"Assign to KPI\:";edit_progress_dialog_title,7,V4016a5fb8,3b016a5fef,;"TODO";error_date_required,7,V4001d06cf,40001d070b,;"Date is required";reminder_option_1_week,7,V4019a6b51,40019a6b8d,;"1 week before";kpi_detail_tab_all_time,7,V400cc3628,3c00cc3660,;"All Time";confirm_delete_task_message,7,V4013d5434,66013d5496,;"Are you sure you want to delete task '%1$s'?";status_due_in_days,7,V4013852a0,49013852e5,;"Status\: Due in %1$d day(s)";report_percentage_label,7,V400c13359,3e00c13393,;"Percentage";google_api_key,236,V400040119,660004017b,;"AIzaSyDemoKeyForKPITrackerApp123456789";error_target_required,7,V4001103d6,4400110416,;"Target is required";required_daily_rate_label,7,V400d237a2,4500d237e3,;"Required Daily\:";kpi_deleted_success,7,V40178631f,340178634f,;"TODO";add_progress_title,7,V40016051f,3b00160556,;"Add Progress";owner_type_other,7,V4003f0f94,32003f0fc2,;"Other";color_reset_success,7,V401675f10,3401675f40,;"TODO";total_monthly_target_label,7,V4006318cb,4f00631916,;"Total Monthly Target\: %s";action_search_edit_progress,7,V4015b5bff,4c015b5c47,;"Search/Edit Progress";value_hint,7,V401274e8b,2c01274eb3,;"Value";label_individual_color,7,V4010b47cd,43010b480c,;"Card Background\:";kpi_expiry_reminder_text_annual,7,V400fa4282,7700fa42f5,;"Annual target for '%1$s' is approaching. Review progress.";ocr_import_failed,7,V40185665a,3201856688,;"TODO";no_target_set_short,7,V400d5386c,3900d538a1,;"No Target";kpi_unit_label,7,V4000a025e,2f000a0289,;"Unit";all_users_report_option,7,V400c634d1,4a00c63517,;"All Users (Aggregated)";pref_summary_expiry_reminder_notif,7,V40101451a,6f01014585,;"Receive reminders for KPIs nearing review dates.";last_entry_date_label,7,V4016b5ff5,36016b6027,;"TODO";notification_channel_name,7,V4005b1678,47005b16bb,;"KPI Notifications";kpi_summary_item_format,7,V4006c1b52,4c006c1b9a,;"%1$s\: M %2$d%%\, Y %3$d%%";pref_key_expiry_reminder_notif,7,V400ff4465,5700ff44b8,;"expiry_reminder_notification";import_failed_with_errors,7,V400b12f51,5a00b12fa7,;"Import failed. %1$d errors occurred.";main_activity_title,7,V400751cf3,3d00751d2c,;"KPI Dashboard";ocr_date_label,7,V4008a22c6,30008a22f2,;"Date\:";annual_progress_label_short,7,V400621885,44006218c5,;"Year\: %1$d%%";performance_report_title,7,V4010f48cd,47010f4910,;"Performance Report";kpi_card_item_last_update_label,7,V401545a71,4801545ab5,;"Last Update\:";search_hint,7,V400300c0d,3600300c3f,;"Search KPIs...";google_crash_reporting_api_key,236,V4000601e7,7600060259,;"AIzaSyDemoKeyForKPITrackerApp123456789";excel_import_title,7,V40098270f,3b00982746,;"Excel Import";report_target_label,7,V400bf32e5,3600bf3317,;"Target";user_deleted_success,7,V400e83d3b,4b00e83d82,;"User deleted successfully.";error_invalid_value,7,V4001c0690,3d001c06c9,;"Invalid value";total_annual_achieved_label,7,V4006619c0,5100661a0d,;"Total Annual Achieved\: %s";ocr_import_title,7,V400852191,37008521c4,;"OCR Import";select_kpi_for_report,7,V400b630b1,3d00b630ea,;"Select KPI\:";save_button,7,V401284eb9,2c01284ee1,;"Save";trend_chart_title,7,V400c23399,3c00c233d1,;"Progress Trend";filter_task_follow_up,7,V401204cd5,4001204d11,;"Task follow-up";create_new_user_dialog_title,7,V40049124d,450049128e,;"Add New User";report_achieved_label,7,V400c0331d,3a00c03353,;"Achieved";kpi_duplication_failed,7,V400f33ffc,4b00f34043,;"Failed to duplicate KPI.";select_month_year_title,7,V400d638a7,4900d638ec,;"Select Month and Year";confirm_delete_ocr_item_message,7,V4009626a1,6a00962707,;"Are you sure you want to delete this OCR item?";total_monthly_achieved_label,7,V40064191c,530064196b,;"Total Monthly Achieved\: %s";pref_title_expiry_reminder_notif,7,V4010044be,5a01004514,;"Expiry Reminder Notifications";progress_date_hint,7,V4001905d2,3300190601,;"Date";error_column_selection,7,V400a52b4d,5c00a52ba5,;"Please select columns for Value and Date.";save_progress_button_text,7,V4001a0607,43001a0646,;"Save Progress";search_edit_progress_title,7,V40111495e,4b011149a5,;"Search/Edit Progress";select_users_button,7,V401184af6,3c01184b2e,;"Select Users";select_owner_image_button_text,7,V400350d2a,4d00350d73,;"Select Owner Image";error_at_least_one_user,7,V400451107,5400451157,;"Please select at least one user.";error_saving_user,7,V400e53c0a,4000e53c46,;"Error saving user.";default_web_client_id,236,V400020037,8f000200c2,;"123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com";user_management_title,7,V400df3a7d,3e00df3ab7,;"Manage Users";unit_number,7,V4000b028f,2e000b02b9,;"Number";menu_manage_users,7,V400dd3a3f,3a00dd3a75,;"Manage Users";preview_data_button,7,V400a42b0f,3c00a42b47,;"Preview Data";select_date_prompt,7,V401896735,3301896764,;"TODO";error_generating_report,7,V400c43434,4c00c4347c,;"Error generating report.";dialog_kpi_action_reset_color,7,V40148570e,450148574f,;"Reset Color";import_ocr_data_button,7,V400882252,3e0088228c,;"Import Data";progress_updated_success,7,V4016d605e,39016d6093,;"TODO";select_image_for_ocr,7,V4008621ca,450086220b,;"Select Image for OCR";user_kpi_list_title_prefix,7,V400721c60,3f00721c9b,;"KPIs for";status_due_today,7,V401375260,3e0137529a,;"Status\: Due Today";no_users_exist,7,V400ef3f10,5400ef3f60,;"No users exist. Add users to assign KPIs.";expiry_return_notification_message,7,V4018e685d,43018e689c,;"TODO";ocr_activity_title,7,V4011249ab,3b011249e2,;"OCR Activity";delete_entry_confirmation_message,7,V401706114,4201706152,;"TODO";action_view_details,7,V400390e3e,3c00390e76,;"View Details";error_kpi_not_found,7,V4001404a2,4a001404e8,;"KPI not found for editing.";delete_progress_entry_menu_item,7,V400290a1a,4800290a5e,;"Delete Entry";dialog_kpi_action_share_card,7,V401465680,43014656bf,;"Share Card";ocr_import_cancelled,7,V40186668e,35018666bf,;"TODO";owner_image_description,7,V401194b34,3f01194b6f,;"Owner Image";error_reading_sheet_names,7,V400a62bab,6000a62c07,;"Error reading sheet names from Excel file.";title_activity_kpi_list,7,V4018b67a4,38018b67d8,;"TODO";google_storage_bucket,236,V40007025e,67000702c1,;"kpi-tracker-app-demo.appspot.com";kpi_name_not_editable_editing,7,V40192697b,60019269d7,;"KPI name cannot be changed during edit";add_task_title,7,V4012c4fa8,37012c4fdb,;"Add New Task";master_card_no_data,7,V401665ebd,5101665f0a,;"No data available for master card";manual_user_input_hint,7,V400411002,4200411040,;"Enter User Name";google_app_id,236,V400050180,66000501e2,;"1\:123456789012\:android\:abcdef1234567890";delete_kpi_confirmation_message,7,V4017762dd,4001776319,;"TODO";remaining_days_label,7,V400d13765,3b00d1379c,;"Days Left\:";reminder_option_on_due_date,7,V401966a48,4301966a87,;"On due date";clear_progress_success,7,V4017d6445,37017d6478,;"TODO";toggle_master_card,7,V401214d17,3f01214d52,;"View master card";reminder_option_1_day,7,V401976a8d,3e01976ac7,;"1 day before";search_kpis_title,7,V400310c45,3900310c7a,;"Search KPIs";confirm_delete_progress_entry_message,7,V4002b0aba,76002b0b2c,;"Are you sure you want to delete this progress entry?";dialog_kpi_action_edit,7,V4014455f6,3b0144562d,;"Edit KPI";color_picker_title_summary_card_bottom,7,V40057157e,5c005715d6,;"Summary Card Bottom Color";kpi_expiry_reminder_title,7,V400f7413e,4800f74182,;"KPI Reminder\: %1$s";confirm_import_button,7,V401254e03,4001254e3f,;"Confirm Import";no_kpis_assigned_for_master_card,7,V400671a13,5b00671a6a,;"No KPIs assigned to users yet.";aggregation_type_label,7,V400ac2d82,4400ac2dc2,;"Aggregation Type\:";menu_import_excel,7,V400d9392e,3f00d93969,;"Import from Excel";filter_all_months,7,V4006e1ba2,38006e1bd6,;"All Months";dialog_ok,7,V4002e0bb1,28002e0bd5,;"OK";ocr_no_entries_found,7,V4018866fe,350188672f,;"TODO";kpi_card_context_menu_title,7,V401064677,48010646bb,;"KPI Card Actions";error_updating_colors,7,V4010d4881,48010d48c5,;"Error updating colors.";confirm_clear_progress_message,7,V4007f1f8f,8a007f2015,;"Are you sure you want to clear all progress entries for this KPI for this user?";task_reminder_days_hint,7,V4012f505e,4a012f50a4,;"Reminder (days before)";error_name_required,7,V400100394,40001003d0,;"Name is required";show_master_card,7,V401224d58,3d01224d91,;"Show master card";my_tasks_title,7,V401325132,3301325161,;"My Tasks";days_since_last_update_label,7,V400d337e9,4b00d33830,;"Days Since Update\:";total_annual_target_label,7,V400651971,4d006519ba,;"Total Annual Target\: %s";action_view_report,7,V4015d5c90,4d015d5cd9,;"Interactive Performance Report";excel_data_import_failed,7,V4009e28c5,51009e2912,;"Failed to import Excel data.";error_copying_image,7,V400360d79,4300360db8,;"Error copying image";error_updating_progress,7,V4018a676a,38018a679e,;"TODO";map_date_column_label,7,V400a22a79,3e00a22ab3,;"Date Column\:";ocr_waiting_for_review,7,V4018766c5,37018766f8,;"TODO";kpi_name_hint,7,V4000400b3,32000400e1,;"KPI Name";no_user_summaries_to_display,7,V4007a1e44,56007a1e96,;"No user summaries to display.";progress_saved_success,7,V4016e6099,37016e60cc,;"TODO";clear_progress_confirmation_title,7,V4017a638a,42017a63c8,;"TODO";ocr_user_label,7,V4008b22f8,3b008b232f,;"User (Optional)\:";select_kpi_for_ocr_hint,7,V4009124c3,3e009124fd,;"Select KPI";error_saving_kpi,7,V400130463,3d0013049c,;"Error saving KPI";target_achieved_notification_text,7,V4005e1786,6b005e17ed,;"You achieved %1$.1f for %2$s (Target\: %3$.1f)";dialog_kpi_action_duplicate_copy,7,V401455633,4b0145567a,;"Duplicate/Copy";kpi_card_item_average_label,7,V401555abb,3f01555af6,;"Average";user_already_selected_error,7,V4004a1294,56004a12e6,;"This user is already selected.";select_sheet_label,7,V400a72c0d,3c00a72c45,;"Select Sheet\:";expiry_return_notification_title,7,V4018d681a,41018d6857,;"TODO";+style:MasterCardButton,237,V4001002ed,c001c056e,;DWidget.MaterialComponents.Button,android\:textSize:12sp,android\:paddingStart:12dp,android\:paddingEnd:12dp,android\:paddingTop:8dp,android\:paddingBottom:8dp,android\:minHeight:40dp,cornerRadius:20dp,icon:@drawable/ic_master_card_24,iconGravity:textStart,iconPadding:8dp,iconSize:18dp,;CustomFilterChip,237,V40003006d,c000d02b2,;DWidget.MaterialComponents.Chip.Filter,android\:textSize:12sp,android\:textColor:@color/chip_text_color,chipIconSize:18dp,chipIconVisible:true,chipMinHeight:40dp,chipMinTouchTargetSize:40dp,chipStartPadding:8dp,chipEndPadding:8dp,chipBackgroundColor:@color/chip_background_color,;Widget.App.Chip.Advanced,237,V4001f05a6,c00290807,;DWidget.MaterialComponents.Chip.Choice,android\:textSize:14sp,android\:textColor:@color/chip_text_selector,chipMinHeight:40dp,chipStartPadding:12dp,chipEndPadding:12dp,chipCornerRadius:20dp,chipBackgroundColor:@color/chip_background_selector,chipStrokeWidth:1dp,chipStrokeColor:@color/purple_accent,;Theme.KPITrackerApp,238,V400020066,c0011038e,;DTheme.MaterialComponents.DayNight.DarkActionBar,colorPrimary:@color/purple_500,colorPrimaryVariant:@color/purple_700,colorOnPrimary:@color/white,colorSecondary:@color/teal_200,colorSecondaryVariant:@color/teal_700,colorOnSecondary:@color/black,chipStyle:@style/Widget.App.Chip,android\:statusBarColor:?attr/colorPrimaryVariant,;Widget.App.Chip,238,V40026073c,c002f0990,;DWidget.MaterialComponents.Chip.Filter,android\:textSize:14sp,android\:textColor:@color/chip_text_selector,chipBackgroundColor:@color/chip_background_selector,chipStrokeColor:@color/purple_500,chipStrokeWidth:1dp,chipCornerRadius:16dp,chipIconTint:@color/chip_text_selector,closeIconTint:@color/chip_text_selector,;Theme.KPITrackerApp.NoActionBar,238,V4001403e1,c00230713,;DTheme.MaterialComponents.DayNight.NoActionBar,colorPrimary:@color/purple_500,colorPrimaryVariant:@color/purple_700,colorOnPrimary:@color/white,colorSecondary:@color/teal_200,colorSecondaryVariant:@color/teal_700,colorOnSecondary:@color/black,chipStyle:@style/Widget.App.Chip,android\:statusBarColor:?attr/colorPrimaryVariant,;+xml:file_paths,239,F;network_security_config,240,F;data_extraction_rules,241,F;backup_rules,242,F;