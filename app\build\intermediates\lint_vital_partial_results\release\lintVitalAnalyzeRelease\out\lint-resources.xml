http://schemas.android.com/apk/res-auto;;${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/bounce_animation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/slide_in_bottom.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/star_pulse.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/fade_scale_in.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/shake_animation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/card_release_scale.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/card_press_scale.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/chip_text_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/bottom_nav_color_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/chip_background_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/chip_text_color.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/chip_background_color.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_event_busy_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_arrow_upward_16.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_filter_list_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_view_list_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/color_selector_orange.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_admin_panel_settings_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_content_copy_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_security_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/online_indicator.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_task_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/status_dot_orange.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_time.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/system_message_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sort_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_format_color_reset_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_filter_report_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/blue_gradient_button_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/search_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notification.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_more_vert_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ripple_effect.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_calendar.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_arrow_downward_16.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_videocam_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_skip.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/card_drag_shadow.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_performance_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notifications.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/unread_badge_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_keyboard_arrow_down_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/chart_fill_gradient_purple.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/color_selector_blue.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_notifications_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_share_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_send_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_blue.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_arrow_back_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_red.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_assessment_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_business_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_email_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_my_location_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_email.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_background_light.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_call_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_drag_handle.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/status_dot_grey.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_check.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/overall_summary_gradient.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_description_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_delete.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_back.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/spinner_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_add_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_timer_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sort_ascending.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_alternate_email_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_chat_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sort_descending.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_switch_account_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_attach_file_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_calendar_today_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_filter_task_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_green.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/card_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_keyboard_arrow_up_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/gradient_admin_welcome.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_mic_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/chart_fill_gradient.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_indicator.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_master_card_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/date_header_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_account_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_delete_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/status_dot_green.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_horizontal_rule_16.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/header_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_priority_high_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_refresh_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add_time.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_filter_expiry_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/color_selector_purple.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_access_time_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_document_scanner_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_logout_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_filter_all_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_phone.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/card_top_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_category_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notification_icon.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/priority_indicator_gradient.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/card_bottom_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/color_swatch_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_edit_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_camera_alt_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_sort_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/reply_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_volume_off_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_messages_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_person_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/color_selector_red.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/color_selector_green.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/text_view_border.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_dashboard_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circular_progress_bar.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_search_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_settings_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sort_alpha.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/status_dot_red.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_location_on_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/header_gradient.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/custom_progress_bar.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_whatsapp.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_search.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_attachment_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_admin_dashboard_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/kpi_card_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/unified_report_table_row.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/unified_report_table_row_binding.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_edit_kpi.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_edit_task_enhanced.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_task_modern.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_admin_dashboard_action.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_kpi_detail.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/chart_share_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_pomodoro_timer.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/excel_review_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/kpi_detail_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_main_dashboard.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_recent_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_edit_kpi_original.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_task_management.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/task_management_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_kpi_actions.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/kpi_detail_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_ocr_review.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/overall_summary_card_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_dashboard.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_performance.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/overall_summary_context_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_custom_reminder.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_expire_management.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_excel_import.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_task_enhanced.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_modern_report.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/compact_report_table_row.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/unified_report_table.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/main.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/main_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_edit_task.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/user_filter_dialog.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/ocr_review_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_report.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/sort_options_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_select_card_colors.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_edit_progress.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/admin_bottom_navigation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/main_bottom_navigation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_chat_message_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_chat_message_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_user_list.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_chat.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_excel_review.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_task_reminder_settings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_task.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_admin_dashboard.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_auto_send_settings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_chat_list.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_search_result.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/report_table_row.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/report_table_row_colored.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/report_table_row_tabular.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_create_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/kpi_summary_detail_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/chat_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_ocr.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_user_kpi_list.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_date_converter.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_subtask_mini.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_account.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_conversation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_search_edit_progress.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_admin_dashboard_stat.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_task_report.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/user_summary_context_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/chat_list_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/ids.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/progress_entry_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/user_summary_card_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_notification.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/kpi_list_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/task_item_actions_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/chart_marker_view.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/user_checkbox_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_search_messages.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_chat_system_message.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_progress_update.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/admin_dashboard_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_task_report.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_messages.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/compact_report_table.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_admin_dashboard_header.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_admin_dashboard_activity.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_custom_pomodoro.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/chart_options_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_notifications.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_chat_date_header.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/divider_view.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/raw/notification_sound.mp3,${\:app*buildDir}/generated/res/processReleaseGoogleServices/values/values.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/styles.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/file_paths.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+anim:bounce_animation,0,F;slide_in_bottom,1,F;star_pulse,2,F;fade_scale_in,3,F;shake_animation,4,F;card_release_scale,5,F;card_press_scale,6,F;+array:reminder_options,7,V4019c6b95,1301a36d17,;@string/reminder_option_no_reminder,@string/reminder_option_on_due_date,@string/reminder_option_1_day,@string/reminder_option_2_days,@string/reminder_option_3_days,@string/reminder_option_1_week,;owner_type_options_add_edit,7,V4004f1416,130054151e,;@string/owner_type_user,@string/owner_type_department,@string/owner_type_company,@string/owner_type_other,;+color:progress_indicator_track,8,V400290a33,3c00290a6b,;"#FFBDBDBD";text_primary,8,V4006c1b1e,30006c1b4a,;"#FF212121";progress_color_default,8,V400300c52,3a00300c88,;"#FF6200EE";status_red,8,V400240892,2e002408bc,;"#FFF44336";light_red,8,V400360e8d,2d00360eb6,;"#FFEF9A9A";default_kpi_card_top_gradient,8,V400190557,**********,;"#FF6200EE";kpi_concern,8,V400200766,2f00200791,;"#FFFF6F00";message_read_color,8,V4007a1dde,36007a1e10,;"#FF4CAF50";chip_text_selector,9,F;notification_reminder_bg,8,V400861fe0,3c00862018,;"#FFFEF7E0";energy_medium,8,V400c42a11,3100c42a3e,;"#FF2196F3";kpi_good,8,V4002107b6,2c002107de,;"#FF4CAF50";soft_lavender_background,8,V4002508d8,3c00250910,;"#FFE6E6FA";dark_gray,8,V4000a01a9,2b000a01d0,;"#A9A9A9";purple_700,8,V400040099,2e000400c3,;"#FF3700B3";doctor_color_3_lighter,8,V400511567,380051159b,;"#F1FAF1";primary_dark,8,V4006e1b84,30006e1bb0,;"#FF3700B3";success_color,8,V400a1244e,3100a1247b,;"#FF4CAF50";status_orange,8,V400230846,3100230873,;"#FFFF9800";icon_tint,8,V400bf2957,2d00bf2980,;"#FF757575";chart_target_color,8,V400160461,3600160493,;"#FFEF5350";chart_achieved_color,8,V4001704b1,38001704e5,;"#FF42A5F5";default_kpi_card_color,8,V400180504,3a0018053a,;"#FFFFFFFF";progress_indicator_blue,8,V4002a0a92,3b002a0ac9,;"#FF2196F3";system_message_background,8,V400781d69,3d00781da2,;"#40757575";surface,8,V40089208a,2b008920b1,;"#FFFFFFFF";doctor_color_5_light,8,V4004c143e,36004c1470,;"#FCE4EC";gray_800,8,V40062198c,2c006219b4,;"#FF424242";blue_500,8,V400d42ca9,2c00d42cd1,;"#FF2196F3";background_light,8,V4006b1ae8,34006b1b18,;"#FFF5F5F5";default_kpi_card_bottom_gradient,8,V4001a05b6,44001a05f6,;"#FF3700B3";search_background,8,V400c02986,3500c029b7,;"#FFFFFFFF";performance_poor,8,V4005a1824,32005a1852,;"#F44336";dark_yellow,8,V4003b100e,2f003b1039,;"#FFFBC02D";urgent_chip_background,8,V400b026ac,3a00b026e2,;"#FFFFE0E0";category_education,8,V400b727e4,3600b72816,;"#FF9C27B0";task_color_orange,8,V400cc2b77,3500cc2ba8,;"#FFFF9800";medium_priority_color,8,V400d02c0b,3900d02c40,;"#FFFF9800";progress_tint,8,V400ab25f1,3100ab261e,;"#FF4CAF50";light_blue_200,8,V400110310,320011033e,;"#FF81D4FA";screen_background_light_blue,8,V4002809d4,4000280a10,;"#FFE3F2FD";priority_urgent,8,V400962298,33009622c7,;"#FFF44336";yellow,8,V4000e026c,2a000e0292,;"#FFFFFF00";light_blue_600,8,V400120344,3200120372,;"#FF039BE5";energy_low,8,V400c329e1,2e00c32a0b,;"#FFFF9800";importance_low,8,V4009922ef,320099231d,;"#FF9E9E9E";urgent_important_bg,8,V400a524dd,3700a52510,;"#FFD32F2F";fab_color,8,V4002d0b31,2d002d0b5a,;"#FF6200EE";doctor_color_4_lighter,8,V4005215b8,38005215ec,;"#FFF8ED";bottom_nav_color_selector,10,F;kpi_warning,8,V4003e1109,2f003e1134,;"#FFFFC107";summary_card_bottom_yellow,8,V400150402,3e0015043c,;"#FFFFF59D";gray_500,8,V4005f1902,2c005f192a,;"#FF9E9E9E";not_urgent_important_bg,8,V400a62516,3b00a6254d,;"#FF1976D2";purple_accent,8,V400260931,310026095e,;"#FF9C27B0";light_gray,8,V40009017b,2c000901a3,;"#D3D3D3";black,8,V400070125,290007014a,;"#FF000000";low_priority_color,8,V400d12c46,3600d12c78,;"#FF4CAF50";orange_500,8,V400d72d33,2e00d72d5d,;"#FFFF9800";priority_medium,8,V400942230,330094225f,;"#FFFF9800";kpi_share_background,8,V4007b1e16,38007b1e4a,;"#FF2196F3";notification_system_bg,8,V40087201e,3a00872054,;"#FFE3F2FD";energy_high,8,V400c52a44,2f00c52a6f,;"#FF4CAF50";light_yellow,8,V4003a0fbd,30003a0fe9,;"#FFFFF59D";progress_text,8,V400ad265d,3100ad268a,;"#FF4CAF50";card_background_default,8,V4003d10b4,3b003d10eb,;"#FFFFFFFF";blue,8,V4000d0242,28000d0266,;"#FF0000FF";background_color,8,V4008e214b,34008e217b,;"#FFF5F5F5";ai_suggestion_background,8,V400c82a9a,3c00c82ad2,;"#FFF3E5F5";chip_background_selector,11,F;doctor_color_1,8,V40041118b,30004111b7,;"#673AB7";doctor_color_2,8,V4004211cd,30004211f9,;"#2196F3";light_green,8,V400380f21,2f00380f4c,;"#FFA5D6A7";doctor_color_3,8,V40043120d,**********,;"#4CAF50";doctor_color_4,8,V40044124e,300044127a,;"#FF9800";doctor_color_5,8,V400451290,30004512bc,;"#E91E63";performance_average,8,V400581754,**********,;"#FFC107";text_hint,8,V4008a20b7,2d008a20e0,;"#FF9E9E9E";read_only_overlay,8,V400661a1d,3500661a4e,;"#40000000";whatsapp_green,8,V4008d2117,32008d2145,;"#FF25D366";priority_high,8,V400952265,**********,;"#FFFF5722";category_health,8,V400b627af,3300b627de,;"#FFFF9800";summary_card_image_background,8,V400320d46,4100320d83,;"#00FFFFFF";text_secondary,8,V4006d1b50,32006d1b7e,;"#FF757575";doctor_color_4_light,8,V4004b13f0,36004b1422,;"#FFF3E0";user_color,8,V400811f18,2e00811f42,;"#FF607D8B";performance_excellent,8,V400561687,37005616ba,;"#4CAF50";dark_red,8,V400370ed8,2c00370f00,;"#FFD32F2F";category_shopping,8,V400ba2887,3500ba28b8,;"#FFFF5722";header_gradient_start,8,V4001e06b8,39001e06ed,;"#FF6200EE";category_work,8,V400b42745,3100b42772,;"#FF2196F3";urgent_color,8,V400a0241c,3000a02448,;"#FFF44336";category_travel,8,V400bb28be,3300bb28ed,;"#FF00BCD4";message_input_background,8,V400741c85,3c00741cbd,;"#FFFFFFFF";doctor_color_5_lighter,8,V40053160a,380053163e,;"#FDF2F6";light_blue_900,8,V400130378,32001303a6,;"#FF01579B";green_500,8,V400d52cd7,2d00d52d00,;"#FF4CAF50";red,8,V4000b01d6,27000b01f9,;"#FFFF0000";doctor_color_1_light,8,V400481309,360048133b,;"#EDE7F6";white,8,V400080150,**********,;"#FFFFFFFF";teal_700,8,V4000600f7,2c0006011f,;"#FF018786";chip_text_color,12,F;performance_good,8,V4005716ed,320057171b,;"#8BC34A";unread_badge_color,8,V400761cf5,3600761d27,;"#FFF44336";not_urgent_not_important_bg,8,V400a82590,3f00a825cb,;"#FF757575";task_color_red,8,V400c92ad8,3200c92b06,;"#FFF44336";purple_500,8,V400030069,2e00030093,;"#FF6200EE";owner_card_border,8,V4006519e6,3500651a17,;"#FF6200EE";category_personal,8,V400b52778,3500b527a9,;"#FF4CAF50";gray_600,8,V400601930,2c00601958,;"#FF757575";summary_card_username_text,8,V400330ddb,3e00330e15,;"#FFFFFFFF";notification_kpi_bg,8,V400851fa7,3700851fda,;"#FFE8F5E8";doctor_color_1_lighter,8,V4004f14c5,38004f14f9,;"#F3F0F9";performance_below_average,8,V4005917bc,3b005917f3,;"#FF9800";chip_background_default,8,V4002e0b89,3b002e0bc0,;"#FFE0E0E0";gray_200,8,V4005d18a6,2c005d18ce,;"#FFEEEEEE";primary_color,8,V4008f2181,31008f21ae,;"#FF6200EE";chip_background_color,13,F;purple_button_text,8,V400270984,36002709b6,;"#FFFFFFFF";progress_share_background,8,V4007c1e50,3d007c1e89,;"#FF4CAF50";progress_background,8,V400ac2624,3700ac2657,;"#FFE0E0E0";summary_card_top_red,8,V4001403ac,38001403e0,;"#FFEF9A9A";overdue_chip_background,8,V400b126e8,3b00b1271f,;"#FFFFE0E0";task_color_purple,8,V400cd2bae,3500cd2bdf,;"#FF9C27B0";date_header_background,8,V400771d2d,3a00771d63,;"#40000000";summary_card_image_tint,8,V400310cb7,3b00310cee,;"#FFFFFFFF";chart_line_purple,8,V4003c105d,35003c108e,;"#FFAB47BC";background,8,V40088205a,2e00882084,;"#FFF5F5F5";task_color_green,8,V400cb2b41,3400cb2b71,;"#FF4CAF50";sent_message_background,8,V400721c07,3b00721c3e,;"#FF6200EE";category_finance,8,V400b8281c,3400b8284c,;"#FFF44336";purple_200,8,V400020039,2e00020063,;"#FFBB86FC";online_color,8,V400751cc3,3000751cef,;"#FF4CAF50";red_500,8,V400d62d06,2b00d62d2d,;"#FFF44336";teal_200,8,V4000500c9,2c000500f1,;"#FF03DAC5";doctor_color_2_light,8,V400491357,3600491389,;"#E3F2FD";progress_track_color,8,V4002f0bef,38002f0c23,;"#FFBDBDBD";doctor_color_2_lighter,8,V400501517,380050154b,;"#EFF8FE";header_gradient_end,8,V4001f0710,37001f0743,;"#FF3700B3";importance_medium,8,V4009a2323,35009a2354,;"#FF2196F3";overdue_color,8,V400a22481,3100a224ae,;"#FFF44336";highlight_yellow,8,V400da2d8f,3400da2dbf,;"#FFFFFF00";importance_critical,8,V4009c238f,37009c23c2,;"#FFE91E63";matrix_toggle_background,8,V400be2919,3c00be2951,;"#FFF0F0F0";status_green,8,V4002207fc,3000220828,;"#FF4CAF50";gray_700,8,V40061195e,2c00611986,;"#FF616161";reply_background,8,V400791da8,3400791dd8,;"#20000000";category_family,8,V400b92852,3300b92881,;"#FFE91E63";green,8,V4000c01ff,29000c0224,;"#FF00FF00";notification_admin_bg,8,V400841f6c,3900841fa1,;"#FFF3E5F5";dark_green,8,V400390f70,2e00390f9a,;"#FF388E3C";view_only_indicator,8,V400681a88,3700681abb,;"#FFFF9800";doctor_color_3_light,8,V4004a13a3,36004a13d5,;"#E8F5E9";edit_indicator,8,V400671a54,3200671a82,;"#FF4CAF50";light_blue_50,8,V4001002dd,310010030a,;"#FFE1F5FE";super_admin_color,8,V4007f1eb0,35007f1ee1,;"#FFE91E63";gray_300,8,V4005e18d4,2c005e18fc,;"#FFE0E0E0";priority_low,8,V4009321fe,300093222a,;"#FF4CAF50";importance_high,8,V4009b235a,33009b2389,;"#FF9C27B0";orange,8,V4000f0298,2a000f02be,;"#FFFFA500";chart_fill_purple_light,8,V4001d0651,3b001d0688,;"#406200EE";received_message_background,8,V400731c44,3f00731c7f,;"#FFFFFFFF";admin_color,8,V400801ee7,2f00801f12,;"#FF9C27B0";task_color_blue,8,V400ca2b0c,3300ca2b3b,;"#FF2196F3";chat_background,8,V400711bd2,3300711c01,;"#FFF5F5F5";today_color,8,V4009f23eb,2f009f2416,;"#FF2196F3";urgent_not_important_bg,8,V400a72553,3b00a7258a,;"#FFFF9800";+dimen:color_swatch_size,14,V40005008c,30000500b8,;"36dp";card_elevation_pressed,14,V4000b01a2,35000b01d3,;"12dp";card_corner_radius,14,V4000c01d9,31000c0206,;"16dp";card_elevation,14,V4000a0174,2c000a019c,;"4dp";fab_margin,14,V400020039,290002005e,;"16dp";color_swatch_stroke_width,14,V4000600be,37000600f1,;"2dp";card_border_width,14,V400090117,2f00090142,;"1dp";+drawable:ic_baseline_event_busy_24,15,F;ic_baseline_arrow_upward_16,16,F;ic_baseline_filter_list_24,17,F;ic_baseline_view_list_24,18,F;color_selector_orange,19,F;ic_baseline_admin_panel_settings_24,20,F;ic_baseline_content_copy_24,21,F;ic_baseline_security_24,22,F;online_indicator,23,F;ic_baseline_task_24,24,F;status_dot_orange,25,F;ic_time,26,F;system_message_background,27,F;ic_sort_24,28,F;ic_baseline_format_color_reset_24,29,F;ic_filter_report_24,30,F;blue_gradient_button_background,31,F;search_background,32,F;ic_notification,33,F;ic_baseline_more_vert_24,34,F;circle_background,35,F;ripple_effect,36,F;ic_calendar,37,F;ic_baseline_arrow_downward_16,38,F;ic_baseline_videocam_24,39,F;ic_skip,40,F;card_drag_shadow,41,F;ic_performance_24,42,F;ic_notifications,43,F;unread_badge_background,44,F;ic_baseline_keyboard_arrow_down_24,45,F;chart_fill_gradient_purple,46,F;color_selector_blue,47,F;ic_baseline_notifications_24,48,F;ic_share_24,49,F;ic_baseline_send_24,50,F;circle_blue,51,F;ic_baseline_arrow_back_24,52,F;circle_red,53,F;ic_baseline_assessment_24,54,F;ic_baseline_business_24,55,F;ic_baseline_email_24,56,F;ic_baseline_my_location_24,57,F;ic_email,58,F;rounded_background_light,59,F;ic_baseline_call_24,60,F;ic_drag_handle,61,F;status_dot_grey,62,F;ic_check,63,F;overall_summary_gradient,64,F;ic_baseline_description_24,65,F;ic_delete,66,F;ic_arrow_back,67,F;spinner_background,68,F;ic_baseline_add_24,69,F;ic_baseline_timer_24,70,F;ic_sort_ascending,71,F;ic_baseline_alternate_email_24,72,F;ic_baseline_chat_24,73,F;ic_sort_descending,74,F;ic_baseline_switch_account_24,75,F;ic_baseline_attach_file_24,76,F;ic_baseline_calendar_today_24,77,F;ic_filter_task_24,78,F;circle_green,79,F;card_background,80,F;ic_baseline_keyboard_arrow_up_24,81,F;gradient_admin_welcome,82,F;ic_baseline_mic_24,83,F;chart_fill_gradient,84,F;circle_indicator,85,F;ic_master_card_24,86,F;date_header_background,87,F;ic_account_24,88,F;ic_baseline_delete_24,89,F;status_dot_green,90,F;ic_baseline_horizontal_rule_16,91,F;header_background,92,F;ic_baseline_priority_high_24,93,F;ic_baseline_refresh_24,94,F;rounded_background,95,F;ic_add_time,96,F;ic_filter_expiry_24,97,F;color_selector_purple,98,F;ic_baseline_access_time_24,99,F;ic_baseline_document_scanner_24,100,F;ic_baseline_logout_24,101,F;ic_filter_all_24,102,F;ic_phone,103,F;card_top_background,104,F;ic_baseline_category_24,105,F;ic_notification_icon,106,F;priority_indicator_gradient,107,F;card_bottom_background,108,F;color_swatch_background,109,F;ic_baseline_edit_24,110,F;ic_baseline_camera_alt_24,111,F;ic_baseline_sort_24,112,F;reply_background,113,F;ic_baseline_volume_off_24,114,F;ic_messages_24,115,F;ic_baseline_person_24,116,F;color_selector_red,117,F;color_selector_green,118,F;text_view_border,119,F;ic_dashboard_24,120,F;circular_progress_bar,121,F;ic_baseline_search_24,122,F;ic_baseline_settings_24,123,F;ic_sort_alpha,124,F;status_dot_red,125,F;ic_baseline_location_on_24,126,F;header_gradient,127,F;custom_progress_bar,128,F;ic_whatsapp,129,F;ic_search,130,F;ic_baseline_attachment_24,131,F;+id:userBadge,132,F;percentageTextView,133,F;percentageTextView,134,F;percentageTextView,135,F;taskDescriptionInputLayout,136,F;taskDescriptionInputLayout,137,F;taskDescriptionInputLayout,138,F;actionTitle,139,F;monthlyProgressPercentageTextView,140,F;action_share_as_pdf,141,F;timerDisplay,142,F;tvExcelReviewDate,143,F;tvExcelReviewDate,143,F;tvExcelReviewDate,143,F;chipContextOffice,136,F;kpiAchievedPercentageTextView,144,F;kpiRecyclerView,145,F;userAvatarImageView,146,F;kpiNameEditText,147,F;tvUrgentTasks,148,F;action_view_task_report,149,F;action_edit_kpi,150,F;action_edit_kpi,151,F;taskEstimatedTimeEditText,136,F;btnAddAnother,136,F;btn_confirm_ocr_import,152,F;btn_confirm_ocr_import,152,F;tvExcelReviewUser,143,F;tvExcelReviewUser,143,F;overallSummaryCardView,153,F;dashboardRecyclerView,154,F;dailyProgressIndicatorContainer,140,F;kpiUnitLabelTextView,147,F;cardExpiryManagement,155,F;tvCompletionRate,148,F;context_overall_change_color,156,F;chipGroupTaskIcon,136,F;datePicker,157,F;layoutSettings,136,F;action_clear_month_progress,151,F;expiryReturnCard,158,F;kpiMonthlyPercentageTextView,144,F;scrollViewFilters,148,F;tvExcelImportStatus,159,F;tvExcelImportStatus,159,F;templateCall,136,F;templateCall,138,F;timePicker,157,F;unitPercentageRadioButton,147,F;masterCardBarrier,160,F;bottom_sheet_title,150,F;priorityIndicator,161,F;rvNotUrgentImportant,148,F;userFilterLayout,162,F;dailyAchievedTextView,163,F;unifiedReportRecyclerView,164,F;action_logout,165,F;action_logout,166,F;actionCard,139,F;toolbarTaskManagement,148,F;etEditTaskExpirationDate,167,F;radioSelectUsers,168,F;reportContentContainer,162,F;taskReminderInputLayout,136,F;taskReminderInputLayout,137,F;btn_delete_review_item,169,F;btn_delete_review_item,169,F;adminRoleAutoComplete,170,F;annualProgressPercentageText,140,F;kpiSelectorAutoCompleteTextView,162,F;kpiSelectorAutoCompleteTextView,171,F;sort_ascending,172,F;buttonSaveColors,173,F;colorBlue,136,F;dialogLastEntryDateTextView,174,F;nav_messages,175,F;nav_messages,176,F;messageCard,177,F;messageCard,178,F;userEmail,179,F;action_duplicate_copy,150,F;messageEditText,180,F;rbCalculateAverage,181,F;radioNoReminder,182,F;btnFilter,148,F;statusIndicatorDot,183,F;statusIndicatorDot,161,F;taskEnergyLevelInputLayout,137,F;nearExpiryCard,158,F;addTimeButton,142,F;rememberMeCheckBox,170,F;fragmentContainer,184,F;fragmentContainer,160,F;switchEmailEnabled,185,F;tvTaskIcon,161,F;fabNewChat,186,F;messageTime,187,F;radioDaily,162,F;radioDaily,171,F;reportMonthlyTarget,188,F;reportMonthlyTarget,189,F;reportMonthlyTarget,190,F;goButton,162,F;goButton,171,F;currentTextView,133,F;currentTextView,133,F;currentTextView,133,F;spinnerUserFilter,181,F;spinnerUserFilter,181,F;progressOverall,148,F;tvExcelReviewValue,143,F;tvExcelReviewValue,143,F;tvExcelReviewValue,143,F;monthFilterSpinner,140,F;summaryBarChart,162,F;summaryBarChart,171,F;sliderProgress,136,F;fullNameEditText,191,F;btnSelectExcelFile,159,F;btnSelectExcelFile,159,F;kpiDetailAnnualPercentTextView,192,F;action_call,193,F;ownerChip,133,F;ownerChip,133,F;loginFormCard,170,F;loginFormCard,170,F;appBarLayoutTaskManagement,148,F;chipGroupEnergyLevel,136,F;tvOcrResult,194,F;reportQuarterlyRow,188,F;reportQuarterlyRow,189,F;reportQuarterlyRow,190,F;kpiTargetInputLayout,147,F;compactReportTable,162,F;compactReportTable,171,F;dialogTitleTextView,174,F;buttonPickBottomColor,173,F;reportKpiNameTextView,188,F;colorRed,136,F;rgAggregationOptions,181,F;rgAggregationOptions,181,F;fabAddKpiUserList,195,F;dayText,196,F;dailyPercentageTextView,163,F;buttonCancelColorSelection,173,F;taskEstimatedTimeInputLayout,136,F;kpiDescriptionEditText,147,F;taskNameText,142,F;etEditTaskName,167,F;chatButton,179,F;recentUsersRecyclerView,170,F;trendChartTitle,162,F;tvSubtaskName,197,F;kpiDailyTargetEditText,147,F;userFilterAutoCompleteTextView,162,F;cardQuickStats,148,F;tvTotalTasks,148,F;currentValueTextView,133,F;textUserEmail,198,F;btnShareBarChart,162,F;et_review_value,169,F;et_review_value,169,F;tvEnergyLevel,161,F;timerStatusText,142,F;cancelButton,137,F;cancelButton,191,F;cancelButton,168,F;priorityLow,138,F;taskCategorySelector,136,F;yearCard,196,F;selectDateButton,158,F;taskImportanceInputLayout,137,F;resultMiladiMonth,196,F;colorOrange,136,F;chipCategory,161,F;btnScheduleTask,161,F;scrollView,140,F;scrollView,162,F;remainingIcon,133,F;remainingIcon,133,F;remainingIcon,133,F;remainingIcon,133,F;remainingIcon,133,F;userName,132,F;userName,199,F;userName,179,F;rvNotUrgentNotImportant,148,F;annualProgressPercentageTextView,140,F;taskTagsInputLayout,137,F;taskLocationEditText,136,F;cardDateConverter,155,F;deleteButton,200,F;welcomeTextView,170,F;welcomeTextView,170,F;statCard,201,F;kpiQuarterlyTargetInputLayout,147,F;appBarLayoutTaskReport,202,F;btnTaskSuggestions,161,F;switchRecurring,136,F;priorityMedium,138,F;senderName,187,F;radioMiladiToHijri,196,F;userFilterRadioGroup,168,F;departmentEditText,191,F;tvTaskDetails,161,F;action_change_user_card_color,203,F;switchBreakDown,136,F;addPhotoButton,191,F;addPhotoButton,191,F;tvNotUrgentImportantCount,148,F;topColorPreview,173,F;chipIconWork,136,F;searchPromptTextView,200,F;appBarLayoutUserKpi,195,F;ivAttachment,161,F;tv_review_date,169,F;unreadBadge,199,F;action_search,204,F;action_search,193,F;averagePerDayTextView,140,F;endDateEditText,162,F;endDateEditText,171,F;chipIconSport,136,F;switchWhatsappReminders,182,F;sortChartButton,205,V40003006a,2d00030093,;"";reportDailyRow,188,F;reportDailyRow,189,F;reportDailyRow,190,F;entryDateTextView,206,F;requiredDailyValueTextView,133,F;topContentContainer,153,F;topContentContainer,207,F;switchReminderEmail,185,F;taskReminderAutoCompleteTextView,136,F;taskReminderAutoCompleteTextView,137,F;detailKpiTargetValueTextView,140,F;remainingTextView,133,F;remainingTextView,133,F;remainingTextView,133,F;kpiUnitRadioGroup,147,F;quickAdd10Button,140,F;progressIndicatorsLayout,140,F;chipGroupFilters,148,F;btnSaveDraft,136,F;reportContentCard,162,F;profilePictureCard,191,F;profilePictureCard,191,F;quickAdd50Button,140,F;dialogTitle,168,F;topBackgroundView,153,F;topBackgroundView,207,F;topBackgroundView,207,F;btnViewMode,148,F;detailKpiNameTextView,140,F;chipContextTravel,136,F;ownerTypeAutoCompleteTextView,147,F;timerDurationText,142,F;kpiLineChart,140,F;userAvatar,177,F;star_animator_tag,205,V400020039,2f00020064,;"";dateSpinner,200,F;messageTextView,208,F;monthText,196,F;trendChart,162,F;trendChart,171,F;action_search_edit_progress,151,F;radio3Days,182,F;targetIcon,133,F;targetIcon,133,F;targetIcon,133,F;targetIcon,133,F;targetIcon,133,F;targetIcon,133,F;btnLocation,136,F;monthlyProgressIndicatorContainer,140,F;layoutActionButtons,161,F;kpiTargetLabelTextView,209,F;action_add_to_calendar,210,F;taskPriorityInputLayout,137,F;textUserName,198,F;remainingValueTextView,133,F;selectOwnerImageButton,147,F;tvUserName,211,F;layoutDetails,161,F;kpiTargetEditText,147,F;tvTaskStatusText,183,F;tvFocusTime,161,F;etEditTaskExpirationTime,167,F;chipUrgent,148,F;resultHijriMonth,196,F;rvExcelReviewItems,181,F;userCard,132,F;userCard,179,F;convertButton,196,F;resultDayName,196,F;chipEnergyMedium,136,F;cbTaskCompleted,167,F;currentPeriodTargetTextView,133,F;currentPeriodTargetTextView,133,F;currentPeriodTargetTextView,133,F;radioAnnual,162,F;radioAnnual,171,F;fabAddKpi,145,F;userCheckbox,212,F;unreadIndicator,208,F;action_admin_dashboard,165,F;action_admin_dashboard,166,F;bottomColorPreview,173,F;taskTagsEditText,137,F;action_auto_send_settings,165,F;buttonTestEmail,182,F;buttonScheduleNow,185,F;startDateEditText,162,F;startDateEditText,171,F;colorPurple,136,F;resultMiladiMonthDays,196,F;summaryChartFilterRadioGroup,162,F;summaryChartFilterRadioGroup,171,F;switchReminderWhatsapp,185,F;reportQuarterlyPercentage,188,F;reportQuarterlyPercentage,189,F;reportQuarterlyPercentage,190,F;typeIconTextView,208,F;muteIcon,199,F;currentPeriodTargetValueTextView,133,F;adminModeCheckBox,170,F;actionIcon,139,F;editedText,177,F;editedText,178,F;templateMeeting,136,F;templateMeeting,138,F;kpiCurrentLabelTextView,209,F;taskNotesEditText,137,F;tvExcelReviewTitle,181,F;tvExcelReviewTitle,181,F;currentPeriodAchievementValueTextView,133,F;btnConfirmExcelImport,181,F;btnConfirmExcelImport,181,F;annualProgressLayout,140,F;ivCompleteTask,161,F;chipGroupTags,161,F;action_test_alarm,165,F;action_chat,165,F;action_chat,166,F;startDateInputLayout,162,F;startDateInputLayout,162,F;startDateInputLayout,162,F;startDateInputLayout,171,F;startDateInputLayout,171,F;startDateInputLayout,171,F;replyMessageText,177,F;replyMessageText,178,F;switchMatrixView,148,F;searchInput,213,F;userNameTextView,146,F;btnSearch,148,F;statValue,201,F;taskCategoryText,136,F;cardInteractiveReport,155,F;btnTemplate,136,F;lastMessage,199,F;tabLayout,186,F;kpiQuarterlyTargetEditText,147,F;priorityHigh,138,F;tvDueDate,161,F;startPauseButton,142,F;radioHijriToMiladi,196,F;systemText,214,F;tvTaskDescription,161,F;reportUserNameTextView,188,F;reportUserNameTextView,189,F;reportUserNameTextView,190,F;userKpiRecyclerView,195,F;progressIndicator,133,F;progressIndicator,133,F;progressIndicator,133,F;progressIndicator,133,F;progressIndicator,133,F;progressIndicator,133,F;progressIndicator,133,F;progressIndicator,133,F;timeText,177,F;timeText,178,F;timeText,199,F;starRatingTextView,133,F;starRatingTextView,133,F;appLogoImageView,170,F;appLogoImageView,170,F;chipOverdue,148,F;seekBarProgress,215,F;chipEnergyHigh,136,F;appBarLayout,136,F;appBarLayout,137,F;appBarLayout,138,F;appBarLayout,184,F;appBarLayout,180,F;appBarLayout,186,F;appBarLayout,196,F;appBarLayout,160,F;appBarLayout,162,F;appBarLayout,171,F;taskEnergyLevelAutoCompleteTextView,137,F;searchView,148,F;reportDailyPercentage,188,F;reportDailyPercentage,189,F;reportDailyPercentage,190,F;dueTimeInputLayout,138,F;chipCompleted,148,F;rbImportIndividual,181,F;action_settings,216,F;action_settings,204,F;chipAll,148,F;dailyProgressLayout,140,F;overallSummaryCardInclude,160,F;currentAchievementProgressIndicator,133,F;currentAchievementProgressIndicator,133,F;currentAchievementProgressIndicator,133,F;currentAchievementProgressIndicator,133,F;currentAchievementProgressIndicator,133,F;currentAchievementProgressIndicator,133,F;currentAchievementProgressIndicator,133,F;currentAchievementProgressIndicator,133,F;resultSyrianiMonth,196,F;guideline_summary_detail_70,192,F;guideline_summary_detail_70,192,F;kpiDailyTargetInputLayout,147,F;tvReportTaskName,217,F;editTextEmail,185,F;editTextEmail,182,F;conversionTypeGroup,196,F;progressBar,142,F;createButton,191,F;reportMonthlyAchieved,188,F;reportMonthlyAchieved,189,F;reportMonthlyAchieved,190,F;saveButton,200,F;sort_descending,172,F;radio1Week,182,F;cardTaskFollowUp,155,F;detailKpiProgressLabelTextView,140,F;rvUrgentNotImportant,148,F;buttonTestWeeklyReport,185,F;detailKpiMonthlyTargetValueTextView,140,F;monthlyProgressPercentageText,140,F;valueEditText,200,F;valueEditText,174,F;kpiDetailMonthlyPercentTextView,192,F;templateStudy,136,F;templateStudy,138,F;headerTextView,191,F;headerTextView,191,F;action_ocr,165,F;action_ocr,166,F;action_modern_task,165,F;switchRecurringTask,137,F;chipIconIdea,136,F;action_save_to_device,141,F;action_video_call,193,F;monthlyTargetTextView,163,F;chipToday,148,F;action_reset_color,150,F;cardChatList,218,F;layoutEisenhowerMatrix,148,F;currentPeriodAchievementIcon,133,F;currentPeriodAchievementIcon,133,F;currentPeriodAchievementIcon,133,F;currentPeriodAchievementIcon,133,F;currentPeriodAchievementIcon,133,F;bottomBackgroundView,207,F;tvAdvancedDetailsHeader,136,F;tvReportTaskExpirationDate,217,F;monthlyPercentageTextView,163,F;detailKpiDailyTargetValueTextView,140,F;taskExpirationTimeInputLayout,136,F;taskExpirationTimeInputLayout,137,F;roleIcon,179,F;action_chat_info,193,F;ivTaskActions,183,F;ivTaskActions,183,F;ivTaskActions,183,F;ivTaskActions,183,F;ivTaskActions,161,F;usernameInputLayout,191,F;usernameInputLayout,170,F;quickAccessTextView,170,F;quickAccessTextView,170,F;rvTasksDueSoon,202,F;textEmailStatus,185,F;rvAllTasksReport,202,F;messageText,177,F;messageText,178,F;messageText,187,F;taskNameEditText,136,F;taskNameEditText,137,F;taskNameEditText,138,F;ownerNameInputLayout,147,F;spinnerMonthFilter,181,F;spinnerMonthFilter,181,F;layoutAdvancedDetails,136,F;orTextView,170,F;onlineIndicator,199,F;onlineIndicator,179,F;messagesRecyclerView,180,F;userSummaryCardView,207,F;emptyStateTextView,145,F;action_switch_user,165,F;action_switch_user,166,F;currentPeriodAchievementTextView,133,F;currentPeriodAchievementTextView,133,F;currentPeriodAchievementTextView,133,F;compactReportRecyclerView,219,F;reportQuarterlyAchieved,188,F;reportQuarterlyAchieved,189,F;reportQuarterlyAchieved,190,F;action_notifications,165,F;reportAnnualTarget,188,F;reportAnnualTarget,189,F;reportAnnualTarget,190,F;action_reminder_settings,149,F;createUserButton,170,F;monthlyProgressLayout,140,F;radio1Day,182,F;progressText,142,F;headerTitle,220,F;action_archived,204,F;dateButton,174,F;currentIcon,133,F;currentIcon,133,F;currentIcon,133,F;currentIcon,133,F;currentIcon,133,F;currentIcon,133,F;radioAllUsers,168,F;taskDescriptionEditText,136,F;taskDescriptionEditText,137,F;taskDescriptionEditText,138,F;switchEmailReminders,182,F;addProgressButtonLayout,140,F;cbTaskComplete,161,F;reportAnnualRow,188,F;reportAnnualRow,189,F;reportAnnualRow,190,F;tilEditTaskReminderDays,167,F;scrollViewOcrResult,194,F;tvComparison,211,F;lineChartTitle,140,F;kpiDetailsContainer,153,F;kpiDetailsContainer,207,F;layoutFocusTime,161,F;btnSelectImage,194,F;tvTimeRemaining,161,F;buttonCancelSchedules,185,F;annualCircularProgressIndicator,140,F;textWhatsappStatus,185,F;tvTaskName,183,F;tvTaskName,183,F;tvTaskName,183,F;tvTaskName,161,F;kpiDescriptionInputLayout,147,F;emptyStateTextViewUserKpi,195,F;statIcon,201,F;tvUrgentImportantCount,148,F;activityUser,221,F;periodTextView,134,F;periodTextView,135,F;kpiNameTextView,144,F;kpiNameTextView,209,F;reportAnnualAchieved,188,F;reportAnnualAchieved,189,F;reportAnnualAchieved,190,F;taskNameInputLayout,136,F;taskNameInputLayout,137,F;taskNameInputLayout,138,F;layoutEnergyFocus,161,F;senderText,177,F;btnCreateMasterCard,160,F;taskEstimatedHoursInputLayout,137,F;action_share_user_card_image,203,F;kpiSubtitleTextView,133,F;kpiSubtitleTextView,133,F;reportAnnualPercentage,188,F;reportAnnualPercentage,189,F;reportAnnualPercentage,190,F;action_delete_task,210,F;appTitleTextView,170,F;appTitleTextView,170,F;userPerformance,132,F;action_clear_progress,151,F;addTaskButton,136,F;addTaskButton,137,F;taskNotesInputLayout,137,F;buttonResetColors,173,F;buttonSave,185,F;buttonSave,182,F;buttonCheckApps,185,F;buttonCheckApps,182,F;loginButton,170,F;tvValue,211,F;fabAddTask,148,F;switchNotifications,136,F;tvNotUrgentNotImportantCount,148,F;barChartContainer,162,F;reportMonthlyPercentage,188,F;reportMonthlyPercentage,189,F;reportMonthlyPercentage,190,F;dailyProgressPercentageText,140,F;taskExpirationDateEditText,136,F;taskExpirationDateEditText,137,F;unitNumberRadioButton,147,F;statTitle,201,F;action_change_color,150,F;tvSettingsHeader,136,F;userSelectionSpinner,170,F;nav_dashboard,175,F;nav_dashboard,176,F;btnVoiceNote,136,F;tvProgressValue,136,F;tvProgressValue,215,F;detailKpiCurrentValueTextView,140,F;cbSubtaskComplete,197,F;ownerTypeInputLayout,147,F;minutesInput,222,F;kpiNameLabel,162,F;kpiNameLabel,171,F;nearExpiryMessageTextView,158,F;adminRoleInputLayout,170,F;tilEditTaskExpirationDate,167,F;action_advanced_task,165,F;reportTableCard,171,F;targetValueTextView,133,F;unitPointRadioButton,147,F;emailInputLayout,191,F;reportDailyTarget,188,F;reportDailyTarget,189,F;reportDailyTarget,190,F;buttonTestWhatsapp,182,F;action_excel_import,151,F;kpiProgressPercentageTextView,209,F;templateExercise,136,F;tvTodayTasks,148,F;kpiTitleTextView,133,F;kpiTitleTextView,133,F;kpiTitleTextView,133,F;kpiTitleTextView,133,F;kpiTitleTextView,133,F;kpiTitleTextView,133,F;kpiTitleTextView,133,F;kpiTitleTextView,133,F;currentPeriodTargetIcon,133,F;currentPeriodTargetIcon,133,F;currentPeriodTargetIcon,133,F;currentPeriodTargetIcon,133,F;currentPeriodTargetIcon,133,F;searchResults,213,F;kpiNameInputLayout,147,F;resultHijriMonthDays,196,F;progress_circular_layout,209,F;resultHijriDate,196,F;lastUpdateIcon,133,F;lastUpdateIcon,133,F;lastUpdateIcon,133,F;lastUpdateIcon,133,F;action_expiry_management,165,F;action_expiry_management,166,F;etEditTaskReminderDays,167,F;chipIconHealth,136,F;btnSortTasks,148,F;action_share,223,F;kpiMonthlyTargetEditText,147,F;replyContainer,177,F;replyContainer,178,F;monthlyCircularProgressIndicator,140,F;cardMatrixToggle,148,F;tvUrgentNotImportantCount,148,F;tvTasksDueSoonTitle,202,F;action_delete_kpi,150,F;action_delete_kpi,151,F;taskExpirationDateInputLayout,136,F;taskExpirationDateInputLayout,137,F;colorGreen,136,F;achievedTextView,134,F;achievedTextView,135,F;departmentInputLayout,191,F;dayCard,196,F;dailyProgressPercentageTextView,140,F;statusText,178,F;rvSubtasks,161,F;userRole,179,F;buttonLogout,198,F;cardProfile,198,F;dueDateEditText,138,F;rvTasks,148,F;fab,224,F;annualPercentageTextView,163,F;actionDescription,139,F;titleTextView,208,F;loadingProgressBar,191,F;loadingProgressBar,170,F;textUserRole,198,F;tvAssignedUser,161,F;switchMonthlyReports,185,F;kpiMonthlyTargetInputLayout,147,F;progressBarExcelImport,159,F;conversationCard,199,F;nav_account,175,F;nav_account,176,F;btnShareTrendChart,162,F;dragHandleImageView,207,F;dragHandleImageView,207,F;chipGroupContext,136,F;rv_ocr_review,152,F;action_share_as_image,141,F;action_ocr_import,151,F;chipPriority,161,F;dueTimeEditText,138,F;rvUrgentImportant,148,F;dateText,225,F;kpiDetailNameTextView,192,F;kpiDetailNameTextView,192,F;kpiDetailNameTextView,192,F;kpiDetailNameTextView,192,F;kpiDetailNameTextView,192,F;kpiDetailNameTextView,192,F;ivSelectedImage,194,F;buttonPickTopColor,173,F;layoutEmptyState,148,F;action_delete_user_kpi,203,F;entryValueTextView,206,F;taskPriorityText,136,F;taskCategoryInputLayout,137,F;skipBreakButton,142,F;layoutAssignedUser,161,F;saveKpiButton,147,F;sendButton,180,F;kpiAnnualPercentageTextView,144,F;chipContextOnline,136,F;reportConstraintLayout,162,F;taskPriorityAutoCompleteTextView,137,F;tvProgressPercent,161,F;yearText,196,F;tvOcrResultLabel,194,F;taskExpirationTimeEditText,136,F;taskExpirationTimeEditText,137,F;emailEditText,191,F;quickStatsCard,140,F;btnTimer,136,F;targetsTitleTextView,140,F;kpiCurrentValueTextView,209,F;currentAchievementPercentageTextView,133,F;userCheckboxRecyclerView,168,F;chipIconStudy,136,F;switchWeeklyReports,185,F;switchAutoReminders,185,F;toolbarUserKpi,195,F;tvEnergyIcon,161,F;action_refresh,216,F;btnCopyText,194,F;userImage,199,F;userImage,179,F;dueDateInputLayout,138,F;tvAllTasksTitle,202,F;bottomAppBar,136,F;ownerImageView,147,F;ownerImageView,207,F;ownerImageView,207,F;ownerImageView,207,F;ownerImageView,207,F;ownerImageView,207,F;ownerImageView,207,F;buttonLayout,191,F;ownerNameSelectionLayout,147,F;profilePictureImageView,191,F;requiredDailyIcon,133,F;requiredDailyIcon,133,F;requiredDailyIcon,133,F;requiredDailyIcon,133,F;requiredDailyIcon,133,F;activityTime,221,F;kpiCircularProgressIndicator,209,F;targetTextView,133,F;targetTextView,133,F;targetTextView,133,F;targetTextView,134,F;targetTextView,135,F;toolbar,136,F;toolbar,137,F;toolbar,138,F;toolbar,184,F;toolbar,185,F;toolbar,180,F;toolbar,186,F;toolbar,196,F;toolbar,160,F;toolbar,162,F;toolbar,224,F;toolbar,171,F;toolbar,182,F;nav_performance,175,F;nav_performance,176,F;formLayout,191,F;formLayout,191,F;reportMonthlyRow,188,F;reportMonthlyRow,189,F;reportMonthlyRow,190,F;annualAchievedTextView,163,F;tvPercentage,211,F;stopButton,142,F;valueInputLayout,200,F;tilEditTaskName,167,F;trendChartContainer,162,F;barChartTitle,162,F;activityAction,221,F;action_edit_task,210,F;kpiTargetValueTextView,209,F;reportQuarterlyTarget,188,F;reportQuarterlyTarget,189,F;reportQuarterlyTarget,190,F;switchWhatsappEnabled,185,F;radioMonthly,162,F;radioMonthly,171,F;bottomNavigation,184,F;bottomNavigation,160,F;currentAchievementLabelTextView,133,F;expiryReturnMessageTextView,158,F;action_share_card,150,F;action_export_user_data_csv,203,F;addProgressButton,140,F;reportDailyAchieved,188,F;reportDailyAchieved,189,F;reportDailyAchieved,190,F;applyButton,168,F;recyclerView,186,F;recyclerView,224,F;progressTask,161,F;annualProgressIndicatorContainer,140,F;cardAiSuggestions,136,F;tilEditTaskExpirationTime,167,F;toolbarTaskReport,202,F;btnCamera,136,F;taskEstimatedHoursEditText,137,F;resultMiladiDate,196,F;ownerNameEditText,147,F;taskPrioritySelector,136,F;kpiOwnerTextView,209,F;activityCard,221,F;btnStartTask,161,F;tvTarget,211,F;requiredDailyTextView,133,F;requiredDailyTextView,133,F;requiredDailyTextView,133,F;lastUpdateValueTextView,133,F;editTextPhone,185,F;editTextPhone,182,F;tvTaskExpirationDate,183,F;layoutQuickActions,161,F;lastUpdateTextView,133,F;lastUpdateTextView,133,F;lastUpdateTextView,133,F;fullNameInputLayout,191,F;sort_alphabetical,172,F;timeTextView,208,F;action_view_modern_report,165,F;action_view_modern_report,166,F;kpiSelectorLayout,162,F;kpiSelectorLayout,171,F;doctorNameTextView,163,F;doctorNameTextView,134,F;doctorNameTextView,135,F;taskImportanceAutoCompleteTextView,137,F;monthCard,196,F;dateHeader,178,F;unitCurrencyRadioButton,147,F;switchLocalNotifications,182,F;dailyTargetTextView,163,F;dailyCircularProgressIndicator,140,F;endDateInputLayout,162,F;endDateInputLayout,162,F;endDateInputLayout,171,F;endDateInputLayout,171,F;progressSummaryTextView,140,F;kpiProgressBar,144,F;ownerNameTextView,207,F;taskLocationInputLayout,136,F;monthlyAchievedTextView,163,F;annualTargetTextView,163,F;attachButton,180,F;chipThisWeek,148,F;btnSaveTaskReport,202,F;usernameEditText,191,F;usernameEditText,170,F;taskCategoryAutoCompleteTextView,137,F;lastEntryDateTextView,140,F;fabSave,138,F;emptyStateText,186,F;emptyStateText,224,F;thisWeekProgressTextView,140,F;chipContextHome,136,F;buttonTestMonthlyReport,185,F;targetsCard,140,F;chipEnergyLow,136,F;replyToText,177,F;replyToText,178,F;+layout:item_task_report,217,F;activity_modern_report,162,F;activity_add_edit_task_enhanced,137,F;dialog_kpi_actions,150,F;activity_kpi_detail,140,F;compact_report_table_row,163,F;item_admin_dashboard_action,139,F;report_table_row_tabular,190,F;activity_excel_review,181,F;fragment_messages,218,F;compact_report_table,219,F;item_chat_message_sent,178,F;kpi_detail_item,144,F;activity_user_kpi_list,195,F;activity_chat_list,186,F;item_task_enhanced,161,F;activity_ocr_review,152,F;item_user_list,179,F;item_subtask_mini,197,F;activity_pomodoro_timer,142,F;activity_date_converter,196,F;item_conversation,199,F;activity_add_edit_kpi,136,F;dialog_add_edit_progress,174,F;activity_login,170,F;report_table_row_colored,189,F;kpi_summary_detail_item,192,F;kpi_list_item,209,F;dialog_edit_task,167,F;unified_report_table_row_binding,135,F;activity_chat,180,F;dialog_progress_update,215,F;activity_report,171,F;item_chat_system_message,214,F;fragment_performance,155,F;overall_summary_card_item,153,F;activity_add_task_modern,138,F;report_table_row,188,F;item_notification,208,F;activity_excel_import,159,F;activity_task_management,148,F;ocr_review_item,169,F;user_checkbox_item,212,F;fragment_dashboard,154,F;item_task,183,F;unified_report_table,164,F;activity_auto_send_settings,185,F;activity_main,160,F;user_filter_dialog,168,F;item_chat_message_received,177,F;dialog_search_messages,213,F;activity_add_edit_kpi_original,147,F;activity_create_user,191,F;fragment_account,198,F;fragment_main_dashboard,145,F;activity_notifications,224,F;item_admin_dashboard_header,220,F;activity_expire_management,158,F;unified_report_table_row,134,F;activity_task_report,202,F;item_recent_user,146,F;chart_marker_view,211,F;divider_view,226,F;user_summary_card_item,207,F;activity_admin_dashboard,184,F;activity_task_reminder_settings,182,F;dialog_custom_reminder,157,F;kpi_card_item,133,F;excel_review_item,143,F;item_admin_dashboard_activity,221,F;dialog_select_card_colors,173,F;dialog_custom_pomodoro,222,F;item_admin_dashboard_user,132,F;activity_search_edit_progress,200,F;activity_ocr,194,F;item_admin_dashboard_stat,201,F;item_chat_date_header,225,F;item_search_result,187,F;progress_entry_item,206,F;+menu:kpi_detail_menu,151,F;task_item_actions_menu,210,F;chat_list_menu,204,F;main_menu,166,F;task_management_menu,149,F;overall_summary_context_menu,156,F;main,165,F;main_bottom_navigation,176,F;chat_menu,193,F;sort_options_menu,172,F;admin_dashboard_menu,216,F;chart_options_menu,223,F;admin_bottom_navigation,175,F;user_summary_context_menu,203,F;chart_share_menu,141,F;+mipmap:ic_launcher,227,F;+raw:notification_sound,228,F;+string:import_successful_count,7,V400b02ef9,5600b02f4b,;"Imported %1$d out of %2$d entries.";add_kpi_title,7,V40002004b,3100020078,;"Add KPI";reset_to_default_color,7,V40059163a,3a00591670,;"Default";kpi_duplicated_success,7,V400f23fab,4f00f23ff6,;"KPI duplicated successfully.";remaining_percentage_label,7,V400d03720,4300d0375f,;"Remaining %\:";import_excel_data_button,7,V4009b27d7,40009b2813,;"Import Data";add_progress_entry_button,7,V4011c4bef,48011c4c33,;"Add Progress Entry";view_history_button_text,7,V40023084f,410023088c,;"View History";selected_users_label,7,V401174ab4,4001174af0,;"Selected Users\:";percentage_value_label,7,V400ce36a2,3e00ce36dc,;"Percentage\:";kpi_expiry_reminder_text_quarterly,7,V400f94203,7d00f9427c,;"Quarterly target for '%1$s' is approaching. Review progress.";no_text_found_ocr,7,V4008d2388,53008d23d7,;"No text found in image or OCR failed.";generate_report_button,7,V400ba31af,4200ba31ed,;"Generate Report";delete_kpi_confirmation_title,7,V40176629d,3e017662d7,;"TODO";monthly_progress_label_short,7,V40061183d,460061187f,;"Month\: %1$d%%";edit_task_dialog_title,7,V40140552c,3c01405564,;"Edit Task";progress_cleared_toast,7,V4008220fb,440082213b,;"Progress cleared.";dialog_kpi_action_title,7,V4014355b5,3f014355f0,;"KPI Actions";kpi_monthly_target_hint,7,V4000801c4,4d0008020d,;"Monthly Target (Optional)";action_edit_kpi,7,V4003a0e7c,34003a0eac,;"Edit KPI";kpi_expiry_reminder_text_monthly,7,V400f84188,7900f841fd,;"Monthly target for '%1$s' is approaching. Review progress.";change_color,7,V4015e5cdf,35015e5d10,;"Change Color";excel_review_title,7,V4009a2795,40009a27d1,;"Review Excel Data";confirm_clear_progress_title,7,V4007e1f3e,4f007e1f89,;"Confirm Clear Progress";edit_ocr_item_title,7,V40094261f,3d00942658,;"Edit OCR Item";kpi_card_item_required_daily_label,7,V401535a25,4a01535a6b,;"Daily Req.\:";error_deleting_kpi,7,V401796355,3301796384,;"TODO";action_clear_month_progress,7,V40080201b,4f00802066,;"Clear Month's Progress";action_edit,7,V401595b9f,2c01595bc7,;"Edit";kpis_assigned_label,7,V4006a1aba,41006a1af7,;"KPIs Assigned\: %d";error_average_for_all_users,7,V400af2e67,9000af2ef3,;"Cannot calculate average for All Users. Select a specific user or import individually.";user_summary_context_menu_title,7,V401054628,4d01054671,;"User Card Actions";duplicate_kpi_menu_item,7,V400f13f68,4100f13fa5,;"Duplicate KPI";unit_point,7,V4000e032b,2c000e0353,;"Point";user_name_label,7,V400421046,3500421077,;"User Name";expiry_notification_channel_description,7,V400f6409e,9e00f64138,;"Reminders for KPIs nearing their implicit monthly/quarterly/annual review or target dates.";user_card_color_top_label,7,V400ed3e7c,4500ed3ebd,;"Card Top Color\:";overall_summary,7,V401565b21,3b01565b58,;"Overall Summary";user_summaries_title,7,V400771d7d,3f00771db8,;"User Summaries";color_picker_title_individual_kpi_card,7,V4005815dc,5c00581634,;"Individual KPI Card Color";menu_search_kpis,7,V400d838f4,3800d83928,;"Search KPIs";import_partially_successful,7,V400b22fad,7800b23021,;"Import partially successful. %1$d entries imported\, %2$d errors.";near_expiry_notification_message,7,V4019068e3,4101906920,;"TODO";summary_detail_annual_format,7,V401655e76,4501655eb7,;"Annual\: %d%%";confirm_clear_month_progress_message,7,V40081206c,8d008120f5,;"Are you sure you want to clear progress for %1$s for this KPI for this user?";map_value_column_label,7,V400a12a37,4000a12a73,;"Value Column\:";overall_kpi_summary_title,7,V400761d32,4900761d77,;"Overall KPI Summary";dialog_kpi_action_delete,7,V401495755,3b0149578c,;"Delete";error_clearing_progress,7,V400832141,4c00832189,;"Error clearing progress.";ocr_value_label,7,V400892292,32008922c0,;"Value\:";no_date_placeholder,7,V4013451ab,37013451de,;"No date";owner_type_hint,7,V400340cf2,3600340d24,;"Owner Type";owner_type_company,7,V4003e0f5c,36003e0f8e,;"Company";kpi_monthly_target_label_formatted,7,V401746215,4301746254,;"TODO";kpi_detail_tab_monthly,7,V400c93572,3a00c935a8,;"Monthly";pref_notifications_title,7,V400fb42fb,4200fb4339,;"Notifications";edit_progress_title,7,V40017055c,3d00170595,;"Edit Progress";target_achieved_notification_title,7,V4005d1735,4f005d1780,;"Target Achieved!";clear_month_progress_confirmation_message,7,V401806514,4a0180655a,;"TODO";filter_by_owner_hint,7,V400320c80,4000320cbc,;"Filter by Owner";edit_kpi_menu_item,7,V4002c0b32,37002c0b65,;"Edit KPI";no_entries_yet,7,V4016c602d,2f016c6058,;"TODO";error_ocr_processing,7,V4008c2335,51008c2382,;"Error processing image with OCR.";summary_detail_monthly_format,7,V401645e2d,4701645e70,;"Monthly\: %d%%";error_excel_processing,7,V4009c2819,4f009c2864,;"Error processing Excel file.";kpi_annual_target_label_formatted,7,V4017361d1,420173620f,;"TODO";select_excel_file_button_text,7,V401726191,3e017261cb,;"TODO";kpi_card_item_target_label,7,V4014d586a,3e014d58a4,;"Target\:";save_user_button_text,7,V400e33b80,3b00e33bb7,;"Save User";kpi_description_hint,7,V4000500e7,470005012a,;"Description (Optional)";error_select_kpi_for_ocr,7,V400922503,5f0092255e,;"Please select a KPI to assign the data to.";filter_expiry,7,V4011e4c65,30011e4c91,;"Expiry";kpi_detail_tab_current,7,V400c83536,3a00c8356c,;"Current";confirm_delete_user_message,7,V400e73c97,a200e73d35,;"Are you sure you want to delete user '%1$s' and all their KPI assignments? This action cannot be undone.";near_expiry_notification_title,7,V4018f68a2,3f018f68dd,;"TODO";reminder_option_2_days,7,V401986acd,4001986b09,;"2 days before";save_kpi_button,7,V4011b4bb9,34011b4be9,;"Save KPI";kpi_card_item_remaining_to_target_label,7,V4015259d5,4e01525a1f,;"Remaining\:";month_year_format,7,V400701c25,3700701c58,;"MMMM yyyy";all_users_excel_filter,7,V400ab2d38,4800ab2d7c,;"All Users (from file)";confirm_delete_task_title,7,V4013c53e9,49013c542e,;"Confirm Delete Task";app_name,7,V400010015,3400010045,;"KPI Tracker Pro";clear_month_progress_select_month_message,7,V4017e647e,4a017e64c4,;"TODO";kpi_card_item_current_period_target_label,7,V40150591f,540150596f,;"Period Target\:";kpi_target_hint,7,V400060130,3f0006016b,;"Annual Target Value";current_progress_label,7,V400200794,44002007d4,;"Current Progress\:";ocr_review_title,7,V400872211,3f0087224c,;"Review OCR Results";user_name_hint_manage,7,V400e13af8,3b00e13b2f,;"User Name";delete_ocr_item_title,7,V40095265e,410095269b,;"Delete OCR Item";action_share_user_card_image,7,V401605d68,4e01605db2,;"Share User Card Image";no_kpis_assigned,7,V4012a4f25,3d012a4f5e,;"No KPIs Assigned";user_image_label_manage,7,V400e23b35,4900e23b7a,;"User Image (Optional)";gcm_defaultSenderId,229,V4000300c7,5100030114,;"123456789012";select_excel_file,7,V40099274c,470099278f,;"Select Excel File (.xlsx)";task_item_expires_prefix,7,V401335167,42013351a5,;"Expires\: %1$s";action_clear_progress,7,V4007d1efc,40007d1f38,;"Clear Progress";error_invalid_target,7,V40012041c,450012045d,;"Invalid target value";unit_percentage,7,V4000c02bf,36000c02f1,;"Percentage";owner_type_user,7,V4003c0eec,30003c0f18,;"User";action_delete,7,V4015a5bcd,30015a5bf9,;"Delete";add_kpi,7,V401244dd6,2b01244dfd,;"Add KPI";error_kpi_or_user_not_found,7,V40184661c,3c01846654,;"TODO";error_no_sheets_found,7,V400a82c4b,5400a82c9b,;"No sheets found in the Excel file.";header_row_label,7,V400a92ca1,4000a92cdd,;"Data starts at row\:";enter_new_user_name_hint,7,V400481203,4800481247,;"Enter New User Name";add_user_button_text,7,V400e03abd,3900e03af2,;"Add User";dialog_cancel,7,V4002f0bdb,30002f0c07,;"Cancel";unit_currency,7,V4000d02f7,32000d0325,;"Currency";no_progress_entries,7,V400240892,48002408d6,;"No progress entries yet.";action_add_progress,7,V400380e00,3c00380e38,;"Add Progress";status_overdue,7,V401365224,3a0136525a,;"Status\: Overdue";edit_user_title,7,V400ea3dd1,3500ea3e02,;"Edit User";task_notification_channel_description,7,V4013b537b,6c013b53e3,;"Notifications for upcoming task deadlines.";delete_entry_confirmation_title,7,V4016f60d2,40016f610e,;"TODO";select_owner_image_button,7,V4011a4b75,42011a4bb3,;"Select Image";project_id,229,V4000802c6,5000080312,;"kpi-tracker-app-demo";report_start_date_label,7,V400b83131,3f00b8316c,;"Start Date\:";select_users_dialog_title,7,V40043107d,42004310bb,;"Select Users";task_deleted_success,7,V4013e549c,47013e54df,;"Task '%1$s' deleted.";filter_by_user_excel_label,7,V400aa2ce3,5300aa2d32,;"Filter by User (from Excel)\:";report_period_monthly,7,V400bc322a,3900bc325f,;"Monthly";menu_import_ocr,7,V400da396f,4300da39ae,;"Import from Image (OCR)";menu_settings,7,V400dc39f6,3200dc3a24,;"Settings";users_not_loaded_yet,7,V40046115d,60004611b9,;"Users not loaded yet\, please try again shortly.";error_user_name_required,7,V400e43bbd,4b00e43c04,;"User name is required.";reset_to_default,7,V401585b60,3d01585b99,;"Reset to Default";report_period_daily,7,V400bb31f3,3500bb3224,;"Daily";menu_generate_report,7,V400db39b4,4000db39f0,;"Generate Report";edit_kpi_title,7,V40003007e,33000300ad,;"Edit KPI";no_overall_summary_to_display,7,V4007b1e9c,5c007b1ef4,;"No overall KPI summary to display.";search_edit_prompt,7,V401264e45,4401264e85,;"Search or Edit Prompt";reset_to_default_colors_button,7,V4014b57d4,4b014b581b,;"Reset to Default";action_edit_user,7,V400eb3e08,3600eb3e3a,;"Edit User";color_set_success,7,V401685f46,3201685f74,;"TODO";add_progress_button_text,7,V40022080c,4100220849,;"Add Progress";no_entries_found,7,V401916926,3101916953,;"TODO";dialog_kpi_action_change_color,7,V4014756c5,4701475708,;"Change Color";add_task_button_text,7,V4013150fc,340131512c,;"Add";notification_channel_description,7,V4005c16c1,72005c172f,;"Notifications for KPI events like target achievement.";dialog_select_card_colors_title,7,V401084707,4e01084751,;"Select Card Colors";excel_import_instructions,7,V4009f2918,d9009f29ed,;"Select an Excel file (.xlsx). Ensure it has columns for Value (numeric)\, Date (e.g.\, YYYY-MM-DD)\, and optionally User Identifier. Review and edit before importing.";reminder_option_no_reminder,7,V401956a03,4301956a42,;"No reminder";ocr_import_instructions,7,V400932564,b900932619,;"Select an image containing numerical data\, dates\, and optional user identifiers. Review and edit the extracted data before importing.";kpi_report_title,7,V400b53078,3700b530ab,;"KPI Report";confirm_delete_kpi_message,7,V4002809a3,7500280a14,;"Are you sure you want to delete this KPI and all its progress?";no_kpis_to_display,7,V400791e00,4200791e3e,;"No KPIs to display.";aggregation_average,7,V400ae2e10,5500ae2e61,;"Calculate Average (for selected user)";progress_value_hint,7,V40018059b,35001805cc,;"Value";no_data_for_selected_month,7,V4018365df,3b01836616,;"TODO";column_mapping_title,7,V400a029f3,4200a02a31,;"Map Excel Columns";colors_updated_successfully,7,V4010c4833,4c010c487b,;"Card colors updated.";action_excel_import,7,V4015c5c4d,41015c5c8a,;"Import from Excel";kpi_daily_target_label_formatted,7,V40175625a,4101756297,;"TODO";action_delete_user,7,V400ec3e40,3a00ec3e76,;"Delete User";error_value_required,7,V4001b064c,42001b068a,;"Value is required";task_expiration_date_hint,7,V4012e5017,45012e5058,;"Expiration Date";owner_type_department,7,V4003d0f1e,3c003d0f56,;"Department";report_end_date_label,7,V400b93172,3b00b931a9,;"End Date\:";settings_activity_title,7,V40102458b,3c010245c3,;"Settings";kpi_actions_dialog_title,7,V400370dbe,4000370dfa,;"KPI Actions";expiry_notification_channel_name,7,V400f5404b,5100f54098,;"KPI Expiry Reminders";user_saved_success,7,V400e93d88,4700e93dcb,;"User saved successfully.";task_name_hint,7,V4012d4fe1,34012d5011,;"Task Name";select_card_gradient_colors,7,V401695f7a,3c01695fb2,;"TODO";task_added_success,7,V4013952eb,430139532a,;"Task '%1$s' added.";task_notification_channel_name,7,V4013a5330,49013a5375,;"Task Reminders";no_users_selected_hint,7,V4004410c1,4400441101,;"No users selected";status_completed,7,V4013551e4,3e0135521e,;"Status\: Completed";label_bottom_color,7,V4010a478f,3c010a47c7,;"Bottom Color\:";label_top_color,7,V401094757,3601094789,;"Top Color\:";action_expiry_management,7,V401104916,4601104958,;"Expiry Management";overall_summary_context_menu_title,7,V4010445cb,5b01044622,;"Overall Summary Card Actions";progress_entry_deleted,7,V401716158,370171618b,;"TODO";select_user_hint,7,V400400fc8,3800400ffc,;"Select User";kpi_detail_tab_quarterly,7,V400ca35ae,3e00ca35e8,;"Quarterly";existing_user_added_to_selection_message,7,V4004b12ec,66004b134e,;"Existing user added to selection.";overall_summary_card_title,7,V4006017f5,4600601837,;"Overall Summary";review_ocr_results_title,7,V4011349e8,4701134a2b,;"Review OCR Results";edit_progress_entry_menu_item,7,V4002d0b6b,44002d0bab,;"Edit Entry";confirm_delete_progress_entry_title,7,V4002a0a64,54002a0ab4,;"Confirm Delete Entry";action_delete_kpi,7,V4003b0eb2,38003b0ee6,;"Delete KPI";kpi_card_item_current_label,7,V4014f58de,3f014f5919,;"Current";pref_key_target_achieved_notif,7,V400fc433f,5700fc4392,;"target_achieved_notification";pref_summary_target_achieved_notif,7,V400fe43f4,6f00fe445f,;"Receive a notification when a KPI target is met.";clear_progress_confirmation_message,7,V4017b63ce,44017b640e,;"TODO";select_user_for_report,7,V400b730f0,3f00b7312b,;"Select User\:";kpi_daily_target_hint,7,V400090213,4900090258,;"Daily Target (Optional)";action_customize_colors,7,V4010746c1,4401074701,;"Customize Colors";filter_by_month_button_text,7,V4006f1bdc,47006f1c1f,;"Filter by Month";dialog_color_picker_save_button,7,V4014c5821,47014c5864,;"Save Colors";no_kpis_assigned_to_user,7,V4006b1afd,53006b1b4c,;"No KPIs assigned to this user.";remaining_value_label,7,V400cf36e2,3c00cf371a,;"Remaining\:";delete_kpi_menu_item,7,V400260921,3b00260958,;"Delete KPI";confirm_delete_kpi_title,7,V40027095e,430027099d,;"Confirm Delete";map_user_column_label,7,V400a32ab9,5400a32b09,;"User Identifier Column (Optional)\:";ocr_data_imported_success,7,V4008e23dd,55008e242e,;"OCR data imported successfully.";user_name_cannot_be_empty_error,7,V4004c1354,56004c13a6,;"User name cannot be empty.";kpi_detail_tab_yearly,7,V400cb35ee,3800cb3622,;"Yearly";user_card_color_bottom_label,7,V400ee3ec3,4b00ee3f0a,;"Card Bottom Color\:";all_kpis_title,7,V400781dbe,4000781dfa,;"All KPIs (Aggregated)";report_period_annual,7,V400be32a4,3f00be32df,;"Annual (Range)";reminder_option_3_days,7,V401996b0f,4001996b4b,;"3 days before";add_progress_dialog_title,7,V4012b4f64,42012b4fa2,;"Add Progress";error_clearing_month_progress,7,V40182659f,3e018265d9,;"TODO";dialog_confirm,7,V4017c6414,2f017c643f,;"TODO";dialog_color_picker_pick_button,7,V4014a5792,40014a57ce,;"Pick";task_expiration_time_hint,7,V4013050aa,50013050f6,;"Expiration Time (Optional)";delete_entry_button,7,V401294ee7,3c01294f1f,;"Delete Entry";kpi_list_title,7,V4001504ee,2f00150519,;"KPIs";kpi_annual_target_hint,7,V401164a72,4001164aae,;"Annual Target";report_period_quarterly,7,V400bd3265,3d00bd329e,;"Quarterly";filter_report,7,V4011f4c97,3c011f4ccf,;"Interactive Report";dialog_color_picker_title,7,V4018c67de,3a018c6814,;"TODO";target_label,7,V4002107da,3000210806,;"Target\:";kpi_card_item_current_period_achievement_label,7,V401515975,5e015159cf,;"Period Achievement\:";save_kpi_button_text,7,V4000f0359,39000f038e,;"Save KPI";add_new_user_button_label,7,V4004711bf,42004711fd,;"Add New User";owner_name_hint,7,V4004d13ac,35004d13dd,;"User Name";achieved_value_label,7,V400cd3666,3a00cd369c,;"Achieved\:";hide_master_card,7,V401234d97,3d01234dd0,;"Hide master card";progress_history_title,7,V4002508dc,430025091b,;"Progress History";no_data_for_report,7,V400c333d7,5b00c3342e,;"No data available for the selected criteria.";report_for_kpi_user_format,7,V400c53482,4d00c534cb,;"Report for %1$s - %2$s";aggregation_individual,7,V400ad2dc8,4600ad2e0a,;"Import Individually";current_label,7,V4014e58aa,32014e58d8,;"Current\:";action_change_user_card_color,7,V4015f5d16,50015f5d62,;"Change User Card Color";no_data_to_import,7,V400b33027,4d00b33070,;"No data to import after review.";clear_month_progress_confirmation_title,7,V4017f64ca,48017f650e,;"TODO";user_kpi_list_title,7,V401144a31,3d01144a6a,;"User KPI List";ocr_data_import_failed,7,V4008f2434,4d008f247d,;"Failed to import OCR data.";excel_data_imported_success,7,V4009d286a,59009d28bf,;"Excel data imported successfully.";error_saving_progress,7,V4001e0711,47001e0754,;"Error saving progress";not_applicable_short,7,V400d43836,3400d43866,;"N/A";color_picker_title_summary_card_top,7,V400561526,5600561578,;"Summary Card Top Color";action_delete_user_kpi,7,V401615db8,4d01615e01,;"Delete User KPI Assignment";filter_all,7,V4011d4c39,2a011d4c5f,;"All";kpi_detail_title,7,V4001f075a,38001f078e,;"KPI Details";kpi_quarterly_target_hint,7,V400070171,51000701be,;"Quarterly Target (Optional)";no_kpis_for_this_user,7,V400731ca1,4e00731ceb,;"No KPIs found for this user.";confirm_delete_user_title,7,V400e63c4c,4900e63c91,;"Confirm Delete User";user_summary_card_title_prefix,7,V400691a72,4600691ab4,;"Summary for";task_updated_success,7,V40141556a,47014155ad,;"Task '%1$s' updated.";error_invalid_expiration_date,7,V401a56d1f,5201a56d6d,;"Invalid expiration date.";pref_title_target_achieved_notif,7,V400fd4398,5a00fd43ee,;"Target Achieved Notifications";clear_month_progress_success,7,V401816560,3d01816599,;"TODO";assign_to_kpi_label,7,V400902483,3e009024bd,;"Assign to KPI\:";edit_progress_dialog_title,7,V4016a5fb8,3b016a5fef,;"TODO";error_date_required,7,V4001d06cf,40001d070b,;"Date is required";reminder_option_1_week,7,V4019a6b51,40019a6b8d,;"1 week before";kpi_detail_tab_all_time,7,V400cc3628,3c00cc3660,;"All Time";confirm_delete_task_message,7,V4013d5434,66013d5496,;"Are you sure you want to delete task '%1$s'?";status_due_in_days,7,V4013852a0,49013852e5,;"Status\: Due in %1$d day(s)";report_percentage_label,7,V400c13359,3e00c13393,;"Percentage";google_api_key,229,V400040119,660004017b,;"AIzaSyDemoKeyForKPITrackerApp123456789";error_target_required,7,V4001103d6,4400110416,;"Target is required";required_daily_rate_label,7,V400d237a2,4500d237e3,;"Required Daily\:";kpi_deleted_success,7,V40178631f,340178634f,;"TODO";add_progress_title,7,V40016051f,3b00160556,;"Add Progress";owner_type_other,7,V4003f0f94,32003f0fc2,;"Other";color_reset_success,7,V401675f10,3401675f40,;"TODO";total_monthly_target_label,7,V4006318cb,4f00631916,;"Total Monthly Target\: %s";action_search_edit_progress,7,V4015b5bff,4c015b5c47,;"Search/Edit Progress";value_hint,7,V401274e8b,2c01274eb3,;"Value";label_individual_color,7,V4010b47cd,43010b480c,;"Card Background\:";kpi_expiry_reminder_text_annual,7,V400fa4282,7700fa42f5,;"Annual target for '%1$s' is approaching. Review progress.";ocr_import_failed,7,V40185665a,3201856688,;"TODO";no_target_set_short,7,V400d5386c,3900d538a1,;"No Target";kpi_unit_label,7,V4000a025e,2f000a0289,;"Unit";all_users_report_option,7,V400c634d1,4a00c63517,;"All Users (Aggregated)";pref_summary_expiry_reminder_notif,7,V40101451a,6f01014585,;"Receive reminders for KPIs nearing review dates.";last_entry_date_label,7,V4016b5ff5,36016b6027,;"TODO";notification_channel_name,7,V4005b1678,47005b16bb,;"KPI Notifications";kpi_summary_item_format,7,V4006c1b52,4c006c1b9a,;"%1$s\: M %2$d%%\, Y %3$d%%";pref_key_expiry_reminder_notif,7,V400ff4465,5700ff44b8,;"expiry_reminder_notification";import_failed_with_errors,7,V400b12f51,5a00b12fa7,;"Import failed. %1$d errors occurred.";main_activity_title,7,V400751cf3,3d00751d2c,;"KPI Dashboard";ocr_date_label,7,V4008a22c6,30008a22f2,;"Date\:";annual_progress_label_short,7,V400621885,44006218c5,;"Year\: %1$d%%";performance_report_title,7,V4010f48cd,47010f4910,;"Performance Report";kpi_card_item_last_update_label,7,V401545a71,4801545ab5,;"Last Update\:";search_hint,7,V400300c0d,3600300c3f,;"Search KPIs...";google_crash_reporting_api_key,229,V4000601e7,7600060259,;"AIzaSyDemoKeyForKPITrackerApp123456789";excel_import_title,7,V40098270f,3b00982746,;"Excel Import";report_target_label,7,V400bf32e5,3600bf3317,;"Target";user_deleted_success,7,V400e83d3b,4b00e83d82,;"User deleted successfully.";error_invalid_value,7,V4001c0690,3d001c06c9,;"Invalid value";total_annual_achieved_label,7,V4006619c0,5100661a0d,;"Total Annual Achieved\: %s";ocr_import_title,7,V400852191,37008521c4,;"OCR Import";select_kpi_for_report,7,V400b630b1,3d00b630ea,;"Select KPI\:";save_button,7,V401284eb9,2c01284ee1,;"Save";trend_chart_title,7,V400c23399,3c00c233d1,;"Progress Trend";filter_task_follow_up,7,V401204cd5,4001204d11,;"Task follow-up";create_new_user_dialog_title,7,V40049124d,450049128e,;"Add New User";report_achieved_label,7,V400c0331d,3a00c03353,;"Achieved";kpi_duplication_failed,7,V400f33ffc,4b00f34043,;"Failed to duplicate KPI.";select_month_year_title,7,V400d638a7,4900d638ec,;"Select Month and Year";confirm_delete_ocr_item_message,7,V4009626a1,6a00962707,;"Are you sure you want to delete this OCR item?";total_monthly_achieved_label,7,V40064191c,530064196b,;"Total Monthly Achieved\: %s";pref_title_expiry_reminder_notif,7,V4010044be,5a01004514,;"Expiry Reminder Notifications";progress_date_hint,7,V4001905d2,3300190601,;"Date";error_column_selection,7,V400a52b4d,5c00a52ba5,;"Please select columns for Value and Date.";save_progress_button_text,7,V4001a0607,43001a0646,;"Save Progress";search_edit_progress_title,7,V40111495e,4b011149a5,;"Search/Edit Progress";select_users_button,7,V401184af6,3c01184b2e,;"Select Users";select_owner_image_button_text,7,V400350d2a,4d00350d73,;"Select Owner Image";error_at_least_one_user,7,V400451107,5400451157,;"Please select at least one user.";error_saving_user,7,V400e53c0a,4000e53c46,;"Error saving user.";default_web_client_id,229,V400020037,8f000200c2,;"123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com";user_management_title,7,V400df3a7d,3e00df3ab7,;"Manage Users";unit_number,7,V4000b028f,2e000b02b9,;"Number";menu_manage_users,7,V400dd3a3f,3a00dd3a75,;"Manage Users";preview_data_button,7,V400a42b0f,3c00a42b47,;"Preview Data";select_date_prompt,7,V401896735,3301896764,;"TODO";error_generating_report,7,V400c43434,4c00c4347c,;"Error generating report.";dialog_kpi_action_reset_color,7,V40148570e,450148574f,;"Reset Color";import_ocr_data_button,7,V400882252,3e0088228c,;"Import Data";progress_updated_success,7,V4016d605e,39016d6093,;"TODO";select_image_for_ocr,7,V4008621ca,450086220b,;"Select Image for OCR";user_kpi_list_title_prefix,7,V400721c60,3f00721c9b,;"KPIs for";status_due_today,7,V401375260,3e0137529a,;"Status\: Due Today";no_users_exist,7,V400ef3f10,5400ef3f60,;"No users exist. Add users to assign KPIs.";expiry_return_notification_message,7,V4018e685d,43018e689c,;"TODO";ocr_activity_title,7,V4011249ab,3b011249e2,;"OCR Activity";delete_entry_confirmation_message,7,V401706114,4201706152,;"TODO";action_view_details,7,V400390e3e,3c00390e76,;"View Details";error_kpi_not_found,7,V4001404a2,4a001404e8,;"KPI not found for editing.";delete_progress_entry_menu_item,7,V400290a1a,4800290a5e,;"Delete Entry";dialog_kpi_action_share_card,7,V401465680,43014656bf,;"Share Card";ocr_import_cancelled,7,V40186668e,35018666bf,;"TODO";owner_image_description,7,V401194b34,3f01194b6f,;"Owner Image";error_reading_sheet_names,7,V400a62bab,6000a62c07,;"Error reading sheet names from Excel file.";title_activity_kpi_list,7,V4018b67a4,38018b67d8,;"TODO";google_storage_bucket,229,V40007025e,67000702c1,;"kpi-tracker-app-demo.appspot.com";kpi_name_not_editable_editing,7,V40192697b,60019269d7,;"KPI name cannot be changed during edit";add_task_title,7,V4012c4fa8,37012c4fdb,;"Add New Task";master_card_no_data,7,V401665ebd,5101665f0a,;"No data available for master card";manual_user_input_hint,7,V400411002,4200411040,;"Enter User Name";google_app_id,229,V400050180,66000501e2,;"1\:123456789012\:android\:abcdef1234567890";delete_kpi_confirmation_message,7,V4017762dd,4001776319,;"TODO";remaining_days_label,7,V400d13765,3b00d1379c,;"Days Left\:";reminder_option_on_due_date,7,V401966a48,4301966a87,;"On due date";clear_progress_success,7,V4017d6445,37017d6478,;"TODO";toggle_master_card,7,V401214d17,3f01214d52,;"View master card";reminder_option_1_day,7,V401976a8d,3e01976ac7,;"1 day before";search_kpis_title,7,V400310c45,3900310c7a,;"Search KPIs";confirm_delete_progress_entry_message,7,V4002b0aba,76002b0b2c,;"Are you sure you want to delete this progress entry?";dialog_kpi_action_edit,7,V4014455f6,3b0144562d,;"Edit KPI";color_picker_title_summary_card_bottom,7,V40057157e,5c005715d6,;"Summary Card Bottom Color";kpi_expiry_reminder_title,7,V400f7413e,4800f74182,;"KPI Reminder\: %1$s";confirm_import_button,7,V401254e03,4001254e3f,;"Confirm Import";no_kpis_assigned_for_master_card,7,V400671a13,5b00671a6a,;"No KPIs assigned to users yet.";aggregation_type_label,7,V400ac2d82,4400ac2dc2,;"Aggregation Type\:";menu_import_excel,7,V400d9392e,3f00d93969,;"Import from Excel";filter_all_months,7,V4006e1ba2,38006e1bd6,;"All Months";dialog_ok,7,V4002e0bb1,28002e0bd5,;"OK";ocr_no_entries_found,7,V4018866fe,350188672f,;"TODO";kpi_card_context_menu_title,7,V401064677,48010646bb,;"KPI Card Actions";error_updating_colors,7,V4010d4881,48010d48c5,;"Error updating colors.";confirm_clear_progress_message,7,V4007f1f8f,8a007f2015,;"Are you sure you want to clear all progress entries for this KPI for this user?";task_reminder_days_hint,7,V4012f505e,4a012f50a4,;"Reminder (days before)";error_name_required,7,V400100394,40001003d0,;"Name is required";show_master_card,7,V401224d58,3d01224d91,;"Show master card";my_tasks_title,7,V401325132,3301325161,;"My Tasks";days_since_last_update_label,7,V400d337e9,4b00d33830,;"Days Since Update\:";total_annual_target_label,7,V400651971,4d006519ba,;"Total Annual Target\: %s";action_view_report,7,V4015d5c90,4d015d5cd9,;"Interactive Performance Report";excel_data_import_failed,7,V4009e28c5,51009e2912,;"Failed to import Excel data.";error_copying_image,7,V400360d79,4300360db8,;"Error copying image";error_updating_progress,7,V4018a676a,38018a679e,;"TODO";map_date_column_label,7,V400a22a79,3e00a22ab3,;"Date Column\:";ocr_waiting_for_review,7,V4018766c5,37018766f8,;"TODO";kpi_name_hint,7,V4000400b3,32000400e1,;"KPI Name";no_user_summaries_to_display,7,V4007a1e44,56007a1e96,;"No user summaries to display.";progress_saved_success,7,V4016e6099,37016e60cc,;"TODO";clear_progress_confirmation_title,7,V4017a638a,42017a63c8,;"TODO";ocr_user_label,7,V4008b22f8,3b008b232f,;"User (Optional)\:";select_kpi_for_ocr_hint,7,V4009124c3,3e009124fd,;"Select KPI";error_saving_kpi,7,V400130463,3d0013049c,;"Error saving KPI";target_achieved_notification_text,7,V4005e1786,6b005e17ed,;"You achieved %1$.1f for %2$s (Target\: %3$.1f)";dialog_kpi_action_duplicate_copy,7,V401455633,4b0145567a,;"Duplicate/Copy";kpi_card_item_average_label,7,V401555abb,3f01555af6,;"Average";user_already_selected_error,7,V4004a1294,56004a12e6,;"This user is already selected.";select_sheet_label,7,V400a72c0d,3c00a72c45,;"Select Sheet\:";expiry_return_notification_title,7,V4018d681a,41018d6857,;"TODO";+style:MasterCardButton,230,V4001002ed,c001c056e,;DWidget.MaterialComponents.Button,android\:textSize:12sp,android\:paddingStart:12dp,android\:paddingEnd:12dp,android\:paddingTop:8dp,android\:paddingBottom:8dp,android\:minHeight:40dp,cornerRadius:20dp,icon:@drawable/ic_master_card_24,iconGravity:textStart,iconPadding:8dp,iconSize:18dp,;CustomFilterChip,230,V40003006d,c000d02b2,;DWidget.MaterialComponents.Chip.Filter,android\:textSize:12sp,android\:textColor:@color/chip_text_color,chipIconSize:18dp,chipIconVisible:true,chipMinHeight:40dp,chipMinTouchTargetSize:40dp,chipStartPadding:8dp,chipEndPadding:8dp,chipBackgroundColor:@color/chip_background_color,;Widget.App.Chip.Advanced,230,V4001f05a6,c00290807,;DWidget.MaterialComponents.Chip.Choice,android\:textSize:14sp,android\:textColor:@color/chip_text_selector,chipMinHeight:40dp,chipStartPadding:12dp,chipEndPadding:12dp,chipCornerRadius:20dp,chipBackgroundColor:@color/chip_background_selector,chipStrokeWidth:1dp,chipStrokeColor:@color/purple_accent,;Theme.KPITrackerApp,231,V400020066,c0011038e,;DTheme.MaterialComponents.DayNight.DarkActionBar,colorPrimary:@color/purple_500,colorPrimaryVariant:@color/purple_700,colorOnPrimary:@color/white,colorSecondary:@color/teal_200,colorSecondaryVariant:@color/teal_700,colorOnSecondary:@color/black,chipStyle:@style/Widget.App.Chip,android\:statusBarColor:?attr/colorPrimaryVariant,;Widget.App.Chip,231,V40026073c,c002f0990,;DWidget.MaterialComponents.Chip.Filter,android\:textSize:14sp,android\:textColor:@color/chip_text_selector,chipBackgroundColor:@color/chip_background_selector,chipStrokeColor:@color/purple_500,chipStrokeWidth:1dp,chipCornerRadius:16dp,chipIconTint:@color/chip_text_selector,closeIconTint:@color/chip_text_selector,;Theme.KPITrackerApp.NoActionBar,231,V4001403e1,c00230713,;DTheme.MaterialComponents.DayNight.NoActionBar,colorPrimary:@color/purple_500,colorPrimaryVariant:@color/purple_700,colorOnPrimary:@color/white,colorSecondary:@color/teal_200,colorSecondaryVariant:@color/teal_700,colorOnSecondary:@color/black,chipStyle:@style/Widget.App.Chip,android\:statusBarColor:?attr/colorPrimaryVariant,;+xml:file_paths,232,F;network_security_config,233,F;data_extraction_rules,234,F;backup_rules,235,F;