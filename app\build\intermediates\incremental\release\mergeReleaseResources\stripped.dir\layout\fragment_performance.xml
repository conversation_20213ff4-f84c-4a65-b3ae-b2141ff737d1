<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="📈 Performance &amp; Reports"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="24dp"
            android:gravity="center" />

        <LinearLayout
            android:id="@+id/cardInteractiveReport"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="20dp"
            android:background="@drawable/card_background"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:text="📊"
                android:textSize="32sp"
                android:gravity="center"
                android:layout_marginEnd="16dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Interactive Report"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="View detailed performance analytics"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/cardExpiryManagement"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="20dp"
            android:background="@drawable/card_background"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:text="📅"
                android:textSize="32sp"
                android:gravity="center"
                android:layout_marginEnd="16dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Expiry Management"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Manage expiration dates"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/cardTaskFollowUp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="20dp"
            android:background="@drawable/card_background"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:text="📋"
                android:textSize="32sp"
                android:gravity="center"
                android:layout_marginEnd="16dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Task Follow-up"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Track and manage tasks"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/cardDateConverter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="20dp"
            android:background="@drawable/card_background"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:text="📆"
                android:textSize="32sp"
                android:gravity="center"
                android:layout_marginEnd="16dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="محول التاريخ"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="تحويل التاريخ بين الميلادي والهجري"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/cardDrugIndex"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="20dp"
            android:background="@drawable/card_background"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:text="💊"
                android:textSize="32sp"
                android:gravity="center"
                android:layout_marginEnd="16dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="فهرس الأدوية"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="البحث عن معلومات الأدوية"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</ScrollView>
