<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.9.1" type="partial_results">
    <map id="AppBundleLocaleChanges">
        <location id="localeChangeLocation"
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/kpitrackerapp/utils/LanguageManager.kt"
            line="50"
            column="9"
            startOffset="1725"
            endLine="50"
            endColumn="33"
            endOffset="1749"/>
    </map>
    <map id="NotificationPermission">
        <location id="class"
            file="$GRADLE_USER_HOME/caches/8.11.1/transforms/7ade2ffb4f9a940dbc99d2e64ef30d9c/transformed/jetified-firebase-messaging-23.4.0/jars/classes.jar"/>
        <entry
            name="className"
            string="com/google/firebase/messaging/DisplayNotification"/>
        <entry
            name="source"
            boolean="true"/>
    </map>
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="android.intent.action.INSERT (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/kpitrackerapp/ui/TaskManagementActivity.kt"
                            line="991"
                            column="25"
                            startOffset="40532"
                            endLine="991"
                            endColumn="53"
                            endOffset="40560"/>
                    </map>
                    <map id="android.intent.action.VIEW (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/kpitrackerapp/ui/AddEditAdvancedTaskActivity.kt"
                            line="1217"
                            column="26"
                            startOffset="48587"
                            endLine="1217"
                            endColumn="83"
                            endOffset="48644"/>
                    </map>
            </map>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="incidents">
                    <map id="0">
                        <entry
                            name="incidentClass"
                            string="com.example.kpitrackerapp.utils.LanguageManager"/>
                        <location id="location"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/kpitrackerapp/utils/LanguageManager.kt"
                            line="62"
                            column="22"
                            startOffset="2080"
                            endLine="62"
                            endColumn="37"
                            endOffset="2095"/>
                        <location id="secondaryLocation"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/kpitrackerapp/utils/LanguageManager.kt"
                            line="64"
                            column="9"
                            startOffset="2130"
                            endLine="64"
                            endColumn="39"
                            endOffset="2160"
                            message="The unsafe intent is launched here."/>
                    </map>
            </map>
            <map id="unprotected">
                <entry
                    name="com.example.kpitrackerapp.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.anim.bounce_animation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/bounce_animation.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="14"
            endColumn="7"
            endOffset="475"/>
        <location id="R.anim.card_press_scale"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/card_press_scale.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="12"
            endColumn="7"
            endOffset="412"/>
        <location id="R.anim.card_release_scale"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/card_release_scale.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="12"
            endColumn="7"
            endOffset="412"/>
        <location id="R.anim.fade_scale_in"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/fade_scale_in.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="17"
            endColumn="7"
            endOffset="585"/>
        <location id="R.anim.shake_animation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/shake_animation.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="7"
            endOffset="352"/>
        <location id="R.anim.slide_in_bottom"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/slide_in_bottom.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="12"
            endColumn="7"
            endOffset="399"/>
        <location id="R.anim.star_pulse"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/star_pulse.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="14"
            endColumn="7"
            endOffset="507"/>
        <location id="R.color.blue"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="14"
            column="12"
            startOffset="585"
            endLine="14"
            endColumn="23"
            endOffset="596"/>
        <location id="R.color.blue_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="213"
            column="12"
            startOffset="11440"
            endLine="213"
            endColumn="27"
            endOffset="11455"/>
        <location id="R.color.chart_achieved_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="24"
            column="12"
            startOffset="1208"
            endLine="24"
            endColumn="39"
            endOffset="1235"/>
        <location id="R.color.chart_target_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="23"
            column="12"
            startOffset="1128"
            endLine="23"
            endColumn="37"
            endOffset="1153"/>
        <location id="R.color.chip_background_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/color/chip_background_color.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="5"
            endColumn="12"
            endOffset="233"/>
        <location id="R.color.chip_background_default"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="47"
            column="12"
            startOffset="2960"
            endLine="47"
            endColumn="42"
            endOffset="2990"/>
        <location id="R.color.chip_text_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/color/chip_text_color.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="5"
            endColumn="12"
            endOffset="233"/>
        <location id="R.color.dark_gray"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="11"
            column="12"
            startOffset="432"
            endLine="11"
            endColumn="28"
            endOffset="448"/>
        <location id="R.color.default_kpi_card_bottom_gradient"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="27"
            column="12"
            startOffset="1469"
            endLine="27"
            endColumn="51"
            endOffset="1508"/>
        <location id="R.color.default_kpi_card_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="25"
            column="12"
            startOffset="1291"
            endLine="25"
            endColumn="41"
            endOffset="1320"/>
        <location id="R.color.default_kpi_card_top_gradient"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="26"
            column="12"
            startOffset="1374"
            endLine="26"
            endColumn="48"
            endOffset="1410"/>
        <location id="R.color.energy_high"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="198"
            column="12"
            startOffset="10827"
            endLine="198"
            endColumn="30"
            endOffset="10845"/>
        <location id="R.color.energy_low"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="196"
            column="12"
            startOffset="10728"
            endLine="196"
            endColumn="29"
            endOffset="10745"/>
        <location id="R.color.energy_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="197"
            column="12"
            startOffset="10776"
            endLine="197"
            endColumn="32"
            endOffset="10796"/>
        <location id="R.color.fab_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="46"
            column="12"
            startOffset="2872"
            endLine="46"
            endColumn="28"
            endOffset="2888"/>
        <location id="R.color.green"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="13"
            column="12"
            startOffset="518"
            endLine="13"
            endColumn="24"
            endOffset="530"/>
        <location id="R.color.green_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="214"
            column="12"
            startOffset="11486"
            endLine="214"
            endColumn="28"
            endOffset="11502"/>
        <location id="R.color.header_gradient_end"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="32"
            column="12"
            startOffset="1815"
            endLine="32"
            endColumn="38"
            endOffset="1841"/>
        <location id="R.color.header_gradient_start"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="31"
            column="12"
            startOffset="1727"
            endLine="31"
            endColumn="40"
            endOffset="1755"/>
        <location id="R.color.icon_tint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="192"
            column="12"
            startOffset="10590"
            endLine="192"
            endColumn="28"
            endOffset="10606"/>
        <location id="R.color.importance_critical"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="157"
            column="12"
            startOffset="9110"
            endLine="157"
            endColumn="38"
            endOffset="9136"/>
        <location id="R.color.importance_high"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="156"
            column="12"
            startOffset="9057"
            endLine="156"
            endColumn="34"
            endOffset="9079"/>
        <location id="R.color.importance_low"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="154"
            column="12"
            startOffset="8950"
            endLine="154"
            endColumn="33"
            endOffset="8971"/>
        <location id="R.color.importance_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="155"
            column="12"
            startOffset="9002"
            endLine="155"
            endColumn="36"
            endOffset="9026"/>
        <location id="R.color.light_blue_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="18"
            column="12"
            startOffset="791"
            endLine="18"
            endColumn="33"
            endOffset="812"/>
        <location id="R.color.light_blue_50"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="17"
            column="12"
            startOffset="740"
            endLine="17"
            endColumn="32"
            endOffset="760"/>
        <location id="R.color.light_blue_600"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="19"
            column="12"
            startOffset="843"
            endLine="19"
            endColumn="33"
            endOffset="864"/>
        <location id="R.color.light_blue_900"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="20"
            column="12"
            startOffset="895"
            endLine="20"
            endColumn="33"
            endOffset="916"/>
        <location id="R.color.light_gray"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="10"
            column="12"
            startOffset="386"
            endLine="10"
            endColumn="29"
            endOffset="403"/>
        <location id="R.color.low_priority_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="210"
            column="12"
            startOffset="11341"
            endLine="210"
            endColumn="37"
            endOffset="11366"/>
        <location id="R.color.medium_priority_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="209"
            column="12"
            startOffset="11282"
            endLine="209"
            endColumn="40"
            endOffset="11310"/>
        <location id="R.color.orange"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="16"
            column="12"
            startOffset="671"
            endLine="16"
            endColumn="25"
            endOffset="684"/>
        <location id="R.color.orange_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="216"
            column="12"
            startOffset="11578"
            endLine="216"
            endColumn="29"
            endOffset="11595"/>
        <location id="R.color.overdue_chip_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="178"
            column="12"
            startOffset="9967"
            endLine="178"
            endColumn="42"
            endOffset="9997"/>
        <location id="R.color.overdue_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="163"
            column="12"
            startOffset="9352"
            endLine="163"
            endColumn="32"
            endOffset="9372"/>
        <location id="R.color.progress_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="174"
            column="12"
            startOffset="9828"
            endLine="174"
            endColumn="32"
            endOffset="9848"/>
        <location id="R.color.progress_tint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="172"
            column="12"
            startOffset="9720"
            endLine="172"
            endColumn="32"
            endOffset="9740"/>
        <location id="R.color.purple_button_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="40"
            column="12"
            startOffset="2443"
            endLine="40"
            endColumn="37"
            endOffset="2468"/>
        <location id="R.color.read_only_overlay"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="103"
            column="12"
            startOffset="6692"
            endLine="103"
            endColumn="36"
            endOffset="6716"/>
        <location id="R.color.red"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="12"
            column="12"
            startOffset="477"
            endLine="12"
            endColumn="22"
            endOffset="487"/>
        <location id="R.color.red_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="215"
            column="12"
            startOffset="11533"
            endLine="215"
            endColumn="26"
            endOffset="11547"/>
        <location id="R.color.urgent_chip_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="177"
            column="12"
            startOffset="9907"
            endLine="177"
            endColumn="41"
            endOffset="9936"/>
        <location id="R.color.yellow"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="15"
            column="12"
            startOffset="627"
            endLine="15"
            endColumn="25"
            endOffset="640"/>
        <location id="R.dimen.card_elevation_pressed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="12"
            column="12"
            startOffset="425"
            endLine="12"
            endColumn="41"
            endOffset="454"/>
        <location id="R.dimen.color_swatch_size"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="6"
            column="12"
            startOffset="147"
            endLine="6"
            endColumn="36"
            endOffset="171"/>
        <location id="R.dimen.color_swatch_stroke_width"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="7"
            column="12"
            startOffset="197"
            endLine="7"
            endColumn="44"
            endOffset="229"/>
        <location id="R.dimen.fab_margin"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="3"
            column="12"
            startOffset="64"
            endLine="3"
            endColumn="29"
            endOffset="81"/>
        <location id="R.drawable.chart_fill_gradient"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/chart_fill_gradient.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="8"
            endColumn="9"
            endOffset="271"/>
        <location id="R.drawable.color_swatch_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/color_swatch_background.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="30"
            endColumn="12"
            endOffset="1475"/>
        <location id="R.drawable.custom_progress_bar"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/custom_progress_bar.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="22"
            endColumn="14"
            endOffset="725"/>
        <location id="R.drawable.gradient_admin_welcome"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/gradient_admin_welcome.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="9"
            endColumn="9"
            endOffset="319"/>
        <location id="R.drawable.header_gradient"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/header_gradient.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="9"
            endColumn="9"
            endOffset="338"/>
        <location id="R.drawable.ic_baseline_arrow_downward_16"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_arrow_downward_16.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="419"/>
        <location id="R.drawable.ic_baseline_arrow_upward_16"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_arrow_upward_16.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="409"/>
        <location id="R.drawable.ic_baseline_attachment_24"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_attachment_24.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="556"/>
        <location id="R.drawable.ic_baseline_edit_24"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_edit_24.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="493"/>
        <location id="R.drawable.ic_baseline_format_color_reset_24"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_format_color_reset_24.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="511"/>
        <location id="R.drawable.ic_baseline_horizontal_rule_16"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_horizontal_rule_16.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="388"/>
        <location id="R.drawable.ic_baseline_logout_24"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_logout_24.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="5"
            endColumn="10"
            endOffset="423"/>
        <location id="R.drawable.ic_baseline_switch_account_24"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_switch_account_24.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="8"
            endColumn="10"
            endOffset="825"/>
        <location id="R.drawable.ic_filter_all_24"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_filter_all_24.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="428"/>
        <location id="R.drawable.ic_filter_expiry_24"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_filter_expiry_24.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="588"/>
        <location id="R.drawable.ic_filter_report_24"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_filter_report_24.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="454"/>
        <location id="R.drawable.ic_filter_task_24"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_filter_task_24.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="585"/>
        <location id="R.drawable.ic_sort_24"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sort_24.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="11"
            endColumn="10"
            endOffset="419"/>
        <location id="R.drawable.ic_sort_alpha"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sort_alpha.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="11"
            endColumn="10"
            endOffset="616"/>
        <location id="R.drawable.ic_sort_ascending"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sort_ascending.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="11"
            endColumn="10"
            endOffset="424"/>
        <location id="R.drawable.ic_sort_descending"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sort_descending.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="11"
            endColumn="10"
            endOffset="428"/>
        <location id="R.drawable.overall_summary_gradient"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/overall_summary_gradient.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="8"
            endColumn="9"
            endOffset="308"/>
        <location id="R.drawable.priority_indicator_gradient"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/priority_indicator_gradient.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="9"
            endColumn="9"
            endOffset="324"/>
        <location id="R.drawable.text_view_border"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/text_view_border.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="9"
            endColumn="9"
            endOffset="486"/>
        <location id="R.layout.activity_add_edit_task_enhanced"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_edit_task_enhanced.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="462"
            endColumn="55"
            endOffset="23692"/>
        <location id="R.layout.dialog_progress_update"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_progress_update.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="56"
            endColumn="16"
            endOffset="1775"/>
        <location id="R.layout.item_subtask_mini"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_subtask_mini.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="30"
            endColumn="16"
            endOffset="1003"/>
        <location id="R.layout.kpi_detail_item"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/kpi_detail_item.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="77"
            endColumn="16"
            endOffset="2996"/>
        <location id="R.layout.kpi_list_item"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/kpi_list_item.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="110"
            endColumn="53"
            endOffset="4961"/>
        <location id="R.layout.report_table_row"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/report_table_row.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="237"
            endColumn="16"
            endOffset="8952"/>
        <location id="R.layout.unified_report_table"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/unified_report_table.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="84"
            endColumn="53"
            endOffset="3311"/>
        <location id="R.layout.unified_report_table_row"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/unified_report_table_row.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="50"
            endColumn="12"
            endOffset="1679"/>
        <location id="R.menu.main_menu"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/main_menu.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="47"
            endColumn="8"
            endOffset="1562"/>
        <location id="R.menu.sort_options_menu"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/sort_options_menu.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="15"
            endColumn="8"
            endOffset="544"/>
        <location id="R.string.achieved_value_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="206"
            column="13"
            startOffset="13934"
            endLine="206"
            endColumn="40"
            endOffset="13961"/>
        <location id="R.string.action_add_progress"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="57"
            column="13"
            startOffset="3592"
            endLine="57"
            endColumn="39"
            endOffset="3618"/>
        <location id="R.string.action_customize_colors"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="264"
            column="13"
            startOffset="18121"
            endLine="264"
            endColumn="43"
            endOffset="18151"/>
        <location id="R.string.action_delete_kpi"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="60"
            column="13"
            startOffset="3770"
            endLine="60"
            endColumn="37"
            endOffset="3794"/>
        <location id="R.string.action_delete_user"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="237"
            column="13"
            startOffset="15944"
            endLine="237"
            endColumn="38"
            endOffset="15969"/>
        <location id="R.string.action_edit_kpi"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="59"
            column="13"
            startOffset="3716"
            endLine="59"
            endColumn="35"
            endOffset="3738"/>
        <location id="R.string.action_edit_user"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="236"
            column="13"
            startOffset="15888"
            endLine="236"
            endColumn="36"
            endOffset="15911"/>
        <location id="R.string.action_view_details"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="58"
            column="13"
            startOffset="3654"
            endLine="58"
            endColumn="39"
            endOffset="3680"/>
        <location id="R.string.action_view_report"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="350"
            column="13"
            startOffset="23704"
            endLine="350"
            endColumn="38"
            endOffset="23729"/>
        <location id="R.string.add_new_user_button_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="72"
            column="13"
            startOffset="4551"
            endLine="72"
            endColumn="45"
            endOffset="4583"/>
        <location id="R.string.add_progress_button_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="35"
            column="13"
            startOffset="2068"
            endLine="35"
            endColumn="44"
            endOffset="2099"/>
        <location id="R.string.add_progress_entry_button"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="285"
            column="13"
            startOffset="19447"
            endLine="285"
            endColumn="45"
            endOffset="19479"/>
        <location id="R.string.add_progress_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="23"
            column="13"
            startOffset="1319"
            endLine="23"
            endColumn="38"
            endOffset="1344"/>
        <location id="R.string.add_task_button_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="306"
            column="13"
            startOffset="20740"
            endLine="306"
            endColumn="40"
            endOffset="20767"/>
        <location id="R.string.add_user_button_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="225"
            column="13"
            startOffset="15045"
            endLine="225"
            endColumn="40"
            endOffset="15072"/>
        <location id="R.string.aggregation_average"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="175"
            column="13"
            startOffset="11800"
            endLine="175"
            endColumn="39"
            endOffset="11826"/>
        <location id="R.string.aggregation_individual"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="174"
            column="13"
            startOffset="11728"
            endLine="174"
            endColumn="42"
            endOffset="11757"/>
        <location id="R.string.aggregation_type_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="173"
            column="13"
            startOffset="11658"
            endLine="173"
            endColumn="42"
            endOffset="11687"/>
        <location id="R.string.all_kpis_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="121"
            column="13"
            startOffset="7622"
            endLine="121"
            endColumn="34"
            endOffset="7643"/>
        <location id="R.string.all_users_excel_filter"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="172"
            column="13"
            startOffset="11584"
            endLine="172"
            endColumn="42"
            endOffset="11613"/>
        <location id="R.string.all_users_report_option"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="199"
            column="13"
            startOffset="13529"
            endLine="199"
            endColumn="43"
            endOffset="13559"/>
        <location id="R.string.annual_progress_label_short"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="99"
            column="13"
            startOffset="6285"
            endLine="99"
            endColumn="47"
            endOffset="6319"/>
        <location id="R.string.assign_to_kpi_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="145"
            column="13"
            startOffset="9355"
            endLine="145"
            endColumn="39"
            endOffset="9381"/>
        <location id="R.string.color_picker_title_individual_kpi_card"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="89"
            column="13"
            startOffset="5604"
            endLine="89"
            endColumn="58"
            endOffset="5649"/>
        <location id="R.string.color_picker_title_summary_card_bottom"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="88"
            column="13"
            startOffset="5510"
            endLine="88"
            endColumn="58"
            endOffset="5555"/>
        <location id="R.string.color_picker_title_summary_card_top"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="87"
            column="13"
            startOffset="5422"
            endLine="87"
            endColumn="55"
            endOffset="5464"/>
        <location id="R.string.colors_updated_successfully"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="269"
            column="13"
            startOffset="18491"
            endLine="269"
            endColumn="47"
            endOffset="18525"/>
        <location id="R.string.column_mapping_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="161"
            column="13"
            startOffset="10747"
            endLine="161"
            endColumn="40"
            endOffset="10774"/>
        <location id="R.string.confirm_clear_month_progress_message"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="130"
            column="13"
            startOffset="8308"
            endLine="130"
            endColumn="56"
            endOffset="8351"/>
        <location id="R.string.confirm_clear_progress_message"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="128"
            column="13"
            startOffset="8087"
            endLine="128"
            endColumn="50"
            endOffset="8124"/>
        <location id="R.string.confirm_clear_progress_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="127"
            column="13"
            startOffset="8006"
            endLine="127"
            endColumn="48"
            endOffset="8041"/>
        <location id="R.string.confirm_delete_kpi_message"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="41"
            column="13"
            startOffset="2475"
            endLine="41"
            endColumn="46"
            endOffset="2508"/>
        <location id="R.string.confirm_delete_kpi_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="40"
            column="13"
            startOffset="2406"
            endLine="40"
            endColumn="44"
            endOffset="2437"/>
        <location id="R.string.confirm_delete_ocr_item_message"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="151"
            column="13"
            startOffset="9897"
            endLine="151"
            endColumn="51"
            endOffset="9935"/>
        <location id="R.string.confirm_delete_progress_entry_message"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="44"
            column="13"
            startOffset="2754"
            endLine="44"
            endColumn="57"
            endOffset="2798"/>
        <location id="R.string.confirm_delete_progress_entry_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="43"
            column="13"
            startOffset="2668"
            endLine="43"
            endColumn="55"
            endOffset="2710"/>
        <location id="R.string.confirm_delete_user_message"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="232"
            column="13"
            startOffset="15519"
            endLine="232"
            endColumn="47"
            endOffset="15553"/>
        <location id="R.string.confirm_delete_user_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="231"
            column="13"
            startOffset="15444"
            endLine="231"
            endColumn="45"
            endOffset="15476"/>
        <location id="R.string.create_new_user_dialog_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="74"
            column="13"
            startOffset="4693"
            endLine="74"
            endColumn="48"
            endOffset="4728"/>
        <location id="R.string.current_progress_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="33"
            column="13"
            startOffset="1948"
            endLine="33"
            endColumn="42"
            endOffset="1977"/>
        <location id="R.string.days_since_last_update_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="212"
            column="13"
            startOffset="14321"
            endLine="212"
            endColumn="48"
            endOffset="14356"/>
        <location id="R.string.delete_kpi_menu_item"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="39"
            column="13"
            startOffset="2345"
            endLine="39"
            endColumn="40"
            endOffset="2372"/>
        <location id="R.string.delete_ocr_item_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="150"
            column="13"
            startOffset="9830"
            endLine="150"
            endColumn="41"
            endOffset="9858"/>
        <location id="R.string.delete_progress_entry_menu_item"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="42"
            column="13"
            startOffset="2594"
            endLine="42"
            endColumn="51"
            endOffset="2632"/>
        <location id="R.string.dialog_ok"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="47"
            column="13"
            startOffset="3001"
            endLine="47"
            endColumn="29"
            endOffset="3017"/>
        <location id="R.string.dialog_select_card_colors_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="265"
            column="13"
            startOffset="18191"
            endLine="265"
            endColumn="51"
            endOffset="18229"/>
        <location id="R.string.duplicate_kpi_menu_item"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="242"
            column="13"
            startOffset="16240"
            endLine="242"
            endColumn="43"
            endOffset="16270"/>
        <location id="R.string.edit_kpi_menu_item"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="45"
            column="13"
            startOffset="2874"
            endLine="45"
            endColumn="38"
            endOffset="2899"/>
        <location id="R.string.edit_ocr_item_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="149"
            column="13"
            startOffset="9767"
            endLine="149"
            endColumn="39"
            endOffset="9793"/>
        <location id="R.string.edit_progress_entry_menu_item"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="46"
            column="13"
            startOffset="2931"
            endLine="46"
            endColumn="49"
            endOffset="2967"/>
        <location id="R.string.edit_progress_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="24"
            column="13"
            startOffset="1380"
            endLine="24"
            endColumn="39"
            endOffset="1406"/>
        <location id="R.string.edit_user_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="235"
            column="13"
            startOffset="15833"
            endLine="235"
            endColumn="35"
            endOffset="15855"/>
        <location id="R.string.enter_new_user_name_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="73"
            column="13"
            startOffset="4619"
            endLine="73"
            endColumn="44"
            endOffset="4650"/>
        <location id="R.string.error_at_least_one_user"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="70"
            column="13"
            startOffset="4367"
            endLine="70"
            endColumn="43"
            endOffset="4397"/>
        <location id="R.string.error_average_for_all_users"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="176"
            column="13"
            startOffset="11887"
            endLine="176"
            endColumn="47"
            endOffset="11921"/>
        <location id="R.string.error_column_selection"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="166"
            column="13"
            startOffset="11093"
            endLine="166"
            endColumn="42"
            endOffset="11122"/>
        <location id="R.string.error_copying_image"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="55"
            column="13"
            startOffset="3457"
            endLine="55"
            endColumn="39"
            endOffset="3483"/>
        <location id="R.string.error_excel_processing"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="157"
            column="13"
            startOffset="10273"
            endLine="157"
            endColumn="42"
            endOffset="10302"/>
        <location id="R.string.error_generating_report"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="197"
            column="13"
            startOffset="13372"
            endLine="197"
            endColumn="43"
            endOffset="13402"/>
        <location id="R.string.error_invalid_target"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="19"
            column="13"
            startOffset="1060"
            endLine="19"
            endColumn="40"
            endOffset="1087"/>
        <location id="R.string.error_kpi_not_found"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="21"
            column="13"
            startOffset="1194"
            endLine="21"
            endColumn="39"
            endOffset="1220"/>
        <location id="R.string.error_name_required"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="17"
            column="13"
            startOffset="924"
            endLine="17"
            endColumn="39"
            endOffset="950"/>
        <location id="R.string.error_no_sheets_found"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="169"
            column="13"
            startOffset="11347"
            endLine="169"
            endColumn="41"
            endOffset="11375"/>
        <location id="R.string.error_ocr_processing"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="141"
            column="13"
            startOffset="9021"
            endLine="141"
            endColumn="40"
            endOffset="9048"/>
        <location id="R.string.error_reading_sheet_names"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="167"
            column="13"
            startOffset="11187"
            endLine="167"
            endColumn="45"
            endOffset="11219"/>
        <location id="R.string.error_saving_kpi"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="20"
            column="13"
            startOffset="1131"
            endLine="20"
            endColumn="36"
            endOffset="1154"/>
        <location id="R.string.error_saving_progress"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="31"
            column="13"
            startOffset="1817"
            endLine="31"
            endColumn="41"
            endOffset="1845"/>
        <location id="R.string.error_saving_user"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="230"
            column="13"
            startOffset="15378"
            endLine="230"
            endColumn="37"
            endOffset="15402"/>
        <location id="R.string.error_select_kpi_for_ocr"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="147"
            column="13"
            startOffset="9483"
            endLine="147"
            endColumn="44"
            endOffset="9514"/>
        <location id="R.string.error_target_required"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="18"
            column="13"
            startOffset="990"
            endLine="18"
            endColumn="41"
            endOffset="1018"/>
        <location id="R.string.error_updating_colors"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="270"
            column="13"
            startOffset="18569"
            endLine="270"
            endColumn="41"
            endOffset="18597"/>
        <location id="R.string.error_user_name_required"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="229"
            column="13"
            startOffset="15301"
            endLine="229"
            endColumn="44"
            endOffset="15332"/>
        <location id="R.string.error_value_required"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="28"
            column="13"
            startOffset="1620"
            endLine="28"
            endColumn="40"
            endOffset="1647"/>
        <location id="R.string.excel_data_import_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="159"
            column="13"
            startOffset="10445"
            endLine="159"
            endColumn="44"
            endOffset="10476"/>
        <location id="R.string.excel_data_imported_success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="158"
            column="13"
            startOffset="10354"
            endLine="158"
            endColumn="47"
            endOffset="10388"/>
        <location id="R.string.excel_import_instructions"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="160"
            column="13"
            startOffset="10528"
            endLine="160"
            endColumn="45"
            endOffset="10560"/>
        <location id="R.string.excel_import_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="153"
            column="13"
            startOffset="10007"
            endLine="153"
            endColumn="38"
            endOffset="10032"/>
        <location id="R.string.excel_review_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="155"
            column="13"
            startOffset="10141"
            endLine="155"
            endColumn="38"
            endOffset="10166"/>
        <location id="R.string.existing_user_added_to_selection_message"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="76"
            column="13"
            startOffset="4852"
            endLine="76"
            endColumn="60"
            endOffset="4899"/>
        <location id="R.string.expiry_notification_channel_description"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="247"
            column="13"
            startOffset="16550"
            endLine="247"
            endColumn="59"
            endOffset="16596"/>
        <location id="R.string.expiry_notification_channel_name"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="246"
            column="13"
            startOffset="16467"
            endLine="246"
            endColumn="52"
            endOffset="16506"/>
        <location id="R.string.filter_all"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="286"
            column="13"
            startOffset="19521"
            endLine="286"
            endColumn="30"
            endOffset="19538"/>
        <location id="R.string.filter_by_month_button_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="112"
            column="13"
            startOffset="7140"
            endLine="112"
            endColumn="47"
            endOffset="7174"/>
        <location id="R.string.filter_by_owner_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="51"
            column="13"
            startOffset="3208"
            endLine="51"
            endColumn="40"
            endOffset="3235"/>
        <location id="R.string.filter_by_user_excel_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="171"
            column="13"
            startOffset="11499"
            endLine="171"
            endColumn="46"
            endOffset="11532"/>
        <location id="R.string.filter_expiry"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="287"
            column="13"
            startOffset="19565"
            endLine="287"
            endColumn="33"
            endOffset="19585"/>
        <location id="R.string.filter_report"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="288"
            column="13"
            startOffset="19615"
            endLine="288"
            endColumn="33"
            endOffset="19635"/>
        <location id="R.string.filter_task_follow_up"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="289"
            column="13"
            startOffset="19677"
            endLine="289"
            endColumn="41"
            endOffset="19705"/>
        <location id="R.string.generate_report_button"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="187"
            column="13"
            startOffset="12727"
            endLine="187"
            endColumn="42"
            endOffset="12756"/>
        <location id="R.string.header_row_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="170"
            column="13"
            startOffset="11433"
            endLine="170"
            endColumn="36"
            endOffset="11456"/>
        <location id="R.string.import_excel_data_button"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="156"
            column="13"
            startOffset="10207"
            endLine="156"
            endColumn="44"
            endOffset="10238"/>
        <location id="R.string.import_failed_with_errors"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="178"
            column="13"
            startOffset="12121"
            endLine="178"
            endColumn="45"
            endOffset="12153"/>
        <location id="R.string.import_ocr_data_button"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="137"
            column="13"
            startOffset="8794"
            endLine="137"
            endColumn="42"
            endOffset="8823"/>
        <location id="R.string.import_partially_successful"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="179"
            column="13"
            startOffset="12213"
            endLine="179"
            endColumn="47"
            endOffset="12247"/>
        <location id="R.string.import_successful_count"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="177"
            column="13"
            startOffset="12033"
            endLine="177"
            endColumn="43"
            endOffset="12063"/>
        <location id="R.string.kpi_actions_dialog_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="56"
            column="13"
            startOffset="3526"
            endLine="56"
            endColumn="44"
            endOffset="3557"/>
        <location id="R.string.kpi_annual_target_label_formatted"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="372"
            column="13"
            startOffset="25049"
            endLine="372"
            endColumn="53"
            endOffset="25089"/>
        <location id="R.string.kpi_card_context_menu_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="263"
            column="13"
            startOffset="18047"
            endLine="263"
            endColumn="47"
            endOffset="18081"/>
        <location id="R.string.kpi_card_item_average_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="342"
            column="13"
            startOffset="23235"
            endLine="342"
            endColumn="47"
            endOffset="23269"/>
        <location id="R.string.kpi_daily_target_label_formatted"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="374"
            column="13"
            startOffset="25186"
            endLine="374"
            endColumn="52"
            endOffset="25225"/>
        <location id="R.string.kpi_detail_tab_all_time"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="205"
            column="13"
            startOffset="13872"
            endLine="205"
            endColumn="43"
            endOffset="13902"/>
        <location id="R.string.kpi_detail_tab_current"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="201"
            column="13"
            startOffset="13630"
            endLine="201"
            endColumn="42"
            endOffset="13659"/>
        <location id="R.string.kpi_detail_tab_monthly"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="202"
            column="13"
            startOffset="13690"
            endLine="202"
            endColumn="42"
            endOffset="13719"/>
        <location id="R.string.kpi_detail_tab_quarterly"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="203"
            column="13"
            startOffset="13750"
            endLine="203"
            endColumn="44"
            endOffset="13781"/>
        <location id="R.string.kpi_detail_tab_yearly"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="204"
            column="13"
            startOffset="13814"
            endLine="204"
            endColumn="41"
            endOffset="13842"/>
        <location id="R.string.kpi_duplication_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="244"
            column="13"
            startOffset="16388"
            endLine="244"
            endColumn="42"
            endOffset="16417"/>
        <location id="R.string.kpi_expiry_reminder_text_annual"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="251"
            column="13"
            startOffset="17034"
            endLine="251"
            endColumn="51"
            endOffset="17072"/>
        <location id="R.string.kpi_expiry_reminder_text_monthly"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="249"
            column="13"
            startOffset="16784"
            endLine="249"
            endColumn="52"
            endOffset="16823"/>
        <location id="R.string.kpi_expiry_reminder_text_quarterly"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="250"
            column="13"
            startOffset="16907"
            endLine="250"
            endColumn="54"
            endOffset="16948"/>
        <location id="R.string.kpi_expiry_reminder_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="248"
            column="13"
            startOffset="16710"
            endLine="248"
            endColumn="45"
            endOffset="16742"/>
        <location id="R.string.kpi_list_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="22"
            column="13"
            startOffset="1270"
            endLine="22"
            endColumn="34"
            endOffset="1291"/>
        <location id="R.string.kpi_monthly_target_label_formatted"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="373"
            column="13"
            startOffset="25117"
            endLine="373"
            endColumn="54"
            endOffset="25158"/>
        <location id="R.string.kpi_name_not_editable_editing"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="403"
            column="13"
            startOffset="27011"
            endLine="403"
            endColumn="49"
            endOffset="27047"/>
        <location id="R.string.kpi_report_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="182"
            column="13"
            startOffset="12416"
            endLine="182"
            endColumn="36"
            endOffset="12439"/>
        <location id="R.string.kpi_summary_item_format"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="109"
            column="13"
            startOffset="7002"
            endLine="109"
            endColumn="43"
            endOffset="7032"/>
        <location id="R.string.kpi_target_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="7"
            column="13"
            startOffset="312"
            endLine="7"
            endColumn="35"
            endOffset="334"/>
        <location id="R.string.kpis_assigned_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="107"
            column="13"
            startOffset="6850"
            endLine="107"
            endColumn="39"
            endOffset="6876"/>
        <location id="R.string.label_bottom_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="267"
            column="13"
            startOffset="18327"
            endLine="267"
            endColumn="38"
            endOffset="18352"/>
        <location id="R.string.label_individual_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="268"
            column="13"
            startOffset="18389"
            endLine="268"
            endColumn="42"
            endOffset="18418"/>
        <location id="R.string.label_top_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="266"
            column="13"
            startOffset="18271"
            endLine="266"
            endColumn="35"
            endOffset="18293"/>
        <location id="R.string.main_activity_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="118"
            column="13"
            startOffset="7419"
            endLine="118"
            endColumn="39"
            endOffset="7445"/>
        <location id="R.string.manual_user_input_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="66"
            column="13"
            startOffset="4106"
            endLine="66"
            endColumn="42"
            endOffset="4135"/>
        <location id="R.string.map_date_column_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="163"
            column="13"
            startOffset="10881"
            endLine="163"
            endColumn="41"
            endOffset="10909"/>
        <location id="R.string.map_user_column_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="164"
            column="13"
            startOffset="10945"
            endLine="164"
            endColumn="41"
            endOffset="10973"/>
        <location id="R.string.map_value_column_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="162"
            column="13"
            startOffset="10815"
            endLine="162"
            endColumn="42"
            endOffset="10844"/>
        <location id="R.string.menu_generate_report"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="220"
            column="13"
            startOffset="14780"
            endLine="220"
            endColumn="40"
            endOffset="14807"/>
        <location id="R.string.menu_import_excel"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="218"
            column="13"
            startOffset="14646"
            endLine="218"
            endColumn="37"
            endOffset="14670"/>
        <location id="R.string.menu_import_ocr"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="219"
            column="13"
            startOffset="14711"
            endLine="219"
            endColumn="35"
            endOffset="14733"/>
        <location id="R.string.menu_manage_users"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="222"
            column="13"
            startOffset="14919"
            endLine="222"
            endColumn="37"
            endOffset="14943"/>
        <location id="R.string.menu_search_kpis"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="217"
            column="13"
            startOffset="14588"
            endLine="217"
            endColumn="36"
            endOffset="14611"/>
        <location id="R.string.menu_settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="221"
            column="13"
            startOffset="14846"
            endLine="221"
            endColumn="33"
            endOffset="14866"/>
        <location id="R.string.month_year_format"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="113"
            column="13"
            startOffset="7213"
            endLine="113"
            endColumn="37"
            endOffset="7237"/>
        <location id="R.string.monthly_progress_label_short"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="98"
            column="13"
            startOffset="6213"
            endLine="98"
            endColumn="48"
            endOffset="6248"/>
        <location id="R.string.no_data_for_report"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="196"
            column="13"
            startOffset="13279"
            endLine="196"
            endColumn="38"
            endOffset="13304"/>
        <location id="R.string.no_data_to_import"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="180"
            column="13"
            startOffset="12335"
            endLine="180"
            endColumn="37"
            endOffset="12359"/>
        <location id="R.string.no_date_placeholder"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="309"
            column="13"
            startOffset="20915"
            endLine="309"
            endColumn="39"
            endOffset="20941"/>
        <location id="R.string.no_kpis_assigned_for_master_card"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="104"
            column="13"
            startOffset="6683"
            endLine="104"
            endColumn="52"
            endOffset="6722"/>
        <location id="R.string.no_kpis_assigned_to_user"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="108"
            column="13"
            startOffset="6917"
            endLine="108"
            endColumn="44"
            endOffset="6948"/>
        <location id="R.string.no_kpis_for_this_user"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="116"
            column="13"
            startOffset="7337"
            endLine="116"
            endColumn="41"
            endOffset="7365"/>
        <location id="R.string.no_kpis_to_display"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="122"
            column="13"
            startOffset="7688"
            endLine="122"
            endColumn="38"
            endOffset="7713"/>
        <location id="R.string.no_overall_summary_to_display"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="124"
            column="13"
            startOffset="7844"
            endLine="124"
            endColumn="49"
            endOffset="7880"/>
        <location id="R.string.no_target_set_short"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="214"
            column="13"
            startOffset="14452"
            endLine="214"
            endColumn="39"
            endOffset="14478"/>
        <location id="R.string.no_text_found_ocr"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="142"
            column="13"
            startOffset="9104"
            endLine="142"
            endColumn="37"
            endOffset="9128"/>
        <location id="R.string.no_user_summaries_to_display"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="123"
            column="13"
            startOffset="7756"
            endLine="123"
            endColumn="48"
            endOffset="7791"/>
        <location id="R.string.no_users_exist"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="240"
            column="13"
            startOffset="16152"
            endLine="240"
            endColumn="34"
            endOffset="16173"/>
        <location id="R.string.no_users_selected_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="69"
            column="13"
            startOffset="4297"
            endLine="69"
            endColumn="42"
            endOffset="4326"/>
        <location id="R.string.not_applicable_short"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="213"
            column="13"
            startOffset="14398"
            endLine="213"
            endColumn="40"
            endOffset="14425"/>
        <location id="R.string.notification_channel_description"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="93"
            column="13"
            startOffset="5833"
            endLine="93"
            endColumn="52"
            endOffset="5872"/>
        <location id="R.string.notification_channel_name"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="92"
            column="13"
            startOffset="5760"
            endLine="92"
            endColumn="45"
            endOffset="5792"/>
        <location id="R.string.ocr_data_import_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="144"
            column="13"
            startOffset="9276"
            endLine="144"
            endColumn="42"
            endOffset="9305"/>
        <location id="R.string.ocr_data_imported_success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="143"
            column="13"
            startOffset="9189"
            endLine="143"
            endColumn="45"
            endOffset="9221"/>
        <location id="R.string.ocr_date_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="139"
            column="13"
            startOffset="8910"
            endLine="139"
            endColumn="34"
            endOffset="8931"/>
        <location id="R.string.ocr_import_instructions"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="148"
            column="13"
            startOffset="9580"
            endLine="148"
            endColumn="43"
            endOffset="9610"/>
        <location id="R.string.ocr_import_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="134"
            column="13"
            startOffset="8601"
            endLine="134"
            endColumn="36"
            endOffset="8624"/>
        <location id="R.string.ocr_review_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="136"
            column="13"
            startOffset="8729"
            endLine="136"
            endColumn="36"
            endOffset="8752"/>
        <location id="R.string.ocr_user_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="140"
            column="13"
            startOffset="8960"
            endLine="140"
            endColumn="34"
            endOffset="8981"/>
        <location id="R.string.ocr_value_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="138"
            column="13"
            startOffset="8858"
            endLine="138"
            endColumn="35"
            endOffset="8880"/>
        <location id="R.string.overall_kpi_summary_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="119"
            column="13"
            startOffset="7482"
            endLine="119"
            endColumn="45"
            endOffset="7514"/>
        <location id="R.string.overall_summary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="343"
            column="13"
            startOffset="23337"
            endLine="343"
            endColumn="35"
            endOffset="23359"/>
        <location id="R.string.overall_summary_card_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="97"
            column="13"
            startOffset="6141"
            endLine="97"
            endColumn="46"
            endOffset="6174"/>
        <location id="R.string.overall_summary_context_menu_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="261"
            column="13"
            startOffset="17875"
            endLine="261"
            endColumn="54"
            endOffset="17916"/>
        <location id="R.string.percentage_value_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="207"
            column="13"
            startOffset="13994"
            endLine="207"
            endColumn="42"
            endOffset="14023"/>
        <location id="R.string.performance_report_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="272"
            column="13"
            startOffset="18645"
            endLine="272"
            endColumn="44"
            endOffset="18676"/>
        <location id="R.string.pref_key_expiry_reminder_notif"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="256"
            column="13"
            startOffset="17517"
            endLine="256"
            endColumn="50"
            endOffset="17554"/>
        <location id="R.string.pref_key_target_achieved_notif"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="253"
            column="13"
            startOffset="17223"
            endLine="253"
            endColumn="50"
            endOffset="17260"/>
        <location id="R.string.pref_notifications_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="252"
            column="13"
            startOffset="17155"
            endLine="252"
            endColumn="44"
            endOffset="17186"/>
        <location id="R.string.pref_summary_expiry_reminder_notif"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="258"
            column="13"
            startOffset="17698"
            endLine="258"
            endColumn="54"
            endOffset="17739"/>
        <location id="R.string.pref_summary_target_achieved_notif"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="255"
            column="13"
            startOffset="17404"
            endLine="255"
            endColumn="54"
            endOffset="17445"/>
        <location id="R.string.pref_title_expiry_reminder_notif"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="257"
            column="13"
            startOffset="17606"
            endLine="257"
            endColumn="52"
            endOffset="17645"/>
        <location id="R.string.pref_title_target_achieved_notif"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="254"
            column="13"
            startOffset="17312"
            endLine="254"
            endColumn="52"
            endOffset="17351"/>
        <location id="R.string.preview_data_button"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="165"
            column="13"
            startOffset="11031"
            endLine="165"
            endColumn="39"
            endOffset="11057"/>
        <location id="R.string.progress_cleared_toast"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="131"
            column="13"
            startOffset="8451"
            endLine="131"
            endColumn="42"
            endOffset="8480"/>
        <location id="R.string.progress_date_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="26"
            column="13"
            startOffset="1498"
            endLine="26"
            endColumn="38"
            endOffset="1523"/>
        <location id="R.string.progress_history_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="38"
            column="13"
            startOffset="2276"
            endLine="38"
            endColumn="42"
            endOffset="2305"/>
        <location id="R.string.remaining_days_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="210"
            column="13"
            startOffset="14189"
            endLine="210"
            endColumn="40"
            endOffset="14216"/>
        <location id="R.string.remaining_percentage_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="209"
            column="13"
            startOffset="14120"
            endLine="209"
            endColumn="46"
            endOffset="14153"/>
        <location id="R.string.remaining_value_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="208"
            column="13"
            startOffset="14058"
            endLine="208"
            endColumn="41"
            endOffset="14086"/>
        <location id="R.string.report_achieved_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="193"
            column="13"
            startOffset="13093"
            endLine="193"
            endColumn="41"
            endOffset="13121"/>
        <location id="R.string.report_end_date_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="186"
            column="13"
            startOffset="12666"
            endLine="186"
            endColumn="41"
            endOffset="12694"/>
        <location id="R.string.report_for_kpi_user_format"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="198"
            column="13"
            startOffset="13450"
            endLine="198"
            endColumn="46"
            endOffset="13483"/>
        <location id="R.string.report_percentage_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="194"
            column="13"
            startOffset="13153"
            endLine="194"
            endColumn="43"
            endOffset="13183"/>
        <location id="R.string.report_period_annual"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="191"
            column="13"
            startOffset="12972"
            endLine="191"
            endColumn="40"
            endOffset="12999"/>
        <location id="R.string.report_period_daily"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="188"
            column="13"
            startOffset="12795"
            endLine="188"
            endColumn="39"
            endOffset="12821"/>
        <location id="R.string.report_period_monthly"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="189"
            column="13"
            startOffset="12850"
            endLine="189"
            endColumn="41"
            endOffset="12878"/>
        <location id="R.string.report_period_quarterly"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="190"
            column="13"
            startOffset="12909"
            endLine="190"
            endColumn="43"
            endOffset="12939"/>
        <location id="R.string.report_start_date_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="185"
            column="13"
            startOffset="12601"
            endLine="185"
            endColumn="43"
            endOffset="12631"/>
        <location id="R.string.report_target_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="192"
            column="13"
            startOffset="13037"
            endLine="192"
            endColumn="39"
            endOffset="13063"/>
        <location id="R.string.required_daily_rate_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="211"
            column="13"
            startOffset="14250"
            endLine="211"
            endColumn="45"
            endOffset="14282"/>
        <location id="R.string.reset_to_default_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="90"
            column="13"
            startOffset="5698"
            endLine="90"
            endColumn="42"
            endOffset="5727"/>
        <location id="R.string.reset_to_default_colors_button"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="332"
            column="13"
            startOffset="22492"
            endLine="332"
            endColumn="50"
            endOffset="22529"/>
        <location id="R.string.save_kpi_button_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="16"
            column="13"
            startOffset="865"
            endLine="16"
            endColumn="40"
            endOffset="892"/>
        <location id="R.string.save_progress_button_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="27"
            column="13"
            startOffset="1551"
            endLine="27"
            endColumn="45"
            endOffset="1583"/>
        <location id="R.string.save_user_button_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="228"
            column="13"
            startOffset="15240"
            endLine="228"
            endColumn="41"
            endOffset="15268"/>
        <location id="R.string.search_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="49"
            column="13"
            startOffset="3093"
            endLine="49"
            endColumn="31"
            endOffset="3111"/>
        <location id="R.string.search_kpis_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="50"
            column="13"
            startOffset="3149"
            endLine="50"
            endColumn="37"
            endOffset="3173"/>
        <location id="R.string.select_excel_file"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="154"
            column="13"
            startOffset="10068"
            endLine="154"
            endColumn="37"
            endOffset="10092"/>
        <location id="R.string.select_image_for_ocr"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="135"
            column="13"
            startOffset="8658"
            endLine="135"
            endColumn="40"
            endOffset="8685"/>
        <location id="R.string.select_kpi_for_ocr_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="146"
            column="13"
            startOffset="9419"
            endLine="146"
            endColumn="43"
            endOffset="9449"/>
        <location id="R.string.select_kpi_for_report"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="183"
            column="13"
            startOffset="12473"
            endLine="183"
            endColumn="41"
            endOffset="12501"/>
        <location id="R.string.select_month_year_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="215"
            column="13"
            startOffset="14511"
            endLine="215"
            endColumn="43"
            endOffset="14541"/>
        <location id="R.string.select_owner_image_button_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="54"
            column="13"
            startOffset="3378"
            endLine="54"
            endColumn="50"
            endOffset="3415"/>
        <location id="R.string.select_sheet_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="168"
            column="13"
            startOffset="11285"
            endLine="168"
            endColumn="38"
            endOffset="11310"/>
        <location id="R.string.select_user_for_report"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="184"
            column="13"
            startOffset="12536"
            endLine="184"
            endColumn="42"
            endOffset="12565"/>
        <location id="R.string.select_user_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="65"
            column="13"
            startOffset="4048"
            endLine="65"
            endColumn="36"
            endOffset="4071"/>
        <location id="R.string.select_users_button"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="281"
            column="13"
            startOffset="19198"
            endLine="281"
            endColumn="39"
            endOffset="19224"/>
        <location id="R.string.select_users_dialog_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="68"
            column="13"
            startOffset="4229"
            endLine="68"
            endColumn="45"
            endOffset="4261"/>
        <location id="R.string.selected_users_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="280"
            column="13"
            startOffset="19132"
            endLine="280"
            endColumn="40"
            endOffset="19159"/>
        <location id="R.string.settings_activity_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="259"
            column="13"
            startOffset="17811"
            endLine="259"
            endColumn="43"
            endOffset="17841"/>
        <location id="R.string.task_expiration_date_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="303"
            column="13"
            startOffset="20511"
            endLine="303"
            endColumn="45"
            endOffset="20543"/>
        <location id="R.string.task_name_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="302"
            column="13"
            startOffset="20457"
            endLine="302"
            endColumn="34"
            endOffset="20478"/>
        <location id="R.string.total_annual_achieved_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="103"
            column="13"
            startOffset="6600"
            endLine="103"
            endColumn="47"
            endOffset="6634"/>
        <location id="R.string.total_annual_target_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="102"
            column="13"
            startOffset="6521"
            endLine="102"
            endColumn="45"
            endOffset="6553"/>
        <location id="R.string.total_monthly_achieved_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="101"
            column="13"
            startOffset="6436"
            endLine="101"
            endColumn="48"
            endOffset="6471"/>
        <location id="R.string.total_monthly_target_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="100"
            column="13"
            startOffset="6355"
            endLine="100"
            endColumn="46"
            endOffset="6388"/>
        <location id="R.string.trend_chart_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="195"
            column="13"
            startOffset="13217"
            endLine="195"
            endColumn="37"
            endOffset="13241"/>
        <location id="R.string.user_already_selected_error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="75"
            column="13"
            startOffset="4764"
            endLine="75"
            endColumn="47"
            endOffset="4798"/>
        <location id="R.string.user_card_color_bottom_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="239"
            column="13"
            startOffset="16075"
            endLine="239"
            endColumn="48"
            endOffset="16110"/>
        <location id="R.string.user_card_color_top_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="238"
            column="13"
            startOffset="16004"
            endLine="238"
            endColumn="45"
            endOffset="16036"/>
        <location id="R.string.user_deleted_success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="233"
            column="13"
            startOffset="15683"
            endLine="233"
            endColumn="40"
            endOffset="15710"/>
        <location id="R.string.user_image_label_manage"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="227"
            column="13"
            startOffset="15165"
            endLine="227"
            endColumn="43"
            endOffset="15195"/>
        <location id="R.string.user_kpi_list_title_prefix"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="115"
            column="13"
            startOffset="7272"
            endLine="115"
            endColumn="46"
            endOffset="7305"/>
        <location id="R.string.user_management_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="224"
            column="13"
            startOffset="14981"
            endLine="224"
            endColumn="41"
            endOffset="15009"/>
        <location id="R.string.user_name_cannot_be_empty_error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="77"
            column="13"
            startOffset="4956"
            endLine="77"
            endColumn="51"
            endOffset="4994"/>
        <location id="R.string.user_name_hint_manage"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="226"
            column="13"
            startOffset="15104"
            endLine="226"
            endColumn="41"
            endOffset="15132"/>
        <location id="R.string.user_name_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="67"
            column="13"
            startOffset="4174"
            endLine="67"
            endColumn="35"
            endOffset="4196"/>
        <location id="R.string.user_saved_success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="234"
            column="13"
            startOffset="15760"
            endLine="234"
            endColumn="38"
            endOffset="15785"/>
        <location id="R.string.user_summaries_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="120"
            column="13"
            startOffset="7557"
            endLine="120"
            endColumn="40"
            endOffset="7584"/>
        <location id="R.string.user_summary_card_title_prefix"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="106"
            column="13"
            startOffset="6778"
            endLine="106"
            endColumn="50"
            endOffset="6815"/>
        <location id="R.string.user_summary_context_menu_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="262"
            column="13"
            startOffset="17968"
            endLine="262"
            endColumn="51"
            endOffset="18006"/>
        <location id="R.string.users_not_loaded_yet"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="71"
            column="13"
            startOffset="4453"
            endLine="71"
            endColumn="40"
            endOffset="4480"/>
        <location id="R.string.view_history_button_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="36"
            column="13"
            startOffset="2135"
            endLine="36"
            endColumn="44"
            endOffset="2166"/>
        <location id="R.style.CustomFilterChip"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="4"
            column="12"
            startOffset="116"
            endLine="4"
            endColumn="35"
            endOffset="139"/>
        <entry
            name="model"
            string="anim[bounce_animation(D),card_press_scale(D),card_release_scale(D),fade_scale_in(D),shake_animation(D),slide_in_bottom(D),star_pulse(D)],array[owner_type_options_add_edit(U),reminder_options(U)],attr[colorPrimary(R),colorControlNormal(R),colorOnSurface(R),colorOnPrimary(R),actionBarSize(R),selectableItemBackgroundBorderless(R),selectableItemBackground(R),textAppearanceBody1(R),textAppearanceHeadline6(R),textAppearanceHeadline5(R),textAppearanceSubtitle1(R),textAppearanceCaption(R),textAppearanceBody2(R),materialButtonOutlinedStyle(R),textAppearanceListItem(R),colorError(R),textAppearanceListItemSecondary(R),textAppearanceBodyLarge(R),textAppearanceTitleMedium(R),textAppearanceSubtitle2(E),colorPrimaryVariant(R)],color[purple_500(U),bottom_nav_color_selector(U),gray_500(U),chip_background_color(D),purple_200(U),chip_background_selector(U),purple_accent(U),gray_200(U),gray_300(U),chip_text_color(D),purple_700(U),chip_text_selector(U),white(U),text_primary(U),text_secondary(U),summary_card_bottom_yellow(U),summary_card_top_red(U),chart_fill_purple_light(U),task_color_blue(U),task_color_green(U),task_color_orange(U),task_color_purple(U),task_color_red(U),teal_700(U),date_header_background(U),header_gradient_end(D),header_gradient_start(D),kpi_concern(U),kpi_good(U),whatsapp_green(U),online_color(U),priority_low(U),priority_urgent(U),reply_background(U),search_background(U),status_green(U),status_orange(U),status_red(U),system_message_background(U),unread_badge_color(U),soft_lavender_background(U),ai_suggestion_background(U),purple_button_text(D),background_light(U),surface(U),background_color(U),primary_color(U),chat_background(U),message_input_background(U),screen_background_light_blue(U),primary_dark(U),progress_indicator_blue(U),progress_indicator_track(U),gray_600(U),gray_700(U),black(U),background(U),today_color(U),urgent_color(U),success_color(U),progress_background(U),matrix_toggle_background(U),urgent_important_bg(U),not_urgent_important_bg(U),urgent_not_important_bg(U),not_urgent_not_important_bg(U),received_message_background(U),sent_message_background(U),message_read_color(U),text_hint(U),gray_800(U),user_color(U),progress_color_default(U),progress_track_color(U),summary_card_image_background(U),summary_card_image_tint(U),summary_card_username_text(U),teal_200(U),light_gray(D),dark_gray(D),red(D),green(D),blue(D),yellow(D),orange(D),light_blue_50(D),light_blue_200(D),light_blue_600(D),light_blue_900(D),chart_target_color(D),chart_achieved_color(D),default_kpi_card_color(D),default_kpi_card_top_gradient(D),default_kpi_card_bottom_gradient(D),fab_color(D),chip_background_default(D),light_red(U),dark_red(U),light_green(U),dark_green(U),light_yellow(U),dark_yellow(U),chart_line_purple(U),card_background_default(U),kpi_warning(U),doctor_color_1(U),doctor_color_2(U),doctor_color_3(U),doctor_color_4(U),doctor_color_5(U),doctor_color_1_light(U),doctor_color_2_light(U),doctor_color_3_light(U),doctor_color_4_light(U),doctor_color_5_light(U),doctor_color_1_lighter(U),doctor_color_2_lighter(U),doctor_color_3_lighter(U),doctor_color_4_lighter(U),doctor_color_5_lighter(U),performance_excellent(U),performance_good(U),performance_average(U),performance_below_average(U),performance_poor(U),owner_card_border(U),read_only_overlay(D),edit_indicator(U),view_only_indicator(U),kpi_share_background(U),progress_share_background(U),super_admin_color(U),admin_color(U),notification_admin_bg(U),notification_kpi_bg(U),notification_reminder_bg(U),notification_system_bg(U),priority_medium(U),priority_high(U),importance_low(D),importance_medium(D),importance_high(D),importance_critical(D),overdue_color(D),progress_tint(D),progress_text(D),urgent_chip_background(D),overdue_chip_background(D),category_work(U),category_personal(U),category_health(U),category_education(U),category_finance(U),category_family(U),category_shopping(U),category_travel(U),icon_tint(D),energy_low(D),energy_medium(D),energy_high(D),medium_priority_color(D),low_priority_color(D),blue_500(D),green_500(D),red_500(D),orange_500(D)],dimen[fab_margin(D),color_swatch_size(D),color_swatch_stroke_width(D),card_border_width(U),card_elevation(U),card_elevation_pressed(D),card_corner_radius(U)],drawable[ic_notification(U),blue_gradient_button_background(U),card_background(U),card_bottom_background(U),card_drag_shadow(U),card_top_background(U),chart_fill_gradient(D),chart_fill_gradient_purple(U),circle_background(U),circle_indicator(U),color_selector_blue(U),color_selector_green(U),color_selector_orange(U),color_selector_purple(U),color_selector_red(U),color_swatch_background(D),custom_progress_bar(D),date_header_background(U),gradient_admin_welcome(D),header_background(U),header_gradient(D),ic_account_24(U),ic_baseline_access_time_24(U),ic_baseline_add_24(U),ic_baseline_admin_panel_settings_24(U),ic_baseline_alternate_email_24(U),ic_baseline_arrow_back_24(U),ic_baseline_arrow_downward_16(D),ic_baseline_arrow_upward_16(D),ic_baseline_assessment_24(U),ic_baseline_attach_file_24(U),ic_baseline_attachment_24(D),ic_baseline_business_24(U),ic_baseline_calendar_today_24(U),ic_baseline_call_24(U),ic_baseline_camera_alt_24(U),ic_baseline_category_24(U),ic_baseline_chat_24(U),ic_baseline_content_copy_24(U),ic_baseline_delete_24(U),ic_baseline_description_24(U),ic_baseline_document_scanner_24(U),ic_baseline_edit_24(D),ic_baseline_email_24(U),ic_baseline_event_busy_24(U),ic_baseline_filter_list_24(U),ic_baseline_format_color_reset_24(D),ic_baseline_horizontal_rule_16(D),ic_baseline_keyboard_arrow_down_24(U),ic_baseline_keyboard_arrow_up_24(U),ic_baseline_location_on_24(U),ic_baseline_logout_24(D),ic_baseline_mic_24(U),ic_baseline_more_vert_24(U),ic_baseline_my_location_24(U),ic_baseline_notifications_24(U),ic_baseline_person_24(U),ic_baseline_priority_high_24(U),ic_baseline_refresh_24(U),ic_baseline_search_24(U),ic_baseline_security_24(U),ic_baseline_send_24(U),ic_baseline_settings_24(U),ic_baseline_sort_24(U),ic_baseline_switch_account_24(D),ic_baseline_task_24(U),ic_baseline_timer_24(U),ic_baseline_videocam_24(U),ic_baseline_view_list_24(U),ic_baseline_volume_off_24(U),ic_dashboard_24(U),ic_delete(U),ic_drag_handle(U),ic_email(U),ic_filter_all_24(D),ic_filter_expiry_24(D),ic_filter_report_24(D),ic_filter_task_24(D),ic_master_card_24(U),ic_messages_24(U),ic_notification_icon(U),ic_notifications(U),ic_performance_24(U),ic_phone(U),ic_share_24(U),ic_sort_24(D),ic_sort_alpha(D),ic_sort_ascending(D),ic_sort_descending(D),ic_whatsapp(U),online_indicator(U),overall_summary_gradient(D),priority_indicator_gradient(D),reply_background(U),ripple_effect(U),rounded_background(U),rounded_background_light(U),search_background(U),spinner_background(U),status_dot_green(U),status_dot_grey(U),status_dot_orange(U),status_dot_red(U),system_message_background(U),text_view_border(D),unread_badge_background(U)],id[appBarLayout(U),toolbar(U),bottomAppBar(D),btnVoiceNote(U),btnCamera(U),btnLocation(U),btnTimer(U),btnTemplate(U),cardAiSuggestions(U),templateMeeting(U),templateStudy(U),templateCall(U),templateExercise(U),taskNameInputLayout(U),taskNameEditText(U),taskDescriptionInputLayout(U),taskDescriptionEditText(U),taskExpirationDateInputLayout(U),taskExpirationDateEditText(U),taskExpirationTimeInputLayout(D),taskExpirationTimeEditText(U),taskPrioritySelector(U),taskPriorityText(U),taskCategorySelector(U),taskCategoryText(U),taskEstimatedTimeInputLayout(U),taskEstimatedTimeEditText(U),tvAdvancedDetailsHeader(U),layoutAdvancedDetails(U),chipGroupEnergyLevel(U),chipEnergyLow(U),chipEnergyMedium(U),chipEnergyHigh(U),tvProgressValue(U),sliderProgress(U),taskLocationInputLayout(U),taskLocationEditText(U),chipGroupContext(U),chipContextHome(U),chipContextOffice(U),chipContextOnline(U),chipContextTravel(U),tvSettingsHeader(U),layoutSettings(U),taskReminderInputLayout(U),taskReminderAutoCompleteTextView(U),switchRecurring(U),switchNotifications(U),switchBreakDown(U),colorRed(U),colorBlue(U),colorGreen(U),colorOrange(U),colorPurple(U),chipGroupTaskIcon(U),chipIconWork(U),chipIconStudy(U),chipIconSport(U),chipIconHealth(U),chipIconIdea(U),btnSaveDraft(U),btnAddAnother(U),addTaskButton(U),kpiNameInputLayout(D),kpiNameEditText(U),kpiDescriptionInputLayout(D),kpiDescriptionEditText(U),kpiTargetInputLayout(D),kpiTargetEditText(U),kpiQuarterlyTargetInputLayout(D),kpiQuarterlyTargetEditText(U),kpiMonthlyTargetInputLayout(D),kpiMonthlyTargetEditText(U),kpiDailyTargetInputLayout(D),kpiDailyTargetEditText(U),ownerTypeInputLayout(U),ownerTypeAutoCompleteTextView(U),ownerNameSelectionLayout(U),ownerNameInputLayout(U),ownerNameEditText(U),ownerImageView(U),selectOwnerImageButton(U),kpiUnitLabelTextView(D),kpiUnitRadioGroup(U),unitNumberRadioButton(U),unitPercentageRadioButton(U),unitCurrencyRadioButton(U),unitPointRadioButton(U),saveKpiButton(U),taskCategoryInputLayout(D),taskCategoryAutoCompleteTextView(D),taskPriorityInputLayout(D),taskPriorityAutoCompleteTextView(D),taskImportanceInputLayout(D),taskImportanceAutoCompleteTextView(D),taskEnergyLevelInputLayout(D),taskEnergyLevelAutoCompleteTextView(D),taskEstimatedHoursInputLayout(D),taskEstimatedHoursEditText(D),taskTagsInputLayout(D),taskTagsEditText(D),taskNotesInputLayout(D),taskNotesEditText(D),switchRecurringTask(D),cancelButton(U),fragmentContainer(U),bottomNavigation(U),editTextEmail(U),editTextPhone(U),textWhatsappStatus(U),textEmailStatus(U),buttonCheckApps(U),switchWeeklyReports(U),switchMonthlyReports(U),buttonTestWeeklyReport(U),buttonTestMonthlyReport(U),switchWhatsappEnabled(U),switchEmailEnabled(U),switchAutoReminders(U),switchReminderWhatsapp(U),switchReminderEmail(U),buttonSave(U),buttonScheduleNow(U),buttonCancelSchedules(U),messagesRecyclerView(U),attachButton(U),messageEditText(U),sendButton(U),tabLayout(U),recyclerView(U),emptyStateText(U),fabNewChat(U),headerTextView(U),profilePictureCard(U),profilePictureImageView(U),addPhotoButton(U),formLayout(U),fullNameInputLayout(U),fullNameEditText(U),usernameInputLayout(U),usernameEditText(U),emailInputLayout(U),emailEditText(U),departmentInputLayout(D),departmentEditText(U),buttonLayout(D),createButton(U),loadingProgressBar(U),btnSelectExcelFile(U),tvExcelImportStatus(U),progressBarExcelImport(U),tvExcelReviewTitle(U),spinnerUserFilter(U),spinnerMonthFilter(U),rgAggregationOptions(U),rbImportIndividual(U),rbCalculateAverage(U),rvExcelReviewItems(U),btnConfirmExcelImport(U),selectDateButton(U),expiryReturnCard(U),expiryReturnMessageTextView(U),nearExpiryCard(U),nearExpiryMessageTextView(U),detailKpiNameTextView(U),targetsCard(U),targetsTitleTextView(D),progressSummaryTextView(U),detailKpiTargetValueTextView(U),annualProgressPercentageTextView(U),detailKpiMonthlyTargetValueTextView(U),monthlyProgressPercentageTextView(U),detailKpiDailyTargetValueTextView(U),dailyProgressPercentageTextView(U),lastEntryDateTextView(U),scrollView(U),progressIndicatorsLayout(U),dailyProgressLayout(U),dailyProgressIndicatorContainer(D),dailyCircularProgressIndicator(U),dailyProgressPercentageText(U),monthlyProgressLayout(U),monthlyProgressIndicatorContainer(D),monthlyCircularProgressIndicator(U),monthlyProgressPercentageText(U),annualProgressLayout(U),annualProgressIndicatorContainer(D),annualCircularProgressIndicator(U),annualProgressPercentageText(U),quickStatsCard(U),detailKpiProgressLabelTextView(D),detailKpiCurrentValueTextView(U),thisWeekProgressTextView(U),averagePerDayTextView(U),addProgressButtonLayout(U),quickAdd10Button(U),quickAdd50Button(U),addProgressButton(U),lineChartTitle(U),monthFilterSpinner(U),kpiLineChart(U),appLogoImageView(U),appTitleTextView(U),welcomeTextView(U),loginFormCard(U),orTextView(D),userSelectionSpinner(U),adminModeCheckBox(U),adminRoleInputLayout(U),adminRoleAutoComplete(U),rememberMeCheckBox(U),loginButton(U),createUserButton(U),quickAccessTextView(U),recentUsersRecyclerView(U),btnCreateMasterCard(U),overallSummaryCardInclude(U),masterCardBarrier(U),reportConstraintLayout(D),kpiSelectorLayout(U),kpiSelectorAutoCompleteTextView(U),userFilterLayout(U),userFilterAutoCompleteTextView(U),startDateInputLayout(U),endDateInputLayout(U),startDateEditText(U),endDateEditText(U),goButton(U),reportContentCard(U),reportContentContainer(U),kpiNameLabel(U),compactReportTable(U),summaryChartFilterRadioGroup(U),radioDaily(U),radioMonthly(U),radioAnnual(U),barChartContainer(U),barChartTitle(U),btnShareBarChart(U),summaryBarChart(U),trendChartContainer(U),trendChartTitle(U),btnShareTrendChart(U),trendChart(U),fab(U),btnSelectImage(U),ivSelectedImage(U),tvOcrResultLabel(U),btnCopyText(U),scrollViewOcrResult(D),tvOcrResult(U),rv_ocr_review(D),btn_confirm_ocr_import(U),reportTableCard(U),searchPromptTextView(U),dateSpinner(U),valueInputLayout(U),valueEditText(U),saveButton(U),deleteButton(U),appBarLayoutTaskManagement(D),toolbarTaskManagement(U),btnSearch(U),btnFilter(U),btnViewMode(U),searchView(U),scrollViewFilters(D),chipGroupFilters(D),chipAll(U),chipToday(U),chipThisWeek(U),chipUrgent(U),chipCompleted(U),chipOverdue(U),cardQuickStats(U),tvTotalTasks(U),tvTodayTasks(U),tvUrgentTasks(U),tvCompletionRate(U),progressOverall(U),cardMatrixToggle(D),switchMatrixView(U),layoutEisenhowerMatrix(U),tvUrgentImportantCount(U),rvUrgentImportant(U),tvNotUrgentImportantCount(U),rvNotUrgentImportant(U),tvUrgentNotImportantCount(U),rvUrgentNotImportant(U),tvNotUrgentNotImportantCount(U),rvNotUrgentNotImportant(U),btnSortTasks(D),rvTasks(U),layoutEmptyState(U),fabAddTask(U),switchEmailReminders(U),switchWhatsappReminders(U),switchLocalNotifications(U),radioNoReminder(U),radio1Day(U),radio3Days(U),radio1Week(U),buttonTestWhatsapp(U),buttonTestEmail(U),appBarLayoutTaskReport(D),toolbarTaskReport(U),tvTasksDueSoonTitle(D),rvTasksDueSoon(U),tvAllTasksTitle(D),rvAllTasksReport(U),btnSaveTaskReport(U),appBarLayoutUserKpi(U),toolbarUserKpi(U),userKpiRecyclerView(U),emptyStateTextViewUserKpi(U),fabAddKpiUserList(U),tvUserName(U),tvValue(U),tvTarget(U),tvPercentage(U),tvComparison(U),compactReportRecyclerView(U),doctorNameTextView(U),dailyTargetTextView(U),dailyAchievedTextView(U),dailyPercentageTextView(U),monthlyTargetTextView(U),monthlyAchievedTextView(U),monthlyPercentageTextView(U),annualTargetTextView(U),annualAchievedTextView(U),annualPercentageTextView(U),dialogTitleTextView(U),dialogLastEntryDateTextView(U),dateButton(U),tilEditTaskName(D),etEditTaskName(U),tilEditTaskExpirationDate(D),etEditTaskExpirationDate(U),tilEditTaskExpirationTime(D),etEditTaskExpirationTime(D),tilEditTaskReminderDays(D),etEditTaskReminderDays(U),cbTaskCompleted(U),bottom_sheet_title(D),action_edit_kpi(U),action_duplicate_copy(U),action_share_card(U),action_change_color(U),action_reset_color(U),action_delete_kpi(U),seekBarProgress(D),topColorPreview(U),buttonPickTopColor(U),bottomColorPreview(U),buttonPickBottomColor(U),buttonResetColors(U),buttonCancelColorSelection(U),buttonSaveColors(U),tvExcelReviewDate(U),tvExcelReviewUser(U),tvExcelReviewValue(U),textUserName(U),textUserRole(U),textUserEmail(U),cardProfile(U),buttonLogout(U),dashboardRecyclerView(U),kpiRecyclerView(U),emptyStateTextView(U),fabAddKpi(U),cardChatList(U),cardInteractiveReport(U),cardExpiryManagement(U),cardTaskFollowUp(U),actionCard(U),actionIcon(U),actionTitle(U),actionDescription(U),activityCard(U),activityUser(U),activityTime(U),activityAction(U),headerTitle(U),statCard(U),statIcon(U),statTitle(U),statValue(U),userCard(U),userBadge(U),userName(U),userPerformance(U),dateText(U),userAvatar(D),messageCard(U),senderText(U),replyContainer(D),replyToText(D),replyMessageText(D),messageText(U),editedText(U),timeText(U),dateHeader(D),statusText(U),systemText(U),conversationCard(U),userImage(U),onlineIndicator(U),lastMessage(U),muteIcon(U),unreadBadge(U),typeIconTextView(U),titleTextView(U),messageTextView(U),timeTextView(U),unreadIndicator(U),userAvatarImageView(U),userNameTextView(U),cbSubtaskComplete(D),tvSubtaskName(D),statusIndicatorDot(U),tvTaskName(U),ivTaskActions(U),tvTaskExpirationDate(U),tvTaskStatusText(U),tvTaskIcon(U),tvTaskDetails(U),progressTask(U),tvProgressPercent(U),tvTimeRemaining(U),layoutActionButtons(D),ivCompleteTask(U),tvTaskDescription(D),rvSubtasks(D),layoutDetails(D),tvDueDate(D),chipCategory(D),layoutAssignedUser(D),tvAssignedUser(D),ivAttachment(D),chipGroupTags(U),layoutEnergyFocus(U),tvEnergyIcon(U),tvEnergyLevel(U),layoutFocusTime(U),tvFocusTime(U),layoutQuickActions(U),btnStartTask(U),btnScheduleTask(U),btnTaskSuggestions(U),priorityIndicator(U),cbTaskComplete(D),chipPriority(U),tvReportTaskName(U),tvReportTaskExpirationDate(U),roleIcon(U),userEmail(U),userRole(U),chatButton(D),kpiTitleTextView(U),starRatingTextView(U),ownerChip(U),kpiSubtitleTextView(U),targetIcon(U),targetTextView(U),targetValueTextView(U),currentIcon(U),currentTextView(U),currentValueTextView(U),currentPeriodTargetIcon(U),currentPeriodTargetTextView(U),currentPeriodTargetValueTextView(U),currentPeriodAchievementIcon(U),currentPeriodAchievementTextView(U),currentPeriodAchievementValueTextView(U),remainingIcon(U),remainingTextView(U),remainingValueTextView(U),requiredDailyIcon(U),requiredDailyTextView(U),requiredDailyValueTextView(U),lastUpdateIcon(U),lastUpdateTextView(U),lastUpdateValueTextView(U),progressIndicator(U),percentageTextView(U),kpiNameTextView(D),kpiAchievedPercentageTextView(D),kpiMonthlyPercentageTextView(D),kpiAnnualPercentageTextView(D),kpiProgressBar(D),kpiOwnerTextView(D),kpiTargetLabelTextView(D),kpiTargetValueTextView(D),kpiCurrentLabelTextView(D),kpiCurrentValueTextView(D),progress_circular_layout(D),kpiCircularProgressIndicator(D),kpiProgressPercentageTextView(D),kpiDetailNameTextView(U),guideline_summary_detail_70(U),kpiDetailMonthlyPercentTextView(U),kpiDetailAnnualPercentTextView(U),tv_review_date(D),et_review_value(U),btn_delete_review_item(U),overallSummaryCardView(U),topBackgroundView(U),topContentContainer(U),kpiDetailsContainer(U),entryDateTextView(U),entryValueTextView(U),reportUserNameTextView(U),reportKpiNameTextView(D),reportDailyRow(U),reportDailyTarget(U),reportDailyAchieved(U),reportDailyPercentage(U),reportMonthlyRow(U),reportMonthlyTarget(U),reportMonthlyAchieved(U),reportMonthlyPercentage(U),reportQuarterlyRow(U),reportQuarterlyTarget(U),reportQuarterlyAchieved(U),reportQuarterlyPercentage(U),reportAnnualRow(U),reportAnnualTarget(U),reportAnnualAchieved(U),reportAnnualPercentage(U),unifiedReportRecyclerView(D),periodTextView(U),achievedTextView(U),userCheckbox(U),dialogTitle(U),userFilterRadioGroup(U),radioAllUsers(U),radioSelectUsers(U),userCheckboxRecyclerView(U),applyButton(U),userSummaryCardView(D),bottomBackgroundView(U),ownerNameTextView(U),dragHandleImageView(U),nav_dashboard(U),nav_performance(U),nav_messages(U),nav_account(U),action_refresh(U),action_settings(U),action_share(U),action_share_as_image(U),action_share_as_pdf(D),action_save_to_device(U),action_search(U),action_archived(U),action_call(U),action_video_call(U),action_chat_info(U),action_clear_progress(U),action_clear_month_progress(U),action_search_edit_progress(U),action_ocr_import(D),action_excel_import(U),action_notifications(U),action_view_modern_report(U),action_expiry_management(U),action_ocr(U),action_chat(U),action_advanced_task(U),action_auto_send_settings(U),action_admin_dashboard(U),action_switch_user(U),action_logout(U),context_overall_change_color(U),sort_ascending(D),sort_descending(D),sort_alphabetical(D),action_edit_task(U),action_delete_task(U),action_add_to_calendar(U),action_view_task_report(U),action_reminder_settings(U),action_change_user_card_color(U),action_share_user_card_image(U),action_delete_user_kpi(U),action_export_user_data_csv(U),star_animator_tag(U),sortChartButton(D),alertTitle(R)],layout[activity_add_edit_kpi(U),activity_add_edit_kpi_original(U),activity_add_edit_task_enhanced(D),activity_admin_dashboard(U),activity_auto_send_settings(U),activity_chat(U),item_chat_message_sent(U),activity_chat_list(U),item_conversation(U),activity_create_user(U),activity_excel_import(U),activity_excel_review(U),excel_review_item(U),activity_expire_management(U),activity_kpi_detail(U),activity_login(U),item_recent_user(U),activity_main(U),overall_summary_card_item(U),activity_modern_report(U),compact_report_table(U),activity_notifications(U),item_notification(U),activity_ocr(U),activity_ocr_review(U),ocr_review_item(U),activity_report(U),activity_search_edit_progress(U),activity_task_management(U),item_task_enhanced(U),activity_task_reminder_settings(U),activity_task_report(U),item_task_report(U),activity_user_kpi_list(U),kpi_card_item(U),chart_marker_view(U),compact_report_table_row(U),dialog_add_edit_progress(U),dialog_edit_task(U),dialog_kpi_actions(U),dialog_progress_update(D),dialog_select_card_colors(U),divider_view(U),fragment_account(U),fragment_dashboard(U),fragment_main_dashboard(U),fragment_messages(U),fragment_performance(U),item_admin_dashboard_action(U),item_admin_dashboard_activity(U),item_admin_dashboard_header(U),item_admin_dashboard_stat(U),item_admin_dashboard_user(U),item_chat_date_header(U),item_chat_message_received(U),item_chat_system_message(U),item_subtask_mini(D),item_task(U),item_user_list(U),kpi_detail_item(D),kpi_list_item(D),kpi_summary_detail_item(U),progress_entry_item(U),report_table_row(D),report_table_row_colored(U),report_table_row_tabular(U),unified_report_table(D),unified_report_table_row(D),unified_report_table_row_binding(U),user_checkbox_item(U),user_filter_dialog(U),user_summary_card_item(U)],menu[admin_bottom_navigation(U),main_bottom_navigation(U),admin_dashboard_menu(U),chart_options_menu(U),chart_share_menu(U),chat_list_menu(U),chat_menu(U),kpi_detail_menu(U),main(U),main_menu(D),overall_summary_context_menu(U),sort_options_menu(D),task_item_actions_menu(U),task_management_menu(U),user_summary_context_menu(U)],mipmap[ic_launcher(U)],string[app_name(U),add_kpi_title(U),kpi_detail_title(U),action_expiry_management(U),search_edit_progress_title(U),ocr_activity_title(U),review_ocr_results_title(U),user_kpi_list_title(U),appbar_scrolling_view_behavior(R),kpi_name_hint(U),kpi_description_hint(U),kpi_annual_target_hint(U),kpi_quarterly_target_hint(U),kpi_monthly_target_hint(U),kpi_daily_target_hint(U),owner_type_hint(U),owner_name_hint(U),owner_image_description(U),select_owner_image_button(U),kpi_unit_label(U),unit_number(U),unit_percentage(U),unit_currency(U),unit_point(U),save_kpi_button(U),add_task_title(U),task_name_hint(D),task_expiration_date_hint(D),task_expiration_time_hint(U),task_reminder_days_hint(U),add_task_button_text(D),toggle_master_card(U),confirm_import_button(U),search_edit_prompt(U),value_hint(U),save_button(U),delete_entry_button(U),my_tasks_title(U),hide_bottom_view_on_scroll_behavior(R),no_kpis_assigned(U),add_kpi(U),add_progress_dialog_title(U),progress_value_hint(U),dialog_kpi_action_title(U),dialog_kpi_action_edit(U),dialog_kpi_action_duplicate_copy(U),dialog_kpi_action_share_card(U),dialog_kpi_action_change_color(U),dialog_kpi_action_reset_color(U),dialog_kpi_action_delete(U),dialog_color_picker_pick_button(U),reset_to_default(U),dialog_cancel(U),dialog_color_picker_save_button(U),target_label(U),kpi_card_item_target_label(U),current_label(U),kpi_card_item_current_label(U),kpi_card_item_current_period_target_label(U),kpi_card_item_current_period_achievement_label(U),kpi_card_item_remaining_to_target_label(U),kpi_card_item_required_daily_label(U),kpi_card_item_last_update_label(U),action_edit(U),action_delete(U),action_clear_progress(U),action_clear_month_progress(U),action_search_edit_progress(U),action_excel_import(U),change_color(U),action_change_user_card_color(U),action_share_user_card_image(U),action_delete_user_kpi(U),edit_kpi_title(U),kpi_target_hint(D),save_kpi_button_text(D),error_name_required(D),error_target_required(D),error_invalid_target(D),error_saving_kpi(D),error_kpi_not_found(D),kpi_list_title(D),add_progress_title(D),edit_progress_title(D),progress_date_hint(D),save_progress_button_text(D),error_value_required(D),error_invalid_value(U),error_date_required(U),error_saving_progress(D),current_progress_label(D),add_progress_button_text(D),view_history_button_text(D),no_progress_entries(U),progress_history_title(D),delete_kpi_menu_item(D),confirm_delete_kpi_title(D),confirm_delete_kpi_message(D),delete_progress_entry_menu_item(D),confirm_delete_progress_entry_title(D),confirm_delete_progress_entry_message(D),edit_kpi_menu_item(D),edit_progress_entry_menu_item(D),dialog_ok(D),search_hint(D),search_kpis_title(D),filter_by_owner_hint(D),select_owner_image_button_text(D),error_copying_image(D),kpi_actions_dialog_title(D),action_add_progress(D),action_view_details(D),action_edit_kpi(D),action_delete_kpi(D),owner_type_user(U),owner_type_department(U),owner_type_company(U),owner_type_other(U),select_user_hint(D),manual_user_input_hint(D),user_name_label(D),select_users_dialog_title(D),no_users_selected_hint(D),error_at_least_one_user(D),users_not_loaded_yet(D),add_new_user_button_label(D),enter_new_user_name_hint(D),create_new_user_dialog_title(D),user_already_selected_error(D),existing_user_added_to_selection_message(D),user_name_cannot_be_empty_error(D),color_picker_title_summary_card_top(D),color_picker_title_summary_card_bottom(D),color_picker_title_individual_kpi_card(D),reset_to_default_color(D),notification_channel_name(D),notification_channel_description(D),target_achieved_notification_title(U),target_achieved_notification_text(U),overall_summary_card_title(D),monthly_progress_label_short(D),annual_progress_label_short(D),total_monthly_target_label(D),total_monthly_achieved_label(D),total_annual_target_label(D),total_annual_achieved_label(D),no_kpis_assigned_for_master_card(D),user_summary_card_title_prefix(D),kpis_assigned_label(D),no_kpis_assigned_to_user(D),kpi_summary_item_format(D),filter_all_months(U),filter_by_month_button_text(D),month_year_format(D),user_kpi_list_title_prefix(D),no_kpis_for_this_user(D),main_activity_title(D),overall_kpi_summary_title(D),user_summaries_title(D),all_kpis_title(D),no_kpis_to_display(D),no_user_summaries_to_display(D),no_overall_summary_to_display(D),confirm_clear_progress_title(D),confirm_clear_progress_message(D),confirm_clear_month_progress_message(D),progress_cleared_toast(D),error_clearing_progress(U),ocr_import_title(D),select_image_for_ocr(D),ocr_review_title(D),import_ocr_data_button(D),ocr_value_label(D),ocr_date_label(D),ocr_user_label(D),error_ocr_processing(D),no_text_found_ocr(D),ocr_data_imported_success(D),ocr_data_import_failed(D),assign_to_kpi_label(D),select_kpi_for_ocr_hint(D),error_select_kpi_for_ocr(D),ocr_import_instructions(D),edit_ocr_item_title(D),delete_ocr_item_title(D),confirm_delete_ocr_item_message(D),excel_import_title(D),select_excel_file(D),excel_review_title(D),import_excel_data_button(D),error_excel_processing(D),excel_data_imported_success(D),excel_data_import_failed(D),excel_import_instructions(D),column_mapping_title(D),map_value_column_label(D),map_date_column_label(D),map_user_column_label(D),preview_data_button(D),error_column_selection(D),error_reading_sheet_names(D),select_sheet_label(D),error_no_sheets_found(D),header_row_label(D),filter_by_user_excel_label(D),all_users_excel_filter(D),aggregation_type_label(D),aggregation_individual(D),aggregation_average(D),error_average_for_all_users(D),import_successful_count(D),import_failed_with_errors(D),import_partially_successful(D),no_data_to_import(D),kpi_report_title(D),select_kpi_for_report(D),select_user_for_report(D),report_start_date_label(D),report_end_date_label(D),generate_report_button(D),report_period_daily(D),report_period_monthly(D),report_period_quarterly(D),report_period_annual(D),report_target_label(D),report_achieved_label(D),report_percentage_label(D),trend_chart_title(D),no_data_for_report(D),error_generating_report(D),report_for_kpi_user_format(D),all_users_report_option(D),kpi_detail_tab_current(D),kpi_detail_tab_monthly(D),kpi_detail_tab_quarterly(D),kpi_detail_tab_yearly(D),kpi_detail_tab_all_time(D),achieved_value_label(D),percentage_value_label(D),remaining_value_label(D),remaining_percentage_label(D),remaining_days_label(D),required_daily_rate_label(D),days_since_last_update_label(D),not_applicable_short(D),no_target_set_short(D),select_month_year_title(D),menu_search_kpis(D),menu_import_excel(D),menu_import_ocr(D),menu_generate_report(D),menu_settings(D),menu_manage_users(D),user_management_title(D),add_user_button_text(D),user_name_hint_manage(D),user_image_label_manage(D),save_user_button_text(D),error_user_name_required(D),error_saving_user(D),confirm_delete_user_title(D),confirm_delete_user_message(D),user_deleted_success(D),user_saved_success(D),edit_user_title(D),action_edit_user(D),action_delete_user(D),user_card_color_top_label(D),user_card_color_bottom_label(D),no_users_exist(D),duplicate_kpi_menu_item(D),kpi_duplicated_success(U),kpi_duplication_failed(D),expiry_notification_channel_name(D),expiry_notification_channel_description(D),kpi_expiry_reminder_title(D),kpi_expiry_reminder_text_monthly(D),kpi_expiry_reminder_text_quarterly(D),kpi_expiry_reminder_text_annual(D),pref_notifications_title(D),pref_key_target_achieved_notif(D),pref_title_target_achieved_notif(D),pref_summary_target_achieved_notif(D),pref_key_expiry_reminder_notif(D),pref_title_expiry_reminder_notif(D),pref_summary_expiry_reminder_notif(D),settings_activity_title(D),overall_summary_context_menu_title(D),user_summary_context_menu_title(D),kpi_card_context_menu_title(D),action_customize_colors(D),dialog_select_card_colors_title(D),label_top_color(D),label_bottom_color(D),label_individual_color(D),colors_updated_successfully(D),error_updating_colors(D),performance_report_title(D),selected_users_label(D),select_users_button(D),add_progress_entry_button(D),filter_all(D),filter_expiry(D),filter_report(D),filter_task_follow_up(D),show_master_card(U),hide_master_card(U),task_item_expires_prefix(U),no_date_placeholder(D),status_completed(U),status_overdue(U),status_due_today(U),status_due_in_days(U),task_added_success(U),task_notification_channel_name(U),task_notification_channel_description(U),confirm_delete_task_title(U),confirm_delete_task_message(U),task_deleted_success(U),edit_task_dialog_title(U),task_updated_success(U),reset_to_default_colors_button(D),kpi_card_item_average_label(D),overall_summary(D),action_view_report(D),summary_detail_monthly_format(U),summary_detail_annual_format(U),master_card_no_data(U),color_reset_success(U),color_set_success(U),select_card_gradient_colors(U),edit_progress_dialog_title(U),last_entry_date_label(U),no_entries_yet(U),progress_updated_success(U),progress_saved_success(U),delete_entry_confirmation_title(U),delete_entry_confirmation_message(U),progress_entry_deleted(U),select_excel_file_button_text(U),kpi_annual_target_label_formatted(D),kpi_monthly_target_label_formatted(D),kpi_daily_target_label_formatted(D),delete_kpi_confirmation_title(U),delete_kpi_confirmation_message(U),kpi_deleted_success(U),error_deleting_kpi(U),clear_progress_confirmation_title(U),clear_progress_confirmation_message(U),dialog_confirm(U),clear_progress_success(U),clear_month_progress_select_month_message(U),clear_month_progress_confirmation_title(U),clear_month_progress_confirmation_message(U),clear_month_progress_success(U),error_clearing_month_progress(U),no_data_for_selected_month(U),error_kpi_or_user_not_found(U),ocr_import_failed(U),ocr_import_cancelled(U),ocr_waiting_for_review(U),ocr_no_entries_found(U),select_date_prompt(U),error_updating_progress(U),title_activity_kpi_list(U),dialog_color_picker_title(U),expiry_return_notification_title(U),expiry_return_notification_message(U),near_expiry_notification_title(U),near_expiry_notification_message(U),no_entries_found(U),kpi_name_not_editable_editing(D),reminder_option_no_reminder(U),reminder_option_on_due_date(U),reminder_option_1_day(U),reminder_option_2_days(U),reminder_option_3_days(U),reminder_option_1_week(U),error_invalid_expiration_date(U)],style[Theme_KPITrackerApp(U),Theme_KPITrackerApp_NoActionBar(U),ThemeOverlay_MaterialComponents_Dark_ActionBar(R),Widget_MaterialComponents_TextInputLayout_OutlinedBox(R),Widget_App_Chip_Advanced(U),Widget_MaterialComponents_TextInputLayout_OutlinedBox_ExposedDropdownMenu(R),Widget_MaterialComponents_Button_OutlinedButton(R),Widget_Material3_Button_OutlinedButton(E),ThemeOverlay_AppCompat_Dark_ActionBar(R),ThemeOverlay_AppCompat_Light(R),Widget_MaterialComponents_Button_TextButton(R),Widget_Material3_Button_IconButton(R),TextAppearance_AppCompat_Medium(R),TextAppearance_AppCompat_Large(R),Widget_MaterialComponents_Button(R),MasterCardButton(U),TextAppearance_MaterialComponents_Headline6(R),Widget_App_Chip(U),ShapeAppearance_Material3_Corner_Full(R),CustomFilterChip(D),Widget_MaterialComponents_Chip_Filter(R),Widget_MaterialComponents_Chip_Choice(R),Theme_MaterialComponents_DayNight_DarkActionBar(R),Theme_MaterialComponents_DayNight_NoActionBar(R)],xml[data_extraction_rules(U),backup_rules(U),network_security_config(U),file_paths(U)],sample[backgrounds/scenic(R)];7^44d^44e^44f^450,8^54f^550^551^552^553^554,1f^1e^20,21^22,23^24^25^26,27^28,29^2a^2b^2c,cb^a,cd^2a^25,ce^2d,d0^2e,d2^2f,d3^25^22,d4^1e,d5^30^24^26,d6^31^24^26,d7^32^24^26,d8^33^24^26,d9^34^24^26,da^9,db^35,dc^36,dd^1e^28,df^37^38,e1^a,e2^a,e5^a,e6^39,e7^3a,e8^a,ea^b,ec^a,f2^a,f4^a,f5^a,f7^a,f8^b,f9^a,100^a,103^a,10a^b,10d^b,10f^b,112^c,114^b,115^a,116^a,117^a,118^a,119^a,11b^a,11c^b,11e^b,11f^a,120^a,121^a,122^a,123^a,124^3b,125^3c,126^37^38,127^3d^3e,128^3f^28,129^22,12b^25,12c^40^26,12d^26,12e^41,130^42,131^43,132^44,134^45,383^46^24^558^d^2a^e^ff^ee^fd^10d^f1^3e3^47^2b^2c^fc^10c^559^f3^ec^e1^104^12a^fb^ef^f^55a^101^102^55b^d9^d5^d6^d7^d8^55c^e2,384^3e4^55b^3e5^559^3e6^3e7^3e8^3e9^3ea^3eb^3ec^103^3ed^55c^3ee^10^3ef^3f0^3f1^3f2^3f3,385^46^24^558^d^3f4^2a^3e3^3f5^559^55b^3f6^ec^3f7^e1^3f8^55d^3f9^48,386^49^55e^28^d^55f^3e3^4a^1f^3cb,387^4b^4c^d^55e^55f^559^560^55c,388^4d^55e^28^d^55f^3e3^389^4e^e9^561^108,389^dc^2c^61^128^2b^62,38a^49^55e^28^d^55f^3e3^38b^2c^f0,38b^22^129^d3^103^125^2b^2c^110^134,38c^28^1b9^25^103^ee^1ba^560^1bc^559^e4^f6^eb^1bd^55c,38d^562^1c9^1ca,38e^563^1cc^1cd^1ce^1d3^1cf^38f,38f^17^29d^29e^19^29c,390^11^560^1d4^10^1d5,391^4f^12^1d9^13^50^14^2c^10^2b^39^12b^15^1da^51^52^1e5^1f2^55c^564^1f7^1fb^1fc,392^3da^3db^28^1fe^53^1ff^200^103^559^20^12d^e3^107^55b^54^560^201^20a^393,393^22^d3^103^64,394^de^55f^d^3db^28^3fa^135^565^20c^395^19f^20e^4a^1f^3cc,395^2a^1e^331,396^2a^d^e5^566^55^135^3e3^55b^210^ec^215^212^559^214^cc^218^11^397^219^21d^13^223^11f^560^222^221^227^226,397^28^2a,398^56^55e^1e^d^55f^3e3^2c^399^112,399^2b^2c^63^d4,39a^22a^572^11^22b^22c^16^10,39b^231^39c^3fb,39c^17^32d^32e^e^3ff^f2,39d^1e^55e^d^e5^2a^3e3^55b^215^210^ec^214^559^218^11^397^232^228^21d,39e^3fc^17^233^3fd^234^559^3fe^235^3ff^18^237^560,39f^46^24^558^d^400^2a^e^106^f8^10f^12c^567^3e3^2c^57^58^59^5a^5b^5c^5d^5e^5f^10a^3a0^3f4^e2^401,3a0^f^2a^2db^2de^2da^2df^2e0^2e1^2e2^e^100,3a1^4b^4c^d^55e^55f^114^559^11e^124^11c^55c^560^564,3a2^55e^9^d^55f^3e3^11^3a3,3a3^17^19,3a4^55e^9^d^55f^26c^3a5^402^10^403,3a5^11^301^10^300^302^567^14^411^e8^303^412^15^304^305^413^414^307^308^415^10d^30a^30b^416^11d^30d^30e^417^f7^310^311^418^ec^313^314^419^316^317^66^67^319,3a6^2a,3a8^404^11^15^405^16,3a9^559^3f7^3f8,3aa^406^11^f^407^10^408^409^40a^40b^40c^18,3ab^2c,3ac^10^40d^560^40e^40f^410^564,3ae^49^2b^cd^d3^1e^2c^43^2a,3af^49,3b0^10^403,3b1^49^2b^cd^2c,3b2^49^2b^cd^2c,3b3^22^129^2b^2c,3b4^22^129^2b^2c,3b5^28,3b6^22^129^2c^2b,3b7^22^129^2b^2c,3b8^dc^2c,3b9^d3^103^60^28^128^2c^2b,3ba^132^2c,3bb^24^2c,3bc^2a^12e^2da^11^2db^2d9^e1^15^14^2dc^e^100^a,3bd^22^129^d3^103^125^2b^2c^65^f0^28^561,3be^10^15^db^31d,3bf^11^14^31b^15^320^321^323^9,3c0^15^329^14^328,3c1^f^1a^1b,3c2^1c^13^14^15,3c3^1e^13^2a^15,3c4^13^14^15,3c5^1e^10^2a,3c6^15,3c7^15,3c9^11^34b^34c^34f^560^2a,3ca^2a^d0^331^ce^330^68^568^103^69^11^6a^185^354^113,3cb^111^11d^11a^e0,3cc^111^11d^11a^e0,3cd^105^109,3ce^11f,3d0^106,3d1^ed^10e^106,3d2^41a^41b^41c^41d^41e^f4^41f,3d3^cb,3d4^e8^f7^3de^3e0^f0^e3^10b^fe,3d5^420,3d6^122^123^121,3d8^e8^11c,3d9^421^422^423,556^56c^1e^28^2a^6b^35^55^1d^567,557^56d^1e^28^2a^6b^35^55^1d^567,55a^56b^23^29^24,565^564^119,567^56a^23^1e^29,569^56a^27^21;;;"/>
    </map>

</incidents>
