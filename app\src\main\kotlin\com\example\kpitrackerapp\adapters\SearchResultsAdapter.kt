package com.example.kpitrackerapp.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.kpitrackerapp.databinding.ItemSearchResultBinding
import com.example.kpitrackerapp.models.ChatMessage
import java.text.SimpleDateFormat
import java.util.*

class SearchResultsAdapter(
    private val onMessageClick: (ChatMessage) -> Unit
) : ListAdapter<ChatMessage, SearchResultsAdapter.SearchResultViewHolder>(SearchResultDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SearchResultViewHolder {
        val binding = ItemSearchResultBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return SearchResultViewHolder(binding)
    }

    override fun onBindViewHolder(holder: SearchResultViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class SearchResultViewHolder(
        private val binding: ItemSearchResultBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(message: ChatMessage) {
            binding.messageText.text = message.message

            // Format timestamp
            val dateFormat = SimpleDateFormat("MMM dd, HH:mm", Locale.getDefault())
            binding.messageTime.text = dateFormat.format(Date(message.timestamp))

            // Show sender info - we'll need to get current user to determine this
            val currentUser = com.example.kpitrackerapp.utils.SessionManager.getCurrentUser()
            binding.senderName.text = if (message.senderId == currentUser?.id) "You" else "Other"

            // Highlight search terms (basic implementation)
            // You can enhance this to actually highlight matching text

            binding.root.setOnClickListener {
                onMessageClick(message)
            }
        }
    }

    private class SearchResultDiffCallback : DiffUtil.ItemCallback<ChatMessage>() {
        override fun areItemsTheSame(oldItem: ChatMessage, newItem: ChatMessage): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: ChatMessage, newItem: ChatMessage): Boolean {
            return oldItem == newItem
        }
    }
}
