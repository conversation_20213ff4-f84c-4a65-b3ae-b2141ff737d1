<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F8F9FA"
    tools:context=".ui.AddEditKpiActivity">

    <!-- Modern App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:elevation="0dp"
        app:elevation="0dp">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="Add New Task"
            app:titleTextColor="#1A1A1A"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:navigationIconTint="#1A1A1A" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- Quick Templates -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Quick Templates"
                android:textSize="14sp"
                android:textColor="#6B7280"
                android:textStyle="bold"
                android:layout_marginBottom="12dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="32dp"
                android:gravity="center">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/templateMeeting"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="0dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="#E5E7EB"
                    app:cardBackgroundColor="@color/white"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="12dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="💼"
                            android:textSize="24sp"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Meeting"
                            android:textSize="12sp"
                            android:textColor="#6B7280"
                            android:textAlignment="center" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/templateStudy"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_weight="1"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="0dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="#E5E7EB"
                    app:cardBackgroundColor="@color/white"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="12dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="📚"
                            android:textSize="24sp"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Study"
                            android:textSize="12sp"
                            android:textColor="#6B7280"
                            android:textAlignment="center" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/templateCall"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="0dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="#E5E7EB"
                    app:cardBackgroundColor="@color/white"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="12dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="📞"
                            android:textSize="24sp"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Call"
                            android:textSize="12sp"
                            android:textColor="#6B7280"
                            android:textAlignment="center" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- Basic Information -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Basic Information"
                android:textSize="18sp"
                android:textColor="#1A1A1A"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <!-- Task Name -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/taskNameInputLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Task name"
                app:boxCornerRadiusTopStart="12dp"
                app:boxCornerRadiusTopEnd="12dp"
                app:boxCornerRadiusBottomStart="12dp"
                app:boxCornerRadiusBottomEnd="12dp"
                app:boxStrokeColor="#E5E7EB"
                app:hintTextColor="#6B7280">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/taskNameEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textCapSentences"
                    android:textColor="#1A1A1A"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- Task Description -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/taskDescriptionInputLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Description (optional)"
                app:boxCornerRadiusTopStart="12dp"
                app:boxCornerRadiusTopEnd="12dp"
                app:boxCornerRadiusBottomStart="12dp"
                app:boxCornerRadiusBottomEnd="12dp"
                app:boxStrokeColor="#E5E7EB"
                app:hintTextColor="#6B7280">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/taskDescriptionEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textMultiLine"
                    android:maxLines="3"
                    android:textColor="#1A1A1A"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- Due Date -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/dueDateInputLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Due date"
                app:boxCornerRadiusTopStart="12dp"
                app:boxCornerRadiusTopEnd="12dp"
                app:boxCornerRadiusBottomStart="12dp"
                app:boxCornerRadiusBottomEnd="12dp"
                app:boxStrokeColor="#E5E7EB"
                app:hintTextColor="#6B7280"
                app:endIconMode="custom"
                app:endIconDrawable="@drawable/ic_calendar">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/dueDateEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:focusable="false"
                    android:clickable="true"
                    android:textColor="#1A1A1A"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- Due Time -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/dueTimeInputLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:hint="Due time (optional)"
                app:boxCornerRadiusTopStart="12dp"
                app:boxCornerRadiusTopEnd="12dp"
                app:boxCornerRadiusBottomStart="12dp"
                app:boxCornerRadiusBottomEnd="12dp"
                app:boxStrokeColor="#E5E7EB"
                app:hintTextColor="#6B7280"
                app:endIconMode="custom"
                app:endIconDrawable="@drawable/ic_time">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/dueTimeEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:focusable="false"
                    android:clickable="true"
                    android:textColor="#1A1A1A"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- Priority Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Priority"
                android:textSize="14sp"
                android:textColor="#6B7280"
                android:textStyle="bold"
                android:layout_marginBottom="12dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="32dp">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/priorityLow"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="0dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="#E5E7EB"
                    app:cardBackgroundColor="@color/white"
                    android:clickable="true"
                    android:focusable="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:gravity="center">

                        <View
                            android:layout_width="8dp"
                            android:layout_height="8dp"
                            android:background="@drawable/circle_green"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Low"
                            android:textSize="14sp"
                            android:textColor="#6B7280" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/priorityMedium"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="0dp"
                    app:strokeWidth="2dp"
                    app:strokeColor="#3B82F6"
                    app:cardBackgroundColor="#EFF6FF"
                    android:clickable="true"
                    android:focusable="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:gravity="center">

                        <View
                            android:layout_width="8dp"
                            android:layout_height="8dp"
                            android:background="@drawable/circle_blue"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Medium"
                            android:textSize="14sp"
                            android:textColor="#3B82F6"
                            android:textStyle="bold" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/priorityHigh"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="0dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="#E5E7EB"
                    app:cardBackgroundColor="@color/white"
                    android:clickable="true"
                    android:focusable="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:gravity="center">

                        <View
                            android:layout_width="8dp"
                            android:layout_height="8dp"
                            android:background="@drawable/circle_red"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="High"
                            android:textSize="14sp"
                            android:textColor="#6B7280" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- Spacer for FAB -->
            <View
                android:layout_width="match_parent"
                android:layout_height="80dp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Modern Save Button -->
    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/fabSave"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="20dp"
        android:text="Save Task"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold"
        app:icon="@drawable/ic_check"
        app:iconTint="@color/white"
        android:backgroundTint="#3B82F6"
        app:cornerRadius="16dp"
        android:elevation="8dp" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
