http://schemas.android.com/apk/res-auto;;${\:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/bounce_animation.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/slide_in_bottom.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/star_pulse.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/fade_scale_in.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/shake_animation.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/card_release_scale.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/card_press_scale.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/color/chip_text_selector.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/color/bottom_nav_color_selector.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/color/chip_background_selector.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/color/chip_text_color.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/color/chip_background_color.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_event_busy_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_arrow_upward_16.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_filter_list_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_view_list_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/color_selector_orange.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_admin_panel_settings_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_content_copy_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_security_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/online_indicator.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_task_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/status_dot_orange.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/system_message_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sort_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_format_color_reset_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_filter_report_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/blue_gradient_button_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/search_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notification.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_more_vert_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/circle_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ripple_effect.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_arrow_downward_16.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_videocam_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/card_drag_shadow.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_performance_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notifications.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/unread_badge_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_keyboard_arrow_down_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/chart_fill_gradient_purple.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/color_selector_blue.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_notifications_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_share_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_send_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_arrow_back_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_assessment_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_business_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_email_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_my_location_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_email.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_background_light.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_call_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_drag_handle.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/status_dot_grey.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/overall_summary_gradient.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_description_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_delete.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/spinner_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_add_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_timer_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sort_ascending.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_alternate_email_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_chat_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sort_descending.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_switch_account_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_attach_file_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_calendar_today_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_filter_task_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/card_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_keyboard_arrow_up_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/gradient_admin_welcome.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_mic_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/chart_fill_gradient.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/circle_indicator.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_master_card_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/date_header_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_account_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_delete_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/status_dot_green.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_horizontal_rule_16.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/header_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_priority_high_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_refresh_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_filter_expiry_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/color_selector_purple.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_access_time_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_document_scanner_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_logout_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_filter_all_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_phone.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/card_top_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_category_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notification_icon.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/priority_indicator_gradient.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/card_bottom_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/color_swatch_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_edit_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_camera_alt_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_sort_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/reply_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_volume_off_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_messages_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_person_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/color_selector_red.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/color_selector_green.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/text_view_border.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_dashboard_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_search_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_settings_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sort_alpha.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/status_dot_red.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_location_on_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/header_gradient.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/custom_progress_bar.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_whatsapp.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_attachment_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_admin_dashboard_user.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/kpi_card_item.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/unified_report_table_row.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/unified_report_table_row_binding.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_edit_kpi.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_edit_task_enhanced.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_admin_dashboard_action.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_kpi_detail.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/chart_share_menu.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/excel_review_item.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/kpi_detail_item.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_main_dashboard.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_recent_user.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_edit_kpi_original.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_task_management.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/task_management_menu.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_kpi_actions.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/kpi_detail_menu.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_ocr_review.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/overall_summary_card_item.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_dashboard.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_performance.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/overall_summary_context_menu.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_expire_management.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_excel_import.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_task_enhanced.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_modern_report.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/compact_report_table_row.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/unified_report_table.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/main.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/main_menu.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_edit_task.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/user_filter_dialog.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/ocr_review_item.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_report.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/sort_options_menu.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_select_card_colors.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_edit_progress.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/admin_bottom_navigation.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/main_bottom_navigation.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_chat_message_received.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_chat_message_sent.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_user_list.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_chat.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_excel_review.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_task_reminder_settings.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_task.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_admin_dashboard.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_auto_send_settings.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_chat_list.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/report_table_row.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/report_table_row_colored.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/report_table_row_tabular.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_create_user.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/kpi_summary_detail_item.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/chat_menu.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_ocr.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_user_kpi_list.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_subtask_mini.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_account.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_conversation.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_search_edit_progress.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_admin_dashboard_stat.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_task_report.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/user_summary_context_menu.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/chat_list_menu.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/ids.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/progress_entry_item.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/user_summary_card_item.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_notification.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/kpi_list_item.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/task_item_actions_menu.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/chart_marker_view.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/user_checkbox_item.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_chat_system_message.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_progress_update.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/admin_dashboard_menu.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_task_report.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_messages.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/compact_report_table.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_admin_dashboard_header.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_admin_dashboard_activity.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/chart_options_menu.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_notifications.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_chat_date_header.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/divider_view.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.png,${\:app*buildDir}/generated/res/processDebugGoogleServices/values/values.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/file_paths.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+anim:bounce_animation,0,F;slide_in_bottom,1,F;star_pulse,2,F;fade_scale_in,3,F;shake_animation,4,F;card_release_scale,5,F;card_press_scale,6,F;+array:reminder_options,7,V4019c6b95,1301a36d17,;@string/reminder_option_no_reminder,@string/reminder_option_on_due_date,@string/reminder_option_1_day,@string/reminder_option_2_days,@string/reminder_option_3_days,@string/reminder_option_1_week,;owner_type_options_add_edit,7,V4004f1416,130054151e,;@string/owner_type_user,@string/owner_type_department,@string/owner_type_company,@string/owner_type_other,;+color:progress_indicator_track,8,V400290a33,3c00290a6b,;"#FFBDBDBD";text_primary,8,V4006c1b1e,30006c1b4a,;"#FF212121";progress_color_default,8,V400300c52,3a00300c88,;"#FF6200EE";status_red,8,V400240892,2e002408bc,;"#FFF44336";light_red,8,V400360e8d,2d00360eb6,;"#FFEF9A9A";default_kpi_card_top_gradient,8,V400190557,**********,;"#FF6200EE";kpi_concern,8,V400200766,2f00200791,;"#FFFF6F00";message_read_color,8,V4007a1dde,36007a1e10,;"#FF4CAF50";chip_text_selector,9,F;notification_reminder_bg,8,V400861fe0,3c00862018,;"#FFFEF7E0";energy_medium,8,V400c42a11,3100c42a3e,;"#FF2196F3";kpi_good,8,V4002107b6,2c002107de,;"#FF4CAF50";soft_lavender_background,8,V4002508d8,3c00250910,;"#FFE6E6FA";dark_gray,8,V4000a01a9,2b000a01d0,;"#A9A9A9";purple_700,8,V400040099,2e000400c3,;"#FF3700B3";doctor_color_3_lighter,8,V400511567,380051159b,;"#F1FAF1";primary_dark,8,V4006e1b84,30006e1bb0,;"#FF3700B3";success_color,8,V400a1244e,3100a1247b,;"#FF4CAF50";status_orange,8,V400230846,3100230873,;"#FFFF9800";icon_tint,8,V400bf2957,2d00bf2980,;"#FF757575";chart_target_color,8,V400160461,3600160493,;"#FFEF5350";chart_achieved_color,8,V4001704b1,38001704e5,;"#FF42A5F5";default_kpi_card_color,8,V400180504,3a0018053a,;"#FFFFFFFF";progress_indicator_blue,8,V4002a0a92,3b002a0ac9,;"#FF2196F3";system_message_background,8,V400781d69,3d00781da2,;"#40757575";surface,8,V40089208a,2b008920b1,;"#FFFFFFFF";doctor_color_5_light,8,V4004c143e,36004c1470,;"#FCE4EC";gray_800,8,V40062198c,2c006219b4,;"#FF424242";blue_500,8,V400d42ca9,2c00d42cd1,;"#FF2196F3";background_light,8,V4006b1ae8,34006b1b18,;"#FFF5F5F5";default_kpi_card_bottom_gradient,8,V4001a05b6,44001a05f6,;"#FF3700B3";search_background,8,V400c02986,3500c029b7,;"#FFFFFFFF";performance_poor,8,V4005a1824,32005a1852,;"#F44336";dark_yellow,8,V4003b100e,2f003b1039,;"#FFFBC02D";urgent_chip_background,8,V400b026ac,3a00b026e2,;"#FFFFE0E0";category_education,8,V400b727e4,3600b72816,;"#FF9C27B0";task_color_orange,8,V400cc2b77,3500cc2ba8,;"#FFFF9800";medium_priority_color,8,V400d02c0b,3900d02c40,;"#FFFF9800";progress_tint,8,V400ab25f1,3100ab261e,;"#FF4CAF50";light_blue_200,8,V400110310,320011033e,;"#FF81D4FA";screen_background_light_blue,8,V4002809d4,4000280a10,;"#FFE3F2FD";priority_urgent,8,V400962298,33009622c7,;"#FFF44336";yellow,8,V4000e026c,2a000e0292,;"#FFFFFF00";light_blue_600,8,V400120344,3200120372,;"#FF039BE5";energy_low,8,V400c329e1,2e00c32a0b,;"#FFFF9800";importance_low,8,V4009922ef,320099231d,;"#FF9E9E9E";urgent_important_bg,8,V400a524dd,3700a52510,;"#FFD32F2F";fab_color,8,V4002d0b31,2d002d0b5a,;"#FF6200EE";doctor_color_4_lighter,8,V4005215b8,38005215ec,;"#FFF8ED";bottom_nav_color_selector,10,F;kpi_warning,8,V4003e1109,2f003e1134,;"#FFFFC107";summary_card_bottom_yellow,8,V400150402,3e0015043c,;"#FFFFF59D";gray_500,8,V4005f1902,2c005f192a,;"#FF9E9E9E";not_urgent_important_bg,8,V400a62516,3b00a6254d,;"#FF1976D2";purple_accent,8,V400260931,310026095e,;"#FF9C27B0";light_gray,8,V40009017b,2c000901a3,;"#D3D3D3";black,8,V400070125,290007014a,;"#FF000000";low_priority_color,8,V400d12c46,3600d12c78,;"#FF4CAF50";orange_500,8,V400d72d33,2e00d72d5d,;"#FFFF9800";priority_medium,8,V400942230,330094225f,;"#FFFF9800";kpi_share_background,8,V4007b1e16,38007b1e4a,;"#FF2196F3";notification_system_bg,8,V40087201e,3a00872054,;"#FFE3F2FD";energy_high,8,V400c52a44,2f00c52a6f,;"#FF4CAF50";light_yellow,8,V4003a0fbd,30003a0fe9,;"#FFFFF59D";progress_text,8,V400ad265d,3100ad268a,;"#FF4CAF50";card_background_default,8,V4003d10b4,3b003d10eb,;"#FFFFFFFF";blue,8,V4000d0242,28000d0266,;"#FF0000FF";background_color,8,V4008e214b,34008e217b,;"#FFF5F5F5";ai_suggestion_background,8,V400c82a9a,3c00c82ad2,;"#FFF3E5F5";chip_background_selector,11,F;doctor_color_1,8,V40041118b,30004111b7,;"#673AB7";doctor_color_2,8,V4004211cd,30004211f9,;"#2196F3";light_green,8,V400380f21,2f00380f4c,;"#FFA5D6A7";doctor_color_3,8,V40043120d,**********,;"#4CAF50";doctor_color_4,8,V40044124e,300044127a,;"#FF9800";doctor_color_5,8,V400451290,30004512bc,;"#E91E63";performance_average,8,V400581754,**********,;"#FFC107";text_hint,8,V4008a20b7,2d008a20e0,;"#FF9E9E9E";read_only_overlay,8,V400661a1d,3500661a4e,;"#40000000";whatsapp_green,8,V4008d2117,32008d2145,;"#FF25D366";priority_high,8,V400952265,**********,;"#FFFF5722";category_health,8,V400b627af,3300b627de,;"#FFFF9800";summary_card_image_background,8,V400320d46,4100320d83,;"#00FFFFFF";text_secondary,8,V4006d1b50,32006d1b7e,;"#FF757575";doctor_color_4_light,8,V4004b13f0,36004b1422,;"#FFF3E0";user_color,8,V400811f18,2e00811f42,;"#FF607D8B";performance_excellent,8,V400561687,37005616ba,;"#4CAF50";dark_red,8,V400370ed8,2c00370f00,;"#FFD32F2F";category_shopping,8,V400ba2887,3500ba28b8,;"#FFFF5722";header_gradient_start,8,V4001e06b8,39001e06ed,;"#FF6200EE";category_work,8,V400b42745,3100b42772,;"#FF2196F3";urgent_color,8,V400a0241c,3000a02448,;"#FFF44336";category_travel,8,V400bb28be,3300bb28ed,;"#FF00BCD4";message_input_background,8,V400741c85,3c00741cbd,;"#FFFFFFFF";doctor_color_5_lighter,8,V40053160a,380053163e,;"#FDF2F6";light_blue_900,8,V400130378,32001303a6,;"#FF01579B";green_500,8,V400d52cd7,2d00d52d00,;"#FF4CAF50";red,8,V4000b01d6,27000b01f9,;"#FFFF0000";doctor_color_1_light,8,V400481309,360048133b,;"#EDE7F6";white,8,V400080150,**********,;"#FFFFFFFF";teal_700,8,V4000600f7,2c0006011f,;"#FF018786";chip_text_color,12,F;performance_good,8,V4005716ed,320057171b,;"#8BC34A";unread_badge_color,8,V400761cf5,3600761d27,;"#FFF44336";not_urgent_not_important_bg,8,V400a82590,3f00a825cb,;"#FF757575";task_color_red,8,V400c92ad8,3200c92b06,;"#FFF44336";purple_500,8,V400030069,2e00030093,;"#FF6200EE";owner_card_border,8,V4006519e6,3500651a17,;"#FF6200EE";category_personal,8,V400b52778,3500b527a9,;"#FF4CAF50";gray_600,8,V400601930,2c00601958,;"#FF757575";summary_card_username_text,8,V400330ddb,3e00330e15,;"#FFFFFFFF";notification_kpi_bg,8,V400851fa7,3700851fda,;"#FFE8F5E8";doctor_color_1_lighter,8,V4004f14c5,38004f14f9,;"#F3F0F9";performance_below_average,8,V4005917bc,3b005917f3,;"#FF9800";chip_background_default,8,V4002e0b89,3b002e0bc0,;"#FFE0E0E0";gray_200,8,V4005d18a6,2c005d18ce,;"#FFEEEEEE";primary_color,8,V4008f2181,31008f21ae,;"#FF6200EE";chip_background_color,13,F;purple_button_text,8,V400270984,36002709b6,;"#FFFFFFFF";progress_share_background,8,V4007c1e50,3d007c1e89,;"#FF4CAF50";progress_background,8,V400ac2624,3700ac2657,;"#FFE0E0E0";summary_card_top_red,8,V4001403ac,38001403e0,;"#FFEF9A9A";overdue_chip_background,8,V400b126e8,3b00b1271f,;"#FFFFE0E0";task_color_purple,8,V400cd2bae,3500cd2bdf,;"#FF9C27B0";date_header_background,8,V400771d2d,3a00771d63,;"#40000000";summary_card_image_tint,8,V400310cb7,3b00310cee,;"#FFFFFFFF";chart_line_purple,8,V4003c105d,35003c108e,;"#FFAB47BC";background,8,V40088205a,2e00882084,;"#FFF5F5F5";task_color_green,8,V400cb2b41,3400cb2b71,;"#FF4CAF50";sent_message_background,8,V400721c07,3b00721c3e,;"#FF6200EE";category_finance,8,V400b8281c,3400b8284c,;"#FFF44336";purple_200,8,V400020039,2e00020063,;"#FFBB86FC";online_color,8,V400751cc3,3000751cef,;"#FF4CAF50";red_500,8,V400d62d06,2b00d62d2d,;"#FFF44336";teal_200,8,V4000500c9,2c000500f1,;"#FF03DAC5";doctor_color_2_light,8,V400491357,3600491389,;"#E3F2FD";progress_track_color,8,V4002f0bef,38002f0c23,;"#FFBDBDBD";doctor_color_2_lighter,8,V400501517,380050154b,;"#EFF8FE";header_gradient_end,8,V4001f0710,37001f0743,;"#FF3700B3";importance_medium,8,V4009a2323,35009a2354,;"#FF2196F3";overdue_color,8,V400a22481,3100a224ae,;"#FFF44336";importance_critical,8,V4009c238f,37009c23c2,;"#FFE91E63";matrix_toggle_background,8,V400be2919,3c00be2951,;"#FFF0F0F0";status_green,8,V4002207fc,3000220828,;"#FF4CAF50";gray_700,8,V40061195e,2c00611986,;"#FF616161";reply_background,8,V400791da8,3400791dd8,;"#20000000";category_family,8,V400b92852,3300b92881,;"#FFE91E63";green,8,V4000c01ff,29000c0224,;"#FF00FF00";notification_admin_bg,8,V400841f6c,3900841fa1,;"#FFF3E5F5";dark_green,8,V400390f70,2e00390f9a,;"#FF388E3C";view_only_indicator,8,V400681a88,3700681abb,;"#FFFF9800";doctor_color_3_light,8,V4004a13a3,36004a13d5,;"#E8F5E9";edit_indicator,8,V400671a54,3200671a82,;"#FF4CAF50";light_blue_50,8,V4001002dd,310010030a,;"#FFE1F5FE";super_admin_color,8,V4007f1eb0,35007f1ee1,;"#FFE91E63";gray_300,8,V4005e18d4,2c005e18fc,;"#FFE0E0E0";priority_low,8,V4009321fe,300093222a,;"#FF4CAF50";importance_high,8,V4009b235a,33009b2389,;"#FF9C27B0";orange,8,V4000f0298,2a000f02be,;"#FFFFA500";chart_fill_purple_light,8,V4001d0651,3b001d0688,;"#406200EE";received_message_background,8,V400731c44,3f00731c7f,;"#FFFFFFFF";admin_color,8,V400801ee7,2f00801f12,;"#FF9C27B0";task_color_blue,8,V400ca2b0c,3300ca2b3b,;"#FF2196F3";chat_background,8,V400711bd2,3300711c01,;"#FFF5F5F5";today_color,8,V4009f23eb,2f009f2416,;"#FF2196F3";urgent_not_important_bg,8,V400a72553,3b00a7258a,;"#FFFF9800";+dimen:color_swatch_size,14,V40005008c,30000500b8,;"36dp";card_elevation_pressed,14,V4000b01a2,35000b01d3,;"12dp";card_corner_radius,14,V4000c01d9,31000c0206,;"16dp";card_elevation,14,V4000a0174,2c000a019c,;"4dp";fab_margin,14,V400020039,290002005e,;"16dp";color_swatch_stroke_width,14,V4000600be,37000600f1,;"2dp";card_border_width,14,V400090117,2f00090142,;"1dp";+drawable:ic_baseline_event_busy_24,15,F;ic_baseline_arrow_upward_16,16,F;ic_baseline_filter_list_24,17,F;ic_baseline_view_list_24,18,F;color_selector_orange,19,F;ic_baseline_admin_panel_settings_24,20,F;ic_baseline_content_copy_24,21,F;ic_baseline_security_24,22,F;online_indicator,23,F;ic_baseline_task_24,24,F;status_dot_orange,25,F;system_message_background,26,F;ic_sort_24,27,F;ic_baseline_format_color_reset_24,28,F;ic_filter_report_24,29,F;blue_gradient_button_background,30,F;search_background,31,F;ic_notification,32,F;ic_baseline_more_vert_24,33,F;circle_background,34,F;ripple_effect,35,F;ic_baseline_arrow_downward_16,36,F;ic_baseline_videocam_24,37,F;card_drag_shadow,38,F;ic_performance_24,39,F;ic_notifications,40,F;unread_badge_background,41,F;ic_baseline_keyboard_arrow_down_24,42,F;chart_fill_gradient_purple,43,F;color_selector_blue,44,F;ic_baseline_notifications_24,45,F;ic_share_24,46,F;ic_baseline_send_24,47,F;ic_baseline_arrow_back_24,48,F;ic_baseline_assessment_24,49,F;ic_baseline_business_24,50,F;ic_baseline_email_24,51,F;ic_baseline_my_location_24,52,F;ic_email,53,F;rounded_background_light,54,F;ic_baseline_call_24,55,F;ic_drag_handle,56,F;status_dot_grey,57,F;overall_summary_gradient,58,F;ic_baseline_description_24,59,F;ic_delete,60,F;spinner_background,61,F;ic_baseline_add_24,62,F;ic_baseline_timer_24,63,F;ic_sort_ascending,64,F;ic_baseline_alternate_email_24,65,F;ic_baseline_chat_24,66,F;ic_sort_descending,67,F;ic_baseline_switch_account_24,68,F;ic_baseline_attach_file_24,69,F;ic_baseline_calendar_today_24,70,F;ic_filter_task_24,71,F;card_background,72,F;ic_baseline_keyboard_arrow_up_24,73,F;gradient_admin_welcome,74,F;ic_baseline_mic_24,75,F;chart_fill_gradient,76,F;circle_indicator,77,F;ic_master_card_24,78,F;date_header_background,79,F;ic_account_24,80,F;ic_baseline_delete_24,81,F;status_dot_green,82,F;ic_baseline_horizontal_rule_16,83,F;header_background,84,F;ic_baseline_priority_high_24,85,F;ic_baseline_refresh_24,86,F;rounded_background,87,F;ic_filter_expiry_24,88,F;color_selector_purple,89,F;ic_baseline_access_time_24,90,F;ic_baseline_document_scanner_24,91,F;ic_baseline_logout_24,92,F;ic_filter_all_24,93,F;ic_phone,94,F;card_top_background,95,F;ic_baseline_category_24,96,F;ic_notification_icon,97,F;priority_indicator_gradient,98,F;card_bottom_background,99,F;color_swatch_background,100,F;ic_baseline_edit_24,101,F;ic_baseline_camera_alt_24,102,F;ic_baseline_sort_24,103,F;reply_background,104,F;ic_baseline_volume_off_24,105,F;ic_messages_24,106,F;ic_baseline_person_24,107,F;color_selector_red,108,F;color_selector_green,109,F;text_view_border,110,F;ic_dashboard_24,111,F;ic_baseline_search_24,112,F;ic_baseline_settings_24,113,F;ic_sort_alpha,114,F;status_dot_red,115,F;ic_baseline_location_on_24,116,F;header_gradient,117,F;custom_progress_bar,118,F;ic_whatsapp,119,F;ic_baseline_attachment_24,120,F;+id:userBadge,121,F;percentageTextView,122,F;percentageTextView,123,F;percentageTextView,124,F;taskDescriptionInputLayout,125,F;taskDescriptionInputLayout,126,F;actionTitle,127,F;monthlyProgressPercentageTextView,128,F;action_share_as_pdf,129,F;tvExcelReviewDate,130,F;tvExcelReviewDate,130,F;tvExcelReviewDate,130,F;chipContextOffice,125,F;kpiAchievedPercentageTextView,131,F;kpiRecyclerView,132,F;userAvatarImageView,133,F;kpiNameEditText,134,F;tvUrgentTasks,135,F;action_view_task_report,136,F;action_edit_kpi,137,F;action_edit_kpi,138,F;taskEstimatedTimeEditText,125,F;btnAddAnother,125,F;btn_confirm_ocr_import,139,F;btn_confirm_ocr_import,139,F;tvExcelReviewUser,130,F;tvExcelReviewUser,130,F;overallSummaryCardView,140,F;dashboardRecyclerView,141,F;dailyProgressIndicatorContainer,128,F;kpiUnitLabelTextView,134,F;cardExpiryManagement,142,F;tvCompletionRate,135,F;context_overall_change_color,143,F;chipGroupTaskIcon,125,F;layoutSettings,125,F;action_clear_month_progress,138,F;expiryReturnCard,144,F;kpiMonthlyPercentageTextView,131,F;scrollViewFilters,135,F;tvExcelImportStatus,145,F;tvExcelImportStatus,145,F;templateCall,125,F;unitPercentageRadioButton,134,F;masterCardBarrier,146,F;bottom_sheet_title,137,F;priorityIndicator,147,F;rvNotUrgentImportant,135,F;userFilterLayout,148,F;dailyAchievedTextView,149,F;unifiedReportRecyclerView,150,F;action_logout,151,F;action_logout,152,F;actionCard,127,F;toolbarTaskManagement,135,F;etEditTaskExpirationDate,153,F;radioSelectUsers,154,F;reportContentContainer,148,F;taskReminderInputLayout,125,F;taskReminderInputLayout,126,F;btn_delete_review_item,155,F;btn_delete_review_item,155,F;adminRoleAutoComplete,156,F;annualProgressPercentageText,128,F;kpiSelectorAutoCompleteTextView,148,F;kpiSelectorAutoCompleteTextView,157,F;sort_ascending,158,F;buttonSaveColors,159,F;colorBlue,125,F;dialogLastEntryDateTextView,160,F;nav_messages,161,F;nav_messages,162,F;messageCard,163,F;messageCard,164,F;userEmail,165,F;action_duplicate_copy,137,F;messageEditText,166,F;rbCalculateAverage,167,F;radioNoReminder,168,F;btnFilter,135,F;statusIndicatorDot,169,F;statusIndicatorDot,147,F;taskEnergyLevelInputLayout,126,F;nearExpiryCard,144,F;rememberMeCheckBox,156,F;fragmentContainer,170,F;fragmentContainer,146,F;switchEmailEnabled,171,F;tvTaskIcon,147,F;fabNewChat,172,F;radioDaily,148,F;radioDaily,157,F;reportMonthlyTarget,173,F;reportMonthlyTarget,174,F;reportMonthlyTarget,175,F;goButton,148,F;goButton,157,F;currentTextView,122,F;currentTextView,122,F;currentTextView,122,F;spinnerUserFilter,167,F;spinnerUserFilter,167,F;progressOverall,135,F;tvExcelReviewValue,130,F;tvExcelReviewValue,130,F;tvExcelReviewValue,130,F;monthFilterSpinner,128,F;summaryBarChart,148,F;summaryBarChart,157,F;sliderProgress,125,F;fullNameEditText,176,F;btnSelectExcelFile,145,F;btnSelectExcelFile,145,F;kpiDetailAnnualPercentTextView,177,F;action_call,178,F;ownerChip,122,F;ownerChip,122,F;loginFormCard,156,F;loginFormCard,156,F;appBarLayoutTaskManagement,135,F;chipGroupEnergyLevel,125,F;tvOcrResult,179,F;reportQuarterlyRow,173,F;reportQuarterlyRow,174,F;reportQuarterlyRow,175,F;kpiTargetInputLayout,134,F;compactReportTable,148,F;compactReportTable,157,F;dialogTitleTextView,160,F;buttonPickBottomColor,159,F;reportKpiNameTextView,173,F;colorRed,125,F;rgAggregationOptions,167,F;rgAggregationOptions,167,F;fabAddKpiUserList,180,F;dailyPercentageTextView,149,F;buttonCancelColorSelection,159,F;taskEstimatedTimeInputLayout,125,F;kpiDescriptionEditText,134,F;etEditTaskName,153,F;chatButton,165,F;recentUsersRecyclerView,156,F;trendChartTitle,148,F;tvSubtaskName,181,F;kpiDailyTargetEditText,134,F;userFilterAutoCompleteTextView,148,F;cardQuickStats,135,F;tvTotalTasks,135,F;currentValueTextView,122,F;textUserEmail,182,F;btnShareBarChart,148,F;et_review_value,155,F;et_review_value,155,F;tvEnergyLevel,147,F;cancelButton,126,F;cancelButton,176,F;cancelButton,154,F;taskCategorySelector,125,F;selectDateButton,144,F;taskImportanceInputLayout,126,F;colorOrange,125,F;chipCategory,147,F;btnScheduleTask,147,F;scrollView,128,F;scrollView,148,F;remainingIcon,122,F;remainingIcon,122,F;remainingIcon,122,F;remainingIcon,122,F;remainingIcon,122,F;userName,121,F;userName,183,F;userName,165,F;rvNotUrgentNotImportant,135,F;annualProgressPercentageTextView,128,F;taskTagsInputLayout,126,F;taskLocationEditText,125,F;deleteButton,184,F;welcomeTextView,156,F;welcomeTextView,156,F;statCard,185,F;kpiQuarterlyTargetInputLayout,134,F;appBarLayoutTaskReport,186,F;btnTaskSuggestions,147,F;switchRecurring,125,F;userFilterRadioGroup,154,F;departmentEditText,176,F;tvTaskDetails,147,F;action_change_user_card_color,187,F;switchBreakDown,125,F;addPhotoButton,176,F;addPhotoButton,176,F;tvNotUrgentImportantCount,135,F;topColorPreview,159,F;chipIconWork,125,F;searchPromptTextView,184,F;appBarLayoutUserKpi,180,F;ivAttachment,147,F;tv_review_date,155,F;unreadBadge,183,F;action_search,188,F;action_search,178,F;averagePerDayTextView,128,F;endDateEditText,148,F;endDateEditText,157,F;chipIconSport,125,F;switchWhatsappReminders,168,F;sortChartButton,189,V40003006a,2d00030093,;"";reportDailyRow,173,F;reportDailyRow,174,F;reportDailyRow,175,F;entryDateTextView,190,F;requiredDailyValueTextView,122,F;topContentContainer,140,F;topContentContainer,191,F;switchReminderEmail,171,F;taskReminderAutoCompleteTextView,125,F;taskReminderAutoCompleteTextView,126,F;detailKpiTargetValueTextView,128,F;remainingTextView,122,F;remainingTextView,122,F;remainingTextView,122,F;kpiUnitRadioGroup,134,F;quickAdd10Button,128,F;progressIndicatorsLayout,128,F;chipGroupFilters,135,F;btnSaveDraft,125,F;reportContentCard,148,F;profilePictureCard,176,F;profilePictureCard,176,F;quickAdd50Button,128,F;dialogTitle,154,F;topBackgroundView,140,F;topBackgroundView,191,F;topBackgroundView,191,F;btnViewMode,135,F;detailKpiNameTextView,128,F;chipContextTravel,125,F;ownerTypeAutoCompleteTextView,134,F;kpiLineChart,128,F;userAvatar,163,F;star_animator_tag,189,V400020039,2f00020064,;"";dateSpinner,184,F;messageTextView,192,F;trendChart,148,F;trendChart,157,F;action_search_edit_progress,138,F;radio3Days,168,F;targetIcon,122,F;targetIcon,122,F;targetIcon,122,F;targetIcon,122,F;targetIcon,122,F;targetIcon,122,F;btnLocation,125,F;monthlyProgressIndicatorContainer,128,F;layoutActionButtons,147,F;kpiTargetLabelTextView,193,F;action_add_to_calendar,194,F;taskPriorityInputLayout,126,F;textUserName,182,F;remainingValueTextView,122,F;selectOwnerImageButton,134,F;tvUserName,195,F;layoutDetails,147,F;kpiTargetEditText,134,F;tvTaskStatusText,169,F;tvFocusTime,147,F;etEditTaskExpirationTime,153,F;chipUrgent,135,F;rvExcelReviewItems,167,F;userCard,121,F;userCard,165,F;chipEnergyMedium,125,F;cbTaskCompleted,153,F;currentPeriodTargetTextView,122,F;currentPeriodTargetTextView,122,F;currentPeriodTargetTextView,122,F;radioAnnual,148,F;radioAnnual,157,F;fabAddKpi,132,F;userCheckbox,196,F;unreadIndicator,192,F;action_admin_dashboard,151,F;action_admin_dashboard,152,F;bottomColorPreview,159,F;taskTagsEditText,126,F;action_auto_send_settings,151,F;buttonTestEmail,168,F;buttonScheduleNow,171,F;startDateEditText,148,F;startDateEditText,157,F;colorPurple,125,F;summaryChartFilterRadioGroup,148,F;summaryChartFilterRadioGroup,157,F;switchReminderWhatsapp,171,F;reportQuarterlyPercentage,173,F;reportQuarterlyPercentage,174,F;reportQuarterlyPercentage,175,F;typeIconTextView,192,F;muteIcon,183,F;currentPeriodTargetValueTextView,122,F;adminModeCheckBox,156,F;actionIcon,127,F;editedText,163,F;editedText,164,F;templateMeeting,125,F;kpiCurrentLabelTextView,193,F;taskNotesEditText,126,F;tvExcelReviewTitle,167,F;tvExcelReviewTitle,167,F;currentPeriodAchievementValueTextView,122,F;btnConfirmExcelImport,167,F;btnConfirmExcelImport,167,F;annualProgressLayout,128,F;ivCompleteTask,147,F;chipGroupTags,147,F;action_chat,151,F;action_chat,152,F;startDateInputLayout,148,F;startDateInputLayout,148,F;startDateInputLayout,148,F;startDateInputLayout,157,F;startDateInputLayout,157,F;startDateInputLayout,157,F;replyMessageText,163,F;replyMessageText,164,F;switchMatrixView,135,F;userNameTextView,133,F;btnSearch,135,F;statValue,185,F;taskCategoryText,125,F;cardInteractiveReport,142,F;btnTemplate,125,F;lastMessage,183,F;tabLayout,172,F;kpiQuarterlyTargetEditText,134,F;tvDueDate,147,F;systemText,197,F;tvTaskDescription,147,F;reportUserNameTextView,173,F;reportUserNameTextView,174,F;reportUserNameTextView,175,F;userKpiRecyclerView,180,F;progressIndicator,122,F;progressIndicator,122,F;progressIndicator,122,F;progressIndicator,122,F;progressIndicator,122,F;timeText,163,F;timeText,164,F;timeText,183,F;starRatingTextView,122,F;starRatingTextView,122,F;appLogoImageView,156,F;appLogoImageView,156,F;chipOverdue,135,F;seekBarProgress,198,F;chipEnergyHigh,125,F;appBarLayout,125,F;appBarLayout,126,F;appBarLayout,170,F;appBarLayout,166,F;appBarLayout,172,F;appBarLayout,146,F;appBarLayout,148,F;appBarLayout,157,F;taskEnergyLevelAutoCompleteTextView,126,F;searchView,135,F;reportDailyPercentage,173,F;reportDailyPercentage,174,F;reportDailyPercentage,175,F;chipCompleted,135,F;rbImportIndividual,167,F;action_settings,199,F;action_settings,188,F;chipAll,135,F;dailyProgressLayout,128,F;overallSummaryCardInclude,146,F;guideline_summary_detail_70,177,F;guideline_summary_detail_70,177,F;kpiDailyTargetInputLayout,134,F;tvReportTaskName,200,F;editTextEmail,171,F;editTextEmail,168,F;createButton,176,F;reportMonthlyAchieved,173,F;reportMonthlyAchieved,174,F;reportMonthlyAchieved,175,F;saveButton,184,F;sort_descending,158,F;radio1Week,168,F;cardTaskFollowUp,142,F;detailKpiProgressLabelTextView,128,F;rvUrgentNotImportant,135,F;buttonTestWeeklyReport,171,F;detailKpiMonthlyTargetValueTextView,128,F;monthlyProgressPercentageText,128,F;valueEditText,184,F;valueEditText,160,F;kpiDetailMonthlyPercentTextView,177,F;templateStudy,125,F;headerTextView,176,F;headerTextView,176,F;action_ocr,151,F;action_ocr,152,F;switchRecurringTask,126,F;chipIconIdea,125,F;action_save_to_device,129,F;action_video_call,178,F;monthlyTargetTextView,149,F;chipToday,135,F;action_reset_color,137,F;cardChatList,201,F;layoutEisenhowerMatrix,135,F;currentPeriodAchievementIcon,122,F;currentPeriodAchievementIcon,122,F;currentPeriodAchievementIcon,122,F;currentPeriodAchievementIcon,122,F;currentPeriodAchievementIcon,122,F;bottomBackgroundView,191,F;tvAdvancedDetailsHeader,125,F;tvReportTaskExpirationDate,200,F;monthlyPercentageTextView,149,F;detailKpiDailyTargetValueTextView,128,F;taskExpirationTimeInputLayout,125,F;taskExpirationTimeInputLayout,126,F;roleIcon,165,F;action_chat_info,178,F;ivTaskActions,169,F;ivTaskActions,169,F;ivTaskActions,169,F;ivTaskActions,169,F;ivTaskActions,147,F;usernameInputLayout,176,F;usernameInputLayout,156,F;quickAccessTextView,156,F;quickAccessTextView,156,F;rvTasksDueSoon,186,F;textEmailStatus,171,F;rvAllTasksReport,186,F;messageText,163,F;messageText,164,F;taskNameEditText,125,F;taskNameEditText,126,F;ownerNameInputLayout,134,F;spinnerMonthFilter,167,F;spinnerMonthFilter,167,F;layoutAdvancedDetails,125,F;orTextView,156,F;onlineIndicator,183,F;onlineIndicator,165,F;messagesRecyclerView,166,F;userSummaryCardView,191,F;emptyStateTextView,132,F;action_switch_user,151,F;action_switch_user,152,F;currentPeriodAchievementTextView,122,F;currentPeriodAchievementTextView,122,F;currentPeriodAchievementTextView,122,F;compactReportRecyclerView,202,F;reportQuarterlyAchieved,173,F;reportQuarterlyAchieved,174,F;reportQuarterlyAchieved,175,F;action_notifications,151,F;reportAnnualTarget,173,F;reportAnnualTarget,174,F;reportAnnualTarget,175,F;action_reminder_settings,136,F;createUserButton,156,F;monthlyProgressLayout,128,F;radio1Day,168,F;headerTitle,203,F;action_archived,188,F;dateButton,160,F;currentIcon,122,F;currentIcon,122,F;currentIcon,122,F;currentIcon,122,F;currentIcon,122,F;currentIcon,122,F;radioAllUsers,154,F;taskDescriptionEditText,125,F;taskDescriptionEditText,126,F;switchEmailReminders,168,F;addProgressButtonLayout,128,F;cbTaskComplete,147,F;reportAnnualRow,173,F;reportAnnualRow,174,F;reportAnnualRow,175,F;tilEditTaskReminderDays,153,F;scrollViewOcrResult,179,F;tvComparison,195,F;lineChartTitle,128,F;kpiDetailsContainer,140,F;kpiDetailsContainer,191,F;layoutFocusTime,147,F;btnSelectImage,179,F;tvTimeRemaining,147,F;buttonCancelSchedules,171,F;annualCircularProgressIndicator,128,F;textWhatsappStatus,171,F;tvTaskName,169,F;tvTaskName,169,F;tvTaskName,169,F;tvTaskName,147,F;kpiDescriptionInputLayout,134,F;emptyStateTextViewUserKpi,180,F;statIcon,185,F;tvUrgentImportantCount,135,F;activityUser,204,F;periodTextView,123,F;periodTextView,124,F;kpiNameTextView,131,F;kpiNameTextView,193,F;reportAnnualAchieved,173,F;reportAnnualAchieved,174,F;reportAnnualAchieved,175,F;taskNameInputLayout,125,F;taskNameInputLayout,126,F;layoutEnergyFocus,147,F;senderText,163,F;btnCreateMasterCard,146,F;taskEstimatedHoursInputLayout,126,F;action_share_user_card_image,187,F;kpiSubtitleTextView,122,F;kpiSubtitleTextView,122,F;reportAnnualPercentage,173,F;reportAnnualPercentage,174,F;reportAnnualPercentage,175,F;action_delete_task,194,F;appTitleTextView,156,F;appTitleTextView,156,F;userPerformance,121,F;action_clear_progress,138,F;addTaskButton,125,F;addTaskButton,126,F;taskNotesInputLayout,126,F;buttonResetColors,159,F;buttonSave,171,F;buttonSave,168,F;buttonCheckApps,171,F;buttonCheckApps,168,F;loginButton,156,F;tvValue,195,F;fabAddTask,135,F;switchNotifications,125,F;tvNotUrgentNotImportantCount,135,F;barChartContainer,148,F;reportMonthlyPercentage,173,F;reportMonthlyPercentage,174,F;reportMonthlyPercentage,175,F;dailyProgressPercentageText,128,F;taskExpirationDateEditText,125,F;taskExpirationDateEditText,126,F;unitNumberRadioButton,134,F;statTitle,185,F;action_change_color,137,F;tvSettingsHeader,125,F;userSelectionSpinner,156,F;nav_dashboard,161,F;nav_dashboard,162,F;btnVoiceNote,125,F;tvProgressValue,125,F;tvProgressValue,198,F;detailKpiCurrentValueTextView,128,F;cbSubtaskComplete,181,F;ownerTypeInputLayout,134,F;kpiNameLabel,148,F;kpiNameLabel,157,F;nearExpiryMessageTextView,144,F;adminRoleInputLayout,156,F;tilEditTaskExpirationDate,153,F;action_advanced_task,151,F;reportTableCard,157,F;targetValueTextView,122,F;unitPointRadioButton,134,F;emailInputLayout,176,F;reportDailyTarget,173,F;reportDailyTarget,174,F;reportDailyTarget,175,F;buttonTestWhatsapp,168,F;action_excel_import,138,F;kpiProgressPercentageTextView,193,F;templateExercise,125,F;tvTodayTasks,135,F;kpiTitleTextView,122,F;kpiTitleTextView,122,F;kpiTitleTextView,122,F;kpiTitleTextView,122,F;kpiTitleTextView,122,F;kpiTitleTextView,122,F;kpiTitleTextView,122,F;kpiTitleTextView,122,F;currentPeriodTargetIcon,122,F;currentPeriodTargetIcon,122,F;currentPeriodTargetIcon,122,F;currentPeriodTargetIcon,122,F;currentPeriodTargetIcon,122,F;kpiNameInputLayout,134,F;progress_circular_layout,193,F;lastUpdateIcon,122,F;lastUpdateIcon,122,F;lastUpdateIcon,122,F;lastUpdateIcon,122,F;action_expiry_management,151,F;action_expiry_management,152,F;etEditTaskReminderDays,153,F;chipIconHealth,125,F;btnSortTasks,135,F;action_share,205,F;kpiMonthlyTargetEditText,134,F;replyContainer,163,F;replyContainer,164,F;monthlyCircularProgressIndicator,128,F;cardMatrixToggle,135,F;tvUrgentNotImportantCount,135,F;tvTasksDueSoonTitle,186,F;action_delete_kpi,137,F;action_delete_kpi,138,F;taskExpirationDateInputLayout,125,F;taskExpirationDateInputLayout,126,F;colorGreen,125,F;achievedTextView,123,F;achievedTextView,124,F;departmentInputLayout,176,F;dailyProgressPercentageTextView,128,F;statusText,164,F;rvSubtasks,147,F;userRole,165,F;buttonLogout,182,F;cardProfile,182,F;rvTasks,135,F;fab,206,F;annualPercentageTextView,149,F;actionDescription,127,F;titleTextView,192,F;loadingProgressBar,176,F;loadingProgressBar,156,F;textUserRole,182,F;tvAssignedUser,147,F;switchMonthlyReports,171,F;kpiMonthlyTargetInputLayout,134,F;progressBarExcelImport,145,F;conversationCard,183,F;nav_account,161,F;nav_account,162,F;btnShareTrendChart,148,F;dragHandleImageView,191,F;dragHandleImageView,191,F;chipGroupContext,125,F;rv_ocr_review,139,F;action_share_as_image,129,F;action_ocr_import,138,F;chipPriority,147,F;rvUrgentImportant,135,F;dateText,207,F;kpiDetailNameTextView,177,F;kpiDetailNameTextView,177,F;kpiDetailNameTextView,177,F;kpiDetailNameTextView,177,F;kpiDetailNameTextView,177,F;kpiDetailNameTextView,177,F;ivSelectedImage,179,F;buttonPickTopColor,159,F;layoutEmptyState,135,F;action_delete_user_kpi,187,F;entryValueTextView,190,F;taskPriorityText,125,F;taskCategoryInputLayout,126,F;layoutAssignedUser,147,F;saveKpiButton,134,F;sendButton,166,F;kpiAnnualPercentageTextView,131,F;chipContextOnline,125,F;reportConstraintLayout,148,F;taskPriorityAutoCompleteTextView,126,F;tvProgressPercent,147,F;tvOcrResultLabel,179,F;taskExpirationTimeEditText,125,F;taskExpirationTimeEditText,126,F;emailEditText,176,F;quickStatsCard,128,F;btnTimer,125,F;targetsTitleTextView,128,F;kpiCurrentValueTextView,193,F;userCheckboxRecyclerView,154,F;chipIconStudy,125,F;switchWeeklyReports,171,F;switchAutoReminders,171,F;toolbarUserKpi,180,F;tvEnergyIcon,147,F;action_refresh,199,F;btnCopyText,179,F;userImage,183,F;userImage,165,F;tvAllTasksTitle,186,F;bottomAppBar,125,F;ownerImageView,134,F;ownerImageView,191,F;ownerImageView,191,F;ownerImageView,191,F;ownerImageView,191,F;ownerImageView,191,F;ownerImageView,191,F;buttonLayout,176,F;ownerNameSelectionLayout,134,F;profilePictureImageView,176,F;requiredDailyIcon,122,F;requiredDailyIcon,122,F;requiredDailyIcon,122,F;requiredDailyIcon,122,F;requiredDailyIcon,122,F;activityTime,204,F;kpiCircularProgressIndicator,193,F;targetTextView,122,F;targetTextView,122,F;targetTextView,122,F;targetTextView,123,F;targetTextView,124,F;toolbar,125,F;toolbar,126,F;toolbar,170,F;toolbar,171,F;toolbar,166,F;toolbar,172,F;toolbar,146,F;toolbar,148,F;toolbar,206,F;toolbar,157,F;toolbar,168,F;nav_performance,161,F;nav_performance,162,F;formLayout,176,F;formLayout,176,F;reportMonthlyRow,173,F;reportMonthlyRow,174,F;reportMonthlyRow,175,F;annualAchievedTextView,149,F;tvPercentage,195,F;valueInputLayout,184,F;tilEditTaskName,153,F;trendChartContainer,148,F;barChartTitle,148,F;activityAction,204,F;action_edit_task,194,F;kpiTargetValueTextView,193,F;reportQuarterlyTarget,173,F;reportQuarterlyTarget,174,F;reportQuarterlyTarget,175,F;switchWhatsappEnabled,171,F;radioMonthly,148,F;radioMonthly,157,F;bottomNavigation,170,F;bottomNavigation,146,F;expiryReturnMessageTextView,144,F;action_share_card,137,F;action_export_user_data_csv,187,F;addProgressButton,128,F;reportDailyAchieved,173,F;reportDailyAchieved,174,F;reportDailyAchieved,175,F;applyButton,154,F;recyclerView,172,F;recyclerView,206,F;progressTask,147,F;annualProgressIndicatorContainer,128,F;cardAiSuggestions,125,F;tilEditTaskExpirationTime,153,F;toolbarTaskReport,186,F;btnCamera,125,F;taskEstimatedHoursEditText,126,F;ownerNameEditText,134,F;taskPrioritySelector,125,F;kpiOwnerTextView,193,F;activityCard,204,F;btnStartTask,147,F;tvTarget,195,F;requiredDailyTextView,122,F;requiredDailyTextView,122,F;requiredDailyTextView,122,F;lastUpdateValueTextView,122,F;editTextPhone,171,F;editTextPhone,168,F;tvTaskExpirationDate,169,F;layoutQuickActions,147,F;lastUpdateTextView,122,F;lastUpdateTextView,122,F;lastUpdateTextView,122,F;fullNameInputLayout,176,F;sort_alphabetical,158,F;timeTextView,192,F;action_view_modern_report,151,F;action_view_modern_report,152,F;kpiSelectorLayout,148,F;kpiSelectorLayout,157,F;doctorNameTextView,149,F;doctorNameTextView,123,F;doctorNameTextView,124,F;taskImportanceAutoCompleteTextView,126,F;dateHeader,164,F;unitCurrencyRadioButton,134,F;switchLocalNotifications,168,F;dailyTargetTextView,149,F;dailyCircularProgressIndicator,128,F;endDateInputLayout,148,F;endDateInputLayout,148,F;endDateInputLayout,157,F;endDateInputLayout,157,F;progressSummaryTextView,128,F;kpiProgressBar,131,F;ownerNameTextView,191,F;taskLocationInputLayout,125,F;monthlyAchievedTextView,149,F;annualTargetTextView,149,F;attachButton,166,F;chipThisWeek,135,F;btnSaveTaskReport,186,F;usernameEditText,176,F;usernameEditText,156,F;taskCategoryAutoCompleteTextView,126,F;lastEntryDateTextView,128,F;emptyStateText,172,F;emptyStateText,206,F;thisWeekProgressTextView,128,F;chipContextHome,125,F;buttonTestMonthlyReport,171,F;targetsCard,128,F;chipEnergyLow,125,F;replyToText,163,F;replyToText,164,F;+layout:item_task_report,200,F;activity_modern_report,148,F;activity_add_edit_task_enhanced,126,F;dialog_kpi_actions,137,F;activity_kpi_detail,128,F;compact_report_table_row,149,F;item_admin_dashboard_action,127,F;report_table_row_tabular,175,F;activity_excel_review,167,F;fragment_messages,201,F;compact_report_table,202,F;item_chat_message_sent,164,F;kpi_detail_item,131,F;activity_user_kpi_list,180,F;activity_chat_list,172,F;item_task_enhanced,147,F;activity_ocr_review,139,F;item_user_list,165,F;item_subtask_mini,181,F;item_conversation,183,F;activity_add_edit_kpi,125,F;dialog_add_edit_progress,160,F;activity_login,156,F;report_table_row_colored,174,F;kpi_summary_detail_item,177,F;kpi_list_item,193,F;dialog_edit_task,153,F;unified_report_table_row_binding,124,F;activity_chat,166,F;dialog_progress_update,198,F;activity_report,157,F;item_chat_system_message,197,F;fragment_performance,142,F;overall_summary_card_item,140,F;report_table_row,173,F;item_notification,192,F;activity_excel_import,145,F;activity_task_management,135,F;ocr_review_item,155,F;user_checkbox_item,196,F;fragment_dashboard,141,F;item_task,169,F;unified_report_table,150,F;activity_auto_send_settings,171,F;activity_main,146,F;user_filter_dialog,154,F;item_chat_message_received,163,F;activity_add_edit_kpi_original,134,F;activity_create_user,176,F;fragment_account,182,F;fragment_main_dashboard,132,F;activity_notifications,206,F;item_admin_dashboard_header,203,F;activity_expire_management,144,F;unified_report_table_row,123,F;activity_task_report,186,F;item_recent_user,133,F;chart_marker_view,195,F;divider_view,208,F;user_summary_card_item,191,F;activity_admin_dashboard,170,F;activity_task_reminder_settings,168,F;kpi_card_item,122,F;excel_review_item,130,F;item_admin_dashboard_activity,204,F;dialog_select_card_colors,159,F;item_admin_dashboard_user,121,F;activity_search_edit_progress,184,F;activity_ocr,179,F;item_admin_dashboard_stat,185,F;item_chat_date_header,207,F;progress_entry_item,190,F;+menu:kpi_detail_menu,138,F;task_item_actions_menu,194,F;chat_list_menu,188,F;main_menu,152,F;task_management_menu,136,F;overall_summary_context_menu,143,F;main,151,F;main_bottom_navigation,162,F;chat_menu,178,F;sort_options_menu,158,F;admin_dashboard_menu,199,F;chart_options_menu,205,F;admin_bottom_navigation,161,F;user_summary_context_menu,187,F;chart_share_menu,129,F;+mipmap:ic_launcher,209,F;+string:import_successful_count,7,V400b02ef9,5600b02f4b,;"Imported %1$d out of %2$d entries.";add_kpi_title,7,V40002004b,3100020078,;"Add KPI";reset_to_default_color,7,V40059163a,3a00591670,;"Default";kpi_duplicated_success,7,V400f23fab,4f00f23ff6,;"KPI duplicated successfully.";remaining_percentage_label,7,V400d03720,4300d0375f,;"Remaining %\:";import_excel_data_button,7,V4009b27d7,40009b2813,;"Import Data";add_progress_entry_button,7,V4011c4bef,48011c4c33,;"Add Progress Entry";view_history_button_text,7,V40023084f,410023088c,;"View History";selected_users_label,7,V401174ab4,4001174af0,;"Selected Users\:";percentage_value_label,7,V400ce36a2,3e00ce36dc,;"Percentage\:";kpi_expiry_reminder_text_quarterly,7,V400f94203,7d00f9427c,;"Quarterly target for '%1$s' is approaching. Review progress.";no_text_found_ocr,7,V4008d2388,53008d23d7,;"No text found in image or OCR failed.";generate_report_button,7,V400ba31af,4200ba31ed,;"Generate Report";delete_kpi_confirmation_title,7,V40176629d,3e017662d7,;"TODO";monthly_progress_label_short,7,V40061183d,460061187f,;"Month\: %1$d%%";edit_task_dialog_title,7,V40140552c,3c01405564,;"Edit Task";progress_cleared_toast,7,V4008220fb,440082213b,;"Progress cleared.";dialog_kpi_action_title,7,V4014355b5,3f014355f0,;"KPI Actions";kpi_monthly_target_hint,7,V4000801c4,4d0008020d,;"Monthly Target (Optional)";action_edit_kpi,7,V4003a0e7c,34003a0eac,;"Edit KPI";kpi_expiry_reminder_text_monthly,7,V400f84188,7900f841fd,;"Monthly target for '%1$s' is approaching. Review progress.";change_color,7,V4015e5cdf,35015e5d10,;"Change Color";excel_review_title,7,V4009a2795,40009a27d1,;"Review Excel Data";confirm_clear_progress_title,7,V4007e1f3e,4f007e1f89,;"Confirm Clear Progress";edit_ocr_item_title,7,V40094261f,3d00942658,;"Edit OCR Item";kpi_card_item_required_daily_label,7,V401535a25,4a01535a6b,;"Daily Req.\:";error_deleting_kpi,7,V401796355,3301796384,;"TODO";action_clear_month_progress,7,V40080201b,4f00802066,;"Clear Month's Progress";action_edit,7,V401595b9f,2c01595bc7,;"Edit";kpis_assigned_label,7,V4006a1aba,41006a1af7,;"KPIs Assigned\: %d";error_average_for_all_users,7,V400af2e67,9000af2ef3,;"Cannot calculate average for All Users. Select a specific user or import individually.";user_summary_context_menu_title,7,V401054628,4d01054671,;"User Card Actions";duplicate_kpi_menu_item,7,V400f13f68,4100f13fa5,;"Duplicate KPI";unit_point,7,V4000e032b,2c000e0353,;"Point";user_name_label,7,V400421046,3500421077,;"User Name";expiry_notification_channel_description,7,V400f6409e,9e00f64138,;"Reminders for KPIs nearing their implicit monthly/quarterly/annual review or target dates.";user_card_color_top_label,7,V400ed3e7c,4500ed3ebd,;"Card Top Color\:";overall_summary,7,V401565b21,3b01565b58,;"Overall Summary";user_summaries_title,7,V400771d7d,3f00771db8,;"User Summaries";color_picker_title_individual_kpi_card,7,V4005815dc,5c00581634,;"Individual KPI Card Color";menu_search_kpis,7,V400d838f4,3800d83928,;"Search KPIs";import_partially_successful,7,V400b22fad,7800b23021,;"Import partially successful. %1$d entries imported\, %2$d errors.";near_expiry_notification_message,7,V4019068e3,4101906920,;"TODO";summary_detail_annual_format,7,V401655e76,4501655eb7,;"Annual\: %d%%";confirm_clear_month_progress_message,7,V40081206c,8d008120f5,;"Are you sure you want to clear progress for %1$s for this KPI for this user?";map_value_column_label,7,V400a12a37,4000a12a73,;"Value Column\:";overall_kpi_summary_title,7,V400761d32,4900761d77,;"Overall KPI Summary";dialog_kpi_action_delete,7,V401495755,3b0149578c,;"Delete";error_clearing_progress,7,V400832141,4c00832189,;"Error clearing progress.";ocr_value_label,7,V400892292,32008922c0,;"Value\:";no_date_placeholder,7,V4013451ab,37013451de,;"No date";owner_type_hint,7,V400340cf2,3600340d24,;"Owner Type";owner_type_company,7,V4003e0f5c,36003e0f8e,;"Company";kpi_monthly_target_label_formatted,7,V401746215,4301746254,;"TODO";kpi_detail_tab_monthly,7,V400c93572,3a00c935a8,;"Monthly";pref_notifications_title,7,V400fb42fb,4200fb4339,;"Notifications";edit_progress_title,7,V40017055c,3d00170595,;"Edit Progress";target_achieved_notification_title,7,V4005d1735,4f005d1780,;"Target Achieved!";clear_month_progress_confirmation_message,7,V401806514,4a0180655a,;"TODO";filter_by_owner_hint,7,V400320c80,4000320cbc,;"Filter by Owner";edit_kpi_menu_item,7,V4002c0b32,37002c0b65,;"Edit KPI";no_entries_yet,7,V4016c602d,2f016c6058,;"TODO";error_ocr_processing,7,V4008c2335,51008c2382,;"Error processing image with OCR.";summary_detail_monthly_format,7,V401645e2d,4701645e70,;"Monthly\: %d%%";error_excel_processing,7,V4009c2819,4f009c2864,;"Error processing Excel file.";kpi_annual_target_label_formatted,7,V4017361d1,420173620f,;"TODO";select_excel_file_button_text,7,V401726191,3e017261cb,;"TODO";kpi_card_item_target_label,7,V4014d586a,3e014d58a4,;"Target\:";save_user_button_text,7,V400e33b80,3b00e33bb7,;"Save User";kpi_description_hint,7,V4000500e7,470005012a,;"Description (Optional)";error_select_kpi_for_ocr,7,V400922503,5f0092255e,;"Please select a KPI to assign the data to.";filter_expiry,7,V4011e4c65,30011e4c91,;"Expiry";kpi_detail_tab_current,7,V400c83536,3a00c8356c,;"Current";confirm_delete_user_message,7,V400e73c97,a200e73d35,;"Are you sure you want to delete user '%1$s' and all their KPI assignments? This action cannot be undone.";near_expiry_notification_title,7,V4018f68a2,3f018f68dd,;"TODO";reminder_option_2_days,7,V401986acd,4001986b09,;"2 days before";save_kpi_button,7,V4011b4bb9,34011b4be9,;"Save KPI";kpi_card_item_remaining_to_target_label,7,V4015259d5,4e01525a1f,;"Remaining\:";month_year_format,7,V400701c25,3700701c58,;"MMMM yyyy";all_users_excel_filter,7,V400ab2d38,4800ab2d7c,;"All Users (from file)";confirm_delete_task_title,7,V4013c53e9,49013c542e,;"Confirm Delete Task";app_name,7,V400010015,3400010045,;"KPI Tracker Pro";clear_month_progress_select_month_message,7,V4017e647e,4a017e64c4,;"TODO";kpi_card_item_current_period_target_label,7,V40150591f,540150596f,;"Period Target\:";kpi_target_hint,7,V400060130,3f0006016b,;"Annual Target Value";current_progress_label,7,V400200794,44002007d4,;"Current Progress\:";ocr_review_title,7,V400872211,3f0087224c,;"Review OCR Results";user_name_hint_manage,7,V400e13af8,3b00e13b2f,;"User Name";delete_ocr_item_title,7,V40095265e,410095269b,;"Delete OCR Item";action_share_user_card_image,7,V401605d68,4e01605db2,;"Share User Card Image";no_kpis_assigned,7,V4012a4f25,3d012a4f5e,;"No KPIs Assigned";user_image_label_manage,7,V400e23b35,4900e23b7a,;"User Image (Optional)";gcm_defaultSenderId,210,V4000300c7,5100030114,;"123456789012";select_excel_file,7,V40099274c,470099278f,;"Select Excel File (.xlsx)";task_item_expires_prefix,7,V401335167,42013351a5,;"Expires\: %1$s";action_clear_progress,7,V4007d1efc,40007d1f38,;"Clear Progress";error_invalid_target,7,V40012041c,450012045d,;"Invalid target value";unit_percentage,7,V4000c02bf,36000c02f1,;"Percentage";owner_type_user,7,V4003c0eec,30003c0f18,;"User";action_delete,7,V4015a5bcd,30015a5bf9,;"Delete";add_kpi,7,V401244dd6,2b01244dfd,;"Add KPI";error_kpi_or_user_not_found,7,V40184661c,3c01846654,;"TODO";error_no_sheets_found,7,V400a82c4b,5400a82c9b,;"No sheets found in the Excel file.";header_row_label,7,V400a92ca1,4000a92cdd,;"Data starts at row\:";enter_new_user_name_hint,7,V400481203,4800481247,;"Enter New User Name";add_user_button_text,7,V400e03abd,3900e03af2,;"Add User";dialog_cancel,7,V4002f0bdb,30002f0c07,;"Cancel";unit_currency,7,V4000d02f7,32000d0325,;"Currency";no_progress_entries,7,V400240892,48002408d6,;"No progress entries yet.";action_add_progress,7,V400380e00,3c00380e38,;"Add Progress";status_overdue,7,V401365224,3a0136525a,;"Status\: Overdue";edit_user_title,7,V400ea3dd1,3500ea3e02,;"Edit User";task_notification_channel_description,7,V4013b537b,6c013b53e3,;"Notifications for upcoming task deadlines.";delete_entry_confirmation_title,7,V4016f60d2,40016f610e,;"TODO";select_owner_image_button,7,V4011a4b75,42011a4bb3,;"Select Image";project_id,210,V4000802c6,5000080312,;"kpi-tracker-app-demo";report_start_date_label,7,V400b83131,3f00b8316c,;"Start Date\:";select_users_dialog_title,7,V40043107d,42004310bb,;"Select Users";task_deleted_success,7,V4013e549c,47013e54df,;"Task '%1$s' deleted.";filter_by_user_excel_label,7,V400aa2ce3,5300aa2d32,;"Filter by User (from Excel)\:";report_period_monthly,7,V400bc322a,3900bc325f,;"Monthly";menu_import_ocr,7,V400da396f,4300da39ae,;"Import from Image (OCR)";menu_settings,7,V400dc39f6,3200dc3a24,;"Settings";users_not_loaded_yet,7,V40046115d,60004611b9,;"Users not loaded yet\, please try again shortly.";error_user_name_required,7,V400e43bbd,4b00e43c04,;"User name is required.";reset_to_default,7,V401585b60,3d01585b99,;"Reset to Default";report_period_daily,7,V400bb31f3,3500bb3224,;"Daily";menu_generate_report,7,V400db39b4,4000db39f0,;"Generate Report";edit_kpi_title,7,V40003007e,33000300ad,;"Edit KPI";no_overall_summary_to_display,7,V4007b1e9c,5c007b1ef4,;"No overall KPI summary to display.";search_edit_prompt,7,V401264e45,4401264e85,;"Search or Edit Prompt";reset_to_default_colors_button,7,V4014b57d4,4b014b581b,;"Reset to Default";action_edit_user,7,V400eb3e08,3600eb3e3a,;"Edit User";color_set_success,7,V401685f46,3201685f74,;"TODO";add_progress_button_text,7,V40022080c,4100220849,;"Add Progress";no_entries_found,7,V401916926,3101916953,;"TODO";dialog_kpi_action_change_color,7,V4014756c5,4701475708,;"Change Color";add_task_button_text,7,V4013150fc,340131512c,;"Add";notification_channel_description,7,V4005c16c1,72005c172f,;"Notifications for KPI events like target achievement.";dialog_select_card_colors_title,7,V401084707,4e01084751,;"Select Card Colors";excel_import_instructions,7,V4009f2918,d9009f29ed,;"Select an Excel file (.xlsx). Ensure it has columns for Value (numeric)\, Date (e.g.\, YYYY-MM-DD)\, and optionally User Identifier. Review and edit before importing.";reminder_option_no_reminder,7,V401956a03,4301956a42,;"No reminder";ocr_import_instructions,7,V400932564,b900932619,;"Select an image containing numerical data\, dates\, and optional user identifiers. Review and edit the extracted data before importing.";kpi_report_title,7,V400b53078,3700b530ab,;"KPI Report";confirm_delete_kpi_message,7,V4002809a3,7500280a14,;"Are you sure you want to delete this KPI and all its progress?";no_kpis_to_display,7,V400791e00,4200791e3e,;"No KPIs to display.";aggregation_average,7,V400ae2e10,5500ae2e61,;"Calculate Average (for selected user)";progress_value_hint,7,V40018059b,35001805cc,;"Value";no_data_for_selected_month,7,V4018365df,3b01836616,;"TODO";column_mapping_title,7,V400a029f3,4200a02a31,;"Map Excel Columns";colors_updated_successfully,7,V4010c4833,4c010c487b,;"Card colors updated.";action_excel_import,7,V4015c5c4d,41015c5c8a,;"Import from Excel";kpi_daily_target_label_formatted,7,V40175625a,4101756297,;"TODO";action_delete_user,7,V400ec3e40,3a00ec3e76,;"Delete User";error_value_required,7,V4001b064c,42001b068a,;"Value is required";task_expiration_date_hint,7,V4012e5017,45012e5058,;"Expiration Date";owner_type_department,7,V4003d0f1e,3c003d0f56,;"Department";report_end_date_label,7,V400b93172,3b00b931a9,;"End Date\:";settings_activity_title,7,V40102458b,3c010245c3,;"Settings";kpi_actions_dialog_title,7,V400370dbe,4000370dfa,;"KPI Actions";expiry_notification_channel_name,7,V400f5404b,5100f54098,;"KPI Expiry Reminders";user_saved_success,7,V400e93d88,4700e93dcb,;"User saved successfully.";task_name_hint,7,V4012d4fe1,34012d5011,;"Task Name";select_card_gradient_colors,7,V401695f7a,3c01695fb2,;"TODO";task_added_success,7,V4013952eb,430139532a,;"Task '%1$s' added.";task_notification_channel_name,7,V4013a5330,49013a5375,;"Task Reminders";no_users_selected_hint,7,V4004410c1,4400441101,;"No users selected";status_completed,7,V4013551e4,3e0135521e,;"Status\: Completed";label_bottom_color,7,V4010a478f,3c010a47c7,;"Bottom Color\:";label_top_color,7,V401094757,3601094789,;"Top Color\:";action_expiry_management,7,V401104916,4601104958,;"Expiry Management";overall_summary_context_menu_title,7,V4010445cb,5b01044622,;"Overall Summary Card Actions";progress_entry_deleted,7,V401716158,370171618b,;"TODO";select_user_hint,7,V400400fc8,3800400ffc,;"Select User";kpi_detail_tab_quarterly,7,V400ca35ae,3e00ca35e8,;"Quarterly";existing_user_added_to_selection_message,7,V4004b12ec,66004b134e,;"Existing user added to selection.";overall_summary_card_title,7,V4006017f5,4600601837,;"Overall Summary";review_ocr_results_title,7,V4011349e8,4701134a2b,;"Review OCR Results";edit_progress_entry_menu_item,7,V4002d0b6b,44002d0bab,;"Edit Entry";confirm_delete_progress_entry_title,7,V4002a0a64,54002a0ab4,;"Confirm Delete Entry";action_delete_kpi,7,V4003b0eb2,38003b0ee6,;"Delete KPI";kpi_card_item_current_label,7,V4014f58de,3f014f5919,;"Current";pref_key_target_achieved_notif,7,V400fc433f,5700fc4392,;"target_achieved_notification";pref_summary_target_achieved_notif,7,V400fe43f4,6f00fe445f,;"Receive a notification when a KPI target is met.";clear_progress_confirmation_message,7,V4017b63ce,44017b640e,;"TODO";select_user_for_report,7,V400b730f0,3f00b7312b,;"Select User\:";kpi_daily_target_hint,7,V400090213,4900090258,;"Daily Target (Optional)";action_customize_colors,7,V4010746c1,4401074701,;"Customize Colors";filter_by_month_button_text,7,V4006f1bdc,47006f1c1f,;"Filter by Month";dialog_color_picker_save_button,7,V4014c5821,47014c5864,;"Save Colors";no_kpis_assigned_to_user,7,V4006b1afd,53006b1b4c,;"No KPIs assigned to this user.";remaining_value_label,7,V400cf36e2,3c00cf371a,;"Remaining\:";delete_kpi_menu_item,7,V400260921,3b00260958,;"Delete KPI";confirm_delete_kpi_title,7,V40027095e,430027099d,;"Confirm Delete";map_user_column_label,7,V400a32ab9,5400a32b09,;"User Identifier Column (Optional)\:";ocr_data_imported_success,7,V4008e23dd,55008e242e,;"OCR data imported successfully.";user_name_cannot_be_empty_error,7,V4004c1354,56004c13a6,;"User name cannot be empty.";kpi_detail_tab_yearly,7,V400cb35ee,3800cb3622,;"Yearly";user_card_color_bottom_label,7,V400ee3ec3,4b00ee3f0a,;"Card Bottom Color\:";all_kpis_title,7,V400781dbe,4000781dfa,;"All KPIs (Aggregated)";report_period_annual,7,V400be32a4,3f00be32df,;"Annual (Range)";reminder_option_3_days,7,V401996b0f,4001996b4b,;"3 days before";add_progress_dialog_title,7,V4012b4f64,42012b4fa2,;"Add Progress";error_clearing_month_progress,7,V40182659f,3e018265d9,;"TODO";dialog_confirm,7,V4017c6414,2f017c643f,;"TODO";dialog_color_picker_pick_button,7,V4014a5792,40014a57ce,;"Pick";task_expiration_time_hint,7,V4013050aa,50013050f6,;"Expiration Time (Optional)";delete_entry_button,7,V401294ee7,3c01294f1f,;"Delete Entry";kpi_list_title,7,V4001504ee,2f00150519,;"KPIs";kpi_annual_target_hint,7,V401164a72,4001164aae,;"Annual Target";report_period_quarterly,7,V400bd3265,3d00bd329e,;"Quarterly";filter_report,7,V4011f4c97,3c011f4ccf,;"Interactive Report";dialog_color_picker_title,7,V4018c67de,3a018c6814,;"TODO";target_label,7,V4002107da,3000210806,;"Target\:";kpi_card_item_current_period_achievement_label,7,V401515975,5e015159cf,;"Period Achievement\:";save_kpi_button_text,7,V4000f0359,39000f038e,;"Save KPI";add_new_user_button_label,7,V4004711bf,42004711fd,;"Add New User";owner_name_hint,7,V4004d13ac,35004d13dd,;"User Name";achieved_value_label,7,V400cd3666,3a00cd369c,;"Achieved\:";hide_master_card,7,V401234d97,3d01234dd0,;"Hide master card";progress_history_title,7,V4002508dc,430025091b,;"Progress History";no_data_for_report,7,V400c333d7,5b00c3342e,;"No data available for the selected criteria.";report_for_kpi_user_format,7,V400c53482,4d00c534cb,;"Report for %1$s - %2$s";aggregation_individual,7,V400ad2dc8,4600ad2e0a,;"Import Individually";current_label,7,V4014e58aa,32014e58d8,;"Current\:";action_change_user_card_color,7,V4015f5d16,50015f5d62,;"Change User Card Color";no_data_to_import,7,V400b33027,4d00b33070,;"No data to import after review.";clear_month_progress_confirmation_title,7,V4017f64ca,48017f650e,;"TODO";user_kpi_list_title,7,V401144a31,3d01144a6a,;"User KPI List";ocr_data_import_failed,7,V4008f2434,4d008f247d,;"Failed to import OCR data.";excel_data_imported_success,7,V4009d286a,59009d28bf,;"Excel data imported successfully.";error_saving_progress,7,V4001e0711,47001e0754,;"Error saving progress";not_applicable_short,7,V400d43836,3400d43866,;"N/A";color_picker_title_summary_card_top,7,V400561526,5600561578,;"Summary Card Top Color";action_delete_user_kpi,7,V401615db8,4d01615e01,;"Delete User KPI Assignment";filter_all,7,V4011d4c39,2a011d4c5f,;"All";kpi_detail_title,7,V4001f075a,38001f078e,;"KPI Details";kpi_quarterly_target_hint,7,V400070171,51000701be,;"Quarterly Target (Optional)";no_kpis_for_this_user,7,V400731ca1,4e00731ceb,;"No KPIs found for this user.";confirm_delete_user_title,7,V400e63c4c,4900e63c91,;"Confirm Delete User";user_summary_card_title_prefix,7,V400691a72,4600691ab4,;"Summary for";task_updated_success,7,V40141556a,47014155ad,;"Task '%1$s' updated.";error_invalid_expiration_date,7,V401a56d1f,5201a56d6d,;"Invalid expiration date.";pref_title_target_achieved_notif,7,V400fd4398,5a00fd43ee,;"Target Achieved Notifications";clear_month_progress_success,7,V401816560,3d01816599,;"TODO";assign_to_kpi_label,7,V400902483,3e009024bd,;"Assign to KPI\:";edit_progress_dialog_title,7,V4016a5fb8,3b016a5fef,;"TODO";error_date_required,7,V4001d06cf,40001d070b,;"Date is required";reminder_option_1_week,7,V4019a6b51,40019a6b8d,;"1 week before";kpi_detail_tab_all_time,7,V400cc3628,3c00cc3660,;"All Time";confirm_delete_task_message,7,V4013d5434,66013d5496,;"Are you sure you want to delete task '%1$s'?";status_due_in_days,7,V4013852a0,49013852e5,;"Status\: Due in %1$d day(s)";report_percentage_label,7,V400c13359,3e00c13393,;"Percentage";google_api_key,210,V400040119,660004017b,;"AIzaSyDemoKeyForKPITrackerApp123456789";error_target_required,7,V4001103d6,4400110416,;"Target is required";required_daily_rate_label,7,V400d237a2,4500d237e3,;"Required Daily\:";kpi_deleted_success,7,V40178631f,340178634f,;"TODO";add_progress_title,7,V40016051f,3b00160556,;"Add Progress";owner_type_other,7,V4003f0f94,32003f0fc2,;"Other";color_reset_success,7,V401675f10,3401675f40,;"TODO";total_monthly_target_label,7,V4006318cb,4f00631916,;"Total Monthly Target\: %s";action_search_edit_progress,7,V4015b5bff,4c015b5c47,;"Search/Edit Progress";value_hint,7,V401274e8b,2c01274eb3,;"Value";label_individual_color,7,V4010b47cd,43010b480c,;"Card Background\:";kpi_expiry_reminder_text_annual,7,V400fa4282,7700fa42f5,;"Annual target for '%1$s' is approaching. Review progress.";ocr_import_failed,7,V40185665a,3201856688,;"TODO";no_target_set_short,7,V400d5386c,3900d538a1,;"No Target";kpi_unit_label,7,V4000a025e,2f000a0289,;"Unit";all_users_report_option,7,V400c634d1,4a00c63517,;"All Users (Aggregated)";pref_summary_expiry_reminder_notif,7,V40101451a,6f01014585,;"Receive reminders for KPIs nearing review dates.";last_entry_date_label,7,V4016b5ff5,36016b6027,;"TODO";notification_channel_name,7,V4005b1678,47005b16bb,;"KPI Notifications";kpi_summary_item_format,7,V4006c1b52,4c006c1b9a,;"%1$s\: M %2$d%%\, Y %3$d%%";pref_key_expiry_reminder_notif,7,V400ff4465,5700ff44b8,;"expiry_reminder_notification";import_failed_with_errors,7,V400b12f51,5a00b12fa7,;"Import failed. %1$d errors occurred.";main_activity_title,7,V400751cf3,3d00751d2c,;"KPI Dashboard";ocr_date_label,7,V4008a22c6,30008a22f2,;"Date\:";annual_progress_label_short,7,V400621885,44006218c5,;"Year\: %1$d%%";performance_report_title,7,V4010f48cd,47010f4910,;"Performance Report";kpi_card_item_last_update_label,7,V401545a71,4801545ab5,;"Last Update\:";search_hint,7,V400300c0d,3600300c3f,;"Search KPIs...";google_crash_reporting_api_key,210,V4000601e7,7600060259,;"AIzaSyDemoKeyForKPITrackerApp123456789";excel_import_title,7,V40098270f,3b00982746,;"Excel Import";report_target_label,7,V400bf32e5,3600bf3317,;"Target";user_deleted_success,7,V400e83d3b,4b00e83d82,;"User deleted successfully.";error_invalid_value,7,V4001c0690,3d001c06c9,;"Invalid value";total_annual_achieved_label,7,V4006619c0,5100661a0d,;"Total Annual Achieved\: %s";ocr_import_title,7,V400852191,37008521c4,;"OCR Import";select_kpi_for_report,7,V400b630b1,3d00b630ea,;"Select KPI\:";save_button,7,V401284eb9,2c01284ee1,;"Save";trend_chart_title,7,V400c23399,3c00c233d1,;"Progress Trend";filter_task_follow_up,7,V401204cd5,4001204d11,;"Task follow-up";create_new_user_dialog_title,7,V40049124d,450049128e,;"Add New User";report_achieved_label,7,V400c0331d,3a00c03353,;"Achieved";kpi_duplication_failed,7,V400f33ffc,4b00f34043,;"Failed to duplicate KPI.";select_month_year_title,7,V400d638a7,4900d638ec,;"Select Month and Year";confirm_delete_ocr_item_message,7,V4009626a1,6a00962707,;"Are you sure you want to delete this OCR item?";total_monthly_achieved_label,7,V40064191c,530064196b,;"Total Monthly Achieved\: %s";pref_title_expiry_reminder_notif,7,V4010044be,5a01004514,;"Expiry Reminder Notifications";progress_date_hint,7,V4001905d2,3300190601,;"Date";error_column_selection,7,V400a52b4d,5c00a52ba5,;"Please select columns for Value and Date.";save_progress_button_text,7,V4001a0607,43001a0646,;"Save Progress";search_edit_progress_title,7,V40111495e,4b011149a5,;"Search/Edit Progress";select_users_button,7,V401184af6,3c01184b2e,;"Select Users";select_owner_image_button_text,7,V400350d2a,4d00350d73,;"Select Owner Image";error_at_least_one_user,7,V400451107,5400451157,;"Please select at least one user.";error_saving_user,7,V400e53c0a,4000e53c46,;"Error saving user.";default_web_client_id,210,V400020037,8f000200c2,;"123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com";user_management_title,7,V400df3a7d,3e00df3ab7,;"Manage Users";unit_number,7,V4000b028f,2e000b02b9,;"Number";menu_manage_users,7,V400dd3a3f,3a00dd3a75,;"Manage Users";preview_data_button,7,V400a42b0f,3c00a42b47,;"Preview Data";select_date_prompt,7,V401896735,3301896764,;"TODO";error_generating_report,7,V400c43434,4c00c4347c,;"Error generating report.";dialog_kpi_action_reset_color,7,V40148570e,450148574f,;"Reset Color";import_ocr_data_button,7,V400882252,3e0088228c,;"Import Data";progress_updated_success,7,V4016d605e,39016d6093,;"TODO";select_image_for_ocr,7,V4008621ca,450086220b,;"Select Image for OCR";user_kpi_list_title_prefix,7,V400721c60,3f00721c9b,;"KPIs for";status_due_today,7,V401375260,3e0137529a,;"Status\: Due Today";no_users_exist,7,V400ef3f10,5400ef3f60,;"No users exist. Add users to assign KPIs.";expiry_return_notification_message,7,V4018e685d,43018e689c,;"TODO";ocr_activity_title,7,V4011249ab,3b011249e2,;"OCR Activity";delete_entry_confirmation_message,7,V401706114,4201706152,;"TODO";action_view_details,7,V400390e3e,3c00390e76,;"View Details";error_kpi_not_found,7,V4001404a2,4a001404e8,;"KPI not found for editing.";delete_progress_entry_menu_item,7,V400290a1a,4800290a5e,;"Delete Entry";dialog_kpi_action_share_card,7,V401465680,43014656bf,;"Share Card";ocr_import_cancelled,7,V40186668e,35018666bf,;"TODO";owner_image_description,7,V401194b34,3f01194b6f,;"Owner Image";error_reading_sheet_names,7,V400a62bab,6000a62c07,;"Error reading sheet names from Excel file.";title_activity_kpi_list,7,V4018b67a4,38018b67d8,;"TODO";google_storage_bucket,210,V40007025e,67000702c1,;"kpi-tracker-app-demo.appspot.com";kpi_name_not_editable_editing,7,V40192697b,60019269d7,;"KPI name cannot be changed during edit";add_task_title,7,V4012c4fa8,37012c4fdb,;"Add New Task";master_card_no_data,7,V401665ebd,5101665f0a,;"No data available for master card";manual_user_input_hint,7,V400411002,4200411040,;"Enter User Name";google_app_id,210,V400050180,66000501e2,;"1\:123456789012\:android\:abcdef1234567890";delete_kpi_confirmation_message,7,V4017762dd,4001776319,;"TODO";remaining_days_label,7,V400d13765,3b00d1379c,;"Days Left\:";reminder_option_on_due_date,7,V401966a48,4301966a87,;"On due date";clear_progress_success,7,V4017d6445,37017d6478,;"TODO";toggle_master_card,7,V401214d17,3f01214d52,;"View master card";reminder_option_1_day,7,V401976a8d,3e01976ac7,;"1 day before";search_kpis_title,7,V400310c45,3900310c7a,;"Search KPIs";confirm_delete_progress_entry_message,7,V4002b0aba,76002b0b2c,;"Are you sure you want to delete this progress entry?";dialog_kpi_action_edit,7,V4014455f6,3b0144562d,;"Edit KPI";color_picker_title_summary_card_bottom,7,V40057157e,5c005715d6,;"Summary Card Bottom Color";kpi_expiry_reminder_title,7,V400f7413e,4800f74182,;"KPI Reminder\: %1$s";confirm_import_button,7,V401254e03,4001254e3f,;"Confirm Import";no_kpis_assigned_for_master_card,7,V400671a13,5b00671a6a,;"No KPIs assigned to users yet.";aggregation_type_label,7,V400ac2d82,4400ac2dc2,;"Aggregation Type\:";menu_import_excel,7,V400d9392e,3f00d93969,;"Import from Excel";filter_all_months,7,V4006e1ba2,38006e1bd6,;"All Months";dialog_ok,7,V4002e0bb1,28002e0bd5,;"OK";ocr_no_entries_found,7,V4018866fe,350188672f,;"TODO";kpi_card_context_menu_title,7,V401064677,48010646bb,;"KPI Card Actions";error_updating_colors,7,V4010d4881,48010d48c5,;"Error updating colors.";confirm_clear_progress_message,7,V4007f1f8f,8a007f2015,;"Are you sure you want to clear all progress entries for this KPI for this user?";task_reminder_days_hint,7,V4012f505e,4a012f50a4,;"Reminder (days before)";error_name_required,7,V400100394,40001003d0,;"Name is required";show_master_card,7,V401224d58,3d01224d91,;"Show master card";my_tasks_title,7,V401325132,3301325161,;"My Tasks";days_since_last_update_label,7,V400d337e9,4b00d33830,;"Days Since Update\:";total_annual_target_label,7,V400651971,4d006519ba,;"Total Annual Target\: %s";action_view_report,7,V4015d5c90,4d015d5cd9,;"Interactive Performance Report";excel_data_import_failed,7,V4009e28c5,51009e2912,;"Failed to import Excel data.";error_copying_image,7,V400360d79,4300360db8,;"Error copying image";error_updating_progress,7,V4018a676a,38018a679e,;"TODO";map_date_column_label,7,V400a22a79,3e00a22ab3,;"Date Column\:";ocr_waiting_for_review,7,V4018766c5,37018766f8,;"TODO";kpi_name_hint,7,V4000400b3,32000400e1,;"KPI Name";no_user_summaries_to_display,7,V4007a1e44,56007a1e96,;"No user summaries to display.";progress_saved_success,7,V4016e6099,37016e60cc,;"TODO";clear_progress_confirmation_title,7,V4017a638a,42017a63c8,;"TODO";ocr_user_label,7,V4008b22f8,3b008b232f,;"User (Optional)\:";select_kpi_for_ocr_hint,7,V4009124c3,3e009124fd,;"Select KPI";error_saving_kpi,7,V400130463,3d0013049c,;"Error saving KPI";target_achieved_notification_text,7,V4005e1786,6b005e17ed,;"You achieved %1$.1f for %2$s (Target\: %3$.1f)";dialog_kpi_action_duplicate_copy,7,V401455633,4b0145567a,;"Duplicate/Copy";kpi_card_item_average_label,7,V401555abb,3f01555af6,;"Average";user_already_selected_error,7,V4004a1294,56004a12e6,;"This user is already selected.";select_sheet_label,7,V400a72c0d,3c00a72c45,;"Select Sheet\:";expiry_return_notification_title,7,V4018d681a,41018d6857,;"TODO";+style:MasterCardButton,211,V4001002ed,c001c056e,;DWidget.MaterialComponents.Button,android\:textSize:12sp,android\:paddingStart:12dp,android\:paddingEnd:12dp,android\:paddingTop:8dp,android\:paddingBottom:8dp,android\:minHeight:40dp,cornerRadius:20dp,icon:@drawable/ic_master_card_24,iconGravity:textStart,iconPadding:8dp,iconSize:18dp,;CustomFilterChip,211,V40003006d,c000d02b2,;DWidget.MaterialComponents.Chip.Filter,android\:textSize:12sp,android\:textColor:@color/chip_text_color,chipIconSize:18dp,chipIconVisible:true,chipMinHeight:40dp,chipMinTouchTargetSize:40dp,chipStartPadding:8dp,chipEndPadding:8dp,chipBackgroundColor:@color/chip_background_color,;Widget.App.Chip.Advanced,211,V4001f05a6,c00290807,;DWidget.MaterialComponents.Chip.Choice,android\:textSize:14sp,android\:textColor:@color/chip_text_selector,chipMinHeight:40dp,chipStartPadding:12dp,chipEndPadding:12dp,chipCornerRadius:20dp,chipBackgroundColor:@color/chip_background_selector,chipStrokeWidth:1dp,chipStrokeColor:@color/purple_accent,;Theme.KPITrackerApp,212,V400020066,c0011038e,;DTheme.MaterialComponents.DayNight.DarkActionBar,colorPrimary:@color/purple_500,colorPrimaryVariant:@color/purple_700,colorOnPrimary:@color/white,colorSecondary:@color/teal_200,colorSecondaryVariant:@color/teal_700,colorOnSecondary:@color/black,chipStyle:@style/Widget.App.Chip,android\:statusBarColor:?attr/colorPrimaryVariant,;Widget.App.Chip,212,V40026073c,c002f0990,;DWidget.MaterialComponents.Chip.Filter,android\:textSize:14sp,android\:textColor:@color/chip_text_selector,chipBackgroundColor:@color/chip_background_selector,chipStrokeColor:@color/purple_500,chipStrokeWidth:1dp,chipCornerRadius:16dp,chipIconTint:@color/chip_text_selector,closeIconTint:@color/chip_text_selector,;Theme.KPITrackerApp.NoActionBar,212,V4001403e1,c00230713,;DTheme.MaterialComponents.DayNight.NoActionBar,colorPrimary:@color/purple_500,colorPrimaryVariant:@color/purple_700,colorOnPrimary:@color/white,colorSecondary:@color/teal_200,colorSecondaryVariant:@color/teal_700,colorOnSecondary:@color/black,chipStyle:@style/Widget.App.Chip,android\:statusBarColor:?attr/colorPrimaryVariant,;+xml:file_paths,213,F;network_security_config,214,F;data_extraction_rules,215,F;backup_rules,216,F;