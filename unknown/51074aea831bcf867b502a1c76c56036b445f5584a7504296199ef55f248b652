<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:id="@android:id/background">
        <shape
            android:innerRadiusRatio="3"
            android:shape="ring"
            android:thicknessRatio="8"
            android:useLevel="false">
            <solid android:color="#E5E7EB" />
        </shape>
    </item>
    <item android:id="@android:id/progress">
        <rotate
            android:fromDegrees="-90"
            android:toDegrees="-90">
            <shape
                android:innerRadiusRatio="3"
                android:shape="ring"
                android:thicknessRatio="8"
                android:useLevel="true">
                <gradient
                    android:startColor="#10B981"
                    android:endColor="#059669"
                    android:type="sweep" />
            </shape>
        </rotate>
    </item>
</layer-list>
