package com.example.kpitrackerapp.fragments

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.example.kpitrackerapp.databinding.FragmentPerformanceBinding
import com.example.kpitrackerapp.ui.ModernReportActivity
import com.example.kpitrackerapp.ui.ExpireManagementActivity
import com.example.kpitrackerapp.ui.TaskManagementActivity
import com.example.kpitrackerapp.ui.DateConverterActivity

class PerformanceFragment : Fragment() {

    private var _binding: FragmentPerformanceBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPerformanceBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupPerformanceOptions()
    }

    private fun setupPerformanceOptions() {
        binding.cardInteractiveReport.setOnClickListener {
            startActivity(Intent(requireContext(), ModernReportActivity::class.java))
        }

        binding.cardExpiryManagement.setOnClickListener {
            startActivity(Intent(requireContext(), ExpireManagementActivity::class.java))
        }

        binding.cardTaskFollowUp.setOnClickListener {
            startActivity(Intent(requireContext(), TaskManagementActivity::class.java))
        }

        binding.cardDateConverter.setOnClickListener {
            startActivity(Intent(requireContext(), DateConverterActivity::class.java))
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
