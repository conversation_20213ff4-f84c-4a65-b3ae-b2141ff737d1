<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F8F9FA"
    tools:context=".ui.DateConverterActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:elevation="0dp"
        app:elevation="0dp">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="محول التاريخ"
            app:titleTextColor="#1A1A1A"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:navigationIconTint="#1A1A1A" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">



            <!-- Shift Calculator Section (Initially Hidden) -->
            <LinearLayout
                android:id="@+id/shiftCalculatorSection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/card_background"
                android:padding="16dp"
                android:layout_marginBottom="20dp"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="shift hour calculator"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#1A1A1A"
                    android:layout_marginBottom="16dp"
                    android:gravity="center"
                    android:layout_gravity="center" />

                <!-- Hours Input -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="عدد الساعات :"
                        android:textSize="14sp"
                        android:textColor="#1A1A1A"
                        android:layout_marginEnd="12dp" />

                    <EditText
                        android:id="@+id/hoursInput"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:background="@drawable/input_background"
                        android:hint="مثال: 9"
                        android:inputType="number"
                        android:textAlignment="center"
                        android:textSize="16sp"
                        android:padding="12dp" />

                </LinearLayout>

                <!-- Start Time Input -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="وقت البداية :"
                        android:textSize="14sp"
                        android:textColor="#1A1A1A"
                        android:layout_marginEnd="12dp" />

                    <EditText
                        android:id="@+id/startTimeInput"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:background="@drawable/input_background"
                        android:hint="مثال: 5:00"
                        android:inputType="time"
                        android:textAlignment="center"
                        android:textSize="16sp"
                        android:padding="12dp" />

                    <Spinner
                        android:id="@+id/amPmSpinner"
                        android:layout_width="wrap_content"
                        android:layout_height="48dp"
                        android:background="@drawable/spinner_background"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

                <!-- Calculate Button -->
                <Button
                    android:id="@+id/calculateShiftButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="احسب الوردية"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/white"
                    android:background="@drawable/button_background"
                    android:layout_marginBottom="16dp" />

                <!-- Result Section -->
                <LinearLayout
                    android:id="@+id/shiftResultSection"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="@drawable/result_background"
                    android:padding="16dp"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="نتيجة الحساب"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#1A1A1A"
                        android:layout_marginBottom="12dp"
                        android:layout_gravity="center" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="وقت البداية"
                            android:textSize="14sp"
                            android:textColor="#666666" />

                        <TextView
                            android:id="@+id/resultStartTime"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:textSize="14sp"
                            android:textColor="#1A1A1A"
                            android:textAlignment="textEnd" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="وقت النهاية"
                            android:textSize="14sp"
                            android:textColor="#666666" />

                        <TextView
                            android:id="@+id/resultEndTime"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:textSize="14sp"
                            android:textColor="#1A1A1A"
                            android:textAlignment="textEnd" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="إجمالي الساعات"
                            android:textSize="14sp"
                            android:textColor="#666666" />

                        <TextView
                            android:id="@+id/resultTotalHours"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:textSize="14sp"
                            android:textColor="#1A1A1A"
                            android:textStyle="bold"
                            android:textAlignment="textEnd" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <!-- Conversion Type Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="نوع التحويل :"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#1A1A1A"
                android:layout_marginBottom="16dp" />

            <!-- Radio Group for Conversion Type -->
            <RadioGroup
                android:id="@+id/conversionTypeGroup"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="24dp"
                android:gravity="center">

                <RadioButton
                    android:id="@+id/radioMiladiToHijri"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="ميلادي الى هجري"
                    android:textSize="14sp"
                    android:textColor="#1F2937"
                    android:checked="true"
                    android:buttonTint="#3B82F6"
                    android:layout_marginEnd="30dp" />

                <RadioButton
                    android:id="@+id/radioHijriToMiladi"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="هجري الى ميلادي"
                    android:textSize="14sp"
                    android:textColor="#1F2937"
                    android:checked="false"
                    android:buttonTint="#3B82F6" />

            </RadioGroup>





            <!-- Date Input Fields -->
            <LinearLayout
                android:id="@+id/inputModeLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="20dp"
                android:visibility="visible">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="6dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="اليوم"
                        android:textSize="12sp"
                        android:textColor="#6B7280"
                        android:gravity="center"
                        android:layout_marginBottom="4dp" />

                    <EditText
                        android:id="@+id/dayInput"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:inputType="number"
                        android:maxLength="2"
                        android:textAlignment="center"
                        android:textSize="16sp"
                        android:background="@drawable/card_background"
                        android:padding="12dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="3dp"
                    android:layout_marginEnd="3dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="الشهر"
                        android:textSize="12sp"
                        android:textColor="#6B7280"
                        android:gravity="center"
                        android:layout_marginBottom="4dp" />

                    <EditText
                        android:id="@+id/monthInput"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:inputType="number"
                        android:maxLength="2"
                        android:textAlignment="center"
                        android:textSize="16sp"
                        android:background="@drawable/card_background"
                        android:padding="12dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="6dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="السنة"
                        android:textSize="12sp"
                        android:textColor="#6B7280"
                        android:gravity="center"
                        android:layout_marginBottom="4dp" />

                    <EditText
                        android:id="@+id/yearInput"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:inputType="number"
                        android:maxLength="4"
                        android:textAlignment="center"
                        android:textSize="16sp"
                        android:background="@drawable/card_background"
                        android:padding="12dp" />

                </LinearLayout>

            </LinearLayout>

            <!-- Convert Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/convertButton"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:text="تحويل التاريخ"
                android:textSize="15sp"
                android:textStyle="bold"
                android:textColor="@color/white"
                app:cornerRadius="16dp"
                app:backgroundTint="#4A90A4"
                android:layout_marginBottom="24dp" />

            <!-- Results Table -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="12dp">

                    <!-- Table Rows -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="10dp"
                        android:background="#F8F9FA">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="التاريخ بالميلادي"
                            android:textSize="13sp"
                            android:textStyle="bold"
                            android:textColor="#1A1A1A"
                            android:gravity="end" />

                        <TextView
                            android:id="@+id/resultMiladiDate"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#6B7280"
                            android:gravity="start"
                            android:layout_marginStart="16dp" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E5E7EB" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="التاريخ بالهجري"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="#1A1A1A"
                            android:gravity="end" />

                        <TextView
                            android:id="@+id/resultHijriDate"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#6B7280"
                            android:gravity="start"
                            android:layout_marginStart="16dp" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E5E7EB" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp"
                        android:background="#F8F9FA">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="اليوم"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="#1A1A1A"
                            android:gravity="end" />

                        <TextView
                            android:id="@+id/resultDayName"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#6B7280"
                            android:gravity="start"
                            android:layout_marginStart="16dp" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E5E7EB" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="الشهر بالهجري"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="#1A1A1A"
                            android:gravity="end" />

                        <TextView
                            android:id="@+id/resultHijriMonth"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#6B7280"
                            android:gravity="start"
                            android:layout_marginStart="16dp" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E5E7EB" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp"
                        android:background="#F8F9FA">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="الشهر بالميلادي"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="#1A1A1A"
                            android:gravity="end" />

                        <TextView
                            android:id="@+id/resultMiladiMonth"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#6B7280"
                            android:gravity="start"
                            android:layout_marginStart="16dp" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E5E7EB" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="الشهر بالسرياني"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="#1A1A1A"
                            android:gravity="end" />

                        <TextView
                            android:id="@+id/resultSyrianiMonth"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#6B7280"
                            android:gravity="start"
                            android:layout_marginStart="16dp" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E5E7EB" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp"
                        android:background="#F8F9FA">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="عدد أيام الشهر بالهجري"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="#1A1A1A"
                            android:gravity="end" />

                        <TextView
                            android:id="@+id/resultHijriMonthDays"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#6B7280"
                            android:gravity="start"
                            android:layout_marginStart="16dp" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E5E7EB" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="عدد أيام الشهر بالميلادي"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="#1A1A1A"
                            android:gravity="end" />

                        <TextView
                            android:id="@+id/resultMiladiMonthDays"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#6B7280"
                            android:gravity="start"
                            android:layout_marginStart="16dp" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
