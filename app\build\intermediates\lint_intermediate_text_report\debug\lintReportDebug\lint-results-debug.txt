D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\NotificationManager.kt:104: Error: Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with checkPermission) or explicitly handle a potential SecurityException [MissingPermission]
        NotificationManagerCompat.from(context).notify(getNextNotificationId(), notification)
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\NotificationManager.kt:139: Error: Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with checkPermission) or explicitly handle a potential SecurityException [MissingPermission]
        NotificationManagerCompat.from(context).notify(getNextNotificationId(), notification)
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\NotificationManager.kt:163: Error: Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with checkPermission) or explicitly handle a potential SecurityException [MissingPermission]
        NotificationManagerCompat.from(context).notify(getNextNotificationId(), notification)
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\NotificationManager.kt:185: Error: Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with checkPermission) or explicitly handle a potential SecurityException [MissingPermission]
        NotificationManagerCompat.from(context).notify(getNextNotificationId(), notification)
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "MissingPermission":
   This check scans through your code and libraries and looks at the APIs
   being used, and checks this against the set of permissions required to
   access those APIs. If the code using those APIs is called at runtime, then
   the program will crash.

   Furthermore, for permissions that are revocable (with targetSdkVersion 23),
   client code must also be prepared to handle the calls throwing an exception
   if the user rejects the request for permission at runtime.

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\AddEditProgressDialogFragment.kt:102: Warning: Use of LayoutInflater.from(Context) detected. Consider using layoutInflater instead [UseGetLayoutInflater from androidx.fragment]
        val inflater = LayoutInflater.from(requireContext())
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseGetLayoutInflater":
   Using LayoutInflater.from(Context) can return a LayoutInflater             
       that does not have the correct theme.

   Vendor: Android Open Source Project
   Identifier: androidx.fragment
   Feedback: https://issuetracker.google.com/issues/new?component=460964

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\viewmodels\KpiViewModel.kt:193: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    String.format("%02d", calculationMonth)
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\viewmodels\KpiViewModel.kt:202: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    String.format("%02d", today.get(Calendar.MONTH) + 1)
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\viewmodels\KpiViewModel.kt:1019: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                String.format("%02d", currentMonth)
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\viewmodels\KpiViewModel.kt:1084: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        val monthStr = String.format("%02d", month) // Ensure month is two digits (e.g., "01", "07")
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\viewmodels\KpiViewModel.kt:1109: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        val monthStr = String.format("%02d", month)
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_task_enhanced.xml:152: Error: This view is not constrained. It only has designtime positions, so it will jump to (0,0) at runtime unless you add the constraints [MissingConstraints]
        <TextView
         ~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_task_enhanced.xml:158: Error: This view is not constrained. It only has designtime positions, so it will jump to (0,0) at runtime unless you add the constraints [MissingConstraints]
        <androidx.recyclerview.widget.RecyclerView
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_task_enhanced.xml:164: Error: This view is not constrained. It only has designtime positions, so it will jump to (0,0) at runtime unless you add the constraints [MissingConstraints]
        <LinearLayout
         ~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_task_enhanced.xml:199: Error: This view is not constrained. It only has designtime positions, so it will jump to (0,0) at runtime unless you add the constraints [MissingConstraints]
        <com.google.android.material.chip.ChipGroup
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_task_enhanced.xml:205: Error: This view is not constrained. It only has designtime positions, so it will jump to (0,0) at runtime unless you add the constraints [MissingConstraints]
        <LinearLayout
         ~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_task_enhanced.xml:235: Error: This view is not constrained. It only has designtime positions, so it will jump to (0,0) at runtime unless you add the constraints [MissingConstraints]
        <LinearLayout
         ~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_task_enhanced.xml:258: Error: This view is not constrained. It only has designtime positions, so it will jump to (0,0) at runtime unless you add the constraints [MissingConstraints]
        <View
         ~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_task_enhanced.xml:264: Error: This view is not constrained. It only has designtime positions, so it will jump to (0,0) at runtime unless you add the constraints [MissingConstraints]
        <View
         ~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_task_enhanced.xml:270: Error: This view is not constrained. It only has designtime positions, so it will jump to (0,0) at runtime unless you add the constraints [MissingConstraints]
        <CheckBox
         ~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_task_enhanced.xml:276: Error: This view is not constrained. It only has designtime positions, so it will jump to (0,0) at runtime unless you add the constraints [MissingConstraints]
        <com.google.android.material.chip.Chip
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "MissingConstraints":
   The layout editor allows you to place widgets anywhere on the canvas, and
   it records the current position with designtime attributes (such as
   layout_editor_absoluteX). These attributes are not applied at runtime, so
   if you push your layout on a device, the widgets may appear in a different
   location than shown in the editor. To fix this, make sure a widget has both
   horizontal and vertical constraints by dragging from the edge connections.

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build.gradle.kts:16: Warning: Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details. [OldTargetApi]
        targetSdk = 34
        ~~~~~~~~~~~~~~

   Explanation for issues of type "OldTargetApi":
   When your application or sdk runs on a version of Android that is more
   recent than your targetSdkVersion specifies that it has been tested with,
   various compatibility modes kick in. This ensures that your application
   continues to work, but it may look out of place. For example, if the
   targetSdkVersion is less than 14, your app may get an option button in the
   UI.

   To fix this issue, set the targetSdkVersion to the highest available value.
   Then test your app to make sure everything works correctly. You may want to
   consult the compatibility notes to see what changes apply to each version
   you are adding support for:
   https://developer.android.com/reference/android/os/Build.VERSION_CODES.html
   as well as follow this guide:
   https://developer.android.com/distribute/best-practices/develop/target-sdk.
   html

   https://developer.android.com/distribute/best-practices/develop/target-sdk.html

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\LanguageManager.kt:50: Warning: Found dynamic locale changes, but did not find corresponding Play Core library calls for downloading languages and splitting by language is not disabled in the bundle configuration [AppBundleLocaleChanges]
        config.setLocale(locale)
        ~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "AppBundleLocaleChanges":
   When changing locales at runtime (e.g. to provide an in-app language
   switcher), the Android App Bundle must be configured to not split by locale
   or the Play Core library must be used to download additional locales at
   runtime.

   https://developer.android.com/guide/app-bundle/configure-base#handling_language_changes

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\UserKpiListActivity.kt:204: Warning: Avoid passing null as the view root (needed to resolve layout parameters on the inflated layout's root element) [InflateParams]
        val sheetView = LayoutInflater.from(this).inflate(R.layout.dialog_kpi_actions, null)
                                                                                       ~~~~

   Explanation for issues of type "InflateParams":
   When inflating a layout, avoid passing in null as the parent view, since
   otherwise any layout parameters on the root of the inflated layout will be
   ignored.

   https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\AppDetectionHelper.kt:113: Warning: As of Android 11, this method no longer returns information about all apps; see https://g.co/dev/packagevisibility for details [QueryPermissionsNeeded]
                val installedPackages = packageManager.getInstalledPackages(0)
                                                       ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\AppDetectionHelper.kt:283: Warning: As of Android 11, this method no longer returns information about all apps; see https://g.co/dev/packagevisibility for details [QueryPermissionsNeeded]
                val installedPackages = packageManager.getInstalledPackages(0)
                                                       ~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "QueryPermissionsNeeded":
   Apps that target Android 11 cannot query or interact with other installed
   apps by default. If you need to query or interact with other installed
   apps, you may need to add a <queries> declaration in your manifest.

   As a corollary, the methods PackageManager#getInstalledPackages and
   PackageManager#getInstalledApplications will no longer return information
   about all installed apps. To query specific apps or types of apps, you can
   use methods like PackageManager#getPackageInfo or
   PackageManager#queryIntentActivities.

   https://g.co/dev/packagevisibility

D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.9.1 is available: 8.11.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.11.0 is difficult: 8.9.3) [AndroidGradlePluginVersion]
agp = "8.9.1" # Android Gradle Plugin version (Downgraded to stable)
      ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.9.1 is available: 8.11.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.11.0 is difficult: 8.9.3) [AndroidGradlePluginVersion]
agp = "8.9.1" # Android Gradle Plugin version (Downgraded to stable)
      ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.9.1 is available: 8.11.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.11.0 is difficult: 8.9.3) [AndroidGradlePluginVersion]
agp = "8.9.1" # Android Gradle Plugin version (Downgraded to stable)
      ~~~~~~~

   Explanation for issues of type "AndroidGradlePluginVersion":
   This detector looks for usage of the Android Gradle Plugin where the
   version you are using is not the current stable release. Using older
   versions is fine, and there are cases where you deliberately want to stick
   with an older version. However, you may simply not be aware that a more
   recent version is available, and that is what this lint check helps find.

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build.gradle.kts:11: Warning: A newer version of compileSdkVersion than 34 is available: 35 [GradleDependency]
    compileSdk = 34
    ~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build.gradle.kts:116: Warning: A newer version of com.google.android.gms:play-services-mlkit-text-recognition than 19.0.0 is available: 19.0.1 [GradleDependency]
    implementation("com.google.android.gms:play-services-mlkit-text-recognition:19.0.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build.gradle.kts:143: Warning: A newer version of com.google.firebase:firebase-bom than 32.7.0 is available: 33.16.0 [GradleDependency]
    implementation(platform("com.google.firebase:firebase-bom:32.7.0"))
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0 [GradleDependency]
coreKtx = "1.12.0"
          ~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0 [GradleDependency]
coreKtx = "1.12.0"
          ~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0 [GradleDependency]
coreKtx = "1.12.0"
          ~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:8: Warning: A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1 [GradleDependency]
appcompat = "1.6.1"
            ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:8: Warning: A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1 [GradleDependency]
appcompat = "1.6.1"
            ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:8: Warning: A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1 [GradleDependency]
appcompat = "1.6.1"
            ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:9: Warning: A newer version of com.google.android.material:material than 1.11.0 is available: 1.12.0 [GradleDependency]
material = "1.11.0"
           ~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:9: Warning: A newer version of com.google.android.material:material than 1.11.0 is available: 1.12.0 [GradleDependency]
material = "1.11.0"
           ~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:9: Warning: A newer version of com.google.android.material:material than 1.11.0 is available: 1.12.0 [GradleDependency]
material = "1.11.0"
           ~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:10: Warning: A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1 [GradleDependency]
constraintlayout = "2.1.4"
                   ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:10: Warning: A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1 [GradleDependency]
constraintlayout = "2.1.4"
                   ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:10: Warning: A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1 [GradleDependency]
constraintlayout = "2.1.4"
                   ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:11: Warning: A newer version of androidx.activity:activity-ktx than 1.8.2 is available: 1.10.1 [GradleDependency]
activity = "1.8.2" # For Activity KTX
           ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:11: Warning: A newer version of androidx.activity:activity-ktx than 1.8.2 is available: 1.10.1 [GradleDependency]
activity = "1.8.2" # For Activity KTX
           ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:11: Warning: A newer version of androidx.activity:activity-ktx than 1.8.2 is available: 1.10.1 [GradleDependency]
activity = "1.8.2" # For Activity KTX
           ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:12: Warning: A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.2 [GradleDependency]
room = "2.6.1" # Room version
       ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:12: Warning: A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.2 [GradleDependency]
room = "2.6.1" # Room version
       ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:12: Warning: A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.2 [GradleDependency]
room = "2.6.1" # Room version
       ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:12: Warning: A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.2 [GradleDependency]
room = "2.6.1" # Room version
       ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:12: Warning: A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.2 [GradleDependency]
room = "2.6.1" # Room version
       ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:12: Warning: A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.2 [GradleDependency]
room = "2.6.1" # Room version
       ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:12: Warning: A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.2 [GradleDependency]
room = "2.6.1" # Room version
       ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:12: Warning: A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.2 [GradleDependency]
room = "2.6.1" # Room version
       ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:12: Warning: A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.2 [GradleDependency]
room = "2.6.1" # Room version
       ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:14: Warning: A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.7.0 is available: 2.9.1 [GradleDependency]
lifecycle = "2.7.0" # Lifecycle components (ViewModel, LiveData)
            ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:14: Warning: A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.7.0 is available: 2.9.1 [GradleDependency]
lifecycle = "2.7.0" # Lifecycle components (ViewModel, LiveData)
            ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:14: Warning: A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.7.0 is available: 2.9.1 [GradleDependency]
lifecycle = "2.7.0" # Lifecycle components (ViewModel, LiveData)
            ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:14: Warning: A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.7.0 is available: 2.9.1 [GradleDependency]
lifecycle = "2.7.0" # Lifecycle components (ViewModel, LiveData)
            ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:14: Warning: A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.7.0 is available: 2.9.1 [GradleDependency]
lifecycle = "2.7.0" # Lifecycle components (ViewModel, LiveData)
            ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:14: Warning: A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.7.0 is available: 2.9.1 [GradleDependency]
lifecycle = "2.7.0" # Lifecycle components (ViewModel, LiveData)
            ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:15: Warning: A newer version of androidx.recyclerview:recyclerview than 1.3.2 is available: 1.4.0 [GradleDependency]
recyclerview = "1.3.2" # RecyclerView version
               ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:15: Warning: A newer version of androidx.recyclerview:recyclerview than 1.3.2 is available: 1.4.0 [GradleDependency]
recyclerview = "1.3.2" # RecyclerView version
               ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:15: Warning: A newer version of androidx.recyclerview:recyclerview than 1.3.2 is available: 1.4.0 [GradleDependency]
recyclerview = "1.3.2" # RecyclerView version
               ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:18: Warning: A newer version of androidx.work:work-runtime-ktx than 2.9.0 is available: 2.10.2 [GradleDependency]
workmanager = "2.9.0" # WorkManager version
              ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:18: Warning: A newer version of androidx.work:work-runtime-ktx than 2.9.0 is available: 2.10.2 [GradleDependency]
workmanager = "2.9.0" # WorkManager version
              ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:18: Warning: A newer version of androidx.work:work-runtime-ktx than 2.9.0 is available: 2.10.2 [GradleDependency]
workmanager = "2.9.0" # WorkManager version
              ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:20: Warning: A newer version of androidx.fragment:fragment-ktx than 1.6.2 is available: 1.8.8 [GradleDependency]
fragmentKtx = "1.6.2" # Added Fragment KTX version
              ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:20: Warning: A newer version of androidx.fragment:fragment-ktx than 1.6.2 is available: 1.8.8 [GradleDependency]
fragmentKtx = "1.6.2" # Added Fragment KTX version
              ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\gradle\libs.versions.toml:20: Warning: A newer version of androidx.fragment:fragment-ktx than 1.6.2 is available: 1.8.8 [GradleDependency]
fragmentKtx = "1.6.2" # Added Fragment KTX version
              ~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\user_summary_context_menu.xml:14: Error: Unexpected text found in menu file: "// TODO: Use string resource" [ExtraText]
        android:title="Export User Data (CSV)" /> // TODO: Use string resource
                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ExtraText":
   Non-value resource files should only contain elements and attributes. Any
   XML text content found in the file is likely accidental (and potentially
   dangerous if the text resembles XML and the developer believes the text to
   be functional).

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:342: Warning: Use app:drawableEndCompat instead of android:drawableEnd [UseCompatTextViewDrawableXml from androidx.appcompat]
                        android:drawableEnd="@drawable/ic_baseline_keyboard_arrow_up_24"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:343: Warning: Use app:drawableTint instead of android:drawableTint [UseCompatTextViewDrawableXml from androidx.appcompat]
                        android:drawableTint="@color/purple_accent" />
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:460: Warning: Use app:drawableStartCompat instead of android:drawableStart [UseCompatTextViewDrawableXml from androidx.appcompat]
                            android:drawableStart="@drawable/ic_baseline_priority_high_24"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:461: Warning: Use app:drawableTint instead of android:drawableTint [UseCompatTextViewDrawableXml from androidx.appcompat]
                            android:drawableTint="@color/purple_accent"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:511: Warning: Use app:drawableStartCompat instead of android:drawableStart [UseCompatTextViewDrawableXml from androidx.appcompat]
                            android:drawableStart="@drawable/ic_baseline_category_24"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:512: Warning: Use app:drawableTint instead of android:drawableTint [UseCompatTextViewDrawableXml from androidx.appcompat]
                            android:drawableTint="@color/purple_accent"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:599: Warning: Use app:drawableEndCompat instead of android:drawableEnd [UseCompatTextViewDrawableXml from androidx.appcompat]
                        android:drawableEnd="@drawable/ic_baseline_keyboard_arrow_down_24"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:600: Warning: Use app:drawableTint instead of android:drawableTint [UseCompatTextViewDrawableXml from androidx.appcompat]
                        android:drawableTint="@color/purple_accent" />
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:795: Warning: Use app:drawableEndCompat instead of android:drawableEnd [UseCompatTextViewDrawableXml from androidx.appcompat]
                        android:drawableEnd="@drawable/ic_baseline_keyboard_arrow_down_24"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:796: Warning: Use app:drawableTint instead of android:drawableTint [UseCompatTextViewDrawableXml from androidx.appcompat]
                        android:drawableTint="@color/purple_accent" />
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_task.xml:47: Warning: Use app:drawableStartCompat instead of android:drawableStart [UseCompatTextViewDrawableXml from androidx.appcompat]
            android:drawableStart="@drawable/ic_baseline_access_time_24"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseCompatTextViewDrawableXml":
   TextView uses android: compound drawable attributes instead of app: ones

   Vendor: Android Open Source Project
   Identifier: androidx.appcompat
   Feedback: https://issuetracker.google.com/issues/new?component=460343

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\AddEditProgressDialogFragment.kt:221: Error: Format string 'last_entry_date_label' is not a valid format string so it should not be passed to String.format [StringFormatInvalid]
                dialogLastEntryDateTextView.text = getString(R.string.last_entry_date_label, formattedDate)
                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:364: This definition does not require arguments
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\AddEditProgressDialogFragment.kt:221: Error: Format string 'last_entry_date_label' is not a valid format string so it should not be passed to String.format [StringFormatInvalid]
                dialogLastEntryDateTextView.text = getString(R.string.last_entry_date_label, formattedDate)
                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:364: This definition does not require arguments
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\KpiDetailActivity.kt:209: Error: Format string 'last_entry_date_label' is not a valid format string so it should not be passed to String.format [StringFormatInvalid]
                        lastEntryDateTextView.text = getString(R.string.last_entry_date_label, formattedDate)
                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:364: This definition does not require arguments
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\KpiDetailActivity.kt:209: Error: Format string 'last_entry_date_label' is not a valid format string so it should not be passed to String.format [StringFormatInvalid]
                        lastEntryDateTextView.text = getString(R.string.last_entry_date_label, formattedDate)
                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:364: This definition does not require arguments
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\KpiDetailActivity.kt:596: Error: Format string 'clear_month_progress_confirmation_title' is not a valid format string so it should not be passed to String.format [StringFormatInvalid]
                .setTitle(getString(R.string.clear_month_progress_confirmation_title, monthName))
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:384: This definition does not require arguments
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\KpiDetailActivity.kt:597: Error: Format string 'clear_month_progress_confirmation_message' is not a valid format string so it should not be passed to String.format [StringFormatInvalid]
                .setMessage(getString(R.string.clear_month_progress_confirmation_message, monthName))
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:385: This definition does not require arguments
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\KpiDetailActivity.kt:621: Error: Format string 'clear_month_progress_success' is not a valid format string so it should not be passed to String.format [StringFormatInvalid]
                        Toast.makeText(this@KpiDetailActivity, getString(R.string.clear_month_progress_success, monthName), Toast.LENGTH_SHORT).show()
                                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:386: This definition does not require arguments
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\KpiDetailActivity.kt:624: Error: Format string 'error_clearing_month_progress' is not a valid format string so it should not be passed to String.format [StringFormatInvalid]
                        Toast.makeText(this@KpiDetailActivity, getString(R.string.error_clearing_month_progress, monthName), Toast.LENGTH_LONG).show()
                                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:387: This definition does not require arguments
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\util\NotificationHelper.kt:120: Error: Format string 'expiry_return_notification_message' is not a valid format string so it should not be passed to String.format [StringFormatInvalid]
        val notificationText = context.getString(R.string.expiry_return_notification_message, monthName)
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:399: This definition does not require arguments
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\util\NotificationHelper.kt:153: Error: Format string 'near_expiry_notification_message' is not a valid format string so it should not be passed to String.format [StringFormatInvalid]
        val notificationText = context.getString(R.string.near_expiry_notification_message, currentMonth, nextMonth)
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:401: This definition does not require arguments
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\UserKpiListActivity.kt:382: Error: Format string 'kpi_duplicated_success' is not a valid format string so it should not be passed to String.format [StringFormatInvalid]
        Toast.makeText(this, getString(R.string.kpi_duplicated_success, kpi.name), Toast.LENGTH_SHORT).show()
                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:243: This definition does not require arguments

   Explanation for issues of type "StringFormatInvalid":
   If a string contains a '%' character, then the string may be a formatting
   string which will be passed to String.format from Java code to replace each
   '%' occurrence with specific values.

   This lint warning checks for two related problems:
   (1) Formatting strings that are invalid, meaning that String.format will
   throw exceptions at runtime when attempting to use the format string.
   (2) Strings containing '%' that are not formatting strings getting passed
   to a String.format call. In this case the '%' will need to be escaped as
   '%%'.

   NOTE: Not all Strings which look like formatting strings are intended for
   use by String.format; for example, they may contain date formats intended
   for android.text.format.Time#format(). Lint cannot always figure out that a
   String is a date format, so you may get false warnings in those scenarios.
   See the suppress help topic for information on how to suppress errors in
   that case.

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:177: Warning: Formatting %d followed by words ("out"): This should probably be a plural rather than a string [PluralsCandidate]
    <string name="import_successful_count">Imported %1$d out of %2$d entries.</string>
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:178: Warning: Formatting %d followed by words ("errors"): This should probably be a plural rather than a string [PluralsCandidate]
    <string name="import_failed_with_errors">Import failed. %1$d errors occurred.</string>
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:179: Warning: Formatting %d followed by words ("entries"): This should probably be a plural rather than a string [PluralsCandidate]
    <string name="import_partially_successful">Import partially successful. %1$d entries imported, %2$d errors.</string>
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:313: Warning: Formatting %d followed by words ("day"): This should probably be a plural rather than a string [PluralsCandidate]
    <string name="status_due_in_days">Status: Due in %1$d day(s)</string>
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "PluralsCandidate":
   This lint check looks for potential errors in internationalization where
   you have translated a message which involves a quantity and it looks like
   other parts of the string may need grammatical changes.

   For example, rather than something like this:
     <string name="try_again">Try again in %d seconds.</string>
   you should be using a plural:
      <plurals name="try_again">
           <item quantity="one">Try again in %d second</item>
           <item quantity="other">Try again in %d seconds</item>
       </plurals>
   This will ensure that in other languages the right set of translations are
   provided for the different quantity classes.

   (This check depends on some heuristics, so it may not accurately determine
   whether a string really should be a quantity. You can use tools:ignore to
   filter out false positives.

   https://developer.android.com/guide/topics/resources/string-resource.html#Plurals

C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.poi\poi-ooxml\5.2.5\df9f2c52371eeba24db8ea8cafa77285c3cc0742\poi-ooxml-5.2.5.jar: Warning: checkClientTrusted is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers [TrustAllX509TrustManager]
C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.poi\poi-ooxml\5.2.5\df9f2c52371eeba24db8ea8cafa77285c3cc0742\poi-ooxml-5.2.5.jar: Warning: checkServerTrusted is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers [TrustAllX509TrustManager]

   Explanation for issues of type "TrustAllX509TrustManager":
   This check looks for X509TrustManager implementations whose
   checkServerTrusted or checkClientTrusted methods do nothing (thus trusting
   any certificate chain) which could result in insecure network traffic
   caused by trusting arbitrary TLS/SSL certificates presented by peers.

   https://goo.gle/TrustAllX509TrustManager

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\adapters\AdminDashboardAdapter.kt:28: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged()
        ~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NotifyDataSetChanged":
   The RecyclerView adapter's onNotifyDataSetChanged method does not specify
   what about the data set has changed, forcing any observers to assume that
   all existing items and structure may no longer be valid. `LayoutManager`s
   will be forced to fully rebind and relayout all visible views.

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\services\FirebaseMessagingService.kt:42: Warning: Unnecessary; SDK_INT is always >= 26 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\util\NotificationHelper.kt:36: Warning: Unnecessary; SDK_INT is always >= 26 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\NotificationManager.kt:27: Warning: Unnecessary; SDK_INT is always >= 26 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\workers\TaskReminderWorker.kt:56: Warning: Unnecessary; SDK_INT is always >= 26 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\workers\TaskReminderWorker.kt:151: Warning: Unnecessary; SDK_INT is always >= 26 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\workers\TaskReminderWorker.kt:178: Warning: Unnecessary; SDK_INT is always >= 26 [ObsoleteSdkInt]
        val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\FirebaseMessageManager.kt:15: Warning: Do not place Android context classes in static fields (static reference to FirebaseMessaging which has field context pointing to Context); this is a memory leak [StaticFieldLeak]
    private val messaging = FirebaseMessaging.getInstance()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\viewmodels\KpiViewModel.kt:68: Warning: This field leaks a context object [StaticFieldLeak]
    private val context: Context = application.applicationContext // Store context
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "StaticFieldLeak":
   A static field will leak contexts.

   Non-static inner classes have an implicit reference to their outer class.
   If that outer class is for example a Fragment or Activity, then this
   reference means that the long-running handler/loader/task will hold a
   reference to the activity which prevents it from getting garbage
   collected.

   Similarly, direct field references to activities and fragments from these
   longer running instances can cause leaks.

   ViewModel classes should never point to Views or non-application Contexts.

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\ic_baseline_settings_24.xml:4: Warning: Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="@android:color/white" android:pathData="M19.14,12.94c0.04,-0.3 0.06,-0.61 0.06,-0.94c0,-0.32 -0.02,-0.64 -0.07,-0.94l2.03,-1.58c0.18,-0.14 0.23,-0.41 0.12,-0.61l-1.92,-3.32c-0.12,-0.22 -0.37,-0.29 -0.59,-0.22l-2.39,0.96c-0.5,-0.38 -1.03,-0.7 -1.62,-0.94L14.4,2.81c-0.04,-0.24 -0.24,-0.41 -0.48,-0.41h-3.84c-0.24,0 -0.43,0.17 -0.47,0.41L9.25,5.35C8.66,5.59 8.12,5.92 7.63,6.29L5.24,5.33c-0.22,-0.08 -0.47,0 -0.59,0.22L2.74,8.87C2.62,9.08 2.66,9.34 2.86,9.48l2.03,1.58C4.84,11.36 4.8,11.69 4.8,12s0.02,0.64 0.07,0.94l-2.03,1.58c-0.18,0.14 -0.23,0.41 -0.12,0.61l1.92,3.32c0.12,0.22 0.37,0.29 0.59,0.22l2.39,-0.96c0.5,0.38 1.03,0.7 1.62,0.94l0.36,2.54c0.05,0.24 0.24,0.41 0.48,0.41h3.84c0.24,0 0.44,-0.17 0.47,-0.41l0.36,-2.54c0.59,-0.24 1.13,-0.56 1.62,-0.94l2.39,0.96c0.22,0.08 0.47,0 0.59,-0.22l1.92,-3.32c0.12,-0.22 0.07,-0.47 -0.12,-0.61L19.14,12.94zM12,15.6c-1.98,0 -3.6,-1.62 -3.6,-3.6s1.62,-3.6 3.6,-3.6s3.6,1.62 3.6,3.6S13.98,15.6 12,15.6z"/>
                                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\ic_whatsapp.xml:9: Warning: Very long vector path (886 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
      android:pathData="M17.472,14.382c-0.297,-0.149 -1.758,-0.867 -2.03,-0.967c-0.273,-0.099 -0.471,-0.148 -0.67,0.15c-0.197,0.297 -0.767,0.966 -0.94,1.164c-0.173,0.199 -0.347,0.223 -0.644,0.075c-0.297,-0.15 -1.255,-0.463 -2.39,-1.475c-0.883,-0.788 -1.48,-1.761 -1.653,-2.059c-0.173,-0.297 -0.018,-0.458 0.13,-0.606c0.134,-0.133 0.298,-0.347 0.446,-0.52c0.149,-0.174 0.198,-0.298 0.298,-0.497c0.099,-0.198 0.05,-0.371 -0.025,-0.52C10.612,9.420 9.96,7.956 9.715,7.361C9.477,6.781 9.237,6.861 9.055,6.852C8.883,6.844 8.685,6.842 8.487,6.842C8.289,6.842 7.967,6.917 7.694,7.215C7.421,7.512 6.655,8.229 6.655,9.693C6.655,11.157 7.719,12.577 7.868,12.775C8.016,12.973 9.96,15.93 12.958,17.218C13.689,17.54 14.263,17.729 14.714,17.867C15.447,18.095 16.124,18.062 16.668,17.987C17.268,17.904 18.497,17.268 18.742,16.57C18.986,15.872 18.986,15.273 18.911,15.149C18.835,15.025 18.637,14.95 18.339,14.801L17.472,14.382Z"/>
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "VectorPath":
   Using long vector paths is bad for performance. There are several ways to
   make the pathData shorter:
   * Using less precision
   * Removing some minor details
   * Using the Android Studio vector conversion tool
   * Rasterizing the image (converting to PNG)

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:147: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
                    <LinearLayout
                     ~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:174: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
                    <LinearLayout
                     ~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_conversation.xml:14: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
    <LinearLayout
     ~~~~~~~~~~~~

   Explanation for issues of type "DisableBaselineAlignment":
   When a LinearLayout is used to distribute the space proportionally between
   nested layouts, the baseline alignment property should be turned off to
   make the layout computation faster.

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:7: Warning: Possible overdraw: Root element paints background @color/soft_lavender_background with a theme that also paints a background (inferred theme is @style/Theme_KPITrackerApp_NoActionBar) [Overdraw]
    android:background="@color/soft_lavender_background"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:7: Warning: Possible overdraw: Root element paints background @color/soft_lavender_background with a theme that also paints a background (inferred theme is @style/Theme_KPITrackerApp_NoActionBar) [Overdraw]
    android:background="@color/soft_lavender_background"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_admin_dashboard.xml:7: Warning: Possible overdraw: Root element paints background @color/background_light with a theme that also paints a background (inferred theme is @style/Theme_KPITrackerApp_NoActionBar) [Overdraw]
    android:background="@color/background_light"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:7: Warning: Possible overdraw: Root element paints background @color/background_color with a theme that also paints a background (inferred theme is @style/Theme.KPITrackerApp) [Overdraw]
    android:background="@color/background_color">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_chat.xml:7: Warning: Possible overdraw: Root element paints background @color/chat_background with a theme that also paints a background (inferred theme is @style/Theme_KPITrackerApp_NoActionBar) [Overdraw]
    android:background="@color/chat_background"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_chat_list.xml:7: Warning: Possible overdraw: Root element paints background @color/background_light with a theme that also paints a background (inferred theme is @style/Theme_KPITrackerApp_NoActionBar) [Overdraw]
    android:background="@color/background_light"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_create_user.xml:7: Warning: Possible overdraw: Root element paints background @android:color/white with a theme that also paints a background (inferred theme is @style/Theme.KPITrackerApp) [Overdraw]
    android:background="@android:color/white"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:8: Warning: Possible overdraw: Root element paints background @color/screen_background_light_blue with a theme that also paints a background (inferred theme is @style/Theme.KPITrackerApp) [Overdraw]
    android:background="@color/screen_background_light_blue"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_login.xml:7: Warning: Possible overdraw: Root element paints background @android:color/white with a theme that also paints a background (inferred theme is @style/Theme_KPITrackerApp_NoActionBar) [Overdraw]
    android:background="@android:color/white"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_main.xml:7: Warning: Possible overdraw: Root element paints background @android:color/white with a theme that also paints a background (inferred theme is @style/Theme_KPITrackerApp_NoActionBar) [Overdraw]
    android:background="@android:color/white"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_modern_report.xml:7: Warning: Possible overdraw: Root element paints background @color/white with a theme that also paints a background (inferred theme is @style/Theme.KPITrackerApp) [Overdraw]
    android:background="@color/white"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_notifications.xml:7: Warning: Possible overdraw: Root element paints background @color/background with a theme that also paints a background (inferred theme is @style/Theme.KPITrackerApp) [Overdraw]
    android:background="@color/background"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_report.xml:7: Warning: Possible overdraw: Root element paints background #F8F9FA with a theme that also paints a background (inferred theme is @style/Theme.KPITrackerApp) [Overdraw]
    android:background="#F8F9FA"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:7: Warning: Possible overdraw: Root element paints background @color/soft_lavender_background with a theme that also paints a background (inferred theme is @style/Theme_KPITrackerApp_NoActionBar) [Overdraw]
    android:background="@color/soft_lavender_background"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:7: Warning: Possible overdraw: Root element paints background @color/background_color with a theme that also paints a background (inferred theme is @style/Theme.KPITrackerApp) [Overdraw]
    android:background="@color/background_color">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\divider_view.xml:9: Warning: Possible overdraw: Root element paints background ?android:attr/listDivider with a theme that also paints a background (inferred theme is @style/Theme.KPITrackerApp) [Overdraw]
    android:background="?android:attr/listDivider" 
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_account.xml:5: Warning: Possible overdraw: Root element paints background @color/background_light with a theme that also paints a background (inferred theme is @style/Theme.KPITrackerApp) [Overdraw]
    android:background="@color/background_light">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_dashboard.xml:7: Warning: Possible overdraw: Root element paints background @color/background_light with a theme that also paints a background (inferred theme is @style/Theme.KPITrackerApp) [Overdraw]
    android:background="@color/background_light">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_main_dashboard.xml:6: Warning: Possible overdraw: Root element paints background @android:color/white with a theme that also paints a background (inferred theme is @style/Theme.KPITrackerApp) [Overdraw]
    android:background="@android:color/white">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_messages.xml:5: Warning: Possible overdraw: Root element paints background @color/background_light with a theme that also paints a background (inferred theme is @style/Theme.KPITrackerApp) [Overdraw]
    android:background="@color/background_light">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_performance.xml:5: Warning: Possible overdraw: Root element paints background @color/background_light with a theme that also paints a background (inferred theme is @style/Theme.KPITrackerApp) [Overdraw]
    android:background="@color/background_light">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:2: Warning: The resource R.layout.activity_add_edit_task_enhanced appears to be unused [UnusedResources]
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\anim\bounce_animation.xml:2: Warning: The resource R.anim.bounce_animation appears to be unused [UnusedResources]
<set xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\anim\card_press_scale.xml:2: Warning: The resource R.anim.card_press_scale appears to be unused [UnusedResources]
<set xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\anim\card_release_scale.xml:2: Warning: The resource R.anim.card_release_scale appears to be unused [UnusedResources]
<set xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\chart_fill_gradient.xml:2: Warning: The resource R.drawable.chart_fill_gradient appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\color\chip_background_color.xml:2: Warning: The resource R.color.chip_background_color appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\color\chip_text_color.xml:2: Warning: The resource R.color.chip_text_color appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\color_swatch_background.xml:2: Warning: The resource R.drawable.color_swatch_background appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:10: Warning: The resource R.color.light_gray appears to be unused [UnusedResources]
    <color name="light_gray">#D3D3D3</color>
           ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:11: Warning: The resource R.color.dark_gray appears to be unused [UnusedResources]
    <color name="dark_gray">#A9A9A9</color>
           ~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:12: Warning: The resource R.color.red appears to be unused [UnusedResources]
    <color name="red">#FFFF0000</color>
           ~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:13: Warning: The resource R.color.green appears to be unused [UnusedResources]
    <color name="green">#FF00FF00</color> <!-- Original Green -->
           ~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:14: Warning: The resource R.color.blue appears to be unused [UnusedResources]
    <color name="blue">#FF0000FF</color>
           ~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:15: Warning: The resource R.color.yellow appears to be unused [UnusedResources]
    <color name="yellow">#FFFFFF00</color>
           ~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:16: Warning: The resource R.color.orange appears to be unused [UnusedResources]
    <color name="orange">#FFFFA500</color> <!-- Original Orange -->
           ~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:17: Warning: The resource R.color.light_blue_50 appears to be unused [UnusedResources]
    <color name="light_blue_50">#FFE1F5FE</color>
           ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:18: Warning: The resource R.color.light_blue_200 appears to be unused [UnusedResources]
    <color name="light_blue_200">#FF81D4FA</color>
           ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:19: Warning: The resource R.color.light_blue_600 appears to be unused [UnusedResources]
    <color name="light_blue_600">#FF039BE5</color>
           ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:20: Warning: The resource R.color.light_blue_900 appears to be unused [UnusedResources]
    <color name="light_blue_900">#FF01579B</color>
           ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:23: Warning: The resource R.color.chart_target_color appears to be unused [UnusedResources]
    <color name="chart_target_color">#FFEF5350</color> <!-- A shade of Red -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:24: Warning: The resource R.color.chart_achieved_color appears to be unused [UnusedResources]
    <color name="chart_achieved_color">#FF42A5F5</color> <!-- A shade of Blue -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:25: Warning: The resource R.color.default_kpi_card_color appears to be unused [UnusedResources]
    <color name="default_kpi_card_color">#FFFFFFFF</color> <!-- Default White -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:26: Warning: The resource R.color.default_kpi_card_top_gradient appears to be unused [UnusedResources]
    <color name="default_kpi_card_top_gradient">#FF6200EE</color> <!-- Default Purple 500 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:27: Warning: The resource R.color.default_kpi_card_bottom_gradient appears to be unused [UnusedResources]
    <color name="default_kpi_card_bottom_gradient">#FF3700B3</color> <!-- Default Purple 700 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:31: Warning: The resource R.color.header_gradient_start appears to be unused [UnusedResources]
    <color name="header_gradient_start">#FF6200EE</color> <!-- Example: Purple 500 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:32: Warning: The resource R.color.header_gradient_end appears to be unused [UnusedResources]
    <color name="header_gradient_end">#FF3700B3</color> <!-- Example: Purple 700 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:40: Warning: The resource R.color.purple_button_text appears to be unused [UnusedResources]
    <color name="purple_button_text">#FFFFFFFF</color> <!-- Example: White -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:46: Warning: The resource R.color.fab_color appears to be unused [UnusedResources]
    <color name="fab_color">#FF6200EE</color> <!-- Referenced in activity_main.xml -->
           ~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:47: Warning: The resource R.color.chip_background_default appears to be unused [UnusedResources]
    <color name="chip_background_default">#FFE0E0E0</color> <!-- Referenced in kpi_card_item.xml -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:103: Warning: The resource R.color.read_only_overlay appears to be unused [UnusedResources]
    <color name="read_only_overlay">#40000000</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:154: Warning: The resource R.color.importance_low appears to be unused [UnusedResources]
    <color name="importance_low">#FF9E9E9E</color>
           ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:155: Warning: The resource R.color.importance_medium appears to be unused [UnusedResources]
    <color name="importance_medium">#FF2196F3</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:156: Warning: The resource R.color.importance_high appears to be unused [UnusedResources]
    <color name="importance_high">#FF9C27B0</color>
           ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:157: Warning: The resource R.color.importance_critical appears to be unused [UnusedResources]
    <color name="importance_critical">#FFE91E63</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:163: Warning: The resource R.color.overdue_color appears to be unused [UnusedResources]
    <color name="overdue_color">#FFF44336</color>
           ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:172: Warning: The resource R.color.progress_tint appears to be unused [UnusedResources]
    <color name="progress_tint">#FF4CAF50</color>
           ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:174: Warning: The resource R.color.progress_text appears to be unused [UnusedResources]
    <color name="progress_text">#FF4CAF50</color>
           ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:177: Warning: The resource R.color.urgent_chip_background appears to be unused [UnusedResources]
    <color name="urgent_chip_background">#FFFFE0E0</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:178: Warning: The resource R.color.overdue_chip_background appears to be unused [UnusedResources]
    <color name="overdue_chip_background">#FFFFE0E0</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:192: Warning: The resource R.color.icon_tint appears to be unused [UnusedResources]
    <color name="icon_tint">#FF757575</color>
           ~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:196: Warning: The resource R.color.energy_low appears to be unused [UnusedResources]
    <color name="energy_low">#FFFF9800</color>
           ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:197: Warning: The resource R.color.energy_medium appears to be unused [UnusedResources]
    <color name="energy_medium">#FF2196F3</color>
           ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:198: Warning: The resource R.color.energy_high appears to be unused [UnusedResources]
    <color name="energy_high">#FF4CAF50</color>
           ~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:209: Warning: The resource R.color.medium_priority_color appears to be unused [UnusedResources]
    <color name="medium_priority_color">#FFFF9800</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:210: Warning: The resource R.color.low_priority_color appears to be unused [UnusedResources]
    <color name="low_priority_color">#FF4CAF50</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:213: Warning: The resource R.color.blue_500 appears to be unused [UnusedResources]
    <color name="blue_500">#FF2196F3</color>
           ~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:214: Warning: The resource R.color.green_500 appears to be unused [UnusedResources]
    <color name="green_500">#FF4CAF50</color>
           ~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:215: Warning: The resource R.color.red_500 appears to be unused [UnusedResources]
    <color name="red_500">#FFF44336</color>
           ~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\colors.xml:216: Warning: The resource R.color.orange_500 appears to be unused [UnusedResources]
    <color name="orange_500">#FFFF9800</color>
           ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\custom_progress_bar.xml:2: Warning: The resource R.drawable.custom_progress_bar appears to be unused [UnusedResources]
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\dialog_progress_update.xml:2: Warning: The resource R.layout.dialog_progress_update appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\dimens.xml:3: Warning: The resource R.dimen.fab_margin appears to be unused [UnusedResources]
    <dimen name="fab_margin">16dp</dimen>
           ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\dimens.xml:6: Warning: The resource R.dimen.color_swatch_size appears to be unused [UnusedResources]
    <dimen name="color_swatch_size">36dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\dimens.xml:7: Warning: The resource R.dimen.color_swatch_stroke_width appears to be unused [UnusedResources]
    <dimen name="color_swatch_stroke_width">2dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\dimens.xml:12: Warning: The resource R.dimen.card_elevation_pressed appears to be unused [UnusedResources]
    <dimen name="card_elevation_pressed">12dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\anim\fade_scale_in.xml:2: Warning: The resource R.anim.fade_scale_in appears to be unused [UnusedResources]
<set xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\gradient_admin_welcome.xml:2: Warning: The resource R.drawable.gradient_admin_welcome appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\header_gradient.xml:2: Warning: The resource R.drawable.header_gradient appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\ic_baseline_arrow_downward_16.xml:1: Warning: The resource R.drawable.ic_baseline_arrow_downward_16 appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\ic_baseline_arrow_upward_16.xml:1: Warning: The resource R.drawable.ic_baseline_arrow_upward_16 appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\ic_baseline_attachment_24.xml:1: Warning: The resource R.drawable.ic_baseline_attachment_24 appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\ic_baseline_edit_24.xml:1: Warning: The resource R.drawable.ic_baseline_edit_24 appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\ic_baseline_format_color_reset_24.xml:1: Warning: The resource R.drawable.ic_baseline_format_color_reset_24 appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\ic_baseline_horizontal_rule_16.xml:1: Warning: The resource R.drawable.ic_baseline_horizontal_rule_16 appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\ic_baseline_logout_24.xml:1: Warning: The resource R.drawable.ic_baseline_logout_24 appears to be unused [UnusedResources]
<vector android:autoMirrored="true" android:height="24dp"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\ic_baseline_switch_account_24.xml:1: Warning: The resource R.drawable.ic_baseline_switch_account_24 appears to be unused [UnusedResources]
<vector android:height="24dp" android:tint="#757575"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\ic_filter_all_24.xml:1: Warning: The resource R.drawable.ic_filter_all_24 appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\ic_filter_expiry_24.xml:1: Warning: The resource R.drawable.ic_filter_expiry_24 appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\ic_filter_report_24.xml:1: Warning: The resource R.drawable.ic_filter_report_24 appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\ic_filter_task_24.xml:1: Warning: The resource R.drawable.ic_filter_task_24 appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\ic_sort_24.xml:2: Warning: The resource R.drawable.ic_sort_24 appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\ic_sort_alpha.xml:2: Warning: The resource R.drawable.ic_sort_alpha appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\ic_sort_ascending.xml:2: Warning: The resource R.drawable.ic_sort_ascending appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\ic_sort_descending.xml:2: Warning: The resource R.drawable.ic_sort_descending appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_subtask_mini.xml:2: Warning: The resource R.layout.item_subtask_mini appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\kpi_detail_item.xml:2: Warning: The resource R.layout.kpi_detail_item appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\kpi_list_item.xml:2: Warning: The resource R.layout.kpi_list_item appears to be unused [UnusedResources]
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main_menu.xml:2: Warning: The resource R.menu.main_menu appears to be unused [UnusedResources]
<menu xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\overall_summary_gradient.xml:2: Warning: The resource R.drawable.overall_summary_gradient appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\priority_indicator_gradient.xml:2: Warning: The resource R.drawable.priority_indicator_gradient appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row.xml:2: Warning: The resource R.layout.report_table_row appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\anim\shake_animation.xml:2: Warning: The resource R.anim.shake_animation appears to be unused [UnusedResources]
<set xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\anim\slide_in_bottom.xml:2: Warning: The resource R.anim.slide_in_bottom appears to be unused [UnusedResources]
<set xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\sort_options_menu.xml:2: Warning: The resource R.menu.sort_options_menu appears to be unused [UnusedResources]
<menu xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\anim\star_pulse.xml:2: Warning: The resource R.anim.star_pulse appears to be unused [UnusedResources]
<set xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:7: Warning: The resource R.string.kpi_target_hint appears to be unused [UnusedResources]
    <string name="kpi_target_hint">Annual Target Value</string>
            ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:16: Warning: The resource R.string.save_kpi_button_text appears to be unused [UnusedResources]
    <string name="save_kpi_button_text">Save KPI</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:17: Warning: The resource R.string.error_name_required appears to be unused [UnusedResources]
    <string name="error_name_required">Name is required</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:18: Warning: The resource R.string.error_target_required appears to be unused [UnusedResources]
    <string name="error_target_required">Target is required</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:19: Warning: The resource R.string.error_invalid_target appears to be unused [UnusedResources]
    <string name="error_invalid_target">Invalid target value</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:20: Warning: The resource R.string.error_saving_kpi appears to be unused [UnusedResources]
    <string name="error_saving_kpi">Error saving KPI</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:21: Warning: The resource R.string.error_kpi_not_found appears to be unused [UnusedResources]
    <string name="error_kpi_not_found">KPI not found for editing.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:22: Warning: The resource R.string.kpi_list_title appears to be unused [UnusedResources]
    <string name="kpi_list_title">KPIs</string>
            ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:23: Warning: The resource R.string.add_progress_title appears to be unused [UnusedResources]
    <string name="add_progress_title">Add Progress</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:24: Warning: The resource R.string.edit_progress_title appears to be unused [UnusedResources]
    <string name="edit_progress_title">Edit Progress</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:26: Warning: The resource R.string.progress_date_hint appears to be unused [UnusedResources]
    <string name="progress_date_hint">Date</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:27: Warning: The resource R.string.save_progress_button_text appears to be unused [UnusedResources]
    <string name="save_progress_button_text">Save Progress</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:28: Warning: The resource R.string.error_value_required appears to be unused [UnusedResources]
    <string name="error_value_required">Value is required</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:31: Warning: The resource R.string.error_saving_progress appears to be unused [UnusedResources]
    <string name="error_saving_progress">Error saving progress</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:33: Warning: The resource R.string.current_progress_label appears to be unused [UnusedResources]
    <string name="current_progress_label">Current Progress:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:35: Warning: The resource R.string.add_progress_button_text appears to be unused [UnusedResources]
    <string name="add_progress_button_text">Add Progress</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:36: Warning: The resource R.string.view_history_button_text appears to be unused [UnusedResources]
    <string name="view_history_button_text">View History</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:38: Warning: The resource R.string.progress_history_title appears to be unused [UnusedResources]
    <string name="progress_history_title">Progress History</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:39: Warning: The resource R.string.delete_kpi_menu_item appears to be unused [UnusedResources]
    <string name="delete_kpi_menu_item">Delete KPI</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:40: Warning: The resource R.string.confirm_delete_kpi_title appears to be unused [UnusedResources]
    <string name="confirm_delete_kpi_title">Confirm Delete</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:41: Warning: The resource R.string.confirm_delete_kpi_message appears to be unused [UnusedResources]
    <string name="confirm_delete_kpi_message">Are you sure you want to delete this KPI and all its progress?</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:42: Warning: The resource R.string.delete_progress_entry_menu_item appears to be unused [UnusedResources]
    <string name="delete_progress_entry_menu_item">Delete Entry</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:43: Warning: The resource R.string.confirm_delete_progress_entry_title appears to be unused [UnusedResources]
    <string name="confirm_delete_progress_entry_title">Confirm Delete Entry</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:44: Warning: The resource R.string.confirm_delete_progress_entry_message appears to be unused [UnusedResources]
    <string name="confirm_delete_progress_entry_message">Are you sure you want to delete this progress entry?</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:45: Warning: The resource R.string.edit_kpi_menu_item appears to be unused [UnusedResources]
    <string name="edit_kpi_menu_item">Edit KPI</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:46: Warning: The resource R.string.edit_progress_entry_menu_item appears to be unused [UnusedResources]
    <string name="edit_progress_entry_menu_item">Edit Entry</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:47: Warning: The resource R.string.dialog_ok appears to be unused [UnusedResources]
    <string name="dialog_ok">OK</string>
            ~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:49: Warning: The resource R.string.search_hint appears to be unused [UnusedResources]
    <string name="search_hint">Search KPIs...</string>
            ~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:50: Warning: The resource R.string.search_kpis_title appears to be unused [UnusedResources]
    <string name="search_kpis_title">Search KPIs</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:51: Warning: The resource R.string.filter_by_owner_hint appears to be unused [UnusedResources]
    <string name="filter_by_owner_hint">Filter by Owner</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:54: Warning: The resource R.string.select_owner_image_button_text appears to be unused [UnusedResources]
    <string name="select_owner_image_button_text">Select Owner Image</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:55: Warning: The resource R.string.error_copying_image appears to be unused [UnusedResources]
    <string name="error_copying_image">Error copying image</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:56: Warning: The resource R.string.kpi_actions_dialog_title appears to be unused [UnusedResources]
    <string name="kpi_actions_dialog_title">KPI Actions</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:57: Warning: The resource R.string.action_add_progress appears to be unused [UnusedResources]
    <string name="action_add_progress">Add Progress</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:58: Warning: The resource R.string.action_view_details appears to be unused [UnusedResources]
    <string name="action_view_details">View Details</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:59: Warning: The resource R.string.action_edit_kpi appears to be unused [UnusedResources]
    <string name="action_edit_kpi">Edit KPI</string>
            ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:60: Warning: The resource R.string.action_delete_kpi appears to be unused [UnusedResources]
    <string name="action_delete_kpi">Delete KPI</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:65: Warning: The resource R.string.select_user_hint appears to be unused [UnusedResources]
    <string name="select_user_hint">Select User</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:66: Warning: The resource R.string.manual_user_input_hint appears to be unused [UnusedResources]
    <string name="manual_user_input_hint">Enter User Name</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:67: Warning: The resource R.string.user_name_label appears to be unused [UnusedResources]
    <string name="user_name_label">User Name</string>
            ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:68: Warning: The resource R.string.select_users_dialog_title appears to be unused [UnusedResources]
    <string name="select_users_dialog_title">Select Users</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:69: Warning: The resource R.string.no_users_selected_hint appears to be unused [UnusedResources]
    <string name="no_users_selected_hint">No users selected</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:70: Warning: The resource R.string.error_at_least_one_user appears to be unused [UnusedResources]
    <string name="error_at_least_one_user">Please select at least one user.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:71: Warning: The resource R.string.users_not_loaded_yet appears to be unused [UnusedResources]
    <string name="users_not_loaded_yet">Users not loaded yet, please try again shortly.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:72: Warning: The resource R.string.add_new_user_button_label appears to be unused [UnusedResources]
    <string name="add_new_user_button_label">Add New User</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:73: Warning: The resource R.string.enter_new_user_name_hint appears to be unused [UnusedResources]
    <string name="enter_new_user_name_hint">Enter New User Name</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:74: Warning: The resource R.string.create_new_user_dialog_title appears to be unused [UnusedResources]
    <string name="create_new_user_dialog_title">Add New User</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:75: Warning: The resource R.string.user_already_selected_error appears to be unused [UnusedResources]
    <string name="user_already_selected_error">This user is already selected.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:76: Warning: The resource R.string.existing_user_added_to_selection_message appears to be unused [UnusedResources]
    <string name="existing_user_added_to_selection_message">Existing user added to selection.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:77: Warning: The resource R.string.user_name_cannot_be_empty_error appears to be unused [UnusedResources]
    <string name="user_name_cannot_be_empty_error">User name cannot be empty.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:87: Warning: The resource R.string.color_picker_title_summary_card_top appears to be unused [UnusedResources]
    <string name="color_picker_title_summary_card_top">Summary Card Top Color</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:88: Warning: The resource R.string.color_picker_title_summary_card_bottom appears to be unused [UnusedResources]
    <string name="color_picker_title_summary_card_bottom">Summary Card Bottom Color</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:89: Warning: The resource R.string.color_picker_title_individual_kpi_card appears to be unused [UnusedResources]
    <string name="color_picker_title_individual_kpi_card">Individual KPI Card Color</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:90: Warning: The resource R.string.reset_to_default_color appears to be unused [UnusedResources]
    <string name="reset_to_default_color">Default</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:92: Warning: The resource R.string.notification_channel_name appears to be unused [UnusedResources]
    <string name="notification_channel_name">KPI Notifications</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:93: Warning: The resource R.string.notification_channel_description appears to be unused [UnusedResources]
    <string name="notification_channel_description">Notifications for KPI events like target achievement.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:97: Warning: The resource R.string.overall_summary_card_title appears to be unused [UnusedResources]
    <string name="overall_summary_card_title">Overall Summary</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:98: Warning: The resource R.string.monthly_progress_label_short appears to be unused [UnusedResources]
    <string name="monthly_progress_label_short">Month: %1$d%%</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:99: Warning: The resource R.string.annual_progress_label_short appears to be unused [UnusedResources]
    <string name="annual_progress_label_short">Year: %1$d%%</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:100: Warning: The resource R.string.total_monthly_target_label appears to be unused [UnusedResources]
    <string name="total_monthly_target_label">Total Monthly Target: %s</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:101: Warning: The resource R.string.total_monthly_achieved_label appears to be unused [UnusedResources]
    <string name="total_monthly_achieved_label">Total Monthly Achieved: %s</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:102: Warning: The resource R.string.total_annual_target_label appears to be unused [UnusedResources]
    <string name="total_annual_target_label">Total Annual Target: %s</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:103: Warning: The resource R.string.total_annual_achieved_label appears to be unused [UnusedResources]
    <string name="total_annual_achieved_label">Total Annual Achieved: %s</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:104: Warning: The resource R.string.no_kpis_assigned_for_master_card appears to be unused [UnusedResources]
    <string name="no_kpis_assigned_for_master_card">No KPIs assigned to users yet.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:106: Warning: The resource R.string.user_summary_card_title_prefix appears to be unused [UnusedResources]
    <string name="user_summary_card_title_prefix">Summary for</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:107: Warning: The resource R.string.kpis_assigned_label appears to be unused [UnusedResources]
    <string name="kpis_assigned_label">KPIs Assigned: %d</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:108: Warning: The resource R.string.no_kpis_assigned_to_user appears to be unused [UnusedResources]
    <string name="no_kpis_assigned_to_user">No KPIs assigned to this user.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:109: Warning: The resource R.string.kpi_summary_item_format appears to be unused [UnusedResources]
    <string name="kpi_summary_item_format">%1$s: M %2$d%%, Y %3$d%%</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:112: Warning: The resource R.string.filter_by_month_button_text appears to be unused [UnusedResources]
    <string name="filter_by_month_button_text">Filter by Month</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:113: Warning: The resource R.string.month_year_format appears to be unused [UnusedResources]
    <string name="month_year_format">MMMM yyyy</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:115: Warning: The resource R.string.user_kpi_list_title_prefix appears to be unused [UnusedResources]
    <string name="user_kpi_list_title_prefix">KPIs for</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:116: Warning: The resource R.string.no_kpis_for_this_user appears to be unused [UnusedResources]
    <string name="no_kpis_for_this_user">No KPIs found for this user.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:118: Warning: The resource R.string.main_activity_title appears to be unused [UnusedResources]
    <string name="main_activity_title">KPI Dashboard</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:119: Warning: The resource R.string.overall_kpi_summary_title appears to be unused [UnusedResources]
    <string name="overall_kpi_summary_title">Overall KPI Summary</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:120: Warning: The resource R.string.user_summaries_title appears to be unused [UnusedResources]
    <string name="user_summaries_title">User Summaries</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:121: Warning: The resource R.string.all_kpis_title appears to be unused [UnusedResources]
    <string name="all_kpis_title">All KPIs (Aggregated)</string>
            ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:122: Warning: The resource R.string.no_kpis_to_display appears to be unused [UnusedResources]
    <string name="no_kpis_to_display">No KPIs to display.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:123: Warning: The resource R.string.no_user_summaries_to_display appears to be unused [UnusedResources]
    <string name="no_user_summaries_to_display">No user summaries to display.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:124: Warning: The resource R.string.no_overall_summary_to_display appears to be unused [UnusedResources]
    <string name="no_overall_summary_to_display">No overall KPI summary to display.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:127: Warning: The resource R.string.confirm_clear_progress_title appears to be unused [UnusedResources]
    <string name="confirm_clear_progress_title">Confirm Clear Progress</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:128: Warning: The resource R.string.confirm_clear_progress_message appears to be unused [UnusedResources]
    <string name="confirm_clear_progress_message">Are you sure you want to clear all progress entries for this KPI for this user?</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:130: Warning: The resource R.string.confirm_clear_month_progress_message appears to be unused [UnusedResources]
    <string name="confirm_clear_month_progress_message">Are you sure you want to clear progress for %1$s for this KPI for this user?</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:131: Warning: The resource R.string.progress_cleared_toast appears to be unused [UnusedResources]
    <string name="progress_cleared_toast">Progress cleared.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:134: Warning: The resource R.string.ocr_import_title appears to be unused [UnusedResources]
    <string name="ocr_import_title">OCR Import</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:135: Warning: The resource R.string.select_image_for_ocr appears to be unused [UnusedResources]
    <string name="select_image_for_ocr">Select Image for OCR</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:136: Warning: The resource R.string.ocr_review_title appears to be unused [UnusedResources]
    <string name="ocr_review_title">Review OCR Results</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:137: Warning: The resource R.string.import_ocr_data_button appears to be unused [UnusedResources]
    <string name="import_ocr_data_button">Import Data</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:138: Warning: The resource R.string.ocr_value_label appears to be unused [UnusedResources]
    <string name="ocr_value_label">Value:</string>
            ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:139: Warning: The resource R.string.ocr_date_label appears to be unused [UnusedResources]
    <string name="ocr_date_label">Date:</string>
            ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:140: Warning: The resource R.string.ocr_user_label appears to be unused [UnusedResources]
    <string name="ocr_user_label">User (Optional):</string>
            ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:141: Warning: The resource R.string.error_ocr_processing appears to be unused [UnusedResources]
    <string name="error_ocr_processing">Error processing image with OCR.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:142: Warning: The resource R.string.no_text_found_ocr appears to be unused [UnusedResources]
    <string name="no_text_found_ocr">No text found in image or OCR failed.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:143: Warning: The resource R.string.ocr_data_imported_success appears to be unused [UnusedResources]
    <string name="ocr_data_imported_success">OCR data imported successfully.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:144: Warning: The resource R.string.ocr_data_import_failed appears to be unused [UnusedResources]
    <string name="ocr_data_import_failed">Failed to import OCR data.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:145: Warning: The resource R.string.assign_to_kpi_label appears to be unused [UnusedResources]
    <string name="assign_to_kpi_label">Assign to KPI:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:146: Warning: The resource R.string.select_kpi_for_ocr_hint appears to be unused [UnusedResources]
    <string name="select_kpi_for_ocr_hint">Select KPI</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:147: Warning: The resource R.string.error_select_kpi_for_ocr appears to be unused [UnusedResources]
    <string name="error_select_kpi_for_ocr">Please select a KPI to assign the data to.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:148: Warning: The resource R.string.ocr_import_instructions appears to be unused [UnusedResources]
    <string name="ocr_import_instructions">Select an image containing numerical data, dates, and optional user identifiers. Review and edit the extracted data before importing.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:149: Warning: The resource R.string.edit_ocr_item_title appears to be unused [UnusedResources]
    <string name="edit_ocr_item_title">Edit OCR Item</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:150: Warning: The resource R.string.delete_ocr_item_title appears to be unused [UnusedResources]
    <string name="delete_ocr_item_title">Delete OCR Item</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:151: Warning: The resource R.string.confirm_delete_ocr_item_message appears to be unused [UnusedResources]
    <string name="confirm_delete_ocr_item_message">Are you sure you want to delete this OCR item?</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:153: Warning: The resource R.string.excel_import_title appears to be unused [UnusedResources]
    <string name="excel_import_title">Excel Import</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:154: Warning: The resource R.string.select_excel_file appears to be unused [UnusedResources]
    <string name="select_excel_file">Select Excel File (.xlsx)</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:155: Warning: The resource R.string.excel_review_title appears to be unused [UnusedResources]
    <string name="excel_review_title">Review Excel Data</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:156: Warning: The resource R.string.import_excel_data_button appears to be unused [UnusedResources]
    <string name="import_excel_data_button">Import Data</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:157: Warning: The resource R.string.error_excel_processing appears to be unused [UnusedResources]
    <string name="error_excel_processing">Error processing Excel file.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:158: Warning: The resource R.string.excel_data_imported_success appears to be unused [UnusedResources]
    <string name="excel_data_imported_success">Excel data imported successfully.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:159: Warning: The resource R.string.excel_data_import_failed appears to be unused [UnusedResources]
    <string name="excel_data_import_failed">Failed to import Excel data.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:160: Warning: The resource R.string.excel_import_instructions appears to be unused [UnusedResources]
    <string name="excel_import_instructions">Select an Excel file (.xlsx). Ensure it has columns for Value (numeric), Date (e.g., YYYY-MM-DD), and optionally User Identifier. Review and edit before importing.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:161: Warning: The resource R.string.column_mapping_title appears to be unused [UnusedResources]
    <string name="column_mapping_title">Map Excel Columns</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:162: Warning: The resource R.string.map_value_column_label appears to be unused [UnusedResources]
    <string name="map_value_column_label">Value Column:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:163: Warning: The resource R.string.map_date_column_label appears to be unused [UnusedResources]
    <string name="map_date_column_label">Date Column:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:164: Warning: The resource R.string.map_user_column_label appears to be unused [UnusedResources]
    <string name="map_user_column_label">User Identifier Column (Optional):</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:165: Warning: The resource R.string.preview_data_button appears to be unused [UnusedResources]
    <string name="preview_data_button">Preview Data</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:166: Warning: The resource R.string.error_column_selection appears to be unused [UnusedResources]
    <string name="error_column_selection">Please select columns for Value and Date.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:167: Warning: The resource R.string.error_reading_sheet_names appears to be unused [UnusedResources]
    <string name="error_reading_sheet_names">Error reading sheet names from Excel file.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:168: Warning: The resource R.string.select_sheet_label appears to be unused [UnusedResources]
    <string name="select_sheet_label">Select Sheet:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:169: Warning: The resource R.string.error_no_sheets_found appears to be unused [UnusedResources]
    <string name="error_no_sheets_found">No sheets found in the Excel file.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:170: Warning: The resource R.string.header_row_label appears to be unused [UnusedResources]
    <string name="header_row_label">Data starts at row:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:171: Warning: The resource R.string.filter_by_user_excel_label appears to be unused [UnusedResources]
    <string name="filter_by_user_excel_label">Filter by User (from Excel):</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:172: Warning: The resource R.string.all_users_excel_filter appears to be unused [UnusedResources]
    <string name="all_users_excel_filter">All Users (from file)</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:173: Warning: The resource R.string.aggregation_type_label appears to be unused [UnusedResources]
    <string name="aggregation_type_label">Aggregation Type:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:174: Warning: The resource R.string.aggregation_individual appears to be unused [UnusedResources]
    <string name="aggregation_individual">Import Individually</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:175: Warning: The resource R.string.aggregation_average appears to be unused [UnusedResources]
    <string name="aggregation_average">Calculate Average (for selected user)</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:176: Warning: The resource R.string.error_average_for_all_users appears to be unused [UnusedResources]
    <string name="error_average_for_all_users">Cannot calculate average for "All Users". Select a specific user or import individually.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:177: Warning: The resource R.string.import_successful_count appears to be unused [UnusedResources]
    <string name="import_successful_count">Imported %1$d out of %2$d entries.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:178: Warning: The resource R.string.import_failed_with_errors appears to be unused [UnusedResources]
    <string name="import_failed_with_errors">Import failed. %1$d errors occurred.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:179: Warning: The resource R.string.import_partially_successful appears to be unused [UnusedResources]
    <string name="import_partially_successful">Import partially successful. %1$d entries imported, %2$d errors.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:180: Warning: The resource R.string.no_data_to_import appears to be unused [UnusedResources]
    <string name="no_data_to_import">No data to import after review.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:182: Warning: The resource R.string.kpi_report_title appears to be unused [UnusedResources]
    <string name="kpi_report_title">KPI Report</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:183: Warning: The resource R.string.select_kpi_for_report appears to be unused [UnusedResources]
    <string name="select_kpi_for_report">Select KPI:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:184: Warning: The resource R.string.select_user_for_report appears to be unused [UnusedResources]
    <string name="select_user_for_report">Select User:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:185: Warning: The resource R.string.report_start_date_label appears to be unused [UnusedResources]
    <string name="report_start_date_label">Start Date:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:186: Warning: The resource R.string.report_end_date_label appears to be unused [UnusedResources]
    <string name="report_end_date_label">End Date:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:187: Warning: The resource R.string.generate_report_button appears to be unused [UnusedResources]
    <string name="generate_report_button">Generate Report</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:188: Warning: The resource R.string.report_period_daily appears to be unused [UnusedResources]
    <string name="report_period_daily">Daily</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:189: Warning: The resource R.string.report_period_monthly appears to be unused [UnusedResources]
    <string name="report_period_monthly">Monthly</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:190: Warning: The resource R.string.report_period_quarterly appears to be unused [UnusedResources]
    <string name="report_period_quarterly">Quarterly</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:191: Warning: The resource R.string.report_period_annual appears to be unused [UnusedResources]
    <string name="report_period_annual">Annual (Range)</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:192: Warning: The resource R.string.report_target_label appears to be unused [UnusedResources]
    <string name="report_target_label">Target</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:193: Warning: The resource R.string.report_achieved_label appears to be unused [UnusedResources]
    <string name="report_achieved_label">Achieved</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:194: Warning: The resource R.string.report_percentage_label appears to be unused [UnusedResources]
    <string name="report_percentage_label">Percentage</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:195: Warning: The resource R.string.trend_chart_title appears to be unused [UnusedResources]
    <string name="trend_chart_title">Progress Trend</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:196: Warning: The resource R.string.no_data_for_report appears to be unused [UnusedResources]
    <string name="no_data_for_report">No data available for the selected criteria.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:197: Warning: The resource R.string.error_generating_report appears to be unused [UnusedResources]
    <string name="error_generating_report">Error generating report.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:198: Warning: The resource R.string.report_for_kpi_user_format appears to be unused [UnusedResources]
    <string name="report_for_kpi_user_format">Report for %1$s - %2$s</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:199: Warning: The resource R.string.all_users_report_option appears to be unused [UnusedResources]
    <string name="all_users_report_option">All Users (Aggregated)</string> <!-- If applicable -->
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:201: Warning: The resource R.string.kpi_detail_tab_current appears to be unused [UnusedResources]
    <string name="kpi_detail_tab_current">Current</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:202: Warning: The resource R.string.kpi_detail_tab_monthly appears to be unused [UnusedResources]
    <string name="kpi_detail_tab_monthly">Monthly</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:203: Warning: The resource R.string.kpi_detail_tab_quarterly appears to be unused [UnusedResources]
    <string name="kpi_detail_tab_quarterly">Quarterly</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:204: Warning: The resource R.string.kpi_detail_tab_yearly appears to be unused [UnusedResources]
    <string name="kpi_detail_tab_yearly">Yearly</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:205: Warning: The resource R.string.kpi_detail_tab_all_time appears to be unused [UnusedResources]
    <string name="kpi_detail_tab_all_time">All Time</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:206: Warning: The resource R.string.achieved_value_label appears to be unused [UnusedResources]
    <string name="achieved_value_label">Achieved:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:207: Warning: The resource R.string.percentage_value_label appears to be unused [UnusedResources]
    <string name="percentage_value_label">Percentage:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:208: Warning: The resource R.string.remaining_value_label appears to be unused [UnusedResources]
    <string name="remaining_value_label">Remaining:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:209: Warning: The resource R.string.remaining_percentage_label appears to be unused [UnusedResources]
    <string name="remaining_percentage_label">Remaining %:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:210: Warning: The resource R.string.remaining_days_label appears to be unused [UnusedResources]
    <string name="remaining_days_label">Days Left:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:211: Warning: The resource R.string.required_daily_rate_label appears to be unused [UnusedResources]
    <string name="required_daily_rate_label">Required Daily:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:212: Warning: The resource R.string.days_since_last_update_label appears to be unused [UnusedResources]
    <string name="days_since_last_update_label">Days Since Update:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:213: Warning: The resource R.string.not_applicable_short appears to be unused [UnusedResources]
    <string name="not_applicable_short">N/A</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:214: Warning: The resource R.string.no_target_set_short appears to be unused [UnusedResources]
    <string name="no_target_set_short">No Target</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:215: Warning: The resource R.string.select_month_year_title appears to be unused [UnusedResources]
    <string name="select_month_year_title">Select Month and Year</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:217: Warning: The resource R.string.menu_search_kpis appears to be unused [UnusedResources]
    <string name="menu_search_kpis">Search KPIs</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:218: Warning: The resource R.string.menu_import_excel appears to be unused [UnusedResources]
    <string name="menu_import_excel">Import from Excel</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:219: Warning: The resource R.string.menu_import_ocr appears to be unused [UnusedResources]
    <string name="menu_import_ocr">Import from Image (OCR)</string>
            ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:220: Warning: The resource R.string.menu_generate_report appears to be unused [UnusedResources]
    <string name="menu_generate_report">Generate Report</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:221: Warning: The resource R.string.menu_settings appears to be unused [UnusedResources]
    <string name="menu_settings">Settings</string> <!-- Placeholder -->
            ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:222: Warning: The resource R.string.menu_manage_users appears to be unused [UnusedResources]
    <string name="menu_manage_users">Manage Users</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:224: Warning: The resource R.string.user_management_title appears to be unused [UnusedResources]
    <string name="user_management_title">Manage Users</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:225: Warning: The resource R.string.add_user_button_text appears to be unused [UnusedResources]
    <string name="add_user_button_text">Add User</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:226: Warning: The resource R.string.user_name_hint_manage appears to be unused [UnusedResources]
    <string name="user_name_hint_manage">User Name</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:227: Warning: The resource R.string.user_image_label_manage appears to be unused [UnusedResources]
    <string name="user_image_label_manage">User Image (Optional)</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:228: Warning: The resource R.string.save_user_button_text appears to be unused [UnusedResources]
    <string name="save_user_button_text">Save User</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:229: Warning: The resource R.string.error_user_name_required appears to be unused [UnusedResources]
    <string name="error_user_name_required">User name is required.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:230: Warning: The resource R.string.error_saving_user appears to be unused [UnusedResources]
    <string name="error_saving_user">Error saving user.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:231: Warning: The resource R.string.confirm_delete_user_title appears to be unused [UnusedResources]
    <string name="confirm_delete_user_title">Confirm Delete User</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:232: Warning: The resource R.string.confirm_delete_user_message appears to be unused [UnusedResources]
    <string name="confirm_delete_user_message">Are you sure you want to delete user \'%1$s\' and all their KPI assignments? This action cannot be undone.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:233: Warning: The resource R.string.user_deleted_success appears to be unused [UnusedResources]
    <string name="user_deleted_success">User deleted successfully.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:234: Warning: The resource R.string.user_saved_success appears to be unused [UnusedResources]
    <string name="user_saved_success">User saved successfully.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:235: Warning: The resource R.string.edit_user_title appears to be unused [UnusedResources]
    <string name="edit_user_title">Edit User</string>
            ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:236: Warning: The resource R.string.action_edit_user appears to be unused [UnusedResources]
    <string name="action_edit_user">Edit User</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:237: Warning: The resource R.string.action_delete_user appears to be unused [UnusedResources]
    <string name="action_delete_user">Delete User</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:238: Warning: The resource R.string.user_card_color_top_label appears to be unused [UnusedResources]
    <string name="user_card_color_top_label">Card Top Color:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:239: Warning: The resource R.string.user_card_color_bottom_label appears to be unused [UnusedResources]
    <string name="user_card_color_bottom_label">Card Bottom Color:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:240: Warning: The resource R.string.no_users_exist appears to be unused [UnusedResources]
    <string name="no_users_exist">No users exist. Add users to assign KPIs.</string>
            ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:242: Warning: The resource R.string.duplicate_kpi_menu_item appears to be unused [UnusedResources]
    <string name="duplicate_kpi_menu_item">Duplicate KPI</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:244: Warning: The resource R.string.kpi_duplication_failed appears to be unused [UnusedResources]
    <string name="kpi_duplication_failed">Failed to duplicate KPI.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:246: Warning: The resource R.string.expiry_notification_channel_name appears to be unused [UnusedResources]
    <string name="expiry_notification_channel_name">KPI Expiry Reminders</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:247: Warning: The resource R.string.expiry_notification_channel_description appears to be unused [UnusedResources]
    <string name="expiry_notification_channel_description">Reminders for KPIs nearing their implicit monthly/quarterly/annual review or target dates.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:248: Warning: The resource R.string.kpi_expiry_reminder_title appears to be unused [UnusedResources]
    <string name="kpi_expiry_reminder_title">KPI Reminder: %1$s</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:249: Warning: The resource R.string.kpi_expiry_reminder_text_monthly appears to be unused [UnusedResources]
    <string name="kpi_expiry_reminder_text_monthly">Monthly target for \'%1$s\' is approaching. Review progress.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:250: Warning: The resource R.string.kpi_expiry_reminder_text_quarterly appears to be unused [UnusedResources]
    <string name="kpi_expiry_reminder_text_quarterly">Quarterly target for \'%1$s\' is approaching. Review progress.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:251: Warning: The resource R.string.kpi_expiry_reminder_text_annual appears to be unused [UnusedResources]
    <string name="kpi_expiry_reminder_text_annual">Annual target for \'%1$s\' is approaching. Review progress.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:252: Warning: The resource R.string.pref_notifications_title appears to be unused [UnusedResources]
    <string name="pref_notifications_title">Notifications</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:253: Warning: The resource R.string.pref_key_target_achieved_notif appears to be unused [UnusedResources]
    <string name="pref_key_target_achieved_notif">target_achieved_notification</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:254: Warning: The resource R.string.pref_title_target_achieved_notif appears to be unused [UnusedResources]
    <string name="pref_title_target_achieved_notif">Target Achieved Notifications</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:255: Warning: The resource R.string.pref_summary_target_achieved_notif appears to be unused [UnusedResources]
    <string name="pref_summary_target_achieved_notif">Receive a notification when a KPI target is met.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:256: Warning: The resource R.string.pref_key_expiry_reminder_notif appears to be unused [UnusedResources]
    <string name="pref_key_expiry_reminder_notif">expiry_reminder_notification</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:257: Warning: The resource R.string.pref_title_expiry_reminder_notif appears to be unused [UnusedResources]
    <string name="pref_title_expiry_reminder_notif">Expiry Reminder Notifications</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:258: Warning: The resource R.string.pref_summary_expiry_reminder_notif appears to be unused [UnusedResources]
    <string name="pref_summary_expiry_reminder_notif">Receive reminders for KPIs nearing review dates.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:259: Warning: The resource R.string.settings_activity_title appears to be unused [UnusedResources]
    <string name="settings_activity_title">Settings</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:261: Warning: The resource R.string.overall_summary_context_menu_title appears to be unused [UnusedResources]
    <string name="overall_summary_context_menu_title">Overall Summary Card Actions</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:262: Warning: The resource R.string.user_summary_context_menu_title appears to be unused [UnusedResources]
    <string name="user_summary_context_menu_title">User Card Actions</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:263: Warning: The resource R.string.kpi_card_context_menu_title appears to be unused [UnusedResources]
    <string name="kpi_card_context_menu_title">KPI Card Actions</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:264: Warning: The resource R.string.action_customize_colors appears to be unused [UnusedResources]
    <string name="action_customize_colors">Customize Colors</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:265: Warning: The resource R.string.dialog_select_card_colors_title appears to be unused [UnusedResources]
    <string name="dialog_select_card_colors_title">Select Card Colors</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:266: Warning: The resource R.string.label_top_color appears to be unused [UnusedResources]
    <string name="label_top_color">Top Color:</string>
            ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:267: Warning: The resource R.string.label_bottom_color appears to be unused [UnusedResources]
    <string name="label_bottom_color">Bottom Color:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:268: Warning: The resource R.string.label_individual_color appears to be unused [UnusedResources]
    <string name="label_individual_color">Card Background:</string> <!-- For individual KPI card -->
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:269: Warning: The resource R.string.colors_updated_successfully appears to be unused [UnusedResources]
    <string name="colors_updated_successfully">Card colors updated.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:270: Warning: The resource R.string.error_updating_colors appears to be unused [UnusedResources]
    <string name="error_updating_colors">Error updating colors.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:272: Warning: The resource R.string.performance_report_title appears to be unused [UnusedResources]
    <string name="performance_report_title">Performance Report</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:280: Warning: The resource R.string.selected_users_label appears to be unused [UnusedResources]
    <string name="selected_users_label">Selected Users:</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:281: Warning: The resource R.string.select_users_button appears to be unused [UnusedResources]
    <string name="select_users_button">Select Users</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:285: Warning: The resource R.string.add_progress_entry_button appears to be unused [UnusedResources]
    <string name="add_progress_entry_button">Add Progress Entry</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:286: Warning: The resource R.string.filter_all appears to be unused [UnusedResources]
    <string name="filter_all">All</string>
            ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:287: Warning: The resource R.string.filter_expiry appears to be unused [UnusedResources]
    <string name="filter_expiry">Expiry</string>
            ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:288: Warning: The resource R.string.filter_report appears to be unused [UnusedResources]
    <string name="filter_report">Interactive Report</string>
            ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:289: Warning: The resource R.string.filter_task_follow_up appears to be unused [UnusedResources]
    <string name="filter_task_follow_up">Task follow-up</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:302: Warning: The resource R.string.task_name_hint appears to be unused [UnusedResources]
    <string name="task_name_hint">Task Name</string>
            ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:303: Warning: The resource R.string.task_expiration_date_hint appears to be unused [UnusedResources]
    <string name="task_expiration_date_hint">Expiration Date</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:306: Warning: The resource R.string.add_task_button_text appears to be unused [UnusedResources]
    <string name="add_task_button_text">Add</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:309: Warning: The resource R.string.no_date_placeholder appears to be unused [UnusedResources]
    <string name="no_date_placeholder">No date</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:332: Warning: The resource R.string.reset_to_default_colors_button appears to be unused [UnusedResources]
    <string name="reset_to_default_colors_button">Reset to Default</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:342: Warning: The resource R.string.kpi_card_item_average_label appears to be unused [UnusedResources]
    <string name="kpi_card_item_average_label">Average</string> <!-- Added for conditional label -->
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:343: Warning: The resource R.string.overall_summary appears to be unused [UnusedResources]
    <string name="overall_summary">Overall Summary</string>
            ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:350: Warning: The resource R.string.action_view_report appears to be unused [UnusedResources]
    <string name="action_view_report">Interactive Performance Report</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:372: Warning: The resource R.string.kpi_annual_target_label_formatted appears to be unused [UnusedResources]
    <string name="kpi_annual_target_label_formatted">TODO</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:373: Warning: The resource R.string.kpi_monthly_target_label_formatted appears to be unused [UnusedResources]
    <string name="kpi_monthly_target_label_formatted">TODO</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:374: Warning: The resource R.string.kpi_daily_target_label_formatted appears to be unused [UnusedResources]
    <string name="kpi_daily_target_label_formatted">TODO</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:403: Warning: The resource R.string.kpi_name_not_editable_editing appears to be unused [UnusedResources]
    <string name="kpi_name_not_editable_editing">KPI name cannot be changed during edit</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\styles.xml:4: Warning: The resource R.style.CustomFilterChip appears to be unused [UnusedResources]
    <style name="CustomFilterChip" parent="Widget.MaterialComponents.Chip.Filter">
           ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\drawable\text_view_border.xml:2: Warning: The resource R.drawable.text_view_border appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\unified_report_table.xml:2: Warning: The resource R.layout.unified_report_table appears to be unused [UnusedResources]
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\unified_report_table_row.xml:2: Warning: The resource R.layout.unified_report_table_row appears to be unused [UnusedResources]
<TableRow xmlns:android="http://schemas.android.com/apk/res/android"
^

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:354: Warning: This GridLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                <GridLayout
                 ~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\compact_report_table.xml:30: Warning: This TableRow layout or its TableLayout parent is unnecessary; transfer the background attribute to the other view [UselessParent]
                <TableRow
                 ~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\compact_report_table_row.xml:8: Warning: This TableRow layout or its TableLayout parent is unnecessary [UselessParent]
    <TableRow
     ~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_chat_message_received.xml:10: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
    <LinearLayout
     ~~~~~~~~~~~~

   Explanation for issues of type "UselessParent":
   A layout with children that has no siblings, is not a scrollview or a root
   layout, and does not have a background, can be removed and have its
   children moved directly into the parent for a flatter and more efficient
   layout hierarchy.

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:747: Warning: activity_add_edit_kpi.xml has more than 80 views, bad for performance [TooManyViews]
                            <com.google.android.material.chip.Chip
                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "TooManyViews":
   Using too many views in a single layout is bad for performance. Consider
   using compound drawables or other tricks for reducing the number of views
   in this layout.

   The maximum view count defaults to 80 but can be configured with the
   environment variable ANDROID_LINT_MAX_VIEW_COUNT.

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\values\strings.xml:49: Warning: Replace "..." with ellipsis character (…, &#8230;) ? [TypographyEllipsis]
    <string name="search_hint">Search KPIs...</string>
                               ~~~~~~~~~~~~~~

   Explanation for issues of type "TypographyEllipsis":
   You can replace the string "..." with a dedicated ellipsis character,
   ellipsis character (u2026, &#8230;). This can help make the text more
   readable.

   https://en.wikipedia.org/wiki/Ellipsis

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png: Warning: Launcher icon used as round icon did not have a circular shape [IconLauncherShape]

   Explanation for issues of type "IconLauncherShape":
   According to the Android Design Guide
   (https://d.android.com/r/studio-ui/designer/material/iconography) your
   launcher icons should "use a distinct silhouette", a "three-dimensional,
   front view, with a slight perspective as if viewed from above, so that
   users perceive some depth."

   The unique silhouette implies that your launcher icon should not be a
   filled square.

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\dialog_select_card_colors.xml:86: Warning: Cancel button should be on the left (was ""Reset to Default" | Cancel", should be "Cancel | "Reset to Default"") [ButtonOrder]
        <Button
         ~~~~~~

   Explanation for issues of type "ButtonOrder":
   According to the Android Design Guide,

   "Action buttons are typically Cancel and/or OK, with OK indicating the
   preferred or most likely action. However, if the options consist of
   specific actions such as Close or Wait rather than a confirmation or
   cancellation of the action described in the content, then all the buttons
   should be active verbs. As a rule, the dismissive action of a dialog is
   always on the left whereas the affirmative actions are on the right."

   This check looks for button bars and buttons which look like cancel
   buttons, and makes sure that these are on the left.

   https://d.android.com/r/studio-ui/designer/material/dialogs

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:391: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                                android:textSize="10sp"
                                ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:437: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                                android:textSize="10sp"
                                ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:483: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                                android:textSize="10sp"
                                ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:529: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                                android:textSize="10sp"
                                ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_chat_message_received.xml:112: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                        android:textSize="10sp"
                        ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_chat_message_sent.xml:109: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                        android:textSize="10sp"
                        ~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SmallSp":
   Avoid using sizes smaller than 11sp.

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main.xml:9: Warning: Prefer "ifRoom" instead of "always" [AlwaysShowAction]
        app:showAsAction="always" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main_menu.xml:27: Warning: Prefer "ifRoom" instead of "always" [AlwaysShowAction]
        app:showAsAction="always" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "AlwaysShowAction":
   Using showAsAction="always" in menu XML, or MenuItem.SHOW_AS_ACTION_ALWAYS
   in Java code is usually a deviation from the user interface style guide.Use
   ifRoom or the corresponding MenuItem.SHOW_AS_ACTION_IF_ROOM instead.

   If always is used sparingly there are usually no problems and behavior is
   roughly equivalent to ifRoom but with preference over other ifRoom items.
   Using it more than twice in the same menu is a bad idea.

   This check looks for menu XML files that contain more than two always
   actions, or some always actions and no ifRoom actions. In Java code, it
   looks for projects that contain references to
   MenuItem.SHOW_AS_ACTION_ALWAYS and no references to
   MenuItem.SHOW_AS_ACTION_IF_ROOM.

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\ocr_review_item.xml:21: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~

   Explanation for issues of type "Autofill":
   Specify an autofillHints attribute when targeting SDK version 26 or higher
   or explicitly specify that the view is not important for autofill. Your app
   can help an autofill service classify the data correctly by providing the
   meaning of each view that could be autofillable, such as views representing
   usernames, passwords, credit card fields, email addresses, etc.

   The hints can have any value, but it is recommended to use predefined
   values like 'username' for a username or 'creditCardNumber' for a credit
   card number. For a list of all predefined autofill hint constants, see the
   AUTOFILL_HINT_ constants in the View reference at
   https://developer.android.com/reference/android/view/View.html.

   You can mark a view unimportant for autofill by specifying an
   importantForAutofill attribute on that view or a parent view. See
   https://developer.android.com/reference/android/view/View.html#setImportant
   ForAutofill(int).

   https://developer.android.com/guide/topics/text/autofill.html

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ChartMarkerView.kt:17: Warning: Custom view ChartMarkerView is missing constructor used by tools: (Context) or (Context,AttributeSet) or (Context,AttributeSet,int) [ViewConstructor]
class ChartMarkerView(context: Context, layoutResource: Int) : MarkerView(context, layoutResource) {
      ~~~~~~~~~~~~~~~

   Explanation for issues of type "ViewConstructor":
   Some layout tools (such as the Android layout editor) need to find a
   constructor with one of the following signatures:
   * View(Context context)
   * View(Context context, AttributeSet attrs)
   * View(Context context, AttributeSet attrs, int defStyle)

   If your custom view needs to perform initialization which does not apply
   when used in a layout editor, you can surround the given code with a check
   to see if View#isInEditMode() is false, since that method will return false
   at runtime but true within a user interface editor.

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\AddEditAdvancedTaskActivity.kt:388: Warning: Use the KTX extension property View.isGone instead? [UseKtx]
        if (layout.visibility == View.GONE) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\AddEditAdvancedTaskActivity.kt:1217: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse("geo:0,0?q=الرياض"))
                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\AddEditAdvancedTaskActivity.kt:1981: Warning: Use the KTX extension function String.toColorInt instead? [UseKtx]
            val colorInt = android.graphics.Color.parseColor(color)
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\AddEditAdvancedTaskActivity.kt:2372: Warning: Use the KTX extension function String.toColorInt instead? [UseKtx]
            val selectedColor = Color.parseColor(colorHex)
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:713: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        prefs.edit()
        ~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:1394: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        prefs.edit().putString(key, value).apply()
        ~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:1399: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        prefs.edit().putBoolean(key, value).apply()
        ~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:1650: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        prefs.edit().putString(key, value).apply()
        ~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:1655: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        prefs.edit().putBoolean(key, value).apply()
        ~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2048: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        prefs.edit()
        ~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\AppDetectionHelper.kt:46: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
            data = Uri.parse("mailto:")
                   ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\AppDetectionHelper.kt:46: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
            data = Uri.parse("mailto:")
                   ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\AppDetectionHelper.kt:302: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                data = Uri.parse("mailto:")
                       ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\AppDetectionHelper.kt:302: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                data = Uri.parse("mailto:")
                       ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\workers\AutoReportSenderWorker.kt:189: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("https://wa.me/$cleanPhone?text=${Uri.encode(message)}")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\workers\AutoReportSenderWorker.kt:189: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("https://wa.me/$cleanPhone?text=${Uri.encode(message)}")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\workers\AutoReportSenderWorker.kt:195: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("https://wa.me/$cleanPhone?text=${Uri.encode(message)}")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\workers\AutoReportSenderWorker.kt:195: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("https://wa.me/$cleanPhone?text=${Uri.encode(message)}")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\workers\AutoReportSenderWorker.kt:201: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("https://wa.me/$cleanPhone?text=${Uri.encode(message)}")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\workers\AutoReportSenderWorker.kt:201: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("https://wa.me/$cleanPhone?text=${Uri.encode(message)}")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\workers\AutoReportSenderWorker.kt:265: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("mailto:$email")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\workers\AutoReportSenderWorker.kt:265: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("mailto:$email")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\FirebaseMessageManager.kt:236: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        prefs.edit().putString("fcm_token", token).apply()
        ~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\services\FirebaseMessagingService.kt:86: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        prefs.edit().putString("fcm_token", token).apply()
        ~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\services\FirebaseMessagingService.kt:95: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
            prefs.edit()
            ~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\KpiListAdapter.kt:167: Warning: Use the KTX extension function String.toColorInt instead? [UseKtx]
                kpi.cardColorHex?.takeIf { it.isNotBlank() }?.let { Color.parseColor(it) } ?: defaultCardColor
                                                                    ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\LanguageManager.kt:28: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        prefs.edit().putString(KEY_LANGUAGE, language).apply()
        ~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\MainActivity.kt:266: Warning: Use the KTX extension property View.isVisible instead? [UseKtx]
            val isCurrentlyVisible = overallCardView.visibility == View.VISIBLE
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\MainActivity.kt:630: Warning: Use the KTX function createBitmap instead? [UseKtx]
            val bitmap = Bitmap.createBitmap(view.width, view.height, Bitmap.Config.ARGB_8888)
                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\MainActivity.kt:1172: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
                sharedPreferences.edit().clear().apply()
                ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\fragments\MainDashboardFragment.kt:273: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
            val editor = sharedPrefs.edit()
                         ~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ModernReportActivity.kt:136: Warning: Use the KTX extension property View.isVisible instead? [UseKtx]
        Log.d("ModernReportActivity", "onCreate: ScrollView visibility: ${binding.scrollView.visibility == View.VISIBLE}")
                                                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ModernReportActivity.kt:137: Warning: Use the KTX extension property View.isVisible instead? [UseKtx]
        Log.d("ModernReportActivity", "onCreate: BarChart container visibility: ${binding.barChartContainer.visibility == View.VISIBLE}")
                                                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ModernReportActivity.kt:138: Warning: Use the KTX extension property View.isVisible instead? [UseKtx]
        Log.d("ModernReportActivity", "onCreate: BarChart visibility: ${binding.summaryBarChart.visibility == View.VISIBLE}")
                                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ModernReportActivity.kt:572: Warning: Use the KTX extension property View.isVisible instead? [UseKtx]
        Log.d("ModernReportActivity", "Bar Chart Container visibility: ${binding.barChartContainer.visibility == View.VISIBLE}")
                                                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ModernReportActivity.kt:573: Warning: Use the KTX extension property View.isVisible instead? [UseKtx]
        Log.d("ModernReportActivity", "Bar Chart visibility: ${barChart.visibility == View.VISIBLE}")
                                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ModernReportActivity.kt:574: Warning: Use the KTX extension property View.isVisible instead? [UseKtx]
        Log.d("ModernReportActivity", "Bar Chart Title visibility: ${binding.barChartTitle.visibility == View.VISIBLE}")
                                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ModernReportActivity.kt:948: Warning: Use the KTX function createBitmap instead? [UseKtx]
        val bitmap = Bitmap.createBitmap(view.width, view.height, Bitmap.Config.ARGB_8888)
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\NotificationManager.kt:196: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        prefs.edit()
        ~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\NotificationManager.kt:241: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        prefs.edit().putBoolean("notification_${notificationId}_read", true).apply()
        ~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\NotificationManager.kt:247: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        prefs.edit().clear().apply()
        ~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\SessionManager.kt:73: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        sharedPreferences?.edit()?.clear()?.apply()
        ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskManagementActivity.kt:208: Warning: Use the KTX extension property View.isGone instead? [UseKtx]
        if (binding.searchView.visibility == android.view.View.GONE) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskManagementActivity.kt:270: Warning: Use the KTX extension property View.isVisible instead? [UseKtx]
        if (binding.layoutEisenhowerMatrix.visibility == android.view.View.VISIBLE) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskManagementActivity.kt:449: Warning: Use the KTX extension function String.toColorInt instead? [UseKtx]
                titleView.setTextColor(android.graphics.Color.parseColor(color))
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskManagementActivity.kt:559: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
                    sharedPrefs.edit().putBoolean("has_seen_matrix_intro", true).apply()
                    ~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskManagementActivity.kt:563: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
                    sharedPrefs.edit().putBoolean("has_seen_matrix_intro", true).apply()
                    ~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskReminderSettingsActivity.kt:197: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("https://wa.me/$cleanPhone?text=${Uri.encode(message)}")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskReminderSettingsActivity.kt:197: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("https://wa.me/$cleanPhone?text=${Uri.encode(message)}")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskReminderSettingsActivity.kt:202: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("https://wa.me/$cleanPhone?text=${Uri.encode(message)}")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskReminderSettingsActivity.kt:202: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("https://wa.me/$cleanPhone?text=${Uri.encode(message)}")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskReminderSettingsActivity.kt:207: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("https://wa.me/$cleanPhone?text=${Uri.encode(message)}")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskReminderSettingsActivity.kt:207: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("https://wa.me/$cleanPhone?text=${Uri.encode(message)}")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskReminderSettingsActivity.kt:283: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("mailto:$email")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskReminderSettingsActivity.kt:283: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("mailto:$email")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\workers\TaskReminderWorker.kt:314: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("https://wa.me/$cleanPhone?text=${Uri.encode(message)}")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\workers\TaskReminderWorker.kt:314: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("https://wa.me/$cleanPhone?text=${Uri.encode(message)}")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\workers\TaskReminderWorker.kt:320: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("https://wa.me/$cleanPhone?text=${Uri.encode(message)}")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\workers\TaskReminderWorker.kt:320: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("https://wa.me/$cleanPhone?text=${Uri.encode(message)}")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\workers\TaskReminderWorker.kt:326: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("https://wa.me/$cleanPhone?text=${Uri.encode(message)}")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\workers\TaskReminderWorker.kt:326: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("https://wa.me/$cleanPhone?text=${Uri.encode(message)}")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\workers\TaskReminderWorker.kt:385: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("mailto:$email")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\workers\TaskReminderWorker.kt:385: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("mailto:$email")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\ThemeManager.kt:17: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        prefs.edit().putString(KEY_THEME_MODE, theme).apply()
        ~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\UserKpiListActivity.kt:323: Warning: Use the KTX function createBitmap instead? [UseKtx]
             val bitmap = Bitmap.createBitmap(view.measuredWidth, view.measuredHeight, Bitmap.Config.ARGB_8888)
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\UserKpiListActivity.kt:330: Warning: Use the KTX function createBitmap instead? [UseKtx]
            val bitmap = Bitmap.createBitmap(view.width, view.height, Bitmap.Config.ARGB_8888)
                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\UserKpiListActivity.kt:407: Warning: Use the KTX extension function String.toColorInt instead? [UseKtx]
                hexColor?.let { Color.parseColor(it) } ?: defaultColorInt
                                ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\UserKpiListActivity.kt:426: Warning: Use the KTX extension function String.toColorInt instead? [UseKtx]
            val initialColorInt = selectedColorHex?.let { try { Color.parseColor(it) } catch (e: Exception) { null } }
                                                                ~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseKtx":
   The Android KTX libraries decorates the Android platform SDK as well as
   various libraries with more convenient extension functions available from
   Kotlin, allowing you to use default parameters, named parameters, and
   more.

   Available options:

   **remove-defaults** (default is true):
   Whether to skip arguments that match the defaults provided by the extension.

   Extensions often provide default values for some of the parameters. For example:
   ```kotlin
   fun Path.readLines(charset: Charset = Charsets.UTF_8): List<String> { return Files.readAllLines(this, charset) }
   ```
   This lint check will by default automatically omit parameters that match the default, so if your code was calling ```kotlin
   Files.readAllLines(file, Charset.UTF_8)
   ```
   lint would replace this with
   ```kotlin
   file.readLines()
   ```
   rather than
   ```kotlin
   file.readLines(Charset.UTF_8
   ```
   You can turn this behavior off using this option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UseKtx">
           <option name="remove-defaults" value="true" />
       </issue>
   </lint>
   ```

   **require-present** (default is true):
   Whether to only offer extensions already available.

   This option lets you only have lint suggest extension replacements if those extensions are already available on the class path (in other words, you're already depending on the library containing the extension method.)

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UseKtx">
           <option name="require-present" value="true" />
       </issue>
   </lint>
   ```

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build.gradle.kts:109: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.code.gson:gson:2.10.1") // Add Gson for JSON parsing
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build.gradle.kts:110: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.github.dhaval2404:colorpicker:2.3") // Add Color Picker library
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build.gradle.kts:116: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.android.gms:play-services-mlkit-text-recognition:19.0.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build.gradle.kts:130: Warning: Use version catalog instead [UseTomlInstead]
    implementation("org.apache.poi:poi:5.2.5") // For .xls files (Updated)
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build.gradle.kts:131: Warning: Use version catalog instead [UseTomlInstead]
    implementation("org.apache.poi:poi-ooxml:5.2.5") // For .xlsx files (Updated)
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build.gradle.kts:133: Warning: Use version catalog instead [UseTomlInstead]
    implementation("org.apache.xmlbeans:xmlbeans:5.2.0") // (Updated)
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build.gradle.kts:134: Warning: Use version catalog instead [UseTomlInstead]
    implementation("org.apache.commons:commons-compress:1.26.0") // Required by POI (Updated)
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build.gradle.kts:135: Warning: Use version catalog instead [UseTomlInstead]
    implementation("org.apache.commons:commons-collections4:4.4") // Required by POI (Seems current)
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build.gradle.kts:143: Warning: Use version catalog instead [UseTomlInstead]
    implementation(platform("com.google.firebase:firebase-bom:32.7.0"))
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build.gradle.kts:144: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.firebase:firebase-database-ktx")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build.gradle.kts:145: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.firebase:firebase-messaging-ktx")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build.gradle.kts:146: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.firebase:firebase-analytics-ktx")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseTomlInstead":
   If your project is using a libs.versions.toml file, you should place all
   Gradle dependencies in the TOML file. This lint check looks for version
   declarations outside of the TOML file and suggests moving them (and in the
   IDE, provides a quickfix to performing the operation automatically).

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\utils\CardGestureHelper.kt:71: Warning: CardGestureHelper#onTouch should call View#performClick when a click is detected [ClickableViewAccessibility]
    override fun onTouch(view: View, event: MotionEvent): Boolean {
                 ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskManagementActivity.kt:783: Warning: Custom view `MaterialCardView` has setOnTouchListener called on it but does not override performClick [ClickableViewAccessibility]
        binding.cardQuickStats?.setOnTouchListener { view, event ->
        ^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskManagementActivity.kt:783: Warning: onTouch lambda should call View#performClick when a click is detected [ClickableViewAccessibility]
        binding.cardQuickStats?.setOnTouchListener { view, event ->
                                                   ^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\UserSummaryAdapter.kt:288: Warning: Custom view `MaterialCardView` has setOnTouchListener called on it but does not override performClick [ClickableViewAccessibility]
            binding.root.setOnTouchListener(gestureHelper)
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\UserSummaryAdapter.kt:350: Warning: Custom view `ImageView` has setOnTouchListener called on it but does not override performClick [ClickableViewAccessibility]
            binding.dragHandleImageView.setOnTouchListener { view, event ->
            ^
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\UserSummaryAdapter.kt:350: Warning: onTouch lambda should call View#performClick when a click is detected [ClickableViewAccessibility]
            binding.dragHandleImageView.setOnTouchListener { view, event ->
                                                           ^

   Explanation for issues of type "ClickableViewAccessibility":
   If a View that overrides onTouchEvent or uses an OnTouchListener does not
   also implement performClick and call it when clicks are detected, the View
   may not handle accessibility actions properly. Logic handling the click
   actions should ideally be placed in View#performClick as some accessibility
   services invoke performClick when a click action should occur.

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:486: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:537: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_chat.xml:92: Warning: Missing contentDescription attribute on image [ContentDescription]
            <com.google.android.material.floatingactionbutton.FloatingActionButton
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_chat_list.xml:69: Warning: Missing contentDescription attribute on image [ContentDescription]
    <com.google.android.material.floatingactionbutton.FloatingActionButton
     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:117: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:145: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:173: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_chat_message_received.xml:17: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_conversation.xml:28: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_conversation.xml:99: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_task_enhanced.xml:192: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_user_list.xml:28: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi_original.xml:120: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <AutoCompleteTextView
             ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:114: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
                        <AutoCompleteTextView
                         ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:167: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
                            <AutoCompleteTextView
                             ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:188: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
                            <AutoCompleteTextView
                             ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:210: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
                        <AutoCompleteTextView
                         ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:325: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
                        <AutoCompleteTextView
                         ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_login.xml:142: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
                <AutoCompleteTextView
                 ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_modern_report.xml:73: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
                <AutoCompleteTextView
                 ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_modern_report.xml:104: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
                <AutoCompleteTextView
                 ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_report.xml:47: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
                <AutoCompleteTextView
                 ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\ocr_review_item.xml:21: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
    <EditText
     ~~~~~~~~

   Explanation for issues of type "LabelFor":
   Editable text fields should provide an android:hint or, provided your
   minSdkVersion is at least 17, they may be referenced by a view with a
   android:labelFor attribute.

   When using android:labelFor, be sure to provide an android:text or an
   android:contentDescription.

   If your view is labeled but by a label in a different layout which includes
   this one, just suppress this warning from lint.

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\AddEditAdvancedTaskActivity.kt:244: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            binding.tvProgressValue.text = "${taskProgress}%"
                                           ~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\AddEditAdvancedTaskActivity.kt:1207: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                    binding.taskLocationEditText.setText("📍 $location")
                                                         ~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\AddEditAdvancedTaskActivity.kt:2332: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            binding.taskNameEditText.setText("$icon $cleanTitle")
                                             ~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\AddEditAdvancedTaskActivity.kt:2390: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            binding.taskNameEditText.setText("$icon $cleanTitle")
                                             ~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\AddEditAdvancedTaskActivity.kt:2392: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            binding.taskNameEditText.setText("$icon مهمة جديدة")
                                             ~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:531: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "Send login credentials to user"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:1806: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "Select Recipients:"
                    ~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:1814: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "Select All Users"
                    ~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:1823: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                text = "${user.name} (${user.role})"
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:1839: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            text = "\nMessage Type:"
                   ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:1839: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "\nMessage Type:"
                      ~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:1870: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            text = "\nSend Options:"
                   ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:1870: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "\nSend Options:"
                      ~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:1877: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "📱 In-App Notification"
                    ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:1883: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "📧 Email Notification"
                    ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2114: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            text = "📧 Recipients: ${users.size} users with email addresses"
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2114: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "📧 Recipients: ${users.size} users with email addresses"
                    ~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2114: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "📧 Recipients: ${users.size} users with email addresses"
                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2121: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            text = "\nEmail Template:"
                   ~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2121: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "\nEmail Template:"
                      ~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2162: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        subjectInput.setText("Monthly Performance Report - KPI Tracker")
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2162: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        subjectInput.setText("Monthly Performance Report - KPI Tracker")
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2163: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        messageInput.setText("Dear Team,\n\nPlease find your monthly performance report attached. Review your KPI progress and plan for the upcoming month.\n\nBest regards,\nAdmin Team")
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2163: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        messageInput.setText("Dear Team,\n\nPlease find your monthly performance report attached. Review your KPI progress and plan for the upcoming month.\n\nBest regards,\nAdmin Team")
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2163: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear Team,\n\nPlease find your monthly performance report attached. Review your KPI progress and plan for the upcoming month.\n\nBest regards,\nAdmin Team")
                                              ~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2163: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear Team,\n\nPlease find your monthly performance report attached. Review your KPI progress and plan for the upcoming month.\n\nBest regards,\nAdmin Team")
                                              ~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2163: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear Team,\n\nPlease find your monthly performance report attached. Review your KPI progress and plan for the upcoming month.\n\nBest regards,\nAdmin Team")
                                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2163: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear Team,\n\nPlease find your monthly performance report attached. Review your KPI progress and plan for the upcoming month.\n\nBest regards,\nAdmin Team")
                                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2163: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear Team,\n\nPlease find your monthly performance report attached. Review your KPI progress and plan for the upcoming month.\n\nBest regards,\nAdmin Team")
                                                                                                                                                                               ~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2163: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear Team,\n\nPlease find your monthly performance report attached. Review your KPI progress and plan for the upcoming month.\n\nBest regards,\nAdmin Team")
                                                                                                                                                                               ~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2163: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear Team,\n\nPlease find your monthly performance report attached. Review your KPI progress and plan for the upcoming month.\n\nBest regards,\nAdmin Team")
                                                                                                                                                                                              ~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2163: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear Team,\n\nPlease find your monthly performance report attached. Review your KPI progress and plan for the upcoming month.\n\nBest regards,\nAdmin Team")
                                                                                                                                                                                              ~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2166: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        subjectInput.setText("Goal Achievement Reminder - KPI Tracker")
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2166: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        subjectInput.setText("Goal Achievement Reminder - KPI Tracker")
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2167: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        messageInput.setText("Hello,\n\nThis is a friendly reminder to update your KPI progress and work towards achieving your goals.\n\nKeep up the great work!\nAdmin Team")
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2167: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        messageInput.setText("Hello,\n\nThis is a friendly reminder to update your KPI progress and work towards achieving your goals.\n\nKeep up the great work!\nAdmin Team")
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2167: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Hello,\n\nThis is a friendly reminder to update your KPI progress and work towards achieving your goals.\n\nKeep up the great work!\nAdmin Team")
                                              ~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2167: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Hello,\n\nThis is a friendly reminder to update your KPI progress and work towards achieving your goals.\n\nKeep up the great work!\nAdmin Team")
                                              ~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2167: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Hello,\n\nThis is a friendly reminder to update your KPI progress and work towards achieving your goals.\n\nKeep up the great work!\nAdmin Team")
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2167: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Hello,\n\nThis is a friendly reminder to update your KPI progress and work towards achieving your goals.\n\nKeep up the great work!\nAdmin Team")
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2167: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Hello,\n\nThis is a friendly reminder to update your KPI progress and work towards achieving your goals.\n\nKeep up the great work!\nAdmin Team")
                                                                                                                                                          ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2167: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Hello,\n\nThis is a friendly reminder to update your KPI progress and work towards achieving your goals.\n\nKeep up the great work!\nAdmin Team")
                                                                                                                                                          ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2167: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Hello,\n\nThis is a friendly reminder to update your KPI progress and work towards achieving your goals.\n\nKeep up the great work!\nAdmin Team")
                                                                                                                                                                                   ~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2167: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Hello,\n\nThis is a friendly reminder to update your KPI progress and work towards achieving your goals.\n\nKeep up the great work!\nAdmin Team")
                                                                                                                                                                                   ~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2170: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        subjectInput.setText("KPI Update Notification - KPI Tracker")
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2170: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        subjectInput.setText("KPI Update Notification - KPI Tracker")
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2171: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        messageInput.setText("Dear User,\n\nYour KPIs have been updated. Please log in to review the changes and continue tracking your progress.\n\nBest regards,\nAdmin Team")
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2171: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        messageInput.setText("Dear User,\n\nYour KPIs have been updated. Please log in to review the changes and continue tracking your progress.\n\nBest regards,\nAdmin Team")
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2171: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear User,\n\nYour KPIs have been updated. Please log in to review the changes and continue tracking your progress.\n\nBest regards,\nAdmin Team")
                                              ~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2171: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear User,\n\nYour KPIs have been updated. Please log in to review the changes and continue tracking your progress.\n\nBest regards,\nAdmin Team")
                                              ~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2171: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear User,\n\nYour KPIs have been updated. Please log in to review the changes and continue tracking your progress.\n\nBest regards,\nAdmin Team")
                                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2171: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear User,\n\nYour KPIs have been updated. Please log in to review the changes and continue tracking your progress.\n\nBest regards,\nAdmin Team")
                                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2171: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear User,\n\nYour KPIs have been updated. Please log in to review the changes and continue tracking your progress.\n\nBest regards,\nAdmin Team")
                                                                                                                                                                     ~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2171: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear User,\n\nYour KPIs have been updated. Please log in to review the changes and continue tracking your progress.\n\nBest regards,\nAdmin Team")
                                                                                                                                                                     ~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2171: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear User,\n\nYour KPIs have been updated. Please log in to review the changes and continue tracking your progress.\n\nBest regards,\nAdmin Team")
                                                                                                                                                                                    ~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2171: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear User,\n\nYour KPIs have been updated. Please log in to review the changes and continue tracking your progress.\n\nBest regards,\nAdmin Team")
                                                                                                                                                                                    ~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2174: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        subjectInput.setText("Team Achievement Announcement - KPI Tracker")
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2174: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        subjectInput.setText("Team Achievement Announcement - KPI Tracker")
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2175: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        messageInput.setText("Congratulations Team!\n\nWe're excited to announce that our team has achieved outstanding results this month. Thank you for your hard work!\n\nCelebrate your success!\nAdmin Team")
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2175: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        messageInput.setText("Congratulations Team!\n\nWe're excited to announce that our team has achieved outstanding results this month. Thank you for your hard work!\n\nCelebrate your success!\nAdmin Team")
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2175: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Congratulations Team!\n\nWe're excited to announce that our team has achieved outstanding results this month. Thank you for your hard work!\n\nCelebrate your success!\nAdmin Team")
                                              ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2175: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Congratulations Team!\n\nWe're excited to announce that our team has achieved outstanding results this month. Thank you for your hard work!\n\nCelebrate your success!\nAdmin Team")
                                              ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2175: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Congratulations Team!\n\nWe're excited to announce that our team has achieved outstanding results this month. Thank you for your hard work!\n\nCelebrate your success!\nAdmin Team")
                                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2175: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Congratulations Team!\n\nWe're excited to announce that our team has achieved outstanding results this month. Thank you for your hard work!\n\nCelebrate your success!\nAdmin Team")
                                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2175: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Congratulations Team!\n\nWe're excited to announce that our team has achieved outstanding results this month. Thank you for your hard work!\n\nCelebrate your success!\nAdmin Team")
                                                                                                                                                                                             ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2175: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Congratulations Team!\n\nWe're excited to announce that our team has achieved outstanding results this month. Thank you for your hard work!\n\nCelebrate your success!\nAdmin Team")
                                                                                                                                                                                             ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2175: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Congratulations Team!\n\nWe're excited to announce that our team has achieved outstanding results this month. Thank you for your hard work!\n\nCelebrate your success!\nAdmin Team")
                                                                                                                                                                                                                      ~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2175: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Congratulations Team!\n\nWe're excited to announce that our team has achieved outstanding results this month. Thank you for your hard work!\n\nCelebrate your success!\nAdmin Team")
                                                                                                                                                                                                                      ~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2178: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        subjectInput.setText("System Maintenance Notice - KPI Tracker")
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2178: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        subjectInput.setText("System Maintenance Notice - KPI Tracker")
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2179: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        messageInput.setText("Dear Users,\n\nWe will be performing system maintenance on [DATE]. The system may be temporarily unavailable. We apologize for any inconvenience.\n\nThank you for your understanding,\nAdmin Team")
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2179: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        messageInput.setText("Dear Users,\n\nWe will be performing system maintenance on [DATE]. The system may be temporarily unavailable. We apologize for any inconvenience.\n\nThank you for your understanding,\nAdmin Team")
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2179: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear Users,\n\nWe will be performing system maintenance on [DATE]. The system may be temporarily unavailable. We apologize for any inconvenience.\n\nThank you for your understanding,\nAdmin Team")
                                              ~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2179: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear Users,\n\nWe will be performing system maintenance on [DATE]. The system may be temporarily unavailable. We apologize for any inconvenience.\n\nThank you for your understanding,\nAdmin Team")
                                              ~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2179: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear Users,\n\nWe will be performing system maintenance on [DATE]. The system may be temporarily unavailable. We apologize for any inconvenience.\n\nThank you for your understanding,\nAdmin Team")
                                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2179: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear Users,\n\nWe will be performing system maintenance on [DATE]. The system may be temporarily unavailable. We apologize for any inconvenience.\n\nThank you for your understanding,\nAdmin Team")
                                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2179: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear Users,\n\nWe will be performing system maintenance on [DATE]. The system may be temporarily unavailable. We apologize for any inconvenience.\n\nThank you for your understanding,\nAdmin Team")
                                                                                                                                                                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2179: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear Users,\n\nWe will be performing system maintenance on [DATE]. The system may be temporarily unavailable. We apologize for any inconvenience.\n\nThank you for your understanding,\nAdmin Team")
                                                                                                                                                                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2179: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear Users,\n\nWe will be performing system maintenance on [DATE]. The system may be temporarily unavailable. We apologize for any inconvenience.\n\nThank you for your understanding,\nAdmin Team")
                                                                                                                                                                                                                                      ~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2179: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        messageInput.setText("Dear Users,\n\nWe will be performing system maintenance on [DATE]. The system may be temporarily unavailable. We apologize for any inconvenience.\n\nThank you for your understanding,\nAdmin Team")
                                                                                                                                                                                                                                      ~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2304: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            text = "📤 Sending to: ${user.name}"
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\AdminDashboardActivity.kt:2304: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "📤 Sending to: ${user.name}"
                    ~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ChartMarkerView.kt:36: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                tvValue.text = "Value: ${formatter.format(entryData.value.roundToInt())}"
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ChartMarkerView.kt:36: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                tvValue.text = "Value: ${formatter.format(entryData.value.roundToInt())}"
                                ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ChartMarkerView.kt:37: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                tvTarget.text = "Target: ${formatter.format(entryData.target.roundToInt())}"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ChartMarkerView.kt:37: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                tvTarget.text = "Target: ${formatter.format(entryData.target.roundToInt())}"
                                 ~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ChartMarkerView.kt:38: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                tvPercentage.text = "Achievement: ${entryData.percentage}%"
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ChartMarkerView.kt:38: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                tvPercentage.text = "Achievement: ${entryData.percentage}%"
                                     ~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ChartMarkerView.kt:54: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                tvComparison.text = "vs Previous: $comparisonText"
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ChartMarkerView.kt:54: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                tvComparison.text = "vs Previous: $comparisonText"
                                     ~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ChartMarkerView.kt:58: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                tvValue.text = "Value: ${entry.y.roundToInt()}"
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ChartMarkerView.kt:58: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                tvValue.text = "Value: ${entry.y.roundToInt()}"
                                ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ChartMarkerView.kt:59: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                tvTarget.text = "Target: N/A"
                                 ~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ChartMarkerView.kt:60: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                tvPercentage.text = "Achievement: N/A"
                                     ~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ChartMarkerView.kt:61: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                tvComparison.text = "vs Previous: N/A"
                                     ~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\adapters\ChatMessageAdapter.kt:106: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                    messageText.text = "📊 ${item.message.message}"
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\adapters\ChatMessageAdapter.kt:110: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                    messageText.text = "📈 ${item.message.message}"
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\adapters\ChatMessageAdapter.kt:149: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                    messageText.text = "📊 ${item.message.message}"
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\adapters\ChatMessageAdapter.kt:153: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                    messageText.text = "📈 ${item.message.message}"
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\EnhancedTaskAdapter.kt:64: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            binding.tvTaskDetails.text = "$formattedDate • $priorityText"
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\EnhancedTaskAdapter.kt:128: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            binding.tvProgressPercent.text = "${task.progress}% مكتمل"
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\EnhancedTaskAdapter.kt:195: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                binding.tvFocusTime.text = "${task.estimatedHours} ساعة"
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExcelImportActivity.kt:112: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                binding.tvExcelImportStatus.text = "Import process completed."
                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExcelImportActivity.kt:117: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                binding.tvExcelImportStatus.text = "Import cancelled or failed."
                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExcelImportActivity.kt:152: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            binding.tvExcelImportStatus.text = "Waiting for file selection..."
                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExcelImportActivity.kt:164: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        binding.tvExcelImportStatus.text = "Processing Excel file..."
                                            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExcelImportActivity.kt:206: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                                                    binding.tvExcelImportStatus.text = "No valid data found using selected columns."
                                                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExcelImportActivity.kt:216: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                                                    binding.tvExcelImportStatus.text = "Parsed ${results.size} entries. Ready for review."
                                                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExcelImportActivity.kt:216: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                                                    binding.tvExcelImportStatus.text = "Parsed ${results.size} entries. Ready for review."
                                                                                        ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExcelImportActivity.kt:216: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                                                    binding.tvExcelImportStatus.text = "Parsed ${results.size} entries. Ready for review."
                                                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExcelImportActivity.kt:226: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                                                binding.tvExcelImportStatus.text = "Error extracting data: ${extractError.localizedMessage}"
                                                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExcelImportActivity.kt:226: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                                                binding.tvExcelImportStatus.text = "Error extracting data: ${extractError.localizedMessage}"
                                                                                    ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExcelImportActivity.kt:308: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        layout.addView(TextView(context).apply { text = "Select Date Column:"; textSize = 16f })
                                                         ~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExcelImportActivity.kt:310: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        layout.addView(TextView(context).apply { text = "Select Value Column:"; textSize = 16f; setPadding(0, 20, 0, 0) })
                                                         ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExcelImportActivity.kt:312: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        layout.addView(TextView(context).apply { text = "Select User Identifier Column (Optional):"; textSize = 16f; setPadding(0, 20, 0, 0) }) // Re-add user label
                                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExcelImportActivity.kt:331: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                     binding.tvExcelImportStatus.text = "Column selection error."
                                                         ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExcelImportActivity.kt:343: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                binding.tvExcelImportStatus.text = "Import cancelled."
                                                    ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExcelImportActivity.kt:571: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        binding.tvExcelImportStatus.text = "Waiting for review..."
                                            ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExcelReviewActivity.kt:90: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            binding.tvExcelReviewTitle.text = "Error: Target KPI ID missing."
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExcelReviewActivity.kt:100: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
             binding.tvExcelReviewTitle.text = "Error: Data ID missing."
                                                ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExcelReviewActivity.kt:164: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
         binding.tvExcelReviewTitle.text = "Review Excel Import Data"
                                            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExpireManagementActivity.kt:63: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        binding.selectDateButton.text = "التاريخ المحدد: ${dateFormat.format(selectedDateCalendar.time)}"
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExpireManagementActivity.kt:86: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            binding.expiryReturnMessageTextView.text = "المطلوب: إرجاع منتهيات شهر $nextMonthName"
                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExpireManagementActivity.kt:92: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            binding.expiryReturnMessageTextView.text = "المطلوب: فصل الأصناف التي تنتهي في نهاية $monthPlus2Name ووضعها في كراتين مغلقة مكتوب عليها معده للإرجاع بعد ادخالها علي النظام"
                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ExpireManagementActivity.kt:107: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            binding.nearExpiryMessageTextView.text = "المطلوب: وضع الأصناف التي تنتهي في $monthPlus3Name و $monthPlus4Name في منطقة قريبة الانتهاء"
                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\KpiDetailActivity.kt:247: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        binding.detailKpiTargetValueTextView.text = "Annual: ${formatValue(kpi.annualTarget, kpi.unit, false)}"
                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\KpiDetailActivity.kt:247: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        binding.detailKpiTargetValueTextView.text = "Annual: ${formatValue(kpi.annualTarget, kpi.unit, false)}"
                                                     ~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\KpiDetailActivity.kt:265: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        binding.progressSummaryTextView.text = "${formatValue(kpiWithProgress.currentProgressValue, kpi.unit, true)}/${formatValue(kpi.annualTarget, kpi.unit, true)}"
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\KpiDetailActivity.kt:360: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            binding.dailyProgressPercentageText.text = "$percentage%"
                                                       ~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\KpiDetailActivity.kt:362: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            binding.dailyProgressPercentageTextView.text = "$percentage%"
                                                           ~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\KpiDetailActivity.kt:377: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            binding.monthlyProgressPercentageText.text = "$percentage%"
                                                         ~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\KpiDetailActivity.kt:379: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            binding.monthlyProgressPercentageTextView.text = "$percentage%"
                                                             ~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\KpiDetailActivity.kt:395: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        binding.annualProgressPercentageText.text = "$annualPercentage%"
                                                    ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\KpiDetailActivity.kt:397: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        binding.annualProgressPercentageTextView.text = "$annualPercentage%"
                                                        ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\KpiListAdapter.kt:215: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                binding.currentPeriodAchievementValueTextView.text = "${kpiWithProgress.currentPeriodAchievement}%"
                                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\KpiListAdapter.kt:244: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                binding.requiredDailyValueTextView.text = "$dailyRateFormatted / day (${kpiWithProgress.remainingDaysInMonth} days left)"
                                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\KpiListAdapter.kt:244: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                binding.requiredDailyValueTextView.text = "$dailyRateFormatted / day (${kpiWithProgress.remainingDaysInMonth} days left)"
                                                                              ~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\KpiListAdapter.kt:244: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                binding.requiredDailyValueTextView.text = "$dailyRateFormatted / day (${kpiWithProgress.remainingDaysInMonth} days left)"
                                                                                                                             ~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\LoginActivity.kt:269: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            binding.loginButton.text = "Sign In as Admin"
                                        ~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\LoginActivity.kt:272: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            binding.loginButton.text = "Sign In"
                                        ~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\LoginActivity.kt:292: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        binding.adminRoleAutoComplete.setText("Admin", false)
                                               ~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\MainActivity.kt:353: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            nameTextView.text = "$emoji $title"
                                ~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ModernReportActivity.kt:207: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    binding.userFilterAutoCompleteTextView.setText("All Users")
                                                                    ~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ModernReportActivity.kt:209: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    binding.userFilterAutoCompleteTextView.setText("No Users Selected")
                                                                    ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ModernReportActivity.kt:213: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                    binding.userFilterAutoCompleteTextView.setText("${selectedUsers.size} Users Selected")
                                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\ModernReportActivity.kt:213: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    binding.userFilterAutoCompleteTextView.setText("${selectedUsers.size} Users Selected")
                                                                                         ~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\NotificationsActivity.kt:58: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            binding.emptyStateText.text = "📭 No notifications yet\n\nYou'll see admin messages, KPI alerts, and reminders here."
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\NotificationsActivity.kt:58: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            binding.emptyStateText.text = "📭 No notifications yet\n\nYou'll see admin messages, KPI alerts, and reminders here."
                                           ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\NotificationsActivity.kt:58: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            binding.emptyStateText.text = "📭 No notifications yet\n\nYou'll see admin messages, KPI alerts, and reminders here."
                                                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\OcrActivity.kt:236: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        binding.tvOcrResult.text = "Processing..." // Indicate processing
                                    ~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\OcrActivity.kt:249: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                    binding.tvOcrResult.text = "Error recognizing text: ${e.message}"
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\OcrActivity.kt:249: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    binding.tvOcrResult.text = "Error recognizing text: ${e.message}"
                                                ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\OcrActivity.kt:257: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            binding.tvOcrResult.text = "Error loading image."
                                        ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskManagementActivity.kt:284: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            binding.tvUrgentImportantCount?.text = "$urgentImportantCount مهام"
                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskManagementActivity.kt:285: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            binding.tvNotUrgentImportantCount?.text = "$notUrgentImportantCount مهام"
                                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskManagementActivity.kt:286: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            binding.tvUrgentNotImportantCount?.text = "$urgentNotImportantCount مهام"
                                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskManagementActivity.kt:287: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            binding.tvNotUrgentNotImportantCount?.text = "$notUrgentNotImportantCount مهام"
                                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskManagementActivity.kt:633: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            binding.tvCompletionRate.text = "${stats.completionRate}%"
                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskReportAdapter.kt:36: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            taskExpirationDateTextView.text = "Expires: ${dateTimeFormat.format(task.expirationDate)}" // Use new format
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\ui\TaskReportAdapter.kt:36: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            taskExpirationDateTextView.text = "Expires: ${dateTimeFormat.format(task.expirationDate)}" // Use new format
                                               ~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\adapters\UserListAdapter.kt:51: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    userRole.text = "Super Admin"
                                     ~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\adapters\UserListAdapter.kt:56: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    userRole.text = "Admin"
                                     ~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\kotlin\com\example\kpitrackerapp\adapters\UserListAdapter.kt:61: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    userRole.text = "User"
                                     ~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:49: Warning: Hardcoded string "تسجيل صوتي", should use @string resource [HardcodedText]
                android:contentDescription="تسجيل صوتي"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:59: Warning: Hardcoded string "كاميرا", should use @string resource [HardcodedText]
                android:contentDescription="كاميرا"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:69: Warning: Hardcoded string "موقع", should use @string resource [HardcodedText]
                android:contentDescription="موقع"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:79: Warning: Hardcoded string "مؤقت", should use @string resource [HardcodedText]
                android:contentDescription="مؤقت"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:88: Warning: Hardcoded string "قوالب", should use @string resource [HardcodedText]
                android:contentDescription="قوالب"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:126: Warning: Hardcoded string "🤖 اقتراحات ذكية", should use @string resource [HardcodedText]
                        android:text="🤖 اقتراحات ذكية"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:135: Warning: Hardcoded string "💡 ننصح بتنفيذ هذه المهمة في الصباح", should use @string resource [HardcodedText]
                        android:text="💡 ننصح بتنفيذ هذه المهمة في الصباح"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:143: Warning: Hardcoded string "⏱️ الوقت المتوقع: 45 دقيقة", should use @string resource [HardcodedText]
                        android:text="⏱️ الوقت المتوقع: 45 دقيقة"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:151: Warning: Hardcoded string "🔗 مهام مشابهة: تحديث التقارير", should use @string resource [HardcodedText]
                        android:text="🔗 مهام مشابهة: تحديث التقارير"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:162: Warning: Hardcoded string "📋 قوالب جاهزة", should use @string resource [HardcodedText]
                android:text="📋 قوالب جاهزة"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:198: Warning: Hardcoded string "💼", should use @string resource [HardcodedText]
                                android:text="💼"
                                ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:205: Warning: Hardcoded string "اجتماع", should use @string resource [HardcodedText]
                                android:text="اجتماع"
                                ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:233: Warning: Hardcoded string "📚", should use @string resource [HardcodedText]
                                android:text="📚"
                                ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:240: Warning: Hardcoded string "دراسة", should use @string resource [HardcodedText]
                                android:text="دراسة"
                                ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:268: Warning: Hardcoded string "📞", should use @string resource [HardcodedText]
                                android:text="📞"
                                ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:275: Warning: Hardcoded string "مكالمة", should use @string resource [HardcodedText]
                                android:text="مكالمة"
                                ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:302: Warning: Hardcoded string "🏃‍♂️", should use @string resource [HardcodedText]
                                android:text="🏃‍♂️"
                                ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:309: Warning: Hardcoded string "رياضة", should use @string resource [HardcodedText]
                                android:text="رياضة"
                                ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:337: Warning: Hardcoded string "✅ المعلومات الأساسية", should use @string resource [HardcodedText]
                        android:text="✅ المعلومات الأساسية"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:352: Warning: Hardcoded string "📝 اسم المهمة", should use @string resource [HardcodedText]
                        android:hint="📝 اسم المهمة"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:374: Warning: Hardcoded string "📄 وصف المهمة (اختياري)", should use @string resource [HardcodedText]
                        android:hint="📄 وصف المهمة (اختياري)"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:398: Warning: Hardcoded string "📅 تاريخ الانتهاء", should use @string resource [HardcodedText]
                        android:hint="📅 تاريخ الانتهاء"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:425: Warning: Hardcoded string "⏰ وقت الانتهاء (اختياري)", should use @string resource [HardcodedText]
                        android:hint="⏰ وقت الانتهاء (اختياري)"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:455: Warning: Hardcoded string "⭐ الأولوية", should use @string resource [HardcodedText]
                            android:text="⭐ الأولوية"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:482: Warning: Hardcoded string "🟡 متوسطة - مهمة عادية", should use @string resource [HardcodedText]
                                android:text="🟡 متوسطة - مهمة عادية"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:506: Warning: Hardcoded string "📂 الفئة", should use @string resource [HardcodedText]
                            android:text="📂 الفئة"
                            ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:533: Warning: Hardcoded string "💼 عمل - مهام العمل والوظيفة", should use @string resource [HardcodedText]
                                android:text="💼 عمل - مهام العمل والوظيفة"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:554: Warning: Hardcoded string "⏱️ الوقت المتوقع (بالساعات)", should use @string resource [HardcodedText]
                        android:hint="⏱️ الوقت المتوقع (بالساعات)"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:567: Warning: Hardcoded string "1.0", should use @string resource [HardcodedText]
                            android:text="1.0" />
                            ~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:591: Warning: Hardcoded string "📊 التفاصيل المتقدمة", should use @string resource [HardcodedText]
                        android:text="📊 التفاصيل المتقدمة"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:613: Warning: Hardcoded string "⚡ مستوى الطاقة المطلوب", should use @string resource [HardcodedText]
                            android:text="⚡ مستوى الطاقة المطلوب"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:630: Warning: Hardcoded string "🔋 منخفض", should use @string resource [HardcodedText]
                                android:text="🔋 منخفض"
                                ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:638: Warning: Hardcoded string "⚡ متوسط", should use @string resource [HardcodedText]
                                android:text="⚡ متوسط"
                                ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:647: Warning: Hardcoded string "🚀 عالي", should use @string resource [HardcodedText]
                                android:text="🚀 عالي"
                                ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:663: Warning: Hardcoded string "📊 التقدم: ", should use @string resource [HardcodedText]
                                android:text="📊 التقدم: "
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:671: Warning: Hardcoded string "0%", should use @string resource [HardcodedText]
                                android:text="0%"
                                ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:696: Warning: Hardcoded string "📍 الموقع (اختياري)", should use @string resource [HardcodedText]
                            android:hint="📍 الموقع (اختياري)"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:717: Warning: Hardcoded string "🏠 السياق", should use @string resource [HardcodedText]
                            android:text="🏠 السياق"
                            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:734: Warning: Hardcoded string "🏠 في المنزل", should use @string resource [HardcodedText]
                                android:text="🏠 في المنزل"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:743: Warning: Hardcoded string "🏢 في المكتب", should use @string resource [HardcodedText]
                                android:text="🏢 في المكتب"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:751: Warning: Hardcoded string "📱 أونلاين", should use @string resource [HardcodedText]
                                android:text="📱 أونلاين"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:759: Warning: Hardcoded string "🚗 في الطريق", should use @string resource [HardcodedText]
                                android:text="🚗 في الطريق"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:787: Warning: Hardcoded string "⚙️ الإعدادات والتخصيص", should use @string resource [HardcodedText]
                        android:text="⚙️ الإعدادات والتخصيص"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:812: Warning: Hardcoded string "🔔 التذكير (أيام قبل الموعد)", should use @string resource [HardcodedText]
                            android:hint="🔔 التذكير (أيام قبل الموعد)"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:826: Warning: Hardcoded string "🔕 لا يوجد تذكير", should use @string resource [HardcodedText]
                                android:text="🔕 لا يوجد تذكير" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:847: Warning: Hardcoded string "🔄 مهمة متكررة", should use @string resource [HardcodedText]
                                android:text="🔄 مهمة متكررة"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:872: Warning: Hardcoded string "🔔 تفعيل الإشعارات", should use @string resource [HardcodedText]
                                android:text="🔔 تفعيل الإشعارات"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:896: Warning: Hardcoded string "🧩 تقسيم إلى مهام فرعية تلقائياً", should use @string resource [HardcodedText]
                                android:text="🧩 تقسيم إلى مهام فرعية تلقائياً"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:906: Warning: Hardcoded string "🎨 لون المهمة", should use @string resource [HardcodedText]
                            android:text="🎨 لون المهمة"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:969: Warning: Hardcoded string "📱 أيقونة المهمة", should use @string resource [HardcodedText]
                            android:text="📱 أيقونة المهمة"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:986: Warning: Hardcoded string "💼", should use @string resource [HardcodedText]
                                android:text="💼"
                                ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:995: Warning: Hardcoded string "📚", should use @string resource [HardcodedText]
                                android:text="📚"
                                ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:1003: Warning: Hardcoded string "🏃‍♂️", should use @string resource [HardcodedText]
                                android:text="🏃‍♂️"
                                ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:1011: Warning: Hardcoded string "🍎", should use @string resource [HardcodedText]
                                android:text="🍎"
                                ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:1019: Warning: Hardcoded string "💡", should use @string resource [HardcodedText]
                                android:text="💡"
                                ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:1045: Warning: Hardcoded string "💾 حفظ كمسودة", should use @string resource [HardcodedText]
                    android:text="💾 حفظ كمسودة"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:1058: Warning: Hardcoded string "➕ إضافة وإنشاء أخرى", should use @string resource [HardcodedText]
                    android:text="➕ إضافة وإنشاء أخرى"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_kpi.xml:1073: Warning: Hardcoded string "✅ إضافة المهمة", should use @string resource [HardcodedText]
                android:text="✅ إضافة المهمة"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:55: Warning: Hardcoded string "📋 المعلومات الأساسية", should use @string resource [HardcodedText]
                        android:text="📋 المعلومات الأساسية"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:87: Warning: Hardcoded string "وصف المهمة (اختياري)", should use @string resource [HardcodedText]
                        android:hint="وصف المهمة (اختياري)"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:108: Warning: Hardcoded string "الفئة", should use @string resource [HardcodedText]
                        android:hint="الفئة"
                        ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:142: Warning: Hardcoded string "⚡ الأولوية والأهمية", should use @string resource [HardcodedText]
                        android:text="⚡ الأولوية والأهمية"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:161: Warning: Hardcoded string "الأولوية", should use @string resource [HardcodedText]
                            android:hint="الأولوية"
                            ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:182: Warning: Hardcoded string "الأهمية", should use @string resource [HardcodedText]
                            android:hint="الأهمية"
                            ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:204: Warning: Hardcoded string "مستوى الطاقة المطلوب", should use @string resource [HardcodedText]
                        android:hint="مستوى الطاقة المطلوب"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:238: Warning: Hardcoded string "📅 التوقيت والمواعيد", should use @string resource [HardcodedText]
                        android:text="📅 التوقيت والمواعيد"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:300: Warning: Hardcoded string "الوقت المتوقع (بالساعات)", should use @string resource [HardcodedText]
                        android:hint="الوقت المتوقع (بالساعات)"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:353: Warning: Hardcoded string "⚙️ خيارات إضافية", should use @string resource [HardcodedText]
                        android:text="⚙️ خيارات إضافية"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:365: Warning: Hardcoded string "العلامات (مفصولة بفواصل)", should use @string resource [HardcodedText]
                        android:hint="العلامات (مفصولة بفواصل)"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:385: Warning: Hardcoded string "ملاحظات إضافية", should use @string resource [HardcodedText]
                        android:hint="ملاحظات إضافية"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:410: Warning: Hardcoded string "🔄 مهمة متكررة", should use @string resource [HardcodedText]
                            android:text="🔄 مهمة متكررة"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_add_edit_task_enhanced.xml:441: Warning: Hardcoded string "إلغاء", should use @string resource [HardcodedText]
                    android:text="إلغاء"
                    ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:45: Warning: Hardcoded string "📞 Contact Information", should use @string resource [HardcodedText]
                        android:text="📞 Contact Information"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:55: Warning: Hardcoded string "📧 Email Address", should use @string resource [HardcodedText]
                        android:hint="📧 Email Address"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:69: Warning: Hardcoded string "📱 Phone Number (with country code)", should use @string resource [HardcodedText]
                        android:hint="📱 Phone Number (with country code)"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:101: Warning: Hardcoded string "📱 App Status", should use @string resource [HardcodedText]
                        android:text="📱 App Status"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:117: Warning: Hardcoded string "WhatsApp:", should use @string resource [HardcodedText]
                            android:text="WhatsApp:"
                            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:124: Warning: Hardcoded string "Checking...", should use @string resource [HardcodedText]
                            android:text="Checking..."
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:140: Warning: Hardcoded string "Email Apps:", should use @string resource [HardcodedText]
                            android:text="Email Apps:"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:147: Warning: Hardcoded string "Checking...", should use @string resource [HardcodedText]
                            android:text="Checking..."
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:157: Warning: Hardcoded string "🔍 Check App Status", should use @string resource [HardcodedText]
                        android:text="🔍 Check App Status"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:181: Warning: Hardcoded string "📊 Automatic Reports", should use @string resource [HardcodedText]
                        android:text="📊 Automatic Reports"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:197: Warning: Hardcoded string "📅 Weekly Reports", should use @string resource [HardcodedText]
                            android:text="📅 Weekly Reports"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:217: Warning: Hardcoded string "📆 Monthly Reports", should use @string resource [HardcodedText]
                            android:text="📆 Monthly Reports"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:238: Warning: Hardcoded string "Test Weekly", should use @string resource [HardcodedText]
                            android:text="Test Weekly"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:247: Warning: Hardcoded string "Test Monthly", should use @string resource [HardcodedText]
                            android:text="Test Monthly"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:273: Warning: Hardcoded string "📤 Send Methods for Reports", should use @string resource [HardcodedText]
                        android:text="📤 Send Methods for Reports"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:289: Warning: Hardcoded string "💬 WhatsApp Reports", should use @string resource [HardcodedText]
                            android:text="💬 WhatsApp Reports"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:308: Warning: Hardcoded string "📧 Email Reports", should use @string resource [HardcodedText]
                            android:text="📧 Email Reports"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:339: Warning: Hardcoded string "⏰ Automatic Reminders", should use @string resource [HardcodedText]
                        android:text="⏰ Automatic Reminders"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:355: Warning: Hardcoded string "🔔 Auto Reminders", should use @string resource [HardcodedText]
                            android:text="🔔 Auto Reminders"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:375: Warning: Hardcoded string "💬 WhatsApp Reminders", should use @string resource [HardcodedText]
                            android:text="💬 WhatsApp Reminders"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:394: Warning: Hardcoded string "📧 Email Reminders", should use @string resource [HardcodedText]
                            android:text="📧 Email Reminders"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:425: Warning: Hardcoded string "⚙️ Actions", should use @string resource [HardcodedText]
                        android:text="⚙️ Actions"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:436: Warning: Hardcoded string "💾 Save Settings", should use @string resource [HardcodedText]
                        android:text="💾 Save Settings"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:451: Warning: Hardcoded string "📅 Schedule Now", should use @string resource [HardcodedText]
                            android:text="📅 Schedule Now"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_auto_send_settings.xml:460: Warning: Hardcoded string "❌ Cancel All", should use @string resource [HardcodedText]
                            android:text="❌ Cancel All"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_chat.xml:83: Warning: Hardcoded string "Type a message...", should use @string resource [HardcodedText]
                    android:hint="Type a message..."
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_chat_list.xml:60: Warning: Hardcoded string "No conversations yetnStart chatting with your colleagues!", should use @string resource [HardcodedText]
            android:text="No conversations yet\nStart chatting with your colleagues!"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_create_user.xml:22: Warning: Hardcoded string "Create New User", should use @string resource [HardcodedText]
            android:text="Create New User"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_create_user.xml:47: Warning: Hardcoded string "Profile Picture", should use @string resource [HardcodedText]
                android:contentDescription="Profile Picture"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_create_user.xml:59: Warning: Hardcoded string "Add Photo", should use @string resource [HardcodedText]
            android:text="Add Photo"
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_create_user.xml:84: Warning: Hardcoded string "Full Name *", should use @string resource [HardcodedText]
                android:hint="Full Name *"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_create_user.xml:103: Warning: Hardcoded string "Username", should use @string resource [HardcodedText]
                android:hint="Username"
                ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_create_user.xml:123: Warning: Hardcoded string "Email", should use @string resource [HardcodedText]
                android:hint="Email"
                ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_create_user.xml:143: Warning: Hardcoded string "Department", should use @string resource [HardcodedText]
                android:hint="Department"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_create_user.xml:178: Warning: Hardcoded string "Cancel", should use @string resource [HardcodedText]
                android:text="Cancel"
                ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_create_user.xml:190: Warning: Hardcoded string "Create User", should use @string resource [HardcodedText]
                android:text="Create User"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_excel_import.xml:16: Warning: Hardcoded string "Select Excel File (.xls, .xlsx)", should use @string resource [HardcodedText]
        android:text="Select Excel File (.xls, .xlsx)"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_excel_import.xml:29: Warning: Hardcoded string "Select an Excel file to begin import.", should use @string resource [HardcodedText]
        android:text="Select an Excel file to begin import."
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_excel_review.xml:16: Warning: Hardcoded string "Review Excel Import Data", should use @string resource [HardcodedText]
        android:text="Review Excel Import Data"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_excel_review.xml:70: Warning: Hardcoded string "Import Individually", should use @string resource [HardcodedText]
            android:text="Import Individually"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_excel_review.xml:78: Warning: Hardcoded string "Calculate Average", should use @string resource [HardcodedText]
            android:text="Calculate Average"/>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_excel_review.xml:101: Warning: Hardcoded string "Confirm Import", should use @string resource [HardcodedText]
        android:text="Confirm Import"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:54: Warning: Hardcoded string "🎯 Targets", should use @string resource [HardcodedText]
                    android:text="🎯 Targets"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:64: Warning: Hardcoded string "0/35,000", should use @string resource [HardcodedText]
                    android:text="0/35,000"
                    ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:82: Warning: Hardcoded string "📅", should use @string resource [HardcodedText]
                    android:text="📅"
                    ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:101: Warning: Hardcoded string "0%", should use @string resource [HardcodedText]
                    android:text="0%"
                    ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:119: Warning: Hardcoded string "📊", should use @string resource [HardcodedText]
                    android:text="📊"
                    ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:138: Warning: Hardcoded string "0%", should use @string resource [HardcodedText]
                    android:text="0%"
                    ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:156: Warning: Hardcoded string "⏰", should use @string resource [HardcodedText]
                    android:text="⏰"
                    ~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:175: Warning: Hardcoded string "0%", should use @string resource [HardcodedText]
                    android:text="0%"
                    ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:195: Warning: Hardcoded string "📝", should use @string resource [HardcodedText]
                    android:text="📝"
                    ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:283: Warning: Hardcoded string "Daily", should use @string resource [HardcodedText]
                        android:text="Daily"
                        ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:326: Warning: Hardcoded string "Monthly", should use @string resource [HardcodedText]
                        android:text="Monthly"
                        ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:369: Warning: Hardcoded string "Annual", should use @string resource [HardcodedText]
                        android:text="Annual"
                        ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:398: Warning: Hardcoded string "📈 Quick Stats", should use @string resource [HardcodedText]
                        android:text="📈 Quick Stats"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:417: Warning: Hardcoded string "Current Progress", should use @string resource [HardcodedText]
                            android:text="Current Progress"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:444: Warning: Hardcoded string "This Week", should use @string resource [HardcodedText]
                            android:text="This Week"
                            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:470: Warning: Hardcoded string "Average/Day", should use @string resource [HardcodedText]
                            android:text="Average/Day"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:508: Warning: Hardcoded string "+10", should use @string resource [HardcodedText]
                    android:text="+10"
                    ~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:520: Warning: Hardcoded string "+50", should use @string resource [HardcodedText]
                    android:text="+50"
                    ~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:534: Warning: Hardcoded string "➕ Add Progress", should use @string resource [HardcodedText]
                    android:text="➕ Add Progress"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_kpi_detail.xml:549: Warning: Hardcoded string "📊 Progress Over Time", should use @string resource [HardcodedText]
                android:text="📊 Progress Over Time"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_login.xml:18: Warning: Hardcoded string "App Logo", should use @string resource [HardcodedText]
        android:contentDescription="App Logo"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_login.xml:43: Warning: Hardcoded string "Welcome! Please sign in to continue", should use @string resource [HardcodedText]
        android:text="Welcome! Please sign in to continue"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_login.xml:73: Warning: Hardcoded string "Username or Email", should use @string resource [HardcodedText]
                android:hint="Username or Email"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_login.xml:94: Warning: Hardcoded string "or select from existing users", should use @string resource [HardcodedText]
                android:text="or select from existing users"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_login.xml:117: Warning: Hardcoded string "Admin Mode", should use @string resource [HardcodedText]
                    android:text="Admin Mode"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_login.xml:126: Warning: Hardcoded string "Admin Icon", should use @string resource [HardcodedText]
                    android:contentDescription="Admin Icon"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_login.xml:137: Warning: Hardcoded string "Admin Role", should use @string resource [HardcodedText]
                android:hint="Admin Role"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_login.xml:147: Warning: Hardcoded string "Admin", should use @string resource [HardcodedText]
                    android:text="Admin" />
                    ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_login.xml:157: Warning: Hardcoded string "Remember me", should use @string resource [HardcodedText]
                android:text="Remember me"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_login.xml:165: Warning: Hardcoded string "Sign In", should use @string resource [HardcodedText]
                android:text="Sign In"
                ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_login.xml:176: Warning: Hardcoded string "Create New User", should use @string resource [HardcodedText]
                android:text="Create New User"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_login.xml:191: Warning: Hardcoded string "Quick Access", should use @string resource [HardcodedText]
        android:text="Quick Access"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_modern_report.xml:62: Warning: Hardcoded string "Select KPI", should use @string resource [HardcodedText]
                android:hint="Select KPI"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_modern_report.xml:80: Warning: Hardcoded string "Global", should use @string resource [HardcodedText]
                    android:text="Global"
                    ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_modern_report.xml:93: Warning: Hardcoded string "Filter Users", should use @string resource [HardcodedText]
                android:hint="Filter Users"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_modern_report.xml:111: Warning: Hardcoded string "All Users", should use @string resource [HardcodedText]
                    android:text="All Users"
                    ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_modern_report.xml:125: Warning: Hardcoded string "From", should use @string resource [HardcodedText]
                android:hint="From"
                ~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_modern_report.xml:158: Warning: Hardcoded string "To", should use @string resource [HardcodedText]
                android:hint="To"
                ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_modern_report.xml:192: Warning: Hardcoded string "GO", should use @string resource [HardcodedText]
                android:text="GO"
                ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_modern_report.xml:264: Warning: Hardcoded string "Daily", should use @string resource [HardcodedText]
                    android:text="Daily"
                    ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_modern_report.xml:273: Warning: Hardcoded string "Monthly", should use @string resource [HardcodedText]
                    android:text="Monthly"
                    ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_modern_report.xml:282: Warning: Hardcoded string "Annual", should use @string resource [HardcodedText]
                    android:text="Annual"
                    ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_modern_report.xml:301: Warning: Hardcoded string "Summary Chart", should use @string resource [HardcodedText]
                    android:text="Summary Chart"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_modern_report.xml:313: Warning: Hardcoded string "Share", should use @string resource [HardcodedText]
                    android:text="Share"
                    ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_modern_report.xml:355: Warning: Hardcoded string "Trend Chart", should use @string resource [HardcodedText]
                    android:text="Trend Chart"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_modern_report.xml:367: Warning: Hardcoded string "Share", should use @string resource [HardcodedText]
                    android:text="Share"
                    ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_notifications.xml:40: Warning: Hardcoded string "📭 No notifications yetnnYou'll see admin messages, KPI alerts, and reminders here.", should use @string resource [HardcodedText]
                android:text="📭 No notifications yet\n\nYou'll see admin messages, KPI alerts, and reminders here."
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_notifications.xml:64: Warning: Hardcoded string "Clear all notifications", should use @string resource [HardcodedText]
        android:contentDescription="Clear all notifications"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_ocr.xml:14: Warning: Hardcoded string "Select Image for OCR", should use @string resource [HardcodedText]
        android:text="Select Image for OCR"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_ocr.xml:25: Warning: Hardcoded string "Selected image preview", should use @string resource [HardcodedText]
        android:contentDescription="Selected image preview"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_ocr.xml:36: Warning: Hardcoded string "Extracted Text:", should use @string resource [HardcodedText]
        android:text="Extracted Text:"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_ocr.xml:45: Warning: Hardcoded string "Copy Text", should use @string resource [HardcodedText]
        android:text="Copy Text"
        ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_report.xml:42: Warning: Hardcoded string "Select KPI", should use @string resource [HardcodedText]
                android:hint="Select KPI"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_report.xml:63: Warning: Hardcoded string "From", should use @string resource [HardcodedText]
                android:hint="From"
                ~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_report.xml:84: Warning: Hardcoded string "To", should use @string resource [HardcodedText]
                android:hint="To"
                ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_report.xml:111: Warning: Hardcoded string "GO", should use @string resource [HardcodedText]
                android:text="GO"
                ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_report.xml:185: Warning: Hardcoded string "Daily", should use @string resource [HardcodedText]
                    android:text="Daily"
                    ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_report.xml:192: Warning: Hardcoded string "Monthly", should use @string resource [HardcodedText]
                    android:text="Monthly"
                    ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_report.xml:199: Warning: Hardcoded string "Annual", should use @string resource [HardcodedText]
                    android:text="Annual"
                    ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:38: Warning: Hardcoded string "Search tasks", should use @string resource [HardcodedText]
                    android:contentDescription="Search tasks"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:47: Warning: Hardcoded string "Filter tasks", should use @string resource [HardcodedText]
                    android:contentDescription="Filter tasks"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:56: Warning: Hardcoded string "Change view mode", should use @string resource [HardcodedText]
                    android:contentDescription="Change view mode"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:95: Warning: Hardcoded string "الكل", should use @string resource [HardcodedText]
                    android:text="الكل"
                    ~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:103: Warning: Hardcoded string "اليوم", should use @string resource [HardcodedText]
                    android:text="اليوم" />
                    ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:110: Warning: Hardcoded string "هذا الأسبوع", should use @string resource [HardcodedText]
                    android:text="هذا الأسبوع" />
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:117: Warning: Hardcoded string "عاجل", should use @string resource [HardcodedText]
                    android:text="عاجل" />
                    ~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:124: Warning: Hardcoded string "مكتملة", should use @string resource [HardcodedText]
                    android:text="مكتملة" />
                    ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:131: Warning: Hardcoded string "متأخرة", should use @string resource [HardcodedText]
                    android:text="متأخرة" />
                    ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:169: Warning: Hardcoded string "📊 ملخص سريع", should use @string resource [HardcodedText]
                        android:text="📊 ملخص سريع"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:193: Warning: Hardcoded string "12", should use @string resource [HardcodedText]
                                android:text="12"
                                ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:201: Warning: Hardcoded string "إجمالي المهام", should use @string resource [HardcodedText]
                                android:text="إجمالي المهام"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:221: Warning: Hardcoded string "3", should use @string resource [HardcodedText]
                                android:text="3"
                                ~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:229: Warning: Hardcoded string "مستحقة اليوم", should use @string resource [HardcodedText]
                                android:text="مستحقة اليوم"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:249: Warning: Hardcoded string "2", should use @string resource [HardcodedText]
                                android:text="2"
                                ~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:257: Warning: Hardcoded string "عاجلة", should use @string resource [HardcodedText]
                                android:text="عاجلة"
                                ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:277: Warning: Hardcoded string "85%", should use @string resource [HardcodedText]
                                android:text="85%"
                                ~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:285: Warning: Hardcoded string "معدل الإنجاز", should use @string resource [HardcodedText]
                                android:text="معدل الإنجاز"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:330: Warning: Hardcoded string "🎯 عرض مصفوفة أيزنهاور", should use @string resource [HardcodedText]
                        android:text="🎯 عرض مصفوفة أيزنهاور"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:380: Warning: Hardcoded string "🔥 عاجل ومهم", should use @string resource [HardcodedText]
                                android:text="🔥 عاجل ومهم"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:390: Warning: Hardcoded string "0 مهام", should use @string resource [HardcodedText]
                                android:text="0 مهام"
                                ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:426: Warning: Hardcoded string "🎯 مهم وغير عاجل", should use @string resource [HardcodedText]
                                android:text="🎯 مهم وغير عاجل"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:436: Warning: Hardcoded string "0 مهام", should use @string resource [HardcodedText]
                                android:text="0 مهام"
                                ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:472: Warning: Hardcoded string "⚡ عاجل وغير مهم", should use @string resource [HardcodedText]
                                android:text="⚡ عاجل وغير مهم"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:482: Warning: Hardcoded string "0 مهام", should use @string resource [HardcodedText]
                                android:text="0 مهام"
                                ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:518: Warning: Hardcoded string "📝 غير مهم وغير عاجل", should use @string resource [HardcodedText]
                                android:text="📝 غير مهم وغير عاجل"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:528: Warning: Hardcoded string "0 مهام", should use @string resource [HardcodedText]
                                android:text="0 مهام"
                                ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:561: Warning: Hardcoded string "📋 قائمة المهام", should use @string resource [HardcodedText]
                    android:text="📋 قائمة المهام"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:571: Warning: Hardcoded string "Sort tasks", should use @string resource [HardcodedText]
                    android:contentDescription="Sort tasks" />
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:595: Warning: Hardcoded string "📝", should use @string resource [HardcodedText]
                    android:text="📝"
                    ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:602: Warning: Hardcoded string "لا توجد مهام", should use @string resource [HardcodedText]
                    android:text="لا توجد مهام"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:610: Warning: Hardcoded string "ابدأ بإضافة مهمة جديدة", should use @string resource [HardcodedText]
                    android:text="ابدأ بإضافة مهمة جديدة"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_management.xml:627: Warning: Hardcoded string "إضافة مهمة", should use @string resource [HardcodedText]
        android:text="إضافة مهمة"
        ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:45: Warning: Hardcoded string "📞 Contact Information", should use @string resource [HardcodedText]
                        android:text="📞 Contact Information"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:55: Warning: Hardcoded string "Email Address", should use @string resource [HardcodedText]
                        android:hint="Email Address"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:71: Warning: Hardcoded string "Phone Number (with country code)", should use @string resource [HardcodedText]
                        android:hint="Phone Number (with country code)"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:104: Warning: Hardcoded string "🔔 Reminder Types", should use @string resource [HardcodedText]
                        android:text="🔔 Reminder Types"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:127: Warning: Hardcoded string "Email Reminders", should use @string resource [HardcodedText]
                            android:text="Email Reminders"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:155: Warning: Hardcoded string "WhatsApp Reminders", should use @string resource [HardcodedText]
                            android:text="WhatsApp Reminders"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:183: Warning: Hardcoded string "Local Notifications", should use @string resource [HardcodedText]
                            android:text="Local Notifications"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:215: Warning: Hardcoded string "⏰ Default Reminder Timing", should use @string resource [HardcodedText]
                        android:text="⏰ Default Reminder Timing"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:229: Warning: Hardcoded string "No reminder", should use @string resource [HardcodedText]
                            android:text="No reminder"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:236: Warning: Hardcoded string "1 day before", should use @string resource [HardcodedText]
                            android:text="1 day before"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:244: Warning: Hardcoded string "3 days before", should use @string resource [HardcodedText]
                            android:text="3 days before"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:251: Warning: Hardcoded string "1 week before", should use @string resource [HardcodedText]
                            android:text="1 week before" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:276: Warning: Hardcoded string "🧪 Test Reminders", should use @string resource [HardcodedText]
                        android:text="🧪 Test Reminders"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:295: Warning: Hardcoded string "Test WhatsApp", should use @string resource [HardcodedText]
                            android:text="Test WhatsApp"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:306: Warning: Hardcoded string "Test Email", should use @string resource [HardcodedText]
                            android:text="Test Email"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:318: Warning: Hardcoded string "📱 Check Available Apps", should use @string resource [HardcodedText]
                        android:text="📱 Check Available Apps"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_reminder_settings.xml:332: Warning: Hardcoded string "💾 Save Settings", should use @string resource [HardcodedText]
                android:text="💾 Save Settings"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_report.xml:39: Warning: Hardcoded string "Tasks Due Soon (Next 7 Days)", should use @string resource [HardcodedText]
                android:text="Tasks Due Soon (Next 7 Days)"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_report.xml:62: Warning: Hardcoded string "All Tasks", should use @string resource [HardcodedText]
                android:text="All Tasks"
                ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\activity_task_report.xml:80: Warning: Hardcoded string "Save Report as Text", should use @string resource [HardcodedText]
                android:text="Save Report as Text" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\admin_bottom_navigation.xml:6: Warning: Hardcoded string "Dashboard", should use @string resource [HardcodedText]
        android:title="Dashboard"
        ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\admin_bottom_navigation.xml:11: Warning: Hardcoded string "Performance", should use @string resource [HardcodedText]
        android:title="Performance"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\admin_bottom_navigation.xml:16: Warning: Hardcoded string "Messages", should use @string resource [HardcodedText]
        android:title="Messages"
        ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\admin_bottom_navigation.xml:21: Warning: Hardcoded string "Account", should use @string resource [HardcodedText]
        android:title="Account"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\admin_dashboard_menu.xml:7: Warning: Hardcoded string "Refresh", should use @string resource [HardcodedText]
        android:title="Refresh"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\admin_dashboard_menu.xml:13: Warning: Hardcoded string "Admin Settings", should use @string resource [HardcodedText]
        android:title="Admin Settings"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\chart_options_menu.xml:5: Warning: Hardcoded string "Share", should use @string resource [HardcodedText]
        android:title="Share"
        ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\chart_share_menu.xml:5: Warning: Hardcoded string "Share as Image", should use @string resource [HardcodedText]
        android:title="Share as Image" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\chart_share_menu.xml:8: Warning: Hardcoded string "Share as PDF", should use @string resource [HardcodedText]
        android:title="Share as PDF" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\chart_share_menu.xml:11: Warning: Hardcoded string "Save to Device", should use @string resource [HardcodedText]
        android:title="Save to Device" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\chat_list_menu.xml:7: Warning: Hardcoded string "Search", should use @string resource [HardcodedText]
        android:title="Search"
        ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\chat_list_menu.xml:13: Warning: Hardcoded string "Archived Chats", should use @string resource [HardcodedText]
        android:title="Archived Chats"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\chat_list_menu.xml:18: Warning: Hardcoded string "Chat Settings", should use @string resource [HardcodedText]
        android:title="Chat Settings"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\chat_menu.xml:7: Warning: Hardcoded string "Voice Call", should use @string resource [HardcodedText]
        android:title="Voice Call"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\chat_menu.xml:13: Warning: Hardcoded string "Video Call", should use @string resource [HardcodedText]
        android:title="Video Call"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\chat_menu.xml:19: Warning: Hardcoded string "Search", should use @string resource [HardcodedText]
        android:title="Search"
        ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\chat_menu.xml:25: Warning: Hardcoded string "Chat Info", should use @string resource [HardcodedText]
        android:title="Chat Info"
        ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\compact_report_table.xml:40: Warning: Hardcoded string "Doctor", should use @string resource [HardcodedText]
                        android:text="Doctor"
                        ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\compact_report_table.xml:50: Warning: Hardcoded string "Daily Target", should use @string resource [HardcodedText]
                        android:text="Daily Target"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\compact_report_table.xml:59: Warning: Hardcoded string "Daily Achieved", should use @string resource [HardcodedText]
                        android:text="Daily Achieved"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\compact_report_table.xml:68: Warning: Hardcoded string "Daily %", should use @string resource [HardcodedText]
                        android:text="Daily %"
                        ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\compact_report_table.xml:78: Warning: Hardcoded string "Monthly Target", should use @string resource [HardcodedText]
                        android:text="Monthly Target"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\compact_report_table.xml:87: Warning: Hardcoded string "Monthly Achieved", should use @string resource [HardcodedText]
                        android:text="Monthly Achieved"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\compact_report_table.xml:96: Warning: Hardcoded string "Monthly %", should use @string resource [HardcodedText]
                        android:text="Monthly %"
                        ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\compact_report_table.xml:106: Warning: Hardcoded string "Annual Target", should use @string resource [HardcodedText]
                        android:text="Annual Target"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\compact_report_table.xml:115: Warning: Hardcoded string "Annual Achieved", should use @string resource [HardcodedText]
                        android:text="Annual Achieved"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\compact_report_table.xml:124: Warning: Hardcoded string "Annual %", should use @string resource [HardcodedText]
                        android:text="Annual %"
                        ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\dialog_add_edit_progress.xml:41: Warning: Hardcoded string "Select Date", should use @string resource [HardcodedText]
        android:text="Select Date" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\dialog_edit_task.xml:14: Warning: Hardcoded string "Task Name", should use @string resource [HardcodedText]
        android:hint="Task Name">
        ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\dialog_edit_task.xml:28: Warning: Hardcoded string "Expiration Date", should use @string resource [HardcodedText]
        android:hint="Expiration Date">
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\dialog_edit_task.xml:76: Warning: Hardcoded string "Mark as Completed", should use @string resource [HardcodedText]
        android:text="Mark as Completed" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\dialog_progress_update.xml:11: Warning: Hardcoded string "تحديد نسبة التقدم", should use @string resource [HardcodedText]
        android:text="تحديد نسبة التقدم"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\dialog_progress_update.xml:20: Warning: Hardcoded string "50%", should use @string resource [HardcodedText]
        android:text="50%"
        ~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\dialog_progress_update.xml:43: Warning: Hardcoded string "0%", should use @string resource [HardcodedText]
            android:text="0%"
            ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\dialog_progress_update.xml:50: Warning: Hardcoded string "100%", should use @string resource [HardcodedText]
            android:text="100%"
            ~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\dialog_select_card_colors.xml:21: Warning: Hardcoded string "Top Section Color", should use @string resource [HardcodedText]
            android:text="Top Section Color"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\dialog_select_card_colors.xml:52: Warning: Hardcoded string "Bottom Section Color", should use @string resource [HardcodedText]
            android:text="Bottom Section Color"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_account.xml:16: Warning: Hardcoded string "👤 Account Settings", should use @string resource [HardcodedText]
            android:text="👤 Account Settings"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_account.xml:34: Warning: Hardcoded string "👤", should use @string resource [HardcodedText]
                android:text="👤"
                ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_account.xml:45: Warning: Hardcoded string "Admin User", should use @string resource [HardcodedText]
                android:text="Admin User"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_account.xml:56: Warning: Hardcoded string "Super Admin", should use @string resource [HardcodedText]
                android:text="Super Admin"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_account.xml:66: Warning: Hardcoded string "<EMAIL>", should use @string resource [HardcodedText]
                android:text="<EMAIL>"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_account.xml:85: Warning: Hardcoded string "👤", should use @string resource [HardcodedText]
                android:text="👤"
                ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_account.xml:94: Warning: Hardcoded string "Profile Settings", should use @string resource [HardcodedText]
                android:text="Profile Settings"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_account.xml:102: Warning: Hardcoded string ">", should use @string resource [HardcodedText]
                android:text=">"
                ~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_account.xml:113: Warning: Hardcoded string "🚪 Logout", should use @string resource [HardcodedText]
            android:text="🚪 Logout"
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_main_dashboard.xml:26: Warning: Hardcoded string "No KPIs found. Add one using the + button.", should use @string resource [HardcodedText]
        android:text="No KPIs found. Add one using the + button."
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_messages.xml:16: Warning: Hardcoded string "💬 Messages & Communication", should use @string resource [HardcodedText]
            android:text="💬 Messages &amp; Communication"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_messages.xml:35: Warning: Hardcoded string "💬", should use @string resource [HardcodedText]
                android:text="💬"
                ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_messages.xml:49: Warning: Hardcoded string "Chat List", should use @string resource [HardcodedText]
                    android:text="Chat List"
                    ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_messages.xml:57: Warning: Hardcoded string "View and manage conversations", should use @string resource [HardcodedText]
                    android:text="View and manage conversations"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_performance.xml:16: Warning: Hardcoded string "📈 Performance & Reports", should use @string resource [HardcodedText]
            android:text="📈 Performance &amp; Reports"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_performance.xml:35: Warning: Hardcoded string "📊", should use @string resource [HardcodedText]
                android:text="📊"
                ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_performance.xml:49: Warning: Hardcoded string "Interactive Report", should use @string resource [HardcodedText]
                    android:text="Interactive Report"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_performance.xml:57: Warning: Hardcoded string "View detailed performance analytics", should use @string resource [HardcodedText]
                    android:text="View detailed performance analytics"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_performance.xml:78: Warning: Hardcoded string "📅", should use @string resource [HardcodedText]
                android:text="📅"
                ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_performance.xml:92: Warning: Hardcoded string "Expiry Management", should use @string resource [HardcodedText]
                    android:text="Expiry Management"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_performance.xml:100: Warning: Hardcoded string "Manage expiration dates", should use @string resource [HardcodedText]
                    android:text="Manage expiration dates"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_performance.xml:121: Warning: Hardcoded string "📋", should use @string resource [HardcodedText]
                android:text="📋"
                ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_performance.xml:135: Warning: Hardcoded string "Task Follow-up", should use @string resource [HardcodedText]
                    android:text="Task Follow-up"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\fragment_performance.xml:143: Warning: Hardcoded string "Track and manage tasks", should use @string resource [HardcodedText]
                    android:text="Track and manage tasks"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_admin_dashboard_action.xml:28: Warning: Hardcoded string "⚙️", should use @string resource [HardcodedText]
            android:text="⚙️"
            ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_admin_dashboard_action.xml:41: Warning: Hardcoded string "Action Title", should use @string resource [HardcodedText]
                android:text="Action Title"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_admin_dashboard_action.xml:51: Warning: Hardcoded string "Action description", should use @string resource [HardcodedText]
                android:text="Action description"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_admin_dashboard_action.xml:60: Warning: Hardcoded string "›", should use @string resource [HardcodedText]
            android:text="›"
            ~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_admin_dashboard_activity.xml:33: Warning: Hardcoded string "User Name", should use @string resource [HardcodedText]
                android:text="User Name"
                ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_admin_dashboard_activity.xml:42: Warning: Hardcoded string "2 hours ago", should use @string resource [HardcodedText]
                android:text="2 hours ago"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_admin_dashboard_activity.xml:53: Warning: Hardcoded string "Activity description", should use @string resource [HardcodedText]
            android:text="Activity description"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_admin_dashboard_header.xml:13: Warning: Hardcoded string "Header Title", should use @string resource [HardcodedText]
        android:text="Header Title"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_admin_dashboard_stat.xml:28: Warning: Hardcoded string "📊", should use @string resource [HardcodedText]
            android:text="📊"
            ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_admin_dashboard_stat.xml:41: Warning: Hardcoded string "Stat Title", should use @string resource [HardcodedText]
                android:text="Stat Title"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_admin_dashboard_stat.xml:49: Warning: Hardcoded string "123", should use @string resource [HardcodedText]
                android:text="123"
                ~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_admin_dashboard_stat.xml:59: Warning: Hardcoded string "›", should use @string resource [HardcodedText]
            android:text="›"
            ~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_admin_dashboard_user.xml:28: Warning: Hardcoded string "🥇", should use @string resource [HardcodedText]
            android:text="🥇"
            ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_admin_dashboard_user.xml:41: Warning: Hardcoded string "User Name", should use @string resource [HardcodedText]
                android:text="User Name"
                ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_admin_dashboard_user.xml:51: Warning: Hardcoded string "95% Performance", should use @string resource [HardcodedText]
                android:text="95% Performance"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_admin_dashboard_user.xml:60: Warning: Hardcoded string "›", should use @string resource [HardcodedText]
            android:text="›"
            ~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_chat_date_header.xml:14: Warning: Hardcoded string "Today", should use @string resource [HardcodedText]
        android:text="Today"
        ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_chat_message_received.xml:50: Warning: Hardcoded string "John Doe", should use @string resource [HardcodedText]
                    android:text="John Doe"
                    ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_chat_message_received.xml:70: Warning: Hardcoded string "Replying to:", should use @string resource [HardcodedText]
                        android:text="Replying to:"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_chat_message_received.xml:79: Warning: Hardcoded string "Original message...", should use @string resource [HardcodedText]
                        android:text="Original message..."
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_chat_message_received.xml:91: Warning: Hardcoded string "Hello! How are you?", should use @string resource [HardcodedText]
                    android:text="Hello! How are you?"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_chat_message_received.xml:110: Warning: Hardcoded string "edited", should use @string resource [HardcodedText]
                        android:text="edited"
                        ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_chat_message_received.xml:120: Warning: Hardcoded string "12:34", should use @string resource [HardcodedText]
                        android:text="12:34"
                        ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_chat_message_sent.xml:17: Warning: Hardcoded string "Today", should use @string resource [HardcodedText]
        android:text="Today"
        ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_chat_message_sent.xml:67: Warning: Hardcoded string "Replying to:", should use @string resource [HardcodedText]
                        android:text="Replying to:"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_chat_message_sent.xml:76: Warning: Hardcoded string "Original message...", should use @string resource [HardcodedText]
                        android:text="Original message..."
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_chat_message_sent.xml:88: Warning: Hardcoded string "Hello! How are you?", should use @string resource [HardcodedText]
                    android:text="Hello! How are you?"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_chat_message_sent.xml:107: Warning: Hardcoded string "edited", should use @string resource [HardcodedText]
                        android:text="edited"
                        ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_chat_message_sent.xml:119: Warning: Hardcoded string "12:34", should use @string resource [HardcodedText]
                        android:text="12:34"
                        ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_chat_message_sent.xml:129: Warning: Hardcoded string "✓✓", should use @string resource [HardcodedText]
                        android:text="✓✓"
                        ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_chat_system_message.xml:14: Warning: Hardcoded string "System message", should use @string resource [HardcodedText]
        android:text="System message"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_conversation.xml:65: Warning: Hardcoded string "John Doe", should use @string resource [HardcodedText]
                    android:text="John Doe"
                    ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_conversation.xml:74: Warning: Hardcoded string "12:34", should use @string resource [HardcodedText]
                    android:text="12:34"
                    ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_conversation.xml:94: Warning: Hardcoded string "Last message preview...", should use @string resource [HardcodedText]
                    android:text="Last message preview..."
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_conversation.xml:116: Warning: Hardcoded string "3", should use @string resource [HardcodedText]
                    android:text="3"
                    ~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_notification.xml:22: Warning: Hardcoded string "📬", should use @string resource [HardcodedText]
            android:text="📬"
            ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_notification.xml:35: Warning: Hardcoded string "Notification Title", should use @string resource [HardcodedText]
                android:text="Notification Title"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_notification.xml:47: Warning: Hardcoded string "Notification message content goes here...", should use @string resource [HardcodedText]
                android:text="Notification message content goes here..."
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_notification.xml:56: Warning: Hardcoded string "2 hours ago", should use @string resource [HardcodedText]
                android:text="2 hours ago"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_recent_user.xml:28: Warning: Hardcoded string "User Avatar", should use @string resource [HardcodedText]
            android:contentDescription="User Avatar"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_task.xml:76: Warning: Hardcoded string "Task Actions", should use @string resource [HardcodedText]
            android:contentDescription="Task Actions"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_task_enhanced.xml:27: Warning: Hardcoded string "📋", should use @string resource [HardcodedText]
            android:text="📋"
            ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_task_enhanced.xml:134: Warning: Hardcoded string "إكمال المهمة", should use @string resource [HardcodedText]
                android:contentDescription="إكمال المهمة" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_task_enhanced.xml:144: Warning: Hardcoded string "المزيد من الخيارات", should use @string resource [HardcodedText]
            android:contentDescription="المزيد من الخيارات"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_user_list.xml:65: Warning: Hardcoded string "John Doe", should use @string resource [HardcodedText]
                    android:text="John Doe"
                    ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_user_list.xml:74: Warning: Hardcoded string "👤", should use @string resource [HardcodedText]
                    android:text="👤"
                    ~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_user_list.xml:87: Warning: Hardcoded string "<EMAIL>", should use @string resource [HardcodedText]
                android:text="<EMAIL>"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\item_user_list.xml:97: Warning: Hardcoded string "User", should use @string resource [HardcodedText]
                android:text="User"
                ~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\kpi_detail_menu.xml:35: Warning: Hardcoded string "Import via OCR", should use @string resource [HardcodedText]
        android:title="Import via OCR"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\kpi_list_item.xml:42: Warning: Hardcoded string "Target:", should use @string resource [HardcodedText]
            android:text="Target:"
            ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\kpi_list_item.xml:63: Warning: Hardcoded string "Current:", should use @string resource [HardcodedText]
            android:text="Current:"
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main.xml:7: Warning: Hardcoded string "🔔 Notifications", should use @string resource [HardcodedText]
        android:title="🔔 Notifications"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main.xml:13: Warning: Hardcoded string "📊 Modern Report", should use @string resource [HardcodedText]
        android:title="📊 Modern Report"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main.xml:18: Warning: Hardcoded string "⏰ Expiry Management", should use @string resource [HardcodedText]
        android:title="⏰ Expiry Management"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main.xml:23: Warning: Hardcoded string "📷 OCR Scanner", should use @string resource [HardcodedText]
        android:title="📷 OCR Scanner"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main.xml:28: Warning: Hardcoded string "💬 Chat", should use @string resource [HardcodedText]
        android:title="💬 Chat"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main.xml:33: Warning: Hardcoded string "🚀 إضافة مهمة متقدمة", should use @string resource [HardcodedText]
        android:title="🚀 إضافة مهمة متقدمة"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main.xml:38: Warning: Hardcoded string "📤 Auto Send Settings", should use @string resource [HardcodedText]
        android:title="📤 Auto Send Settings"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main.xml:43: Warning: Hardcoded string "🔧 Admin Dashboard", should use @string resource [HardcodedText]
        android:title="🔧 Admin Dashboard"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main.xml:49: Warning: Hardcoded string "👤 Switch User", should use @string resource [HardcodedText]
        android:title="👤 Switch User"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main.xml:54: Warning: Hardcoded string "🚪 Logout", should use @string resource [HardcodedText]
        android:title="🚪 Logout"
        ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main_bottom_navigation.xml:6: Warning: Hardcoded string "Dashboard", should use @string resource [HardcodedText]
        android:title="Dashboard"
        ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main_bottom_navigation.xml:11: Warning: Hardcoded string "Performance", should use @string resource [HardcodedText]
        android:title="Performance"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main_bottom_navigation.xml:16: Warning: Hardcoded string "Messages", should use @string resource [HardcodedText]
        android:title="Messages"
        ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main_bottom_navigation.xml:21: Warning: Hardcoded string "Account", should use @string resource [HardcodedText]
        android:title="Account"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main_menu.xml:8: Warning: Hardcoded string "Interactive Performance Report", should use @string resource [HardcodedText]
        android:title="Interactive Performance Report"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main_menu.xml:25: Warning: Hardcoded string "Messages", should use @string resource [HardcodedText]
        android:title="Messages"
        ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main_menu.xml:31: Warning: Hardcoded string "Admin Dashboard", should use @string resource [HardcodedText]
        android:title="Admin Dashboard"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main_menu.xml:37: Warning: Hardcoded string "Switch User", should use @string resource [HardcodedText]
        android:title="Switch User"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\main_menu.xml:43: Warning: Hardcoded string "Logout", should use @string resource [HardcodedText]
        android:title="Logout"
        ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row.xml:38: Warning: Hardcoded string "Period", should use @string resource [HardcodedText]
            android:text="Period"
            ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row.xml:45: Warning: Hardcoded string "Target", should use @string resource [HardcodedText]
            android:text="Target"
            ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row.xml:53: Warning: Hardcoded string "Achieved", should use @string resource [HardcodedText]
            android:text="Achieved"
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row.xml:61: Warning: Hardcoded string "Achieved %", should use @string resource [HardcodedText]
            android:text="Achieved %"
            ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row.xml:80: Warning: Hardcoded string "Daily", should use @string resource [HardcodedText]
            android:text="Daily"
            ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row.xml:121: Warning: Hardcoded string "Monthly", should use @string resource [HardcodedText]
            android:text="Monthly"
            ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row.xml:162: Warning: Hardcoded string "Quarterly", should use @string resource [HardcodedText]
            android:text="Quarterly"
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row.xml:203: Warning: Hardcoded string "Annual", should use @string resource [HardcodedText]
            android:text="Annual"
            ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row_colored.xml:42: Warning: Hardcoded string "Period", should use @string resource [HardcodedText]
                android:text="Period"
                ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row_colored.xml:51: Warning: Hardcoded string "Target", should use @string resource [HardcodedText]
                android:text="Target"
                ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row_colored.xml:60: Warning: Hardcoded string "Achieved", should use @string resource [HardcodedText]
                android:text="Achieved"
                ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row_colored.xml:69: Warning: Hardcoded string "Achieved %", should use @string resource [HardcodedText]
                android:text="Achieved %"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row_colored.xml:88: Warning: Hardcoded string "Daily", should use @string resource [HardcodedText]
                    android:text="Daily"
                    ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row_colored.xml:125: Warning: Hardcoded string "Monthly", should use @string resource [HardcodedText]
                    android:text="Monthly"
                    ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row_colored.xml:161: Warning: Hardcoded string "Annual", should use @string resource [HardcodedText]
                    android:text="Annual"
                    ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row_colored.xml:199: Warning: Hardcoded string "Quarterly", should use @string resource [HardcodedText]
                    android:text="Quarterly"
                    ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row_tabular.xml:35: Warning: Hardcoded string "Period", should use @string resource [HardcodedText]
                android:text="Period"
                ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row_tabular.xml:42: Warning: Hardcoded string "Target", should use @string resource [HardcodedText]
                android:text="Target"
                ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row_tabular.xml:50: Warning: Hardcoded string "Achieved", should use @string resource [HardcodedText]
                android:text="Achieved"
                ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row_tabular.xml:58: Warning: Hardcoded string "Achieved %", should use @string resource [HardcodedText]
                android:text="Achieved %"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row_tabular.xml:72: Warning: Hardcoded string "Daily", should use @string resource [HardcodedText]
                android:text="Daily"
                ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row_tabular.xml:108: Warning: Hardcoded string "Monthly", should use @string resource [HardcodedText]
                android:text="Monthly"
                ~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row_tabular.xml:145: Warning: Hardcoded string "Quarterly", should use @string resource [HardcodedText]
                android:text="Quarterly"
                ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\report_table_row_tabular.xml:181: Warning: Hardcoded string "Annual", should use @string resource [HardcodedText]
                android:text="Annual"
                ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\sort_options_menu.xml:5: Warning: Hardcoded string "Sort Ascending", should use @string resource [HardcodedText]
        android:title="Sort Ascending"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\sort_options_menu.xml:9: Warning: Hardcoded string "Sort Descending", should use @string resource [HardcodedText]
        android:title="Sort Descending"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\sort_options_menu.xml:13: Warning: Hardcoded string "Sort by Name", should use @string resource [HardcodedText]
        android:title="Sort by Name"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\task_item_actions_menu.xml:5: Warning: Hardcoded string "Edit Task", should use @string resource [HardcodedText]
        android:title="Edit Task" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\task_item_actions_menu.xml:8: Warning: Hardcoded string "Delete Task", should use @string resource [HardcodedText]
        android:title="Delete Task" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\task_item_actions_menu.xml:11: Warning: Hardcoded string "Add to Calendar", should use @string resource [HardcodedText]
        android:title="Add to Calendar" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\task_management_menu.xml:6: Warning: Hardcoded string "View Report", should use @string resource [HardcodedText]
        android:title="View Report"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\task_management_menu.xml:11: Warning: Hardcoded string "Reminder Settings", should use @string resource [HardcodedText]
        android:title="Reminder Settings"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\unified_report_table.xml:30: Warning: Hardcoded string "Doctor", should use @string resource [HardcodedText]
                android:text="Doctor"
                ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\unified_report_table.xml:39: Warning: Hardcoded string "Period", should use @string resource [HardcodedText]
                android:text="Period"
                ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\unified_report_table.xml:49: Warning: Hardcoded string "Target", should use @string resource [HardcodedText]
                android:text="Target"
                ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\unified_report_table.xml:59: Warning: Hardcoded string "Achieved", should use @string resource [HardcodedText]
                android:text="Achieved"
                ~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\unified_report_table.xml:69: Warning: Hardcoded string "Achieved %", should use @string resource [HardcodedText]
                android:text="Achieved %"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\user_checkbox_item.xml:12: Warning: Hardcoded string "User Name", should use @string resource [HardcodedText]
        android:text="User Name"
        ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\user_filter_dialog.xml:12: Warning: Hardcoded string "Select Users", should use @string resource [HardcodedText]
        android:text="Select Users"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\user_filter_dialog.xml:30: Warning: Hardcoded string "All Users", should use @string resource [HardcodedText]
            android:text="All Users"
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\user_filter_dialog.xml:37: Warning: Hardcoded string "Select Users", should use @string resource [HardcodedText]
            android:text="Select Users"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\user_filter_dialog.xml:63: Warning: Hardcoded string "Cancel", should use @string resource [HardcodedText]
            android:text="Cancel"
            ~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\user_filter_dialog.xml:70: Warning: Hardcoded string "Apply", should use @string resource [HardcodedText]
            android:text="Apply"
            ~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\layout\user_summary_card_item.xml:109: Warning: Hardcoded string "Touch and drag to reorder", should use @string resource [HardcodedText]
            android:contentDescription="Touch and drag to reorder"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res\menu\user_summary_context_menu.xml:14: Warning: Hardcoded string "Export User Data (CSV)", should use @string resource [HardcodedText]
        android:title="Export User Data (CSV)" /> // TODO: Use string resource
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

26 errors, 1126 warnings
