package com.example.kpitrackerapp.viewmodels

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.example.kpitrackerapp.models.*
import com.example.kpitrackerapp.persistence.AppDatabase
import com.example.kpitrackerapp.repositories.ChatRepository
import com.example.kpitrackerapp.utils.SessionManager
import kotlinx.coroutines.launch
import kotlinx.coroutines.flow.map

class ChatViewModel(application: Application) : AndroidViewModel(application) {

    private val database = AppDatabase.getDatabase(application)
    private val chatRepository = ChatRepository(database.chatDao(), application)
    private val userDao = database.userDao()

    // LiveData for UI
    private val _messages = MutableLiveData<List<ChatMessageItem>>()
    val messages: LiveData<List<ChatMessageItem>> = _messages

    private val _conversations = MutableLiveData<List<ConversationItem>>()
    val conversations: LiveData<List<ConversationItem>> = _conversations

    private val _users = MutableLiveData<List<User>>()
    val users: LiveData<List<User>> = _users

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    private val _totalUnreadCount = MutableLiveData<Int>()
    val totalUnreadCount: LiveData<Int> = _totalUnreadCount

    init {
        // Load total unread count
        loadTotalUnreadCount()
    }

    // ==================== MESSAGES ====================

    fun loadMessages(conversationId: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                chatRepository.getMessagesForConversation(conversationId).asLiveData().observeForever { messageItems ->
                    _messages.value = messageItems
                }
            } catch (e: Exception) {
                _error.value = "Failed to load messages: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    suspend fun sendMessage(
        receiverId: String,
        message: String,
        messageType: MessageType = MessageType.TEXT,
        attachmentPath: String? = null,
        attachmentType: AttachmentType? = null,
        replyToMessageId: String? = null
    ): ChatMessage {
        return chatRepository.sendMessage(
            receiverId = receiverId,
            message = message,
            messageType = messageType,
            attachmentPath = attachmentPath,
            attachmentType = attachmentType,
            replyToMessageId = replyToMessageId
        )
    }

    suspend fun markMessagesAsRead(conversationId: String) {
        try {
            chatRepository.markMessagesAsRead(conversationId)
            loadTotalUnreadCount() // Refresh unread count
        } catch (e: Exception) {
            _error.value = "Failed to mark messages as read: ${e.message}"
        }
    }

    suspend fun deleteMessage(messageId: String) {
        try {
            chatRepository.deleteMessage(messageId)
        } catch (e: Exception) {
            _error.value = "Failed to delete message: ${e.message}"
        }
    }

    suspend fun editMessage(messageId: String, newMessage: String) {
        try {
            chatRepository.editMessage(messageId, newMessage)
        } catch (e: Exception) {
            _error.value = "Failed to edit message: ${e.message}"
        }
    }

    // ==================== CONVERSATIONS ====================

    suspend fun getOrCreateConversation(user1Id: String, user2Id: String): String {
        return chatRepository.getOrCreateConversation(user1Id, user2Id).id
    }

    fun loadConversations() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                chatRepository.getConversationsForCurrentUser().asLiveData().observeForever { conversations ->
                    _conversations.value = conversations
                }
            } catch (e: Exception) {
                _error.value = "Failed to load conversations: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    suspend fun archiveConversation(conversationId: String) {
        try {
            chatRepository.archiveConversation(conversationId)
        } catch (e: Exception) {
            _error.value = "Failed to archive conversation: ${e.message}"
        }
    }

    suspend fun unarchiveConversation(conversationId: String) {
        try {
            chatRepository.unarchiveConversation(conversationId)
        } catch (e: Exception) {
            _error.value = "Failed to unarchive conversation: ${e.message}"
        }
    }

    suspend fun muteConversation(conversationId: String) {
        try {
            chatRepository.muteConversation(conversationId)
        } catch (e: Exception) {
            _error.value = "Failed to mute conversation: ${e.message}"
        }
    }

    suspend fun unmuteConversation(conversationId: String) {
        try {
            chatRepository.unmuteConversation(conversationId)
        } catch (e: Exception) {
            _error.value = "Failed to unmute conversation: ${e.message}"
        }
    }

    suspend fun deleteConversation(conversationId: String) {
        try {
            chatRepository.deleteConversation(conversationId)
            loadTotalUnreadCount() // Refresh unread count
        } catch (e: Exception) {
            _error.value = "Failed to delete conversation: ${e.message}"
        }
    }

    // ==================== USERS ====================

    fun loadAllUsers() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val currentUser = SessionManager.getCurrentUser()
                if (currentUser != null) {
                    userDao.getAllUsers().collect { allUsers ->
                        // Filter out current user
                        val otherUsers = allUsers.filter { user -> user.id != currentUser.id }
                        _users.value = otherUsers
                        _isLoading.value = false
                    }
                }
            } catch (e: Exception) {
                _error.value = "Failed to load users: ${e.message}"
                _isLoading.value = false
            }
        }
    }

    fun searchUsers(query: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val currentUser = SessionManager.getCurrentUser()
                if (currentUser != null) {
                    // For now, we'll use getAllUsers and filter by name
                    userDao.getAllUsers().collect { allUsers ->
                        val searchResults = allUsers.filter { user ->
                            user.name.contains(query, ignoreCase = true) ||
                            (user.email?.contains(query, ignoreCase = true) ?: false)
                        }
                        // Filter out current user
                        val otherUsers = searchResults.filter { user -> user.id != currentUser.id }
                        _users.value = otherUsers
                        _isLoading.value = false
                    }
                }
            } catch (e: Exception) {
                _error.value = "Failed to search users: ${e.message}"
                _isLoading.value = false
            }
        }
    }

    // ==================== UTILITY FUNCTIONS ====================

    private fun loadTotalUnreadCount() {
        viewModelScope.launch {
            try {
                val count = chatRepository.getTotalUnreadCount()
                _totalUnreadCount.value = count
            } catch (e: Exception) {
                _error.value = "Failed to load unread count: ${e.message}"
            }
        }
    }

    suspend fun searchMessages(query: String, conversationId: String? = null): List<ChatMessage> {
        return try {
            chatRepository.searchMessages(query, conversationId)
        } catch (e: Exception) {
            _error.value = "Failed to search messages: ${e.message}"
            emptyList()
        }
    }

    suspend fun shareKpiData(receiverId: String, kpiId: String, kpiName: String): ChatMessage {
        return chatRepository.shareKpiData(receiverId, kpiId, kpiName)
    }

    suspend fun shareProgressData(receiverId: String, progressData: String): ChatMessage {
        return chatRepository.shareProgressData(receiverId, progressData)
    }

    // ==================== ERROR HANDLING ====================

    fun clearError() {
        _error.value = null
    }

    // ==================== REAL-TIME FEATURES (Future) ====================

    fun startTyping(conversationId: String) {
        // TODO: Implement typing indicator
    }

    fun stopTyping(conversationId: String) {
        // TODO: Implement typing indicator
    }

    fun setUserOnlineStatus(isOnline: Boolean) {
        // TODO: Implement online status
    }

    // ==================== BLOCKING FUNCTIONALITY ====================

    suspend fun blockUser(userId: String) {
        try {
            chatRepository.blockUser(userId)
            _error.value = null
        } catch (e: Exception) {
            _error.value = "Failed to block user: ${e.message}"
            throw e
        }
    }

    suspend fun unblockUser(userId: String) {
        try {
            chatRepository.unblockUser(userId)
            _error.value = null
        } catch (e: Exception) {
            _error.value = "Failed to unblock user: ${e.message}"
            throw e
        }
    }

    suspend fun isUserBlocked(userId: String): Boolean {
        return try {
            chatRepository.isUserBlocked(userId)
        } catch (e: Exception) {
            _error.value = "Failed to check block status: ${e.message}"
            false
        }
    }

    fun getBlockedUsers(): LiveData<List<String>> {
        return chatRepository.getBlockedUsers().asLiveData()
    }



    // ==================== BACKUP & EXPORT ====================

    suspend fun exportConversation(conversationId: String): String {
        // TODO: Implement conversation export
        return "Export feature coming soon!"
    }

    suspend fun backupAllChats(): Boolean {
        // TODO: Implement chat backup
        return false
    }

    suspend fun restoreChatsFromBackup(backupData: String): Boolean {
        // TODO: Implement chat restore
        return false
    }
}
