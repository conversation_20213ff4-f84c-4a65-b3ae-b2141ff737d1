// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.Chip;
import com.google.android.material.progressindicator.CircularProgressIndicator;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class KpiCardItemBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView currentAchievementLabelTextView;

  @NonNull
  public final TextView currentAchievementPercentageTextView;

  @NonNull
  public final CircularProgressIndicator currentAchievementProgressIndicator;

  @NonNull
  public final ImageView currentIcon;

  @NonNull
  public final ImageView currentPeriodAchievementIcon;

  @NonNull
  public final TextView currentPeriodAchievementTextView;

  @NonNull
  public final TextView currentPeriodAchievementValueTextView;

  @NonNull
  public final ImageView currentPeriodTargetIcon;

  @NonNull
  public final TextView currentPeriodTargetTextView;

  @NonNull
  public final TextView currentPeriodTargetValueTextView;

  @NonNull
  public final TextView currentTextView;

  @NonNull
  public final TextView currentValueTextView;

  @NonNull
  public final TextView kpiSubtitleTextView;

  @NonNull
  public final TextView kpiTitleTextView;

  @NonNull
  public final ImageView lastUpdateIcon;

  @NonNull
  public final TextView lastUpdateTextView;

  @NonNull
  public final TextView lastUpdateValueTextView;

  @NonNull
  public final Chip ownerChip;

  @NonNull
  public final TextView percentageTextView;

  @NonNull
  public final CircularProgressIndicator progressIndicator;

  @NonNull
  public final ImageView remainingIcon;

  @NonNull
  public final TextView remainingTextView;

  @NonNull
  public final TextView remainingValueTextView;

  @NonNull
  public final ImageView requiredDailyIcon;

  @NonNull
  public final TextView requiredDailyTextView;

  @NonNull
  public final TextView requiredDailyValueTextView;

  @NonNull
  public final TextView starRatingTextView;

  @NonNull
  public final ImageView targetIcon;

  @NonNull
  public final TextView targetTextView;

  @NonNull
  public final TextView targetValueTextView;

  private KpiCardItemBinding(@NonNull MaterialCardView rootView,
      @NonNull TextView currentAchievementLabelTextView,
      @NonNull TextView currentAchievementPercentageTextView,
      @NonNull CircularProgressIndicator currentAchievementProgressIndicator,
      @NonNull ImageView currentIcon, @NonNull ImageView currentPeriodAchievementIcon,
      @NonNull TextView currentPeriodAchievementTextView,
      @NonNull TextView currentPeriodAchievementValueTextView,
      @NonNull ImageView currentPeriodTargetIcon, @NonNull TextView currentPeriodTargetTextView,
      @NonNull TextView currentPeriodTargetValueTextView, @NonNull TextView currentTextView,
      @NonNull TextView currentValueTextView, @NonNull TextView kpiSubtitleTextView,
      @NonNull TextView kpiTitleTextView, @NonNull ImageView lastUpdateIcon,
      @NonNull TextView lastUpdateTextView, @NonNull TextView lastUpdateValueTextView,
      @NonNull Chip ownerChip, @NonNull TextView percentageTextView,
      @NonNull CircularProgressIndicator progressIndicator, @NonNull ImageView remainingIcon,
      @NonNull TextView remainingTextView, @NonNull TextView remainingValueTextView,
      @NonNull ImageView requiredDailyIcon, @NonNull TextView requiredDailyTextView,
      @NonNull TextView requiredDailyValueTextView, @NonNull TextView starRatingTextView,
      @NonNull ImageView targetIcon, @NonNull TextView targetTextView,
      @NonNull TextView targetValueTextView) {
    this.rootView = rootView;
    this.currentAchievementLabelTextView = currentAchievementLabelTextView;
    this.currentAchievementPercentageTextView = currentAchievementPercentageTextView;
    this.currentAchievementProgressIndicator = currentAchievementProgressIndicator;
    this.currentIcon = currentIcon;
    this.currentPeriodAchievementIcon = currentPeriodAchievementIcon;
    this.currentPeriodAchievementTextView = currentPeriodAchievementTextView;
    this.currentPeriodAchievementValueTextView = currentPeriodAchievementValueTextView;
    this.currentPeriodTargetIcon = currentPeriodTargetIcon;
    this.currentPeriodTargetTextView = currentPeriodTargetTextView;
    this.currentPeriodTargetValueTextView = currentPeriodTargetValueTextView;
    this.currentTextView = currentTextView;
    this.currentValueTextView = currentValueTextView;
    this.kpiSubtitleTextView = kpiSubtitleTextView;
    this.kpiTitleTextView = kpiTitleTextView;
    this.lastUpdateIcon = lastUpdateIcon;
    this.lastUpdateTextView = lastUpdateTextView;
    this.lastUpdateValueTextView = lastUpdateValueTextView;
    this.ownerChip = ownerChip;
    this.percentageTextView = percentageTextView;
    this.progressIndicator = progressIndicator;
    this.remainingIcon = remainingIcon;
    this.remainingTextView = remainingTextView;
    this.remainingValueTextView = remainingValueTextView;
    this.requiredDailyIcon = requiredDailyIcon;
    this.requiredDailyTextView = requiredDailyTextView;
    this.requiredDailyValueTextView = requiredDailyValueTextView;
    this.starRatingTextView = starRatingTextView;
    this.targetIcon = targetIcon;
    this.targetTextView = targetTextView;
    this.targetValueTextView = targetValueTextView;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static KpiCardItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static KpiCardItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.kpi_card_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static KpiCardItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.currentAchievementLabelTextView;
      TextView currentAchievementLabelTextView = ViewBindings.findChildViewById(rootView, id);
      if (currentAchievementLabelTextView == null) {
        break missingId;
      }

      id = R.id.currentAchievementPercentageTextView;
      TextView currentAchievementPercentageTextView = ViewBindings.findChildViewById(rootView, id);
      if (currentAchievementPercentageTextView == null) {
        break missingId;
      }

      id = R.id.currentAchievementProgressIndicator;
      CircularProgressIndicator currentAchievementProgressIndicator = ViewBindings.findChildViewById(rootView, id);
      if (currentAchievementProgressIndicator == null) {
        break missingId;
      }

      id = R.id.currentIcon;
      ImageView currentIcon = ViewBindings.findChildViewById(rootView, id);
      if (currentIcon == null) {
        break missingId;
      }

      id = R.id.currentPeriodAchievementIcon;
      ImageView currentPeriodAchievementIcon = ViewBindings.findChildViewById(rootView, id);
      if (currentPeriodAchievementIcon == null) {
        break missingId;
      }

      id = R.id.currentPeriodAchievementTextView;
      TextView currentPeriodAchievementTextView = ViewBindings.findChildViewById(rootView, id);
      if (currentPeriodAchievementTextView == null) {
        break missingId;
      }

      id = R.id.currentPeriodAchievementValueTextView;
      TextView currentPeriodAchievementValueTextView = ViewBindings.findChildViewById(rootView, id);
      if (currentPeriodAchievementValueTextView == null) {
        break missingId;
      }

      id = R.id.currentPeriodTargetIcon;
      ImageView currentPeriodTargetIcon = ViewBindings.findChildViewById(rootView, id);
      if (currentPeriodTargetIcon == null) {
        break missingId;
      }

      id = R.id.currentPeriodTargetTextView;
      TextView currentPeriodTargetTextView = ViewBindings.findChildViewById(rootView, id);
      if (currentPeriodTargetTextView == null) {
        break missingId;
      }

      id = R.id.currentPeriodTargetValueTextView;
      TextView currentPeriodTargetValueTextView = ViewBindings.findChildViewById(rootView, id);
      if (currentPeriodTargetValueTextView == null) {
        break missingId;
      }

      id = R.id.currentTextView;
      TextView currentTextView = ViewBindings.findChildViewById(rootView, id);
      if (currentTextView == null) {
        break missingId;
      }

      id = R.id.currentValueTextView;
      TextView currentValueTextView = ViewBindings.findChildViewById(rootView, id);
      if (currentValueTextView == null) {
        break missingId;
      }

      id = R.id.kpiSubtitleTextView;
      TextView kpiSubtitleTextView = ViewBindings.findChildViewById(rootView, id);
      if (kpiSubtitleTextView == null) {
        break missingId;
      }

      id = R.id.kpiTitleTextView;
      TextView kpiTitleTextView = ViewBindings.findChildViewById(rootView, id);
      if (kpiTitleTextView == null) {
        break missingId;
      }

      id = R.id.lastUpdateIcon;
      ImageView lastUpdateIcon = ViewBindings.findChildViewById(rootView, id);
      if (lastUpdateIcon == null) {
        break missingId;
      }

      id = R.id.lastUpdateTextView;
      TextView lastUpdateTextView = ViewBindings.findChildViewById(rootView, id);
      if (lastUpdateTextView == null) {
        break missingId;
      }

      id = R.id.lastUpdateValueTextView;
      TextView lastUpdateValueTextView = ViewBindings.findChildViewById(rootView, id);
      if (lastUpdateValueTextView == null) {
        break missingId;
      }

      id = R.id.ownerChip;
      Chip ownerChip = ViewBindings.findChildViewById(rootView, id);
      if (ownerChip == null) {
        break missingId;
      }

      id = R.id.percentageTextView;
      TextView percentageTextView = ViewBindings.findChildViewById(rootView, id);
      if (percentageTextView == null) {
        break missingId;
      }

      id = R.id.progressIndicator;
      CircularProgressIndicator progressIndicator = ViewBindings.findChildViewById(rootView, id);
      if (progressIndicator == null) {
        break missingId;
      }

      id = R.id.remainingIcon;
      ImageView remainingIcon = ViewBindings.findChildViewById(rootView, id);
      if (remainingIcon == null) {
        break missingId;
      }

      id = R.id.remainingTextView;
      TextView remainingTextView = ViewBindings.findChildViewById(rootView, id);
      if (remainingTextView == null) {
        break missingId;
      }

      id = R.id.remainingValueTextView;
      TextView remainingValueTextView = ViewBindings.findChildViewById(rootView, id);
      if (remainingValueTextView == null) {
        break missingId;
      }

      id = R.id.requiredDailyIcon;
      ImageView requiredDailyIcon = ViewBindings.findChildViewById(rootView, id);
      if (requiredDailyIcon == null) {
        break missingId;
      }

      id = R.id.requiredDailyTextView;
      TextView requiredDailyTextView = ViewBindings.findChildViewById(rootView, id);
      if (requiredDailyTextView == null) {
        break missingId;
      }

      id = R.id.requiredDailyValueTextView;
      TextView requiredDailyValueTextView = ViewBindings.findChildViewById(rootView, id);
      if (requiredDailyValueTextView == null) {
        break missingId;
      }

      id = R.id.starRatingTextView;
      TextView starRatingTextView = ViewBindings.findChildViewById(rootView, id);
      if (starRatingTextView == null) {
        break missingId;
      }

      id = R.id.targetIcon;
      ImageView targetIcon = ViewBindings.findChildViewById(rootView, id);
      if (targetIcon == null) {
        break missingId;
      }

      id = R.id.targetTextView;
      TextView targetTextView = ViewBindings.findChildViewById(rootView, id);
      if (targetTextView == null) {
        break missingId;
      }

      id = R.id.targetValueTextView;
      TextView targetValueTextView = ViewBindings.findChildViewById(rootView, id);
      if (targetValueTextView == null) {
        break missingId;
      }

      return new KpiCardItemBinding((MaterialCardView) rootView, currentAchievementLabelTextView,
          currentAchievementPercentageTextView, currentAchievementProgressIndicator, currentIcon,
          currentPeriodAchievementIcon, currentPeriodAchievementTextView,
          currentPeriodAchievementValueTextView, currentPeriodTargetIcon,
          currentPeriodTargetTextView, currentPeriodTargetValueTextView, currentTextView,
          currentValueTextView, kpiSubtitleTextView, kpiTitleTextView, lastUpdateIcon,
          lastUpdateTextView, lastUpdateValueTextView, ownerChip, percentageTextView,
          progressIndicator, remainingIcon, remainingTextView, remainingValueTextView,
          requiredDailyIcon, requiredDailyTextView, requiredDailyValueTextView, starRatingTextView,
          targetIcon, targetTextView, targetValueTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
