package com.example.kpitrackerapp.repositories

import android.content.Context
import com.example.kpitrackerapp.models.*
import com.example.kpitrackerapp.persistence.ChatDao
import com.example.kpitrackerapp.persistence.ConversationWithDetails
import com.example.kpitrackerapp.utils.SessionManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import java.text.SimpleDateFormat
import java.util.*

class ChatRepository(private val chatDao: ChatDao, private val context: Context) {

    // ==================== MESSAGES ====================

    suspend fun sendMessage(
        receiverId: String,
        message: String,
        messageType: MessageType = MessageType.TEXT,
        attachmentPath: String? = null,
        attachmentType: AttachmentType? = null,
        replyToMessageId: String? = null
    ): ChatMessage {
        val currentUser = SessionManager.getCurrentUser()
            ?: throw IllegalStateException("No current user")

        // Get or create conversation
        val conversation = getOrCreateConversation(currentUser.id, receiverId)

        // Create message
        val chatMessage = ChatMessage(
            conversationId = conversation.id,
            senderId = currentUser.id,
            receiverId = receiverId,
            message = message,
            messageType = messageType,
            attachmentPath = attachmentPath,
            attachmentType = attachmentType,
            replyToMessageId = replyToMessageId
        )

        // Insert message
        chatDao.insertMessage(chatMessage)

        // Update conversation
        chatDao.updateConversationWithNewMessage(
            conversationId = conversation.id,
            lastMessage = message,
            timestamp = chatMessage.timestamp,
            senderId = currentUser.id,
            receiverId = receiverId
        )

        return chatMessage
    }

    suspend fun getOrCreateConversation(user1Id: String, user2Id: String): Conversation {
        // Check if conversation already exists
        val existingConversation = chatDao.getConversationBetweenUsers(user1Id, user2Id)
        if (existingConversation != null) {
            return existingConversation
        }

        // Create new conversation
        val conversation = Conversation(
            participant1Id = user1Id,
            participant2Id = user2Id
        )

        chatDao.insertConversation(conversation)
        return conversation
    }

    fun getMessagesForConversation(conversationId: String): Flow<List<ChatMessageItem>> {
        return chatDao.getMessagesForConversation(conversationId).map { messages ->
            messages.mapIndexed { index, message ->
                val currentUser = SessionManager.getCurrentUser()
                val isFromCurrentUser = message.senderId == currentUser?.id

                // Check if we need to show date header
                val showDateHeader = if (index == 0) {
                    true
                } else {
                    val previousMessage = messages[index - 1]
                    !isSameDay(message.timestamp, previousMessage.timestamp)
                }

                ChatMessageItem(
                    message = message,
                    senderName = if (isFromCurrentUser) "You" else "Other", // Will be populated with actual names
                    senderImagePath = null, // Will be populated with actual image paths
                    isFromCurrentUser = isFromCurrentUser,
                    showDateHeader = showDateHeader,
                    dateHeaderText = if (showDateHeader) formatDateHeader(message.timestamp) else null
                )
            }
        }
    }

    suspend fun markMessagesAsRead(conversationId: String) {
        val currentUser = SessionManager.getCurrentUser() ?: return
        chatDao.markMessagesAsRead(conversationId, currentUser.id)

        // Clear unread count for current user
        val conversation = chatDao.getConversationById(conversationId) ?: return
        if (conversation.participant1Id == currentUser.id) {
            chatDao.clearUnreadCountForParticipant1(conversationId, currentUser.id)
        } else {
            chatDao.clearUnreadCountForParticipant2(conversationId, currentUser.id)
        }
    }

    suspend fun deleteMessage(messageId: String) {
        chatDao.deleteMessageById(messageId)
    }

    suspend fun editMessage(messageId: String, newMessage: String) {
        val message = chatDao.getMessageById(messageId) ?: return
        val updatedMessage = message.copy(
            message = newMessage,
            isEdited = true,
            editedAt = System.currentTimeMillis()
        )
        chatDao.updateMessage(updatedMessage)
    }

    // ==================== CONVERSATIONS ====================

    fun getConversationsForCurrentUser(): Flow<List<ConversationItem>> {
        val currentUser = SessionManager.getCurrentUser()
            ?: return kotlinx.coroutines.flow.flowOf(emptyList())

        return chatDao.getActiveConversationsForUser(currentUser.id).map { conversations ->
            conversations.map { conv ->
                ConversationItem(
                    conversation = Conversation(
                        id = conv.id,
                        participant1Id = conv.participant1Id,
                        participant2Id = conv.participant2Id,
                        lastMessage = conv.lastMessage,
                        lastMessageTime = conv.lastMessageTime,
                        lastMessageSenderId = conv.lastMessageSenderId,
                        unreadCount1 = conv.unreadCount1,
                        unreadCount2 = conv.unreadCount2,
                        isArchived1 = conv.isArchived1,
                        isArchived2 = conv.isArchived2,
                        isMuted1 = conv.isMuted1,
                        isMuted2 = conv.isMuted2,
                        createdAt = conv.createdAt
                    ),
                    otherParticipant = User(
                        id = if (conv.participant1Id == currentUser.id) conv.participant2Id else conv.participant1Id,
                        name = conv.otherParticipantName,
                        email = "", // Will be populated if needed
                        imagePath = conv.otherParticipantImage
                    ),
                    unreadCount = conv.unreadCount,
                    isOnline = false, // Will be implemented with real-time features
                    lastSeenTime = null
                )
            }
        }
    }

    suspend fun archiveConversation(conversationId: String) {
        val currentUser = SessionManager.getCurrentUser() ?: return
        val conversation = chatDao.getConversationById(conversationId) ?: return

        if (conversation.participant1Id == currentUser.id) {
            chatDao.setArchiveStatusForParticipant1(conversationId, currentUser.id, true)
        } else {
            chatDao.setArchiveStatusForParticipant2(conversationId, currentUser.id, true)
        }
    }

    suspend fun unarchiveConversation(conversationId: String) {
        val currentUser = SessionManager.getCurrentUser() ?: return
        val conversation = chatDao.getConversationById(conversationId) ?: return

        if (conversation.participant1Id == currentUser.id) {
            chatDao.setArchiveStatusForParticipant1(conversationId, currentUser.id, false)
        } else {
            chatDao.setArchiveStatusForParticipant2(conversationId, currentUser.id, false)
        }
    }

    suspend fun muteConversation(conversationId: String) {
        val currentUser = SessionManager.getCurrentUser() ?: return
        val conversation = chatDao.getConversationById(conversationId) ?: return

        if (conversation.participant1Id == currentUser.id) {
            chatDao.setMuteStatusForParticipant1(conversationId, currentUser.id, true)
        } else {
            chatDao.setMuteStatusForParticipant2(conversationId, currentUser.id, true)
        }
    }

    suspend fun unmuteConversation(conversationId: String) {
        val currentUser = SessionManager.getCurrentUser() ?: return
        val conversation = chatDao.getConversationById(conversationId) ?: return

        if (conversation.participant1Id == currentUser.id) {
            chatDao.setMuteStatusForParticipant1(conversationId, currentUser.id, false)
        } else {
            chatDao.setMuteStatusForParticipant2(conversationId, currentUser.id, false)
        }
    }

    suspend fun deleteConversation(conversationId: String) {
        chatDao.deleteAllMessagesInConversation(conversationId)
        val conversation = chatDao.getConversationById(conversationId)
        if (conversation != null) {
            chatDao.deleteConversation(conversation)
        }
    }

    // ==================== UTILITY FUNCTIONS ====================

    suspend fun getTotalUnreadCount(): Int {
        val currentUser = SessionManager.getCurrentUser() ?: return 0
        return chatDao.getTotalUnreadCountForUser(currentUser.id)
    }

    suspend fun searchMessages(query: String, conversationId: String? = null): List<ChatMessage> {
        val currentUser = SessionManager.getCurrentUser() ?: return emptyList()

        return if (conversationId != null) {
            chatDao.searchMessagesInConversation(conversationId, query)
        } else {
            chatDao.searchAllMessages(currentUser.id, query)
        }
    }

    // ==================== BLOCKING FUNCTIONALITY ====================

    suspend fun blockUser(userId: String) {
        val currentUser = SessionManager.getCurrentUser() ?: return

        // Add to blocked users list (using SharedPreferences for simplicity)
        val sharedPrefs = context.getSharedPreferences("blocked_users", Context.MODE_PRIVATE)
        val blockedUsers = sharedPrefs.getStringSet("blocked_list_${currentUser.id}", mutableSetOf()) ?: mutableSetOf()
        blockedUsers.add(userId)

        sharedPrefs.edit()
            .putStringSet("blocked_list_${currentUser.id}", blockedUsers)
            .apply()

        // Archive all conversations with this user
        val conversationId = generateConversationId(currentUser.id, userId)
        chatDao.archiveConversation(conversationId, currentUser.id)
    }

    suspend fun unblockUser(userId: String) {
        val currentUser = SessionManager.getCurrentUser() ?: return

        val sharedPrefs = context.getSharedPreferences("blocked_users", Context.MODE_PRIVATE)
        val blockedUsers = sharedPrefs.getStringSet("blocked_list_${currentUser.id}", mutableSetOf()) ?: mutableSetOf()
        blockedUsers.remove(userId)

        sharedPrefs.edit()
            .putStringSet("blocked_list_${currentUser.id}", blockedUsers)
            .apply()
    }

    suspend fun isUserBlocked(userId: String): Boolean {
        val currentUser = SessionManager.getCurrentUser() ?: return false

        val sharedPrefs = context.getSharedPreferences("blocked_users", Context.MODE_PRIVATE)
        val blockedUsers = sharedPrefs.getStringSet("blocked_list_${currentUser.id}", mutableSetOf()) ?: mutableSetOf()

        return blockedUsers.contains(userId)
    }

    fun getBlockedUsers(): Flow<List<String>> = flow {
        val currentUser = SessionManager.getCurrentUser()
        if (currentUser != null) {
            val sharedPrefs = context.getSharedPreferences("blocked_users", Context.MODE_PRIVATE)
            val blockedUsers = sharedPrefs.getStringSet("blocked_list_${currentUser.id}", mutableSetOf()) ?: mutableSetOf()
            emit(blockedUsers.toList())
        } else {
            emit(emptyList())
        }
    }

    suspend fun shareKpiData(receiverId: String, kpiId: String, kpiName: String): ChatMessage {
        val message = "📊 Shared KPI: $kpiName"
        return sendMessage(
            receiverId = receiverId,
            message = message,
            messageType = MessageType.KPI_SHARE,
            attachmentPath = kpiId,
            attachmentType = AttachmentType.KPI_DATA
        )
    }

    suspend fun shareProgressData(receiverId: String, progressData: String): ChatMessage {
        val message = "📈 Shared progress data"
        return sendMessage(
            receiverId = receiverId,
            message = message,
            messageType = MessageType.PROGRESS_SHARE,
            attachmentPath = progressData,
            attachmentType = AttachmentType.PROGRESS_DATA
        )
    }

    // ==================== HELPER FUNCTIONS ====================

    private fun isSameDay(timestamp1: Long, timestamp2: Long): Boolean {
        val cal1 = Calendar.getInstance().apply { timeInMillis = timestamp1 }
        val cal2 = Calendar.getInstance().apply { timeInMillis = timestamp2 }

        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR)
    }

    private fun formatDateHeader(timestamp: Long): String {
        val calendar = Calendar.getInstance().apply { timeInMillis = timestamp }
        val today = Calendar.getInstance()
        val yesterday = Calendar.getInstance().apply { add(Calendar.DAY_OF_YEAR, -1) }

        return when {
            isSameDay(timestamp, today.timeInMillis) -> "Today"
            isSameDay(timestamp, yesterday.timeInMillis) -> "Yesterday"
            else -> {
                val dayOfWeek = when (calendar.get(Calendar.DAY_OF_WEEK)) {
                    Calendar.SUNDAY -> "Sunday"
                    Calendar.MONDAY -> "Monday"
                    Calendar.TUESDAY -> "Tuesday"
                    Calendar.WEDNESDAY -> "Wednesday"
                    Calendar.THURSDAY -> "Thursday"
                    Calendar.FRIDAY -> "Friday"
                    Calendar.SATURDAY -> "Saturday"
                    else -> ""
                }
                val month = when (calendar.get(Calendar.MONTH)) {
                    Calendar.JANUARY -> "Jan"
                    Calendar.FEBRUARY -> "Feb"
                    Calendar.MARCH -> "Mar"
                    Calendar.APRIL -> "Apr"
                    Calendar.MAY -> "May"
                    Calendar.JUNE -> "Jun"
                    Calendar.JULY -> "Jul"
                    Calendar.AUGUST -> "Aug"
                    Calendar.SEPTEMBER -> "Sep"
                    Calendar.OCTOBER -> "Oct"
                    Calendar.NOVEMBER -> "Nov"
                    Calendar.DECEMBER -> "Dec"
                    else -> ""
                }
                "$dayOfWeek, $month ${calendar.get(Calendar.DAY_OF_MONTH)}"
            }
        }
    }

    private fun generateConversationId(userId1: String, userId2: String): String {
        // Create consistent conversation ID regardless of order
        val sortedIds = listOf(userId1, userId2).sorted()
        return "${sortedIds[0]}_${sortedIds[1]}"
    }
}
