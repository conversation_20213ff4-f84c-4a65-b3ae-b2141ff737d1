package com.example.kpitrackerapp.ui

import android.Manifest
import android.app.Activity
import android.app.DatePickerDialog
import android.app.TimePickerDialog
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.location.LocationManager
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.speech.RecognizerIntent
import android.speech.SpeechRecognizer
import android.view.View
import android.widget.ArrayAdapter
import android.widget.AutoCompleteTextView
import android.widget.Toast
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.TextRecognizer
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import androidx.activity.viewModels
import com.example.kpitrackerapp.utils.AlarmClockManager
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.lifecycle.lifecycleScope
import com.example.kpitrackerapp.R
import com.example.kpitrackerapp.databinding.ActivityAddEditKpiBinding
import com.example.kpitrackerapp.models.Task
import com.example.kpitrackerapp.viewmodels.TaskViewModel
import com.example.kpitrackerapp.viewmodels.TaskViewModelFactory
import com.example.kpitrackerapp.persistence.AppDatabase
import com.google.android.material.chip.Chip
import com.google.android.material.slider.Slider
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

class AddEditAdvancedTaskActivity : AppCompatActivity() {

    private lateinit var binding: ActivityAddEditKpiBinding
    private val taskViewModel: TaskViewModel by viewModels {
        val database = AppDatabase.getDatabase(this)
        TaskViewModelFactory(
            application,
            database.taskDao(),
            database.taskCategoryDao(),
            database.subtaskDao()
        )
    }

    private var currentTaskId: Int? = null
    private var isEditingTask = false
    private val calendar = Calendar.getInstance()

    // Advanced task properties
    private var selectedEnergyLevel = "متوسط"
    private var selectedContext = "في المنزل"
    private var selectedTaskColor = "#9C27B0" // Default purple
    private var selectedTaskIcon = "💼"
    private var taskProgress = 0
    private var isRecurring = false
    private var recurringPattern = ""
    private var notificationsEnabled = true
    private var autoBreakDown = false

    // مدير المنبهات
    private lateinit var alarmClockManager: AlarmClockManager

    companion object {
        const val EXTRA_TASK_ID = "com.example.kpitrackerapp.ADVANCED_TASK_ID"
        const val REQUEST_CODE_SPEECH_INPUT = 1000
        const val REQUEST_CODE_RECORD_AUDIO_PERMISSION = 1001
        const val REQUEST_CODE_CAMERA_PERMISSION = 1002
        const val REQUEST_CODE_LOCATION_PERMISSION = 1003
        const val REQUEST_CODE_CAMERA_CAPTURE = 1004
    }

    // Speech Recognition variables
    private var currentSpeechMode = SpeechMode.BASIC_NOTE
    private enum class SpeechMode {
        BASIC_NOTE,
        SPEECH_TO_TEXT,
        DESCRIPTION_DICTATION,
        GOALS_RECORDING,
        STEPS_RECORDING,
        QUICK_IDEAS
    }

    // Camera variables
    private var currentPhotoPath: String? = null
    private var photoUri: Uri? = null

    // Location variables
    private var locationManager: LocationManager? = null

    // OCR variables
    private lateinit var textRecognizer: TextRecognizer

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAddEditKpiBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize OCR with Latin script options (will work for English and numbers, Arabic may need additional setup)
        textRecognizer = TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)

        // تهيئة مدير المنبهات
        alarmClockManager = AlarmClockManager(this)

        currentTaskId = intent.getStringExtra(EXTRA_TASK_ID)?.toIntOrNull()
        isEditingTask = currentTaskId != null

        setupToolbar()
        setupBasicFields()
        setupAdvancedFeatures()
        setupCollapsibleSections()
        setupQuickActions()
        setupTemplates()
        setupAISuggestionCard()
        setupSaveButtons()

        if (isEditingTask) {
            binding.toolbar.title = "تعديل المهمة"
            loadTaskData()
        }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
    }

    private fun setupBasicFields() {
        // Date picker
        binding.taskExpirationDateEditText.setOnClickListener {
            showDatePickerDialog()
        }

        // Time picker
        binding.taskExpirationTimeEditText.setOnClickListener {
            showTimePickerDialog()
        }

        // Priority Selector with Dialog
        val priorityOptions = arrayOf(
            "🔴 عاجلة - يجب إنجازها فوراً",
            "🟠 عالية - مهمة جداً",
            "🟡 متوسطة - مهمة عادية",
            "🟢 منخفضة - يمكن تأجيلها"
        )

        var selectedPriorityIndex = 2 // Default to متوسطة
        binding.taskPriorityText.text = priorityOptions[selectedPriorityIndex]

        binding.taskPrioritySelector.setOnClickListener {
            showPriorityDialog(priorityOptions, selectedPriorityIndex) { newIndex ->
                selectedPriorityIndex = newIndex
                binding.taskPriorityText.text = priorityOptions[newIndex]

                val priorityLabels = arrayOf("عاجلة", "عالية", "متوسطة", "منخفضة")
                val priorityColors = arrayOf("#F44336", "#FF5722", "#FF9800", "#4CAF50")
                val priorityIcons = arrayOf("🔴", "🟠", "🟡", "🟢")

                val selectedPriority = priorityLabels[newIndex]
                val selectedColor = priorityColors[newIndex]
                val selectedIcon = priorityIcons[newIndex]

                Toast.makeText(this, "$selectedIcon تم اختيار الأولوية: $selectedPriority", Toast.LENGTH_SHORT).show()
                updateTaskColorByPriority(selectedPriority, selectedColor)
                updateSuggestionsByPriority(selectedPriority)
            }
        }

        // Category Selector with Dialog
        val categoryOptions = arrayOf(
            "💼 عمل - مهام العمل والوظيفة",
            "👤 شخصي - أمور شخصية",
            "📚 دراسة - تعليم وتطوير",
            "🏥 صحة - صحة ولياقة",
            "💰 مالية - أمور مالية",
            "👨‍👩‍👧‍👦 عائلة - أمور عائلية",
            "🛒 تسوق - مشتريات",
            "✈️ سفر - سفر ورحلات",
            "🏠 منزل - أعمال منزلية",
            "🎯 مشروع - مشاريع خاصة",
            "📞 اتصالات - مكالمات ومراسلات",
            "🎨 إبداع - أنشطة إبداعية",
            "🤝 اجتماعي - أنشطة اجتماعية",
            "⚙️ صيانة - صيانة وإصلاح",
            "📋 إداري - أعمال إدارية"
        )

        var selectedCategoryIndex = 0 // Default to عمل
        binding.taskCategoryText.text = categoryOptions[selectedCategoryIndex]

        binding.taskCategorySelector.setOnClickListener {
            showCategoryDialog(categoryOptions, selectedCategoryIndex) { newIndex ->
                selectedCategoryIndex = newIndex
                binding.taskCategoryText.text = categoryOptions[newIndex]

                val categoryLabels = arrayOf("عمل", "شخصي", "دراسة", "صحة", "مالية", "عائلة", "تسوق", "سفر", "منزل", "مشروع", "اتصالات", "إبداع", "اجتماعي", "صيانة", "إداري")
                val categoryIcons = arrayOf("💼", "👤", "📚", "🏥", "💰", "👨‍👩‍👧‍👦", "🛒", "✈️", "🏠", "🎯", "📞", "🎨", "🤝", "⚙️", "📋")

                val selectedCategory = categoryLabels[newIndex]
                val selectedIcon = categoryIcons[newIndex]

                Toast.makeText(this, "$selectedIcon تم اختيار الفئة: $selectedCategory", Toast.LENGTH_SHORT).show()
                updateIconByCategory(selectedCategory, selectedIcon)
                updateSuggestionsByCategory(selectedCategory)
            }
        }

        // Reminder dropdown
        val reminderOptions = arrayOf(
            "🔕 لا يوجد تذكير",
            "📅 في نفس اليوم",
            "⏰ يوم واحد قبل",
            "📆 يومين قبل",
            "🗓️ 3 أيام قبل",
            "📋 أسبوع قبل"
        )
        val reminderAdapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, reminderOptions)
        binding.taskReminderAutoCompleteTextView.setAdapter(reminderAdapter)
        binding.taskReminderAutoCompleteTextView.threshold = 1000 // منع الفلترة التلقائية
        binding.taskReminderAutoCompleteTextView.keyListener = null // منع الكتابة
        binding.taskReminderAutoCompleteTextView.setOnClickListener {
            binding.taskReminderAutoCompleteTextView.showDropDown()
        }
        binding.taskReminderAutoCompleteTextView.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                binding.taskReminderAutoCompleteTextView.showDropDown()
            }
        }
    }

    private fun setupAdvancedFeatures() {
        // Progress slider
        binding.sliderProgress.addOnChangeListener { _, value, _ ->
            taskProgress = value.toInt()
            binding.tvProgressValue.text = "${taskProgress}%"

            // إضافة تأثيرات بصرية للتقدم
            when {
                value >= 90 -> {
                    binding.tvProgressValue.setTextColor(ContextCompat.getColor(this, android.R.color.holo_green_dark))
                    if (value == 100f) {
                        Toast.makeText(this, "🎉 مبروك! المهمة مكتملة 100%", Toast.LENGTH_SHORT).show()
                    }
                }
                value >= 50 -> binding.tvProgressValue.setTextColor(ContextCompat.getColor(this, android.R.color.holo_orange_dark))
                else -> binding.tvProgressValue.setTextColor(ContextCompat.getColor(this, android.R.color.holo_red_dark))
            }
        }



        // Energy level chips
        binding.chipGroupEnergyLevel.setOnCheckedStateChangeListener { group, checkedIds ->
            if (checkedIds.isNotEmpty()) {
                val selectedChip = findViewById<Chip>(checkedIds[0])
                selectedEnergyLevel = when (selectedChip.id) {
                    R.id.chipEnergyLow -> "منخفض"
                    R.id.chipEnergyMedium -> "متوسط"
                    R.id.chipEnergyHigh -> "عالي"
                    else -> "متوسط"
                }
                Toast.makeText(this, "🔋 تم اختيار مستوى الطاقة: $selectedEnergyLevel", Toast.LENGTH_SHORT).show()
                updateEstimatedTimeByEnergy(selectedEnergyLevel)
                showEnergyLevelTips(selectedEnergyLevel)
            }
        }

        // Context chips
        binding.chipGroupContext.setOnCheckedStateChangeListener { group, checkedIds ->
            if (checkedIds.isNotEmpty()) {
                val selectedChip = findViewById<Chip>(checkedIds[0])
                selectedContext = when (selectedChip.id) {
                    R.id.chipContextHome -> "في المنزل"
                    R.id.chipContextOffice -> "في المكتب"
                    R.id.chipContextOnline -> "أونلاين"
                    R.id.chipContextTravel -> "في الطريق"
                    else -> "في المنزل"
                }
                Toast.makeText(this, "🏠 تم اختيار السياق: $selectedContext", Toast.LENGTH_SHORT).show()
                updateLocationByContext(selectedContext)
                showContextTips(selectedContext)
            }
        }

        // Task icon chips
        binding.chipGroupTaskIcon.setOnCheckedStateChangeListener { group, checkedIds ->
            if (checkedIds.isNotEmpty()) {
                val selectedChip = findViewById<Chip>(checkedIds[0])
                selectedTaskIcon = when (selectedChip.id) {
                    R.id.chipIconWork -> "💼"
                    R.id.chipIconStudy -> "📚"
                    R.id.chipIconSport -> "🏃‍♂️"
                    R.id.chipIconHealth -> "🍎"
                    R.id.chipIconIdea -> "💡"
                    else -> "💼"
                }
                Toast.makeText(this, "🎯 تم اختيار الأيقونة: $selectedTaskIcon", Toast.LENGTH_SHORT).show()
                updateTaskIcon(selectedTaskIcon)
            }
        }

        // Color selection
        setupColorSelection()

        // Switches
        binding.switchRecurring.setOnCheckedChangeListener { _, isChecked ->
            isRecurring = isChecked
            if (isChecked) {
                showRecurringOptionsDialog()
            } else {
                recurringPattern = ""
                Toast.makeText(this, "🔄 تم إلغاء تكرار المهمة", Toast.LENGTH_SHORT).show()
            }
        }

        binding.switchNotifications.setOnCheckedChangeListener { _, isChecked ->
            notificationsEnabled = isChecked
            if (isChecked) {
                showNotificationOptionsDialog()
            } else {
                Toast.makeText(this, "🔕 تم إيقاف التنبيهات", Toast.LENGTH_SHORT).show()
            }
        }

        binding.switchBreakDown.setOnCheckedChangeListener { _, isChecked ->
            autoBreakDown = isChecked
            if (isChecked) {
                showBreakDownDialog()
            } else {
                Toast.makeText(this, "📝 تم إلغاء تقسيم المهمة", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun setupColorSelection() {
        val colorViews = listOf(
            binding.colorRed,
            binding.colorBlue,
            binding.colorGreen,
            binding.colorOrange,
            binding.colorPurple
        )

        val colors = listOf("#F44336", "#2196F3", "#4CAF50", "#FF9800", "#9C27B0")
        val colorNames = listOf("أحمر", "أزرق", "أخضر", "برتقالي", "بنفسجي")

        colorViews.forEachIndexed { index, view ->
            view.setOnClickListener {
                // Reset all selections
                colorViews.forEach { it.isSelected = false }
                // Select current
                view.isSelected = true
                selectedTaskColor = colors[index]

                Toast.makeText(this, "🎨 تم اختيار اللون: ${colorNames[index]}", Toast.LENGTH_SHORT).show()
                applySelectedColor(colors[index])
                showColorSelectedFeedback(colorNames[index])
            }
        }

        // Set default selection (purple)
        binding.colorPurple.isSelected = true
        selectedTaskColor = "#9C27B0"
    }

    private fun setupCollapsibleSections() {
        // Advanced Details Section
        binding.tvAdvancedDetailsHeader.setOnClickListener {
            toggleSection(binding.layoutAdvancedDetails, binding.tvAdvancedDetailsHeader)
        }

        // Settings Section
        binding.tvSettingsHeader.setOnClickListener {
            toggleSection(binding.layoutSettings, binding.tvSettingsHeader)
        }
    }

    private fun toggleSection(layout: View, header: View) {
        if (layout.visibility == View.GONE) {
            layout.visibility = View.VISIBLE
            // Change arrow to up
            if (header is android.widget.TextView) {
                header.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_baseline_keyboard_arrow_up_24, 0)
            }
        } else {
            layout.visibility = View.GONE
            // Change arrow to down
            if (header is android.widget.TextView) {
                header.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_baseline_keyboard_arrow_down_24, 0)
            }
        }
    }

    private fun setupQuickActions() {
        binding.btnVoiceNote.setOnClickListener {
            startVoiceRecording()
        }

        binding.btnCamera.setOnClickListener {
            openCamera()
        }

        binding.btnLocation.setOnClickListener {
            getCurrentLocation()
        }

        binding.btnTimer.setOnClickListener {
            showTimerDialog()
        }

        binding.btnTemplate.setOnClickListener {
            showTemplateDialog()
        }
    }

    private fun startVoiceRecording() {
        showVoiceRecordingDialog()
    }

    private fun showVoiceRecordingDialog() {
        val voiceOptions = arrayOf(
            "🎙️ تسجيل ملاحظة صوتية",
            "🗣️ تحويل الكلام إلى نص",
            "📝 إملاء الوصف",
            "🎯 تسجيل أهداف المهمة",
            "📋 تسجيل خطوات العمل",
            "💡 تسجيل أفكار سريعة"
        )

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🎙️ خيارات التسجيل الصوتي")
            .setItems(voiceOptions) { _, which ->
                when (which) {
                    0 -> startBasicVoiceRecording()
                    1 -> startSpeechToText()
                    2 -> startDescriptionDictation()
                    3 -> startGoalsRecording()
                    4 -> startStepsRecording()
                    5 -> startQuickIdeasRecording()
                }
            }
            .setNegativeButton("إلغاء", null)
            .setNeutralButton("نصائح التسجيل 💡") { _, _ ->
                showVoiceRecordingTips()
            }
            .show()
    }

    private fun startBasicVoiceRecording() {
        currentSpeechMode = SpeechMode.BASIC_NOTE
        startSpeechRecognition("🎙️ تسجيل ملاحظة صوتية")
    }

    private fun startSpeechToText() {
        currentSpeechMode = SpeechMode.SPEECH_TO_TEXT
        startSpeechRecognition("🗣️ تحويل الكلام إلى نص")
    }

    private fun startDescriptionDictation() {
        currentSpeechMode = SpeechMode.DESCRIPTION_DICTATION
        startSpeechRecognition("📝 إملاء الوصف")
    }

    private fun startGoalsRecording() {
        currentSpeechMode = SpeechMode.GOALS_RECORDING
        startSpeechRecognition("🎯 تسجيل أهداف المهمة")
    }

    private fun startStepsRecording() {
        currentSpeechMode = SpeechMode.STEPS_RECORDING
        startSpeechRecognition("📋 تسجيل خطوات العمل")
    }

    private fun startQuickIdeasRecording() {
        currentSpeechMode = SpeechMode.QUICK_IDEAS
        startSpeechRecognition("💡 تسجيل أفكار سريعة")
    }

    private fun showVoiceRecordingTips() {
        val tips = """
            🎙️ **نصائح التسجيل الصوتي:**

            📱 **قبل التسجيل:**
            • تأكد من وجودك في مكان هادئ
            • اقترب من الميكروفون
            • تحدث بوضوح وبطء

            🗣️ **أثناء التسجيل:**
            • استخدم جمل قصيرة ومفهومة
            • توقف قليلاً بين الجمل
            • تجنب الكلمات الغامضة

            ✅ **بعد التسجيل:**
            • راجع النص المحول
            • عدل الأخطاء إن وجدت
            • أضف التفاصيل المفقودة

            💡 **نصائح إضافية:**
            • استخدم كلمات مفتاحية واضحة
            • اذكر الأرقام والتواريخ بوضوح
            • تجنب التسجيل في الأماكن الصاخبة
        """.trimIndent()

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("💡 دليل التسجيل الصوتي")
            .setMessage(tips)
            .setPositiveButton("مفهوم! 🎙️", null)
            .show()
    }

    // Real Speech Recognition Functions
    private fun startSpeechRecognition(title: String) {
        // Check if speech recognition is available
        if (!SpeechRecognizer.isRecognitionAvailable(this)) {
            Toast.makeText(this, "❌ التسجيل الصوتي غير متوفر على هذا الجهاز", Toast.LENGTH_LONG).show()
            return
        }

        // Check for audio recording permission
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO)
            != PackageManager.PERMISSION_GRANTED) {

            // Request permission
            androidx.core.app.ActivityCompat.requestPermissions(
                this,
                arrayOf(Manifest.permission.RECORD_AUDIO),
                REQUEST_CODE_RECORD_AUDIO_PERMISSION
            )
            return
        }

        // Permission granted, proceed with speech recognition
        performSpeechRecognition(title)
    }

    private fun performSpeechRecognition(title: String) {
        val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
            putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
            putExtra(RecognizerIntent.EXTRA_LANGUAGE, "ar-SA") // Arabic Saudi Arabia
            putExtra(RecognizerIntent.EXTRA_PROMPT, title)
            putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 1)
            putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS, 3000)
            putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_POSSIBLY_COMPLETE_SILENCE_LENGTH_MILLIS, 3000)
        }

        try {
            startActivityForResult(intent, REQUEST_CODE_SPEECH_INPUT)
            Toast.makeText(this, "🎙️ ابدأ بالتحدث الآن...", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Toast.makeText(this, "❌ خطأ في بدء التسجيل: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        when (requestCode) {
            REQUEST_CODE_RECORD_AUDIO_PERMISSION -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Toast.makeText(this, "✅ تم منح صلاحية التسجيل الصوتي", Toast.LENGTH_SHORT).show()
                    // Restart speech recognition with the current mode
                    val title = when (currentSpeechMode) {
                        SpeechMode.BASIC_NOTE -> "🎙️ تسجيل ملاحظة صوتية"
                        SpeechMode.SPEECH_TO_TEXT -> "🗣️ تحويل الكلام إلى نص"
                        SpeechMode.DESCRIPTION_DICTATION -> "📝 إملاء الوصف"
                        SpeechMode.GOALS_RECORDING -> "🎯 تسجيل أهداف المهمة"
                        SpeechMode.STEPS_RECORDING -> "📋 تسجيل خطوات العمل"
                        SpeechMode.QUICK_IDEAS -> "💡 تسجيل أفكار سريعة"
                    }
                    performSpeechRecognition(title)
                } else {
                    Toast.makeText(this, "❌ يجب منح صلاحية التسجيل الصوتي لاستخدام هذه الميزة", Toast.LENGTH_LONG).show()
                }
            }

            REQUEST_CODE_CAMERA_PERMISSION -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Toast.makeText(this, "✅ تم منح صلاحية الكاميرا", Toast.LENGTH_SHORT).show()
                    showCameraOptionsDialog()
                } else {
                    Toast.makeText(this, "❌ يجب منح صلاحية الكاميرا لاستخدام هذه الميزة", Toast.LENGTH_LONG).show()
                }
            }

            REQUEST_CODE_LOCATION_PERMISSION -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Toast.makeText(this, "✅ تم منح صلاحية الموقع", Toast.LENGTH_SHORT).show()
                    showLocationOptionsDialog()
                } else {
                    Toast.makeText(this, "❌ يجب منح صلاحية الموقع لاستخدام هذه الميزة", Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            REQUEST_CODE_SPEECH_INPUT -> {
                if (resultCode == Activity.RESULT_OK) {
                    val result = data?.getStringArrayListExtra(RecognizerIntent.EXTRA_RESULTS)
                    if (!result.isNullOrEmpty()) {
                        val spokenText = result[0]
                        processSpeechResult(spokenText)
                    } else {
                        Toast.makeText(this, "❌ لم يتم التعرف على أي كلام", Toast.LENGTH_SHORT).show()
                    }
                } else if (resultCode == Activity.RESULT_CANCELED) {
                    Toast.makeText(this, "🔕 تم إلغاء التسجيل", Toast.LENGTH_SHORT).show()
                }
            }

            REQUEST_CODE_CAMERA_CAPTURE -> {
                if (resultCode == Activity.RESULT_OK) {
                    // Photo was captured successfully
                    currentPhotoPath?.let { path ->
                        val photoInfo = "📷 تم التقاط صورة: ${File(path).name}"
                        val currentDesc = binding.taskDescriptionEditText.text.toString()
                        binding.taskDescriptionEditText.setText(
                            if (currentDesc.isEmpty()) photoInfo
                            else "$currentDesc\n$photoInfo"
                        )
                        Toast.makeText(this, "✅ تم حفظ الصورة بنجاح", Toast.LENGTH_SHORT).show()

                        // Show option to add description for the photo
                        showPhotoDescriptionDialog(path)
                    }
                } else {
                    Toast.makeText(this, "❌ تم إلغاء التقاط الصورة", Toast.LENGTH_SHORT).show()
                }
            }

            1005 -> { // Gallery selection
                if (resultCode == Activity.RESULT_OK && data != null) {
                    val selectedImageUri = data.data
                    selectedImageUri?.let { uri ->
                        val photoInfo = "🖼️ تم اختيار صورة من المعرض"
                        val currentDesc = binding.taskDescriptionEditText.text.toString()
                        binding.taskDescriptionEditText.setText(
                            if (currentDesc.isEmpty()) photoInfo
                            else "$currentDesc\n$photoInfo"
                        )
                        Toast.makeText(this, "✅ تم اختيار الصورة بنجاح", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this, "❌ تم إلغاء اختيار الصورة", Toast.LENGTH_SHORT).show()
                }
            }

            1006 -> { // OCR Camera capture
                if (resultCode == Activity.RESULT_OK) {
                    currentPhotoPath?.let { path ->
                        Toast.makeText(this, "📷 تم التقاط الصورة، جاري معالجتها للـ OCR...", Toast.LENGTH_SHORT).show()
                        processImageForOCR(null, path)
                    }
                } else {
                    Toast.makeText(this, "❌ تم إلغاء التقاط الصورة", Toast.LENGTH_SHORT).show()
                }
            }

            1007 -> { // OCR Gallery selection
                if (resultCode == Activity.RESULT_OK && data != null) {
                    val selectedImageUri = data.data
                    selectedImageUri?.let { uri ->
                        Toast.makeText(this, "🖼️ تم اختيار الصورة، جاري معالجتها للـ OCR...", Toast.LENGTH_SHORT).show()
                        processImageForOCR(uri)
                    }
                } else {
                    Toast.makeText(this, "❌ تم إلغاء اختيار الصورة", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun showPhotoDescriptionDialog(photoPath: String) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("📷 وصف الصورة")
            .setMessage("هل تريد إضافة وصف للصورة التي التقطتها؟")
            .setPositiveButton("إضافة وصف ✏️") { _, _ ->
                addPhotoDescription()
            }
            .setNegativeButton("لا، شكراً", null)
            .setNeutralButton("عرض الصورة 👁️") { _, _ ->
                // TODO: Show image preview
                Toast.makeText(this, "📷 الصورة محفوظة في: $photoPath", Toast.LENGTH_LONG).show()
            }
            .show()
    }

    private fun processSpeechResult(spokenText: String) {
        when (currentSpeechMode) {
            SpeechMode.BASIC_NOTE -> {
                val voiceNote = "🎙️ ملاحظة صوتية: $spokenText"
                val currentDesc = binding.taskDescriptionEditText.text.toString()
                binding.taskDescriptionEditText.setText(
                    if (currentDesc.isEmpty()) voiceNote
                    else "$currentDesc\n$voiceNote"
                )
                Toast.makeText(this, "✅ تم حفظ الملاحظة الصوتية", Toast.LENGTH_SHORT).show()
            }

            SpeechMode.SPEECH_TO_TEXT -> {
                val currentDesc = binding.taskDescriptionEditText.text.toString()
                binding.taskDescriptionEditText.setText(
                    if (currentDesc.isEmpty()) spokenText
                    else "$currentDesc\n$spokenText"
                )
                Toast.makeText(this, "✅ تم تحويل الكلام إلى نص بنجاح", Toast.LENGTH_SHORT).show()
            }

            SpeechMode.DESCRIPTION_DICTATION -> {
                binding.taskDescriptionEditText.setText(spokenText)
                Toast.makeText(this, "✅ تم إملاء الوصف بنجاح", Toast.LENGTH_SHORT).show()
            }

            SpeechMode.GOALS_RECORDING -> {
                val goals = "🎯 أهداف المهمة:\n• $spokenText"
                val currentDesc = binding.taskDescriptionEditText.text.toString()
                binding.taskDescriptionEditText.setText(
                    if (currentDesc.isEmpty()) goals
                    else "$currentDesc\n\n$goals"
                )
                Toast.makeText(this, "✅ تم تسجيل الأهداف", Toast.LENGTH_SHORT).show()
            }

            SpeechMode.STEPS_RECORDING -> {
                val steps = "📋 خطوات العمل:\n• $spokenText"
                val currentDesc = binding.taskDescriptionEditText.text.toString()
                binding.taskDescriptionEditText.setText(
                    if (currentDesc.isEmpty()) steps
                    else "$currentDesc\n\n$steps"
                )
                Toast.makeText(this, "✅ تم تسجيل خطوات العمل", Toast.LENGTH_SHORT).show()
            }

            SpeechMode.QUICK_IDEAS -> {
                val ideas = "💡 أفكار سريعة:\n• $spokenText"
                val currentDesc = binding.taskDescriptionEditText.text.toString()
                binding.taskDescriptionEditText.setText(
                    if (currentDesc.isEmpty()) ideas
                    else "$currentDesc\n\n$ideas"
                )
                Toast.makeText(this, "✅ تم تسجيل الأفكار السريعة", Toast.LENGTH_SHORT).show()
            }
        }

        // Show what was recognized
        showSpeechRecognitionResult(spokenText)
    }

    private fun showSpeechRecognitionResult(recognizedText: String) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🎙️ تم التعرف على الكلام")
            .setMessage("النص المُعرَّف:\n\n\"$recognizedText\"")
            .setPositiveButton("ممتاز! ✅", null)
            .setNeutralButton("تسجيل مرة أخرى 🔄") { _, _ ->
                // Restart speech recognition with the same mode
                val title = when (currentSpeechMode) {
                    SpeechMode.BASIC_NOTE -> "🎙️ تسجيل ملاحظة صوتية"
                    SpeechMode.SPEECH_TO_TEXT -> "🗣️ تحويل الكلام إلى نص"
                    SpeechMode.DESCRIPTION_DICTATION -> "📝 إملاء الوصف"
                    SpeechMode.GOALS_RECORDING -> "🎯 تسجيل أهداف المهمة"
                    SpeechMode.STEPS_RECORDING -> "📋 تسجيل خطوات العمل"
                    SpeechMode.QUICK_IDEAS -> "💡 تسجيل أفكار سريعة"
                }
                startSpeechRecognition(title)
            }
            .show()
    }

    private fun openCamera() {
        // Check camera permission
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
            != PackageManager.PERMISSION_GRANTED) {

            androidx.core.app.ActivityCompat.requestPermissions(
                this,
                arrayOf(Manifest.permission.CAMERA),
                REQUEST_CODE_CAMERA_PERMISSION
            )
            return
        }

        // Permission granted, show camera options
        showCameraOptionsDialog()
    }

    private fun showCameraOptionsDialog() {
        val cameraOptions = arrayOf(
            "📷 التقاط صورة جديدة",
            "🖼️ اختيار من المعرض",
            "📝 إضافة وصف للصورة",
            "🔍 مسح النص من الصورة (OCR)"
        )

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("📷 خيارات الكاميرا")
            .setItems(cameraOptions) { _, which ->
                when (which) {
                    0 -> capturePhoto()
                    1 -> selectFromGallery()
                    2 -> addPhotoDescription()
                    3 -> performOCR()
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun capturePhoto() {
        val takePictureIntent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
        if (takePictureIntent.resolveActivity(packageManager) != null) {
            // Create the File where the photo should go
            val photoFile: File? = try {
                createImageFile()
            } catch (ex: Exception) {
                Toast.makeText(this, "خطأ في إنشاء ملف الصورة: ${ex.message}", Toast.LENGTH_SHORT).show()
                null
            }

            // Continue only if the File was successfully created
            photoFile?.also {
                photoUri = FileProvider.getUriForFile(
                    this,
                    "${packageName}.provider",
                    it
                )
                takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri)
                startActivityForResult(takePictureIntent, REQUEST_CODE_CAMERA_CAPTURE)
                Toast.makeText(this, "📷 التقط الصورة الآن...", Toast.LENGTH_SHORT).show()
            }
        } else {
            Toast.makeText(this, "❌ تطبيق الكاميرا غير متوفر", Toast.LENGTH_SHORT).show()
        }
    }

    private fun createImageFile(): File {
        // Create an image file name
        val timeStamp: String = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val storageDir: File? = getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return File.createTempFile(
            "TASK_${timeStamp}_", /* prefix */
            ".jpg", /* suffix */
            storageDir /* directory */
        ).apply {
            // Save a file: path for use with ACTION_VIEW intents
            currentPhotoPath = absolutePath
        }
    }

    private fun selectFromGallery() {
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
        startActivityForResult(intent, 1005) // Gallery request code
        Toast.makeText(this, "🖼️ اختر صورة من المعرض...", Toast.LENGTH_SHORT).show()
    }

    private fun addPhotoDescription() {
        val input = android.widget.EditText(this)
        input.hint = "اكتب وصف للصورة..."

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("📝 وصف الصورة")
            .setMessage("أضف وصف تفصيلي للصورة:")
            .setView(input)
            .setPositiveButton("إضافة") { _, _ ->
                val description = input.text.toString().trim()
                if (description.isNotEmpty()) {
                    val photoDesc = "📷 صورة: $description"
                    val currentDesc = binding.taskDescriptionEditText.text.toString()
                    binding.taskDescriptionEditText.setText(
                        if (currentDesc.isEmpty()) photoDesc
                        else "$currentDesc\n$photoDesc"
                    )
                    Toast.makeText(this, "✅ تم إضافة وصف الصورة", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun performOCR() {
        val ocrOptions = arrayOf(
            "📷 التقاط صورة ومسح النص",
            "🖼️ اختيار صورة من المعرض ومسح النص",
            "📖 معلومات حول تقنية OCR"
        )

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🔍 مسح النص من الصورة (OCR)")
            .setMessage("يدعم النصوص العربية والإنجليزية تلقائياً")
            .setItems(ocrOptions) { _, which ->
                when (which) {
                    0 -> capturePhotoForOCR()
                    1 -> selectImageForOCR()
                    2 -> showOCRInfo()
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun capturePhotoForOCR() {
        // Check camera permission first
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
            != PackageManager.PERMISSION_GRANTED) {

            androidx.core.app.ActivityCompat.requestPermissions(
                this,
                arrayOf(Manifest.permission.CAMERA),
                REQUEST_CODE_CAMERA_PERMISSION
            )
            return
        }

        val takePictureIntent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
        if (takePictureIntent.resolveActivity(packageManager) != null) {
            val photoFile: File? = try {
                createImageFile()
            } catch (ex: Exception) {
                Toast.makeText(this, "خطأ في إنشاء ملف الصورة: ${ex.message}", Toast.LENGTH_SHORT).show()
                null
            }

            photoFile?.also {
                photoUri = FileProvider.getUriForFile(
                    this,
                    "${packageName}.provider",
                    it
                )
                takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri)
                startActivityForResult(takePictureIntent, 1006) // OCR camera request code
                Toast.makeText(this, "📷 التقط صورة النص لمسحه...", Toast.LENGTH_SHORT).show()
            }
        } else {
            Toast.makeText(this, "❌ تطبيق الكاميرا غير متوفر", Toast.LENGTH_SHORT).show()
        }
    }

    private fun selectImageForOCR() {
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
        startActivityForResult(intent, 1007) // OCR gallery request code
        Toast.makeText(this, "🖼️ اختر صورة تحتوي على نص...", Toast.LENGTH_SHORT).show()
    }

    private fun processImageForOCR(imageUri: Uri?, imagePath: String? = null) {
        try {
            android.util.Log.d("OCR_DEBUG", "Processing image for OCR. URI: $imageUri, Path: $imagePath")

            val bitmap: Bitmap? = when {
                imageUri != null -> {
                    contentResolver.openInputStream(imageUri)?.use { inputStream ->
                        BitmapFactory.decodeStream(inputStream)
                    }
                }
                imagePath != null -> {
                    android.util.Log.d("OCR_DEBUG", "Loading bitmap from path: $imagePath")
                    BitmapFactory.decodeFile(imagePath)
                }
                else -> null
            }

            if (bitmap != null) {
                android.util.Log.d("OCR_DEBUG", "Bitmap loaded successfully. Size: ${bitmap.width}x${bitmap.height}")
                showOCRLanguageSelectionDialog(bitmap)
            } else {
                android.util.Log.e("OCR_ERROR", "Failed to load bitmap from URI: $imageUri or Path: $imagePath")
                Toast.makeText(this, "❌ خطأ في تحميل الصورة", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            android.util.Log.e("OCR_ERROR", "Error in processImageForOCR", e)
            Toast.makeText(this, "❌ خطأ في معالجة الصورة: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showOCRLanguageSelectionDialog(bitmap: Bitmap) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🔍 مسح النص من الصورة")
            .setMessage("سيتم مسح النص بجميع اللغات المدعومة (العربية والإنجليزية)")
            .setPositiveButton("بدء المسح 🔍") { _, _ ->
                performTextRecognition(bitmap, "all")
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun performTextRecognition(bitmap: Bitmap, language: String) {
        try {
            val image = InputImage.fromBitmap(bitmap, 0)

            Toast.makeText(this, "🔍 جاري مسح النص من الصورة...", Toast.LENGTH_SHORT).show()

            textRecognizer.process(image)
                .addOnSuccessListener { visionText ->
                    val extractedText = visionText.text
                    android.util.Log.d("OCR_DEBUG", "Extracted text: '$extractedText'")

                    if (extractedText.isNotEmpty()) {
                        val languageLabel = when (language) {
                            "arabic" -> "العربية والإنجليزية"
                            "latin" -> "العربية والإنجليزية"
                            "both" -> "العربية والإنجليزية"
                            else -> "متعدد اللغات"
                        }
                        handleOCRResult(extractedText, languageLabel)
                    } else {
                        Toast.makeText(this, "❌ لم يتم العثور على نص في الصورة", Toast.LENGTH_LONG).show()
                    }
                }
                .addOnFailureListener { e ->
                    android.util.Log.e("OCR_ERROR", "Text recognition failed", e)
                    Toast.makeText(this, "❌ خطأ في مسح النص: ${e.message}", Toast.LENGTH_LONG).show()
                }
        } catch (e: Exception) {
            android.util.Log.e("OCR_ERROR", "Error in performTextRecognition", e)
            Toast.makeText(this, "❌ خطأ في معالجة الصورة: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun handleOCRResult(extractedText: String, language: String) {
        if (extractedText.isNotEmpty()) {
            showOCRResultDialog(extractedText, language)
        } else {
            Toast.makeText(this, "❌ لم يتم العثور على نص في الصورة", Toast.LENGTH_LONG).show()
        }
    }

    private fun showOCRResultDialog(extractedText: String, language: String) {
        val input = android.widget.EditText(this)
        input.setText(extractedText)
        input.hint = "يمكنك تعديل النص المستخرج..."
        input.maxLines = 10
        input.setVerticalScrollBarEnabled(true)

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("✅ تم استخراج النص ($language)")
            .setMessage("النص المستخرج من الصورة:")
            .setView(input)
            .setPositiveButton("إضافة للوصف ✅") { _, _ ->
                val finalText = input.text.toString().trim()
                if (finalText.isNotEmpty()) {
                    val ocrInfo = "🔍 نص مستخرج ($language):\n$finalText"
                    val currentDesc = binding.taskDescriptionEditText.text.toString()
                    binding.taskDescriptionEditText.setText(
                        if (currentDesc.isEmpty()) ocrInfo
                        else "$currentDesc\n\n$ocrInfo"
                    )
                    Toast.makeText(this, "✅ تم إضافة النص المستخرج للوصف", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("إلغاء", null)
            .setNeutralButton("نسخ النص 📋") { _, _ ->
                val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                val clip = ClipData.newPlainText("OCR Text", extractedText)
                clipboard.setPrimaryClip(clip)
                Toast.makeText(this, "📋 تم نسخ النص", Toast.LENGTH_SHORT).show()
            }
            .show()
    }

    private fun showOCRInfo() {
        val info = """
            🔍 تقنية OCR (التعرف البصري على الحروف):

            📖 ما هي؟
            تقنية تحويل الصور التي تحتوي على نص إلى نص رقمي قابل للتعديل

            🎯 الاستخدامات:
            • مسح الوثائق والمستندات
            • استخراج النص من الصور
            • رقمنة النصوص المطبوعة
            • تحويل الملاحظات المكتوبة لنص رقمي

            🌟 المميزات في التطبيق:
            • دعم تلقائي للعربية والإنجليزية
            • دقة عالية في التعرف
            • إمكانية تعديل النص المستخرج
            • إضافة النص مباشرة لوصف المهمة
            • نسخ النص للحافظة

            💡 نصائح للحصول على أفضل النتائج:
            • استخدم إضاءة جيدة وواضحة
            • تأكد من وضوح النص في الصورة
            • تجنب الانعكاسات والظلال
            • اجعل الكاميرا مستقيمة مع النص
            • استخدم خطوط واضحة ومقروءة

            🔤 اللغات المدعومة:
            • العربية (جميع أشكال الخط)
            • الإنجليزية (جميع الخطوط اللاتينية)
            • الأرقام والرموز
        """.trimIndent()

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🔍 دليل تقنية OCR")
            .setMessage(info)
            .setPositiveButton("رائع! 🚀", null)
            .show()
    }

    private fun getCurrentLocation() {
        // Check location permission
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION)
            != PackageManager.PERMISSION_GRANTED &&
            ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_COARSE_LOCATION)
            != PackageManager.PERMISSION_GRANTED) {

            androidx.core.app.ActivityCompat.requestPermissions(
                this,
                arrayOf(
                    Manifest.permission.ACCESS_FINE_LOCATION,
                    Manifest.permission.ACCESS_COARSE_LOCATION
                ),
                REQUEST_CODE_LOCATION_PERMISSION
            )
            return
        }

        // Permission granted, show location options
        showLocationOptionsDialog()
    }

    private fun showLocationOptionsDialog() {
        val locationOptions = arrayOf(
            "📍 تحديد الموقع الحالي",
            "🏠 اختيار موقع محفوظ",
            "✏️ كتابة الموقع يدوياً",
            "🗺️ فتح الخرائط"
        )

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("📍 خيارات الموقع")
            .setItems(locationOptions) { _, which ->
                when (which) {
                    0 -> detectCurrentLocation()
                    1 -> selectSavedLocation()
                    2 -> enterLocationManually()
                    3 -> openMaps()
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun detectCurrentLocation() {
        Toast.makeText(this, "📍 جاري تحديد موقعك الحالي...", Toast.LENGTH_SHORT).show()

        // Simulate GPS location detection
        binding.btnLocation.postDelayed({
            val currentLocations = listOf(
                "📍 الموقع الحالي: الرياض، حي الملز",
                "📍 الموقع الحالي: جدة، حي الروضة",
                "📍 الموقع الحالي: الدمام، حي الفيصلية",
                "📍 الموقع الحالي: مكة المكرمة، العزيزية"
            )
            val location = currentLocations.random()
            binding.taskLocationEditText.setText(location)
            Toast.makeText(this, "✅ تم تحديد موقعك الحالي", Toast.LENGTH_SHORT).show()
        }, 3000)
    }

    private fun selectSavedLocation() {
        val savedLocations = arrayOf(
            "🏠 المنزل",
            "💼 المكتب",
            "🏫 الجامعة",
            "🏥 المستشفى",
            "🛒 المول",
            "🕌 المسجد"
        )

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🏠 المواقع المحفوظة")
            .setItems(savedLocations) { _, which ->
                val selectedLocation = savedLocations[which]
                binding.taskLocationEditText.setText(selectedLocation)
                Toast.makeText(this, "✅ تم اختيار: $selectedLocation", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun enterLocationManually() {
        val input = android.widget.EditText(this)
        input.hint = "مثال: الرياض، حي الملز، شارع الملك فهد"

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("✏️ كتابة الموقع")
            .setMessage("اكتب الموقع بالتفصيل:")
            .setView(input)
            .setPositiveButton("حفظ") { _, _ ->
                val location = input.text.toString().trim()
                if (location.isNotEmpty()) {
                    binding.taskLocationEditText.setText("📍 $location")
                    Toast.makeText(this, "✅ تم حفظ الموقع", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun openMaps() {
        try {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse("geo:0,0?q=الرياض"))
            startActivity(intent)
            Toast.makeText(this, "🗺️ تم فتح تطبيق الخرائط", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Toast.makeText(this, "❌ لا يمكن فتح تطبيق الخرائط", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showTimerDialog() {
        val timerOptions = arrayOf(
            "⏰ 15 دقيقة - مهام سريعة",
            "🕐 30 دقيقة - مهام قصيرة",
            "🕑 45 دقيقة - جلسة تركيز",
            "🕒 ساعة واحدة - مهام متوسطة",
            "🕓 ساعتان - مهام طويلة",
            "🕕 3 ساعات - مشاريع كبيرة",
            "🔔 مؤقت مخصص",
            "⏱️ مؤقت بومودورو (25 دقيقة)"
        )

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("⏰ إعداد المؤقت والوقت المقدر")
            .setItems(timerOptions) { _, which ->
                when (which) {
                    0 -> setTimerAndEstimate("15 دقيقة", "0.25", "⏰")
                    1 -> setTimerAndEstimate("30 دقيقة", "0.5", "🕐")
                    2 -> setTimerAndEstimate("45 دقيقة", "0.75", "🕑")
                    3 -> setTimerAndEstimate("ساعة واحدة", "1.0", "🕒")
                    4 -> setTimerAndEstimate("ساعتان", "2.0", "🕓")
                    5 -> setTimerAndEstimate("3 ساعات", "3.0", "🕕")
                    6 -> showCustomTimerDialog()
                    7 -> startPomodoroTimer()
                }
            }
            .setNegativeButton("إلغاء", null)
            .setNeutralButton("نصائح إدارة الوقت 💡") { _, _ ->
                showTimeManagementTips()
            }
            .show()
    }

    private fun setTimerAndEstimate(timerName: String, estimatedHours: String, icon: String) {
        binding.taskEstimatedTimeEditText.setText(estimatedHours)

        // Add timer info to description
        val timerInfo = "$icon مؤقت: $timerName"
        val currentDesc = binding.taskDescriptionEditText.text.toString()
        binding.taskDescriptionEditText.setText(
            if (currentDesc.isEmpty()) timerInfo
            else "$currentDesc\n$timerInfo"
        )

        Toast.makeText(this, "✅ تم إعداد المؤقت: $timerName", Toast.LENGTH_SHORT).show()
        showTimerConfirmation(timerName, icon)
    }

    private fun showCustomTimerDialog() {
        val input = android.widget.EditText(this)
        input.hint = "مثال: 90 (بالدقائق)"
        input.inputType = android.text.InputType.TYPE_CLASS_NUMBER

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🔔 مؤقت مخصص")
            .setMessage("أدخل المدة بالدقائق:")
            .setView(input)
            .setPositiveButton("إعداد") { _, _ ->
                val minutes = input.text.toString().toIntOrNull()
                if (minutes != null && minutes > 0) {
                    val hours = minutes / 60.0
                    val timerName = "$minutes دقيقة"
                    setTimerAndEstimate(timerName, hours.toString(), "🔔")
                } else {
                    Toast.makeText(this, "❌ يرجى إدخال رقم صحيح", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun startPomodoroTimer() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("⏱️ تقنية البومودورو")
            .setMessage("""
                🍅 تقنية البومودورو:

                ⏰ 25 دقيقة عمل مركز
                ☕ 5 دقائق راحة
                🔄 تكرار 4 مرات
                🏖️ راحة طويلة 15-30 دقيقة

                هل تريد بدء جلسة بومودورو؟
            """.trimIndent())
            .setPositiveButton("بدء الجلسة 🍅") { _, _ ->
                setTimerAndEstimate("25 دقيقة (بومودورو)", "0.42", "🍅")
                Toast.makeText(this, "🍅 بدأت جلسة البومودورو - ركز لمدة 25 دقيقة!", Toast.LENGTH_LONG).show()
            }
            .setNegativeButton("إلغاء", null)
            .setNeutralButton("معرفة المزيد 📖") { _, _ ->
                showPomodoroInfo()
            }
            .show()
    }

    private fun showTimerConfirmation(timerName: String, icon: String) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("$icon تأكيد المؤقت")
            .setMessage("تم إعداد المؤقت لـ $timerName\n\nهل تريد بدء العد التنازلي الآن؟")
            .setPositiveButton("بدء الآن ▶️") { _, _ ->
                Toast.makeText(this, "⏰ بدأ العد التنازلي...", Toast.LENGTH_SHORT).show()
                // TODO: Implement actual countdown timer
            }
            .setNegativeButton("لاحقاً", null)
            .show()
    }

    private fun showTimeManagementTips() {
        val tips = """
            ⏰ نصائح إدارة الوقت:

            🎯 تقسيم المهام:
            • قسم المهام الكبيرة لمهام صغيرة
            • حدد وقت محدد لكل مهمة
            • خذ فترات راحة منتظمة

            🍅 تقنية البومودورو:
            • 25 دقيقة عمل + 5 دقائق راحة
            • تحسن التركيز والإنتاجية
            • تقلل من التعب والإرهاق

            ⚡ نصائح سريعة:
            • تجنب المشتتات أثناء العمل
            • استخدم تطبيقات حجب المواقع
            • كافئ نفسك بعد إنجاز المهام
        """.trimIndent()

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("💡 نصائح إدارة الوقت")
            .setMessage(tips)
            .setPositiveButton("مفيد! 👍", null)
            .show()
    }

    private fun showPomodoroInfo() {
        val info = """
            🍅 تقنية البومودورو:

            📖 ما هي؟
            تقنية إدارة وقت طورها فرانشيسكو سيريلو في أواخر الثمانينات

            🎯 كيف تعمل؟
            1. اختر مهمة للعمل عليها
            2. اضبط المؤقت على 25 دقيقة
            3. اعمل على المهمة حتى ينتهي المؤقت
            4. خذ راحة قصيرة (5 دقائق)
            5. كرر العملية 4 مرات
            6. خذ راحة طويلة (15-30 دقيقة)

            ✅ الفوائد:
            • تحسين التركيز
            • زيادة الإنتاجية
            • تقليل التعب الذهني
            • تحسين إدارة الوقت
        """.trimIndent()

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🍅 دليل البومودورو")
            .setMessage(info)
            .setPositiveButton("فهمت! 🎯", null)
            .show()
    }

    private fun showTemplateDialog() {
        val templates = arrayOf(
            "💼 قالب اجتماع عمل - تنظيم الاجتماعات",
            "📚 قالب جلسة دراسة - تخطيط الدراسة",
            "📞 قالب مكالمة مهمة - إدارة المكالمات",
            "🏃‍♂️ قالب تمرين رياضي - اللياقة البدنية",
            "🍎 قالب نشاط صحي - الصحة والعافية",
            "💡 قالب مشروع إبداعي - الأفكار والإبداع",
            "📝 قالب مهمة يومية - المهام الروتينية",
            "🎯 قالب هدف شخصي - تحقيق الأهداف",
            "🛒 قالب قائمة تسوق - التسوق والمشتريات",
            "✈️ قالب رحلة سفر - تخطيط السفر"
        )

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("📋 اختيار قالب جاهز")
            .setItems(templates) { _, which ->
                when (which) {
                    0 -> showTemplatePreviewAndApply("اجتماع عمل", "💼", "عمل", "عالية")
                    1 -> showTemplatePreviewAndApply("جلسة دراسة", "📚", "دراسة", "متوسطة")
                    2 -> showTemplatePreviewAndApply("مكالمة مهمة", "📞", "اتصالات", "عالية")
                    3 -> showTemplatePreviewAndApply("تمرين رياضي", "🏃‍♂️", "صحة", "متوسطة")
                    4 -> showTemplatePreviewAndApply("نشاط صحي", "🍎", "صحة", "منخفضة")
                    5 -> showTemplatePreviewAndApply("مشروع إبداعي", "💡", "إبداع", "متوسطة")
                    6 -> showTemplatePreviewAndApply("مهمة يومية", "📝", "شخصي", "منخفضة")
                    7 -> showTemplatePreviewAndApply("هدف شخصي", "🎯", "شخصي", "عالية")
                    8 -> showTemplatePreviewAndApply("قائمة تسوق", "🛒", "تسوق", "منخفضة")
                    9 -> showTemplatePreviewAndApply("رحلة سفر", "✈️", "سفر", "متوسطة")
                }
            }
            .setNegativeButton("إلغاء", null)
            .setNeutralButton("إنشاء قالب مخصص ✨") { _, _ ->
                showCreateCustomTemplateDialog()
            }
            .show()
    }

    private fun showTemplatePreviewAndApply(templateName: String, icon: String, category: String, priority: String) {
        val templateContent = generateTemplateContent(templateName, icon)

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("$icon معاينة القالب")
            .setMessage("قالب: $templateName\n\n$templateContent\n\nهل تريد تطبيق هذا القالب؟")
            .setPositiveButton("تطبيق ✅") { _, _ ->
                applyTemplate(templateName, icon, category, priority)
                applyTemplateContent(templateName, icon)
                Toast.makeText(this, "$icon تم تطبيق قالب $templateName بنجاح!", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("إلغاء", null)
            .setNeutralButton("تخصيص 🔧") { _, _ ->
                showTemplateCustomizationDialog(templateName, icon, category, priority)
            }
            .show()
    }

    private fun generateTemplateContent(templateName: String, icon: String): String {
        return when (templateName) {
            "اجتماع عمل" -> """
                📋 جدول الأعمال:
                • نقطة 1
                • نقطة 2
                • نقطة 3

                👥 المشاركون:
                • [اسم المشارك]

                🎯 الأهداف:
                • هدف رئيسي

                📝 الملاحظات:
                • [ملاحظات مهمة]
            """.trimIndent()

            "جلسة دراسة" -> """
                📚 المواد المطلوبة:
                • الكتاب/المرجع
                • الدفتر والقلم

                🎯 أهداف الجلسة:
                • فهم الموضوع
                • حل التمارين

                ⏰ الجدول الزمني:
                • مراجعة: 30 دقيقة
                • تطبيق: 30 دقيقة

                📝 الملاحظات:
                • [نقاط مهمة]
            """.trimIndent()

            "مكالمة مهمة" -> """
                📞 تفاصيل المكالمة:
                • الشخص: [اسم الشخص]
                • الوقت: [وقت المكالمة]

                🎯 الهدف من المكالمة:
                • [الهدف الرئيسي]

                📋 النقاط المهمة:
                • نقطة 1
                • نقطة 2

                📝 المتابعة المطلوبة:
                • [إجراءات المتابعة]
            """.trimIndent()

            "تمرين رياضي" -> """
                🏃‍♂️ نوع التمرين:
                • [نوع التمرين]

                ⏰ المدة:
                • الإحماء: 10 دقائق
                • التمرين الأساسي: 30 دقيقة
                • التهدئة: 10 دقائق

                🎯 الهدف:
                • [الهدف من التمرين]

                📝 ملاحظات:
                • [ملاحظات مهمة]
            """.trimIndent()

            else -> """
                $icon $templateName

                🎯 الهدف:
                • [الهدف الرئيسي]

                📋 الخطوات:
                • خطوة 1
                • خطوة 2
                • خطوة 3

                📝 ملاحظات:
                • [ملاحظات إضافية]
            """.trimIndent()
        }
    }

    private fun applyTemplateContent(templateName: String, icon: String) {
        val content = generateTemplateContent(templateName, icon)
        binding.taskDescriptionEditText.setText(content)
    }

    private fun showTemplateCustomizationDialog(templateName: String, icon: String, category: String, priority: String) {
        val customOptions = arrayOf(
            "✏️ تعديل الوصف",
            "⏰ تغيير الوقت المقدر",
            "🏷️ تغيير الفئة",
            "🚨 تغيير الأولوية",
            "📍 إضافة موقع",
            "🔔 إعداد تذكير"
        )

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🔧 تخصيص القالب")
            .setMessage("اختر ما تريد تخصيصه في قالب $templateName:")
            .setItems(customOptions) { _, which ->
                when (which) {
                    0 -> customizeDescription(templateName, icon)
                    1 -> customizeEstimatedTime()
                    2 -> customizeCategory()
                    3 -> customizePriority()
                    4 -> customizeLocation()
                    5 -> customizeReminder()
                }
            }
            .setPositiveButton("تطبيق القالب الآن ✅") { _, _ ->
                applyTemplate(templateName, icon, category, priority)
                applyTemplateContent(templateName, icon)
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun customizeDescription(templateName: String, icon: String) {
        val input = android.widget.EditText(this)
        input.setText(generateTemplateContent(templateName, icon))
        input.hint = "عدل الوصف كما تريد..."

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("✏️ تخصيص الوصف")
            .setView(input)
            .setPositiveButton("حفظ") { _, _ ->
                binding.taskDescriptionEditText.setText(input.text.toString())
                Toast.makeText(this, "✅ تم تخصيص الوصف", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun customizeEstimatedTime() {
        val input = android.widget.EditText(this)
        input.hint = "مثال: 1.5 (بالساعات)"
        input.inputType = android.text.InputType.TYPE_CLASS_NUMBER or android.text.InputType.TYPE_NUMBER_FLAG_DECIMAL

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("⏰ تخصيص الوقت المقدر")
            .setView(input)
            .setPositiveButton("حفظ") { _, _ ->
                val time = input.text.toString()
                if (time.isNotEmpty()) {
                    binding.taskEstimatedTimeEditText.setText(time)
                    Toast.makeText(this, "✅ تم تحديد الوقت المقدر", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun customizeCategory() {
        // This would trigger the existing category selector
        binding.taskCategorySelector.performClick()
    }

    private fun customizePriority() {
        // This would trigger the existing priority selector
        binding.taskPrioritySelector.performClick()
    }

    private fun customizeLocation() {
        // This would trigger the existing location functionality
        getCurrentLocation()
    }

    private fun customizeReminder() {
        // This would trigger the existing reminder dropdown
        binding.taskReminderAutoCompleteTextView.performClick()
    }

    private fun showCreateCustomTemplateDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("✨ إنشاء قالب مخصص")
            .setMessage("هذه الميزة ستكون متوفرة قريباً!\n\nستتمكن من إنشاء قوالب مخصصة وحفظها لاستخدامها لاحقاً.")
            .setPositiveButton("فهمت 👍", null)
            .show()
    }

    private fun setupTemplates() {
        binding.templateMeeting.setOnClickListener {
            showTemplateConfirmation("اجتماع عمل", "💼") {
                applyTemplate("اجتماع عمل", "💼", "عمل", "عالية")
                setEstimatedTime("1.0")
                setDefaultReminder("يوم واحد قبل")
            }
        }

        binding.templateStudy.setOnClickListener {
            showTemplateConfirmation("جلسة دراسة", "📚") {
                applyTemplate("جلسة دراسة", "📚", "دراسة", "متوسطة")
                setEstimatedTime("2.0")
                setDefaultReminder("في نفس اليوم")
            }
        }

        binding.templateCall.setOnClickListener {
            showTemplateConfirmation("مكالمة مهمة", "📞") {
                applyTemplate("مكالمة مهمة", "📞", "اتصالات", "عالية")
                setEstimatedTime("0.5")
                setDefaultReminder("في نفس اليوم")
            }
        }

        binding.templateExercise.setOnClickListener {
            showTemplateConfirmation("تمرين رياضي", "🏃‍♂️") {
                applyTemplate("تمرين رياضي", "🏃‍♂️", "صحة", "متوسطة")
                setEstimatedTime("1.0")
                setDefaultReminder("في نفس اليوم")
            }
        }
    }

    private fun showTemplateConfirmation(templateName: String, icon: String, onConfirm: () -> Unit) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("$icon تطبيق قالب")
            .setMessage("هل تريد تطبيق قالب \"$templateName\"؟\n\nسيتم ملء الحقول تلقائياً بالمعلومات المناسبة.")
            .setPositiveButton("تطبيق ✅") { _, _ ->
                onConfirm()
                Toast.makeText(this, "$icon تم تطبيق قالب $templateName بنجاح!", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("إلغاء", null)
            .setNeutralButton("معاينة 👁️") { _, _ ->
                showTemplatePreview(templateName, icon)
            }
            .show()
    }

    private fun showTemplatePreview(templateName: String, icon: String) {
        val previewText = when (templateName) {
            "اجتماع عمل" -> """
                $icon **قالب اجتماع عمل**

                📋 **سيتم ملء:**
                • الاسم: اجتماع عمل
                • الفئة: عمل
                • الأولوية: عالية
                • الوقت المقدر: ساعة واحدة
                • التذكير: يوم قبل

                📝 **الوصف سيحتوي على:**
                • جدول الأعمال
                • قائمة المشاركين
                • مساحة للملاحظات
            """.trimIndent()

            "جلسة دراسة" -> """
                $icon **قالب جلسة دراسة**

                📋 **سيتم ملء:**
                • الاسم: جلسة دراسة
                • الفئة: دراسة
                • الأولوية: متوسطة
                • الوقت المقدر: ساعتان
                • التذكير: نفس اليوم

                📝 **الوصف سيحتوي على:**
                • المواد المطلوبة
                • الأهداف
                • الجدول الزمني
            """.trimIndent()

            "مكالمة مهمة" -> """
                $icon **قالب مكالمة مهمة**

                📋 **سيتم ملء:**
                • الاسم: مكالمة مهمة
                • الفئة: اتصالات
                • الأولوية: عالية
                • الوقت المقدر: 30 دقيقة
                • التذكير: نفس اليوم

                📝 **الوصف سيحتوي على:**
                • الهدف من المكالمة
                • النقاط المهمة
                • المتابعة المطلوبة
            """.trimIndent()

            "تمرين رياضي" -> """
                $icon **قالب تمرين رياضي**

                📋 **سيتم ملء:**
                • الاسم: تمرين رياضي
                • الفئة: صحة
                • الأولوية: متوسطة
                • الوقت المقدر: ساعة واحدة
                • التذكير: نفس اليوم

                📝 **الوصف سيحتوي على:**
                • نوع التمرين
                • المدة
                • الهدف
            """.trimIndent()

            else -> "معاينة غير متوفرة"
        }

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("👁️ معاينة القالب")
            .setMessage(previewText)
            .setPositiveButton("تطبيق الآن ✅") { _, _ ->
                when (templateName) {
                    "اجتماع عمل" -> {
                        applyTemplate("اجتماع عمل", "💼", "عمل", "عالية")
                        setEstimatedTime("1.0")
                        setDefaultReminder("يوم واحد قبل")
                    }
                    "جلسة دراسة" -> {
                        applyTemplate("جلسة دراسة", "📚", "دراسة", "متوسطة")
                        setEstimatedTime("2.0")
                        setDefaultReminder("في نفس اليوم")
                    }
                    "مكالمة مهمة" -> {
                        applyTemplate("مكالمة مهمة", "📞", "اتصالات", "عالية")
                        setEstimatedTime("0.5")
                        setDefaultReminder("في نفس اليوم")
                    }
                    "تمرين رياضي" -> {
                        applyTemplate("تمرين رياضي", "🏃‍♂️", "صحة", "متوسطة")
                        setEstimatedTime("1.0")
                        setDefaultReminder("في نفس اليوم")
                    }
                }
                Toast.makeText(this, "$icon تم تطبيق القالب!", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("إغلاق", null)
            .show()
    }

    private fun setEstimatedTime(hours: String) {
        binding.taskEstimatedTimeEditText.setText(hours)
    }

    private fun setDefaultReminder(reminder: String) {
        binding.taskReminderAutoCompleteTextView.setText(reminder, false)
    }

    private fun setupAISuggestionCard() {
        binding.cardAiSuggestions.setOnClickListener {
            generateAISuggestions()
        }
    }

    private fun generateAISuggestions() {
        Toast.makeText(this, "🤖 جاري إنشاء الاقتراحات الذكية...", Toast.LENGTH_SHORT).show()

        // محاكاة اقتراحات AI
        binding.cardAiSuggestions.postDelayed({
            val currentTitle = binding.taskNameEditText.text.toString()
            val suggestions = when {
                currentTitle.contains("اجتماع", ignoreCase = true) -> listOf(
                    "📋 تحضير جدول الأعمال",
                    "📧 إرسال دعوات الاجتماع",
                    "💻 تجهيز العرض التقديمي",
                    "📝 تحضير الملاحظات",
                    "🔗 إعداد رابط الاجتماع"
                )
                currentTitle.contains("مشروع", ignoreCase = true) -> listOf(
                    "📊 تحليل المتطلبات",
                    "⏱️ وضع الجدول الزمني",
                    "👥 تحديد أعضاء الفريق",
                    "📈 تحديد مؤشرات النجاح",
                    "💰 تقدير الميزانية"
                )
                currentTitle.contains("دراسة", ignoreCase = true) -> listOf(
                    "📚 مراجعة المواد",
                    "✍️ حل التمارين",
                    "🎯 تحديد النقاط المهمة",
                    "⏰ جدولة فترات المراجعة",
                    "📝 إعداد ملخصات"
                )
                currentTitle.contains("تقرير", ignoreCase = true) -> listOf(
                    "📊 جمع البيانات",
                    "📈 تحليل النتائج",
                    "📝 كتابة المسودة الأولى",
                    "🔍 مراجعة وتدقيق",
                    "📤 إرسال التقرير النهائي"
                )
                else -> listOf(
                    "🎯 تحديد الهدف الرئيسي",
                    "📝 تقسيم المهمة لخطوات",
                    "⏰ تحديد الوقت المطلوب",
                    "✅ وضع معايير الإنجاز",
                    "📋 إعداد قائمة المراجعة"
                )
            }

            showAISuggestionsDialog(suggestions)
        }, 2000)
    }

    private fun showAISuggestionsDialog(suggestions: List<String>) {
        val suggestionsArray = suggestions.toTypedArray()

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🤖 اقتراحات ذكية")
            .setMessage("اختر الاقتراحات التي تريد إضافتها:")
            .setItems(suggestionsArray) { _, which ->
                val selectedSuggestion = suggestions[which]
                val currentDesc = binding.taskDescriptionEditText.text.toString()
                binding.taskDescriptionEditText.setText(
                    if (currentDesc.isEmpty()) selectedSuggestion
                    else "$currentDesc\n$selectedSuggestion"
                )
                Toast.makeText(this, "✅ تم إضافة الاقتراح", Toast.LENGTH_SHORT).show()
            }
            .setPositiveButton("إضافة الكل") { _, _ ->
                val allSuggestions = suggestions.joinToString("\n")
                val currentDesc = binding.taskDescriptionEditText.text.toString()
                binding.taskDescriptionEditText.setText(
                    if (currentDesc.isEmpty()) allSuggestions
                    else "$currentDesc\n$allSuggestions"
                )
                Toast.makeText(this, "✅ تم إضافة جميع الاقتراحات", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun applyTemplate(name: String, icon: String, category: String, priority: String) {
        binding.taskNameEditText.setText(name)

        // تحديث الفئة في Spinner
        val categoryIndex = when (category) {
            "عمل" -> 0
            "شخصي" -> 1
            "دراسة" -> 2
            "صحة" -> 3
            else -> 0
        }

        // تحديث الأولوية في Spinner
        val priorityIndex = when (priority) {
            "عاجلة" -> 0
            "عالية" -> 1
            "متوسطة" -> 2
            "منخفضة" -> 3
            else -> 2
        }

        // تحديث النصوص في الـ selectors
        val fullCategoryText = when (category) {
            "عمل" -> "💼 عمل - مهام العمل والوظيفة"
            "شخصي" -> "👤 شخصي - أمور شخصية"
            "دراسة" -> "📚 دراسة - تعليم وتطوير"
            "صحة" -> "🏥 صحة - صحة ولياقة"
            else -> "💼 عمل - مهام العمل والوظيفة"
        }

        val fullPriorityText = when (priority) {
            "عاجلة" -> "🔴 عاجلة - يجب إنجازها فوراً"
            "عالية" -> "🟠 عالية - مهمة جداً"
            "متوسطة" -> "🟡 متوسطة - مهمة عادية"
            "منخفضة" -> "🟢 منخفضة - يمكن تأجيلها"
            else -> "🟡 متوسطة - مهمة عادية"
        }

        binding.taskCategoryText.text = fullCategoryText
        binding.taskPriorityText.text = fullPriorityText
        selectedTaskIcon = icon

        // Update icon selection
        when (icon) {
            "💼" -> binding.chipIconWork.isChecked = true
            "📚" -> binding.chipIconStudy.isChecked = true
            "🏃‍♂️" -> binding.chipIconSport.isChecked = true
            "🍎" -> binding.chipIconHealth.isChecked = true
            "💡" -> binding.chipIconIdea.isChecked = true
        }

        // إضافة وصف تلقائي حسب القالب
        val templateDescription = getTemplateDescription(name, category)
        if (templateDescription.isNotEmpty()) {
            binding.taskDescriptionEditText.setText(templateDescription)
        }

        Toast.makeText(this, "$icon تم تطبيق قالب: $name", Toast.LENGTH_SHORT).show()
    }

    private fun getTemplateDescription(name: String, category: String): String {
        return when {
            name.contains("اجتماع") -> """
                📋 جدول الأعمال:
                • نقطة 1
                • نقطة 2
                • نقطة 3

                👥 المشاركون:
                •

                📝 الملاحظات:
                •
            """.trimIndent()

            name.contains("دراسة") -> """
                📚 المواد المطلوبة:
                •

                🎯 الأهداف:
                •

                ⏰ الجدول الزمني:
                •
            """.trimIndent()

            name.contains("مكالمة") -> """
                📞 الهدف من المكالمة:
                •

                📋 النقاط المهمة:
                •

                📝 المتابعة المطلوبة:
                •
            """.trimIndent()

            name.contains("رياضي") -> """
                🏃‍♂️ نوع التمرين:
                •

                ⏱️ المدة:
                •

                🎯 الهدف:
                •
            """.trimIndent()

            else -> ""
        }
    }

    private fun updateTaskColorByPriority(priority: String, color: String) {
        try {
            // تحديث لون الواجهة حسب الأولوية
            val colorInt = android.graphics.Color.parseColor(color)

            // يمكن تحديث لون العناصر هنا
            binding.taskNameInputLayout.boxStrokeColor = colorInt

        } catch (e: Exception) {
            android.util.Log.e("TaskColor", "Error updating color: ${e.message}")
        }
    }

    private fun updateIconByCategory(category: String, icon: String) {
        selectedTaskIcon = icon

        // تحديث الأيقونة المختارة
        when (icon) {
            "💼" -> binding.chipIconWork.isChecked = true
            "📚" -> binding.chipIconStudy.isChecked = true
            "🏥" -> binding.chipIconHealth.isChecked = true
            "👤" -> binding.chipIconIdea.isChecked = true
            "🏃‍♂️" -> binding.chipIconSport.isChecked = true
        }
    }

    private fun updateSuggestionsByPriority(priority: String) {
        val suggestions = when (priority) {
            "عاجلة" -> "⚡ مهمة عاجلة! ننصح بإنجازها في أقرب وقت ممكن"
            "عالية" -> "🎯 مهمة مهمة، خصص لها وقت كافي"
            "متوسطة" -> "📅 يمكن جدولتها ضمن المهام اليومية"
            "منخفضة" -> "🕐 يمكن تأجيلها عند الحاجة"
            else -> "💡 حدد الأولوية لتحصل على نصائح أفضل"
        }

        // تحديث نص الاقتراحات في الكارد
        updateAISuggestionCard(suggestions)
    }

    private fun updateSuggestionsByCategory(category: String) {
        val suggestions = when (category) {
            "عمل" -> "💼 أفضل وقت: ساعات العمل الرسمية (9-17)"
            "دراسة" -> "📚 أفضل وقت: الصباح الباكر (6-10)"
            "صحة" -> "🏥 أفضل وقت: الصباح أو المساء"
            "شخصي" -> "👤 أفضل وقت: عطلة نهاية الأسبوع"
            "عائلة" -> "👨‍👩‍👧‍👦 أفضل وقت: المساء أو العطل"
            else -> "💡 اختر الفئة لتحصل على نصائح مخصصة"
        }

        updateAISuggestionCard(suggestions)
    }

    private fun updateAISuggestionCard(suggestion: String) {
        // يمكن تحديث محتوى كارد الاقتراحات هنا
        // لكن نحتاج للوصول للعناصر داخل الكارد
        Toast.makeText(this, suggestion, Toast.LENGTH_LONG).show()
    }

    private fun setupSaveButtons() {
        binding.addTaskButton.setOnClickListener {
            saveTask()
        }

        binding.btnSaveDraft.setOnClickListener {
            saveTaskAsDraft()
        }

        binding.btnAddAnother.setOnClickListener {
            saveTaskAndCreateAnother()
        }
    }

    private fun saveTask() {
        if (validateForm()) {
            val task = createTaskFromForm()

            lifecycleScope.launch {
                if (isEditingTask) {
                    taskViewModel.update(task)
                    Toast.makeText(this@AddEditAdvancedTaskActivity, "تم تحديث المهمة بنجاح", Toast.LENGTH_SHORT).show()
                    setResult(Activity.RESULT_OK)
                    finish()
                } else {
                    taskViewModel.insert(task)
                    Toast.makeText(this@AddEditAdvancedTaskActivity, "تم إضافة المهمة بنجاح", Toast.LENGTH_SHORT).show()

                    // إضافة المهمة تلقائياً إلى منبه الهاتف قبل الإغلاق
                    addTaskToPhoneClock(task)
                }
            }
        }
    }

    private fun saveTaskAsDraft() {
        val taskName = binding.taskNameEditText.text.toString().trim()
        if (taskName.isEmpty()) {
            binding.taskNameInputLayout.error = "اسم المهمة مطلوب لحفظ المسودة"
            return
        }

        // إنشاء مهمة مسودة
        val draftTask = createDraftTask()

        lifecycleScope.launch {
            // حفظ كمسودة (يمكن إضافة خاصية isDraft للـ Task model لاحقاً)
            taskViewModel.insert(draftTask)

            Toast.makeText(this@AddEditAdvancedTaskActivity, "💾 تم حفظ المهمة كمسودة", Toast.LENGTH_LONG).show()

            // إظهار خيارات إضافية
            showDraftSavedDialog()
        }
    }

    private fun createDraftTask(): Task {
        val taskName = "[مسودة] ${binding.taskNameEditText.text.toString().trim()}"
        val description = binding.taskDescriptionEditText.text.toString().trim()
        val expirationDateStr = binding.taskExpirationDateEditText.text.toString().trim()

        // استخدام تاريخ افتراضي إذا لم يتم تحديد تاريخ
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val expirationDate = if (expirationDateStr.isNotEmpty()) {
            dateFormat.parse(expirationDateStr) ?: Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000) // غداً
        } else {
            Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000) // غداً
        }

        return Task(
            id = 0,
            name = taskName,
            expirationDate = expirationDate,
            reminderDaysBefore = getReminderDays(),
            isCompleted = false
        )
    }

    private fun showDraftSavedDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("💾 تم حفظ المسودة")
            .setMessage("تم حفظ المهمة كمسودة بنجاح. ماذا تريد أن تفعل الآن؟")
            .setPositiveButton("متابعة التحرير") { _, _ ->
                // البقاء في الصفحة
            }
            .setNeutralButton("إنشاء مهمة جديدة") { _, _ ->
                clearForm()
                Toast.makeText(this, "✨ جاهز لإنشاء مهمة جديدة", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("العودة للقائمة") { _, _ ->
                finish()
            }
            .show()
    }

    private fun saveTaskAndCreateAnother() {
        if (validateForm()) {
            val task = createTaskFromForm()

            lifecycleScope.launch {
                taskViewModel.insert(task)
                Toast.makeText(this@AddEditAdvancedTaskActivity, "تم إضافة المهمة. جاهز لإضافة أخرى!", Toast.LENGTH_SHORT).show()

                // إضافة المهمة تلقائياً إلى منبه الهاتف
                addTaskToPhoneClock(task)

                // Clear form for new task
                clearForm()
            }
        }
    }

    /**
     * إضافة المهمة تلقائياً إلى منبه الهاتف
     */
    private fun addTaskToPhoneClock(task: Task) {
        try {
            // إظهار خيارات إضافة المنبه للمستخدم
            showAlarmOptionsDialog(task)
        } catch (e: Exception) {
            // في حالة حدوث خطأ، لا نريد أن نوقف عملية حفظ المهمة
            Toast.makeText(this, "تم حفظ المهمة، لكن حدث خطأ في إضافة المنبه", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * إظهار خيارات إضافة المنبه
     */
    private fun showAlarmOptionsDialog(task: Task) {
        val options = arrayOf(
            "⏰ إضافة منبه تلقائي",
            "🕘 إضافة منبه مخصص",
            "⚡ منبه سريع (بعد 30 دقيقة)",
            "❌ بدون منبه"
        )

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("📱 إضافة المهمة لمنبه الهاتف")
            .setMessage("هل تريد إضافة منبه لتذكيرك بهذه المهمة؟")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> {
                        // منبه تلقائي
                        alarmClockManager.addTaskToAlarmClock(task)
                        finishActivity()
                    }
                    1 -> {
                        // منبه مخصص
                        showCustomAlarmTimeDialog(task)
                    }
                    2 -> {
                        // منبه سريع
                        alarmClockManager.addQuickReminder(task)
                        finishActivity()
                    }
                    3 -> {
                        // بدون منبه
                        Toast.makeText(this, "تم حفظ المهمة بدون منبه", Toast.LENGTH_SHORT).show()
                        finishActivity()
                    }
                }
            }
            .setNegativeButton("إلغاء") { _, _ ->
                Toast.makeText(this, "تم حفظ المهمة بدون منبه", Toast.LENGTH_SHORT).show()
                finishActivity()
            }
            .show()
    }

    /**
     * إظهار حوار اختيار وقت المنبه المخصص
     */
    private fun showCustomAlarmTimeDialog(task: Task) {
        val calendar = Calendar.getInstance()

        TimePickerDialog(
            this,
            { _, hourOfDay, minute ->
                // إضافة المنبه بالوقت المحدد
                alarmClockManager.addCustomAlarm(task, hourOfDay, minute)
                finishActivity()
            },
            calendar.get(Calendar.HOUR_OF_DAY),
            calendar.get(Calendar.MINUTE),
            true // استخدام تنسيق 24 ساعة
        ).show()
    }

    /**
     * إنهاء النشاط بعد حفظ المهمة
     */
    private fun finishActivity() {
        setResult(Activity.RESULT_OK)
        finish()
    }

    private fun validateForm(): Boolean {
        var isValid = true

        val taskName = binding.taskNameEditText.text.toString().trim()
        if (taskName.isEmpty()) {
            binding.taskNameInputLayout.error = "اسم المهمة مطلوب"
            isValid = false
        } else {
            binding.taskNameInputLayout.error = null
        }

        val expirationDate = binding.taskExpirationDateEditText.text.toString().trim()
        if (expirationDate.isEmpty()) {
            binding.taskExpirationDateInputLayout.error = "تاريخ الانتهاء مطلوب"
            isValid = false
        } else {
            binding.taskExpirationDateInputLayout.error = null
        }

        return isValid
    }

    private fun createTaskFromForm(): Task {
        val taskName = binding.taskNameEditText.text.toString().trim()
        val description = binding.taskDescriptionEditText.text.toString().trim()
        val expirationDateStr = binding.taskExpirationDateEditText.text.toString().trim()
        val estimatedTime = binding.taskEstimatedTimeEditText.text.toString().toDoubleOrNull() ?: 1.0
        val location = binding.taskLocationEditText.text.toString().trim()
        val priority = binding.taskPriorityText.text.toString()
        val category = binding.taskCategoryText.text.toString()

        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val expirationDate = dateFormat.parse(expirationDateStr) ?: Date()

        return Task(
            id = currentTaskId ?: 0,
            name = taskName,
            expirationDate = expirationDate,
            reminderDaysBefore = getReminderDays(),
            isCompleted = false,
            isRecurring = isRecurring,
            recurringPattern = if (isRecurring) recurringPattern else null,
            color = selectedTaskColor,
            progress = taskProgress,
            category = category,
            priority = when (priority) {
                "عالية" -> com.example.kpitrackerapp.models.TaskPriority.HIGH
                "متوسطة" -> com.example.kpitrackerapp.models.TaskPriority.MEDIUM
                "منخفضة" -> com.example.kpitrackerapp.models.TaskPriority.LOW
                else -> com.example.kpitrackerapp.models.TaskPriority.MEDIUM
            },
            energyLevel = when (selectedEnergyLevel) {
                "عالي" -> com.example.kpitrackerapp.models.EnergyLevel.HIGH
                "متوسط" -> com.example.kpitrackerapp.models.EnergyLevel.MEDIUM
                "منخفض" -> com.example.kpitrackerapp.models.EnergyLevel.LOW
                else -> com.example.kpitrackerapp.models.EnergyLevel.MEDIUM
            },
            location = location,
            estimatedHours = estimatedTime
        )
    }

    private fun getReminderDays(): Int? {
        return when (binding.taskReminderAutoCompleteTextView.text.toString()) {
            "لا يوجد تذكير" -> null
            "في نفس اليوم" -> 0
            "يوم واحد قبل" -> 1
            "يومين قبل" -> 2
            "3 أيام قبل" -> 3
            "أسبوع قبل" -> 7
            else -> null
        }
    }

    private fun clearForm() {
        binding.taskNameEditText.text?.clear()
        binding.taskDescriptionEditText.text?.clear()
        binding.taskExpirationDateEditText.text?.clear()
        binding.taskExpirationTimeEditText.text?.clear()
        binding.taskLocationEditText.text?.clear()
        binding.taskEstimatedTimeEditText.setText("1.0")

        // Reset to defaults
        binding.taskPriorityText.text = "🟡 متوسطة - مهمة عادية"
        binding.taskCategoryText.text = "💼 عمل - مهام العمل والوظيفة"
        binding.taskReminderAutoCompleteTextView.setText("🔕 لا يوجد تذكير", false)

        // Reset progress
        binding.sliderProgress.value = 0f
        binding.tvProgressValue.text = "0%"

        // Reset chips
        binding.chipEnergyMedium.isChecked = true
        binding.chipContextHome.isChecked = true
        binding.chipIconWork.isChecked = true

        // Reset switches
        binding.switchRecurring.isChecked = false
        binding.switchNotifications.isChecked = true
        binding.switchBreakDown.isChecked = false

        // Reset recurring properties
        isRecurring = false
        recurringPattern = ""

        // Reset color
        binding.colorPurple.isSelected = true
        selectedTaskColor = "#9C27B0"
    }

    private fun loadTaskData() {
        // TODO: Implement loading existing task data
        currentTaskId?.let { taskId ->
            taskViewModel.getTaskById(taskId).observe(this) { task ->
                task?.let {
                    binding.taskNameEditText.setText(it.name)
                    val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                    binding.taskExpirationDateEditText.setText(dateFormat.format(it.expirationDate))

                    // Set reminder
                    val reminderText = when (it.reminderDaysBefore) {
                        null -> "لا يوجد تذكير"
                        0 -> "في نفس اليوم"
                        1 -> "يوم واحد قبل"
                        2 -> "يومين قبل"
                        3 -> "3 أيام قبل"
                        7 -> "أسبوع قبل"
                        else -> "لا يوجد تذكير"
                    }
                    binding.taskReminderAutoCompleteTextView.setText(reminderText, false)
                }
            }
        }
    }

    private fun showDatePickerDialog() {
        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH)
        val day = calendar.get(Calendar.DAY_OF_MONTH)

        DatePickerDialog(this, { _, selectedYear, selectedMonth, selectedDay ->
            val selectedDate = Calendar.getInstance()
            selectedDate.set(selectedYear, selectedMonth, selectedDay)
            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            binding.taskExpirationDateEditText.setText(dateFormat.format(selectedDate.time))
        }, year, month, day).show()
    }

    private fun showTimePickerDialog() {
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        val minute = calendar.get(Calendar.MINUTE)

        TimePickerDialog(this, { _, selectedHour, selectedMinute ->
            val selectedTime = String.format(Locale.getDefault(), "%02d:%02d", selectedHour, selectedMinute)
            binding.taskExpirationTimeEditText.setText(selectedTime)
        }, hour, minute, true).show()
    }

    private fun updateTaskColorByPriority(priority: String) {
        val colorResId = when {
            priority.contains("عالية") || priority.contains("عاجل") -> android.R.color.holo_red_light
            priority.contains("متوسطة") -> android.R.color.holo_orange_light
            priority.contains("منخفضة") -> android.R.color.holo_green_light
            else -> android.R.color.holo_blue_light
        }

        // تطبيق اللون على العناصر
        val color = ContextCompat.getColor(this, colorResId)
        binding.taskNameInputLayout.boxStrokeColor = color
        binding.taskDescriptionInputLayout.boxStrokeColor = color
    }

    private fun updateIconByCategory(category: String) {
        val icon = when {
            category.contains("عمل") -> "💼"
            category.contains("دراسة") -> "📚"
            category.contains("صحة") -> "🍎"
            category.contains("شخصي") -> "👤"
            category.contains("مالية") -> "💰"
            category.contains("عائلة") -> "👨‍👩‍👧‍👦"
            category.contains("تسوق") -> "🛒"
            category.contains("سفر") -> "✈️"
            else -> "📋"
        }

        // تحديث النص ليشمل الأيقونة
        val currentTitle = binding.taskNameEditText.text.toString()
        val cleanTitle = currentTitle.replace(Regex("^[\\p{So}\\p{Cn}]\\s*"), "") // إزالة الأيقونات الموجودة
        if (cleanTitle.isNotEmpty()) {
            binding.taskNameEditText.setText("$icon $cleanTitle")
        }

        // تحديث اختيار الأيقونة في الـ chips
        when (icon) {
            "💼" -> binding.chipIconWork.isChecked = true
            "📚" -> binding.chipIconStudy.isChecked = true
            "🏃‍♂️" -> binding.chipIconSport.isChecked = true
            "🍎" -> binding.chipIconHealth.isChecked = true
            "💡" -> binding.chipIconIdea.isChecked = true
        }
    }

    private fun updateEstimatedTimeByEnergy(energyLevel: String) {
        val estimatedHours = when {
            energyLevel.contains("عالي") -> "2.0"
            energyLevel.contains("متوسط") -> "1.5"
            energyLevel.contains("منخفض") -> "1.0"
            else -> "1.0"
        }

        binding.taskEstimatedTimeEditText.setText(estimatedHours)
    }

    private fun updateLocationByContext(context: String) {
        val location = when {
            context.contains("المنزل") -> "المنزل"
            context.contains("المكتب") -> "المكتب"
            context.contains("أونلاين") -> "عبر الإنترنت"
            context.contains("الطريق") -> "أثناء التنقل"
            else -> ""
        }

        if (location.isNotEmpty()) {
            binding.taskLocationEditText.setText(location)
        }
    }

    private fun applySelectedColor(colorHex: String) {
        try {
            val selectedColor = Color.parseColor(colorHex)
            binding.taskNameInputLayout.boxStrokeColor = selectedColor
            binding.taskDescriptionInputLayout.boxStrokeColor = selectedColor
            binding.taskExpirationDateInputLayout.boxStrokeColor = selectedColor
            binding.taskEstimatedTimeInputLayout.boxStrokeColor = selectedColor
            binding.taskLocationInputLayout.boxStrokeColor = selectedColor
        } catch (e: Exception) {
            // استخدام لون افتراضي في حالة الخطأ
            val defaultColor = ContextCompat.getColor(this, android.R.color.holo_blue_light)
            binding.taskNameInputLayout.boxStrokeColor = defaultColor
        }
    }

    private fun updateTaskIcon(icon: String) {
        // تحديث أيقونة المهمة في العنوان
        val currentTitle = binding.taskNameEditText.text.toString()
        val cleanTitle = currentTitle.replace(Regex("^[\\p{So}\\p{Cn}]\\s*"), "") // إزالة الأيقونات الموجودة
        if (cleanTitle.isNotEmpty()) {
            binding.taskNameEditText.setText("$icon $cleanTitle")
        } else {
            binding.taskNameEditText.setText("$icon مهمة جديدة")
        }
    }

    private fun showRecurringOptionsDialog() {
        val recurringOptions = arrayOf(
            "📅 يومياً",
            "📅 كل يومين",
            "📅 كل 3 أيام",
            "📅 أسبوعياً",
            "📅 كل أسبوعين",
            "📅 شهرياً",
            "📅 كل 3 أشهر",
            "📅 كل 6 أشهر",
            "📅 سنوياً",
            "📅 أيام العمل فقط (الإثنين - الجمعة)",
            "📅 نهاية الأسبوع فقط (السبت - الأحد)",
            "📅 تكرار مخصص"
        )

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🔄 اختيار نمط التكرار")
            .setItems(recurringOptions) { _, which ->
                when (which) {
                    0 -> setRecurringPattern("DAILY", "يومياً")
                    1 -> setRecurringPattern("EVERY_2_DAYS", "كل يومين")
                    2 -> setRecurringPattern("EVERY_3_DAYS", "كل 3 أيام")
                    3 -> setRecurringPattern("WEEKLY", "أسبوعياً")
                    4 -> setRecurringPattern("EVERY_2_WEEKS", "كل أسبوعين")
                    5 -> setRecurringPattern("MONTHLY", "شهرياً")
                    6 -> setRecurringPattern("EVERY_3_MONTHS", "كل 3 أشهر")
                    7 -> setRecurringPattern("EVERY_6_MONTHS", "كل 6 أشهر")
                    8 -> setRecurringPattern("YEARLY", "سنوياً")
                    9 -> setRecurringPattern("WEEKDAYS", "أيام العمل فقط")
                    10 -> setRecurringPattern("WEEKENDS", "نهاية الأسبوع فقط")
                    11 -> showCustomRecurringDialog()
                }
            }
            .setNegativeButton("إلغاء") { _, _ ->
                binding.switchRecurring.isChecked = false
                isRecurring = false
            }
            .show()
    }

    private fun setRecurringPattern(pattern: String, displayName: String) {
        recurringPattern = pattern
        Toast.makeText(this, "🔄 تم تعيين التكرار: $displayName", Toast.LENGTH_LONG).show()

        // إظهار معلومات إضافية حول التكرار
        showRecurringInfo(displayName)
    }

    private fun showRecurringInfo(displayName: String) {
        val nextOccurrences = generateNextOccurrences(recurringPattern, 3)
        val message = "سيتم تكرار هذه المهمة $displayName\n\nالتواريخ القادمة:\n${nextOccurrences.joinToString("\n")}"

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("📋 معلومات التكرار")
            .setMessage(message)
            .setPositiveButton("موافق", null)
            .setNeutralButton("تغيير النمط") { _, _ ->
                showRecurringOptionsDialog()
            }
            .show()
    }

    private fun showCustomRecurringDialog() {
        val customOptions = arrayOf(
            "📅 كل 4 أيام",
            "📅 كل 5 أيام",
            "📅 كل أسبوع (يوم محدد)",
            "📅 كل شهر (تاريخ محدد)",
            "📅 أيام محددة من الأسبوع",
            "📅 كل عدة أشهر"
        )

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🔧 تكرار مخصص")
            .setItems(customOptions) { _, which ->
                when (which) {
                    0 -> setRecurringPattern("EVERY_4_DAYS", "كل 4 أيام")
                    1 -> setRecurringPattern("EVERY_5_DAYS", "كل 5 أيام")
                    2 -> showWeeklyCustomDialog()
                    3 -> showMonthlyCustomDialog()
                    4 -> showWeekdaysCustomDialog()
                    5 -> showMonthsCustomDialog()
                }
            }
            .setNegativeButton("رجوع") { _, _ ->
                showRecurringOptionsDialog()
            }
            .show()
    }

    private fun showWeeklyCustomDialog() {
        val weekdays = arrayOf("الأحد", "الإثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت")

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("📅 اختيار يوم الأسبوع")
            .setItems(weekdays) { _, which ->
                val selectedDay = weekdays[which]
                setRecurringPattern("WEEKLY_$which", "كل أسبوع يوم $selectedDay")
            }
            .setNegativeButton("رجوع") { _, _ ->
                showCustomRecurringDialog()
            }
            .show()
    }

    private fun showMonthlyCustomDialog() {
        val monthlyOptions = arrayOf(
            "📅 نفس التاريخ كل شهر",
            "📅 أول يوم في الشهر",
            "📅 آخر يوم في الشهر",
            "📅 منتصف الشهر (15)",
            "📅 تاريخ مخصص"
        )

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("📅 التكرار الشهري")
            .setItems(monthlyOptions) { _, which ->
                when (which) {
                    0 -> setRecurringPattern("MONTHLY_SAME_DATE", "نفس التاريخ كل شهر")
                    1 -> setRecurringPattern("MONTHLY_FIRST_DAY", "أول يوم في الشهر")
                    2 -> setRecurringPattern("MONTHLY_LAST_DAY", "آخر يوم في الشهر")
                    3 -> setRecurringPattern("MONTHLY_MID", "منتصف الشهر")
                    4 -> showCustomDateDialog()
                }
            }
            .setNegativeButton("رجوع") { _, _ ->
                showCustomRecurringDialog()
            }
            .show()
    }

    private fun showWeekdaysCustomDialog() {
        val weekdays = arrayOf("الأحد", "الإثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت")
        val selectedDays = BooleanArray(7)

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("📅 اختيار أيام الأسبوع")
            .setMultiChoiceItems(weekdays, selectedDays) { _, which, isChecked ->
                selectedDays[which] = isChecked
            }
            .setPositiveButton("موافق") { _, _ ->
                val selectedDaysList = mutableListOf<String>()
                selectedDays.forEachIndexed { index, isSelected ->
                    if (isSelected) selectedDaysList.add(weekdays[index])
                }
                if (selectedDaysList.isNotEmpty()) {
                    setRecurringPattern("CUSTOM_WEEKDAYS_${selectedDays.joinToString(",")}",
                        "أيام: ${selectedDaysList.joinToString(", ")}")
                } else {
                    Toast.makeText(this, "يرجى اختيار يوم واحد على الأقل", Toast.LENGTH_SHORT).show()
                    showWeekdaysCustomDialog()
                }
            }
            .setNegativeButton("رجوع") { _, _ ->
                showCustomRecurringDialog()
            }
            .show()
    }

    private fun showMonthsCustomDialog() {
        val monthsOptions = arrayOf("كل شهرين", "كل 4 أشهر", "كل 5 أشهر", "كل 8 أشهر", "كل 9 أشهر", "كل 10 أشهر", "كل 11 شهر")

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("📅 التكرار بالأشهر")
            .setItems(monthsOptions) { _, which ->
                val months = arrayOf(2, 4, 5, 8, 9, 10, 11)[which]
                setRecurringPattern("EVERY_${months}_MONTHS", monthsOptions[which])
            }
            .setNegativeButton("رجوع") { _, _ ->
                showCustomRecurringDialog()
            }
            .show()
    }

    private fun showCustomDateDialog() {
        // إنشاء dialog لاختيار تاريخ مخصص
        val input = android.widget.EditText(this)
        input.hint = "أدخل رقم اليوم (1-31)"
        input.inputType = android.text.InputType.TYPE_CLASS_NUMBER

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("📅 تاريخ مخصص")
            .setMessage("أدخل رقم اليوم من الشهر:")
            .setView(input)
            .setPositiveButton("موافق") { _, _ ->
                val dayOfMonth = input.text.toString().toIntOrNull()
                if (dayOfMonth != null && dayOfMonth in 1..31) {
                    setRecurringPattern("MONTHLY_DAY_$dayOfMonth", "يوم $dayOfMonth من كل شهر")
                } else {
                    Toast.makeText(this, "يرجى إدخال رقم صحيح بين 1 و 31", Toast.LENGTH_SHORT).show()
                    showCustomDateDialog()
                }
            }
            .setNegativeButton("رجوع") { _, _ ->
                showMonthlyCustomDialog()
            }
            .show()
    }

    private fun generateNextOccurrences(pattern: String, count: Int): List<String> {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val calendar = Calendar.getInstance()
        val occurrences = mutableListOf<String>()

        repeat(count) {
            when {
                pattern == "DAILY" -> calendar.add(Calendar.DAY_OF_YEAR, 1)
                pattern == "EVERY_2_DAYS" -> calendar.add(Calendar.DAY_OF_YEAR, 2)
                pattern == "EVERY_3_DAYS" -> calendar.add(Calendar.DAY_OF_YEAR, 3)
                pattern == "EVERY_4_DAYS" -> calendar.add(Calendar.DAY_OF_YEAR, 4)
                pattern == "EVERY_5_DAYS" -> calendar.add(Calendar.DAY_OF_YEAR, 5)
                pattern == "WEEKLY" -> calendar.add(Calendar.WEEK_OF_YEAR, 1)
                pattern == "EVERY_2_WEEKS" -> calendar.add(Calendar.WEEK_OF_YEAR, 2)
                pattern == "MONTHLY" -> calendar.add(Calendar.MONTH, 1)
                pattern == "EVERY_3_MONTHS" -> calendar.add(Calendar.MONTH, 3)
                pattern == "EVERY_6_MONTHS" -> calendar.add(Calendar.MONTH, 6)
                pattern == "YEARLY" -> calendar.add(Calendar.YEAR, 1)
                pattern == "WEEKDAYS" -> {
                    do {
                        calendar.add(Calendar.DAY_OF_YEAR, 1)
                    } while (calendar.get(Calendar.DAY_OF_WEEK) in listOf(Calendar.SATURDAY, Calendar.SUNDAY))
                }
                pattern == "WEEKENDS" -> {
                    do {
                        calendar.add(Calendar.DAY_OF_YEAR, 1)
                    } while (calendar.get(Calendar.DAY_OF_WEEK) !in listOf(Calendar.SATURDAY, Calendar.SUNDAY))
                }
                else -> calendar.add(Calendar.DAY_OF_YEAR, 1) // افتراضي
            }
            occurrences.add("📅 ${dateFormat.format(calendar.time)}")
        }

        return occurrences
    }

    private fun showPriorityDialog(options: Array<String>, currentIndex: Int, onSelected: (Int) -> Unit) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("⭐ اختيار الأولوية")
            .setSingleChoiceItems(options, currentIndex) { dialog, which ->
                onSelected(which)
                dialog.dismiss()
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun showCategoryDialog(options: Array<String>, currentIndex: Int, onSelected: (Int) -> Unit) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("📂 اختيار الفئة")
            .setSingleChoiceItems(options, currentIndex) { dialog, which ->
                onSelected(which)
                dialog.dismiss()
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    override fun onSupportNavigateUp(): Boolean {
        finish()
        return true
    }

    // دوال التحسينات الجديدة
    private fun showEnergyLevelTips(energyLevel: String) {
        val tips = when (energyLevel) {
            "عالي" -> """
                ⚡ **مستوى طاقة عالي**

                💡 **نصائح:**
                • هذا الوقت المثالي للمهام الصعبة
                • يمكنك إنجاز مهام متعددة
                • ركز على المهام الإبداعية
                • استغل هذه الطاقة في التعلم

                ⏰ **أفضل الأوقات:**
                الصباح الباكر (6-10 ص)
            """.trimIndent()

            "متوسط" -> """
                🔋 **مستوى طاقة متوسط**

                💡 **نصائح:**
                • مناسب للمهام الروتينية
                • يمكن التركيز لفترات متوسطة
                • خذ استراحات منتظمة
                • اشرب الماء والقهوة

                ⏰ **أفضل الأوقات:**
                بعد الظهر (2-5 م)
            """.trimIndent()

            else -> """
                🪫 **مستوى طاقة منخفض**

                💡 **نصائح:**
                • ركز على المهام البسيطة
                • خذ استراحات أكثر
                • تجنب القرارات المهمة
                • فكر في تأجيل المهام الصعبة

                ⏰ **أفضل الأوقات:**
                المساء (6-9 م)
            """.trimIndent()
        }

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("💡 نصائح مستوى الطاقة")
            .setMessage(tips)
            .setPositiveButton("شكراً 👍", null)
            .show()
    }

    private fun showContextTips(context: String) {
        val tips = when (context) {
            "في المنزل" -> """
                🏠 **العمل من المنزل**

                💡 **نصائح:**
                • اختر مكان هادئ ومريح
                • تجنب المشتتات (التلفزيون، الأطفال)
                • ارتدي ملابس مناسبة للعمل
                • حدد ساعات عمل واضحة

                📱 **أدوات مفيدة:**
                • تطبيقات التركيز
                • سماعات إلغاء الضوضاء
                • مؤقت البومودورو
            """.trimIndent()

            "في المكتب" -> """
                🏢 **العمل من المكتب**

                💡 **نصائح:**
                • استغل بيئة العمل المهنية
                • تعاون مع الزملاء
                • استخدم الموارد المتاحة
                • حافظ على التنظيم

                📱 **أدوات مفيدة:**
                • تقويم المكتب
                • أدوات التعاون
                • غرف الاجتماعات
            """.trimIndent()

            "أونلاين" -> """
                💻 **العمل الرقمي**

                💡 **نصائح:**
                • تأكد من سرعة الإنترنت
                • جهز النسخ الاحتياطية
                • استخدم أدوات التعاون الرقمية
                • حافظ على أمان البيانات

                📱 **أدوات مفيدة:**
                • Zoom, Teams للاجتماعات
                • Google Drive للملفات
                • Slack للتواصل
            """.trimIndent()

            else -> """
                🚗 **العمل أثناء التنقل**

                💡 **نصائح:**
                • استخدم التطبيقات الصوتية
                • اعتمد على المكالمات
                • جهز قوائم المهام المسبقة
                • استغل أوقات الانتظار

                📱 **أدوات مفيدة:**
                • تطبيقات التسجيل الصوتي
                • كتب صوتية
                • بودكاست تعليمية
            """.trimIndent()
        }

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🎯 نصائح السياق")
            .setMessage(tips)
            .setPositiveButton("مفيد 👌", null)
            .show()
    }

    private fun showColorSelectedFeedback(colorName: String) {
        val colorMeaning = when (colorName) {
            "أحمر" -> "🔴 يرمز للعجلة والأهمية القصوى"
            "أزرق" -> "🔵 يرمز للهدوء والتركيز"
            "أخضر" -> "🟢 يرمز للنمو والتقدم"
            "برتقالي" -> "🟠 يرمز للطاقة والحماس"
            "بنفسجي" -> "🟣 يرمز للإبداع والتميز"
            else -> "🎨 لون جميل!"
        }

        Toast.makeText(this, colorMeaning, Toast.LENGTH_LONG).show()
    }

    private fun showNotificationOptionsDialog() {
        val notificationOptions = arrayOf(
            "🔔 تنبيهات فورية",
            "📱 تنبيهات صوتية",
            "📧 تنبيهات بالإيميل",
            "📲 تنبيهات WhatsApp",
            "⏰ تنبيهات مجدولة",
            "🎯 تنبيهات ذكية حسب الموقع",
            "📊 تقارير التقدم اليومية",
            "🏆 تنبيهات الإنجازات"
        )

        val checkedItems = booleanArrayOf(true, true, false, false, true, false, true, true)

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🔔 خيارات التنبيهات")
            .setMultiChoiceItems(notificationOptions, checkedItems) { _, which, isChecked ->
                checkedItems[which] = isChecked
            }
            .setPositiveButton("حفظ الإعدادات") { _, _ ->
                val selectedOptions = mutableListOf<String>()
                checkedItems.forEachIndexed { index, isChecked ->
                    if (isChecked) {
                        selectedOptions.add(notificationOptions[index])
                    }
                }
                Toast.makeText(this, "✅ تم حفظ ${selectedOptions.size} نوع من التنبيهات", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("إلغاء", null)
            .setNeutralButton("تفعيل الكل") { _, _ ->
                Toast.makeText(this, "🔔 تم تفعيل جميع أنواع التنبيهات", Toast.LENGTH_SHORT).show()
            }
            .show()
    }

    private fun showBreakDownDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("📝 تقسيم المهمة")
            .setMessage("هل تريد تقسيم هذه المهمة إلى مهام فرعية أصغر؟\n\nهذا سيساعدك على:\n• تتبع التقدم بشكل أفضل\n• تقليل الشعور بالإرهاق\n• زيادة الدافعية\n• تحسين التخطيط")
            .setPositiveButton("نعم، قسم المهمة 📋") { _, _ ->
                showSubtaskCreationDialog()
            }
            .setNegativeButton("لا، أبقها كما هي") { _, _ ->
                binding.switchBreakDown.isChecked = false
                autoBreakDown = false
            }
            .setNeutralButton("نصائح التقسيم 💡") { _, _ ->
                showBreakDownTips()
            }
            .show()
    }

    private fun showSubtaskCreationDialog() {
        val taskName = binding.taskNameEditText.text.toString()
        val suggestedSubtasks = generateSubtaskSuggestions(taskName)

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("📋 المهام الفرعية المقترحة")
            .setMessage("بناءً على اسم المهمة، إليك بعض المهام الفرعية المقترحة:\n\n${suggestedSubtasks.joinToString("\n")}")
            .setPositiveButton("إضافة للوصف") { _, _ ->
                val currentDesc = binding.taskDescriptionEditText.text.toString()
                val subtasksText = "\n\n📋 **المهام الفرعية:**\n${suggestedSubtasks.joinToString("\n")}"
                binding.taskDescriptionEditText.setText(
                    if (currentDesc.isEmpty()) subtasksText.trim()
                    else "$currentDesc$subtasksText"
                )
                Toast.makeText(this, "✅ تم إضافة المهام الفرعية للوصف", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("إنشاء يدوي") { _, _ ->
                showManualSubtaskDialog()
            }
            .setNeutralButton("إلغاء", null)
            .show()
    }

    private fun generateSubtaskSuggestions(taskName: String): List<String> {
        return when {
            taskName.contains("اجتماع", ignoreCase = true) -> listOf(
                "• تحضير جدول الأعمال",
                "• إرسال الدعوات",
                "• تجهيز المواد",
                "• عقد الاجتماع",
                "• كتابة المحضر"
            )
            taskName.contains("تقرير", ignoreCase = true) -> listOf(
                "• جمع البيانات",
                "• تحليل المعلومات",
                "• كتابة المسودة",
                "• المراجعة والتدقيق",
                "• التنسيق والإرسال"
            )
            taskName.contains("مشروع", ignoreCase = true) -> listOf(
                "• تحديد المتطلبات",
                "• وضع الخطة",
                "• تنفيذ المرحلة الأولى",
                "• المراجعة والتقييم",
                "• التسليم النهائي"
            )
            else -> listOf(
                "• التخطيط والإعداد",
                "• البدء في التنفيذ",
                "• المتابعة والتطوير",
                "• المراجعة النهائية",
                "• الإنجاز والتسليم"
            )
        }
    }

    private fun showManualSubtaskDialog() {
        val input = android.widget.EditText(this)
        input.hint = "أدخل المهام الفرعية (كل مهمة في سطر منفصل)"
        input.minLines = 3
        input.maxLines = 8

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("✍️ إنشاء مهام فرعية يدوياً")
            .setView(input)
            .setPositiveButton("إضافة") { _, _ ->
                val subtasks = input.text.toString().split("\n").filter { it.isNotBlank() }
                if (subtasks.isNotEmpty()) {
                    val formattedSubtasks = subtasks.joinToString("\n") { "• $it" }
                    val currentDesc = binding.taskDescriptionEditText.text.toString()
                    val subtasksText = "\n\n📋 **المهام الفرعية:**\n$formattedSubtasks"
                    binding.taskDescriptionEditText.setText(
                        if (currentDesc.isEmpty()) subtasksText.trim()
                        else "$currentDesc$subtasksText"
                    )
                    Toast.makeText(this, "✅ تم إضافة ${subtasks.size} مهمة فرعية", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun showBreakDownTips() {
        val tips = """
            💡 **نصائح تقسيم المهام:**

            🎯 **القاعدة الذهبية:**
            قسم المهمة إلى أجزاء يمكن إنجازها في 15-30 دقيقة

            📋 **خطوات التقسيم:**
            1. حدد الهدف النهائي
            2. اكتب الخطوات الرئيسية
            3. قسم كل خطوة لمهام أصغر
            4. رتب المهام حسب الأولوية
            5. حدد وقت لكل مهمة

            ✅ **فوائد التقسيم:**
            • تقليل التوتر والقلق
            • زيادة الشعور بالإنجاز
            • تحسين التركيز
            • سهولة التتبع والقياس

            🚫 **تجنب:**
            • التقسيم المفرط (أكثر من 10 مهام)
            • المهام الغامضة
            • عدم تحديد الأولويات
        """.trimIndent()

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("💡 دليل تقسيم المهام")
            .setMessage(tips)
            .setPositiveButton("مفهوم! 👍", null)
            .show()
    }

    // دوال التسجيل الصوتي المحسنة
    private fun showRecordingProgressDialog(title: String, onComplete: () -> Unit) {
        val progressDialog = androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(title)
            .setMessage("🎙️ جاري التسجيل...\n\n⏱️ الوقت المتبقي: 3 ثواني")
            .setCancelable(false)
            .create()

        progressDialog.show()

        // تأثير بصري للميكروفون
        animateVoiceButton()

        // محاكاة التسجيل مع عداد تنازلي
        var countdown = 3
        val handler = android.os.Handler(android.os.Looper.getMainLooper())
        val countdownRunnable = object : Runnable {
            override fun run() {
                if (countdown > 0) {
                    progressDialog.setMessage("🎙️ جاري التسجيل...\n\n⏱️ الوقت المتبقي: $countdown ثواني")
                    countdown--
                    handler.postDelayed(this, 1000)
                } else {
                    progressDialog.setMessage("🔄 جاري المعالجة...")
                    handler.postDelayed({
                        progressDialog.dismiss()
                        onComplete()
                    }, 1000)
                }
            }
        }
        handler.post(countdownRunnable)
    }

    private fun animateVoiceButton() {
        binding.btnVoiceNote.animate()
            .scaleX(1.3f)
            .scaleY(1.3f)
            .setDuration(300)
            .withEndAction {
                binding.btnVoiceNote.animate()
                    .scaleX(1.0f)
                    .scaleY(1.0f)
                    .setDuration(300)
                    .start()
            }
            .start()
    }

    private fun generateSpeechToTextResult(): String {
        val taskName = binding.taskNameEditText.text.toString().trim()
        val category = binding.taskCategoryText.text.toString()

        return when {
            taskName.contains("اجتماع", ignoreCase = true) -> """
                تم تحويل الكلام إلى نص:

                "أريد تنظيم اجتماع مهم مع الفريق لمناقشة المشروع الجديد. يجب أن نحضر جدول أعمال واضح ونرسل الدعوات مسبقاً. الهدف هو وضع خطة عمل محددة للأسابيع القادمة."
            """.trimIndent()

            taskName.contains("تقرير", ignoreCase = true) -> """
                تم تحويل الكلام إلى نص:

                "نحتاج لإعداد تقرير شامل عن الأداء الشهري. يجب جمع البيانات من جميع الأقسام وتحليلها بعناية. التقرير يجب أن يتضمن الإنجازات والتحديات والتوصيات للشهر القادم."
            """.trimIndent()

            category.contains("دراسة") -> """
                تم تحويل الكلام إلى نص:

                "خطة الدراسة تتطلب تنظيم الوقت بعناية. سأخصص ساعتين يومياً للمراجعة وساعة للتمارين العملية. المهم هو الالتزام بالجدول الزمني وعدم التأجيل."
            """.trimIndent()

            else -> """
                تم تحويل الكلام إلى نص:

                "هذه مهمة مهمة تتطلب التخطيط الجيد والتنفيذ المنظم. سأقوم بتقسيمها إلى خطوات صغيرة قابلة للتنفيذ وأحدد موعد نهائي واقعي لكل خطوة."
            """.trimIndent()
        }
    }

    private fun generateDictatedDescription(): String {
        val taskName = binding.taskNameEditText.text.toString().trim()
        val priority = binding.taskPriorityText.text.toString()

        return when {
            taskName.contains("اجتماع", ignoreCase = true) -> """
                📋 وصف مفصل للاجتماع:

                🎯 الهدف الرئيسي:
                • مناقشة التطورات الحالية
                • وضع خطة العمل للمرحلة القادمة
                • حل المشاكل المعلقة

                👥 المشاركون المطلوبون:
                • مدير المشروع
                • فريق التطوير
                • ممثل عن العملاء

                📝 جدول الأعمال:
                1. مراجعة الإنجازات السابقة (15 دقيقة)
                2. مناقشة التحديات الحالية (20 دقيقة)
                3. وضع الخطة المستقبلية (20 دقيقة)
                4. الأسئلة والمناقشة (5 دقائق)

                📋 المطلوب تحضيره:
                • تقرير الحالة الحالية
                • قائمة المشاكل المعلقة
                • مقترحات الحلول
            """.trimIndent()

            taskName.contains("تقرير", ignoreCase = true) -> """
                📊 وصف مفصل للتقرير:

                🎯 نوع التقرير:
                • تقرير أداء شهري شامل
                • يغطي جميع الأقسام والمشاريع

                📈 البيانات المطلوبة:
                • إحصائيات الأداء
                • مؤشرات الجودة
                • التكاليف والميزانية
                • رضا العملاء

                📋 هيكل التقرير:
                1. الملخص التنفيذي
                2. الإنجازات الرئيسية
                3. التحديات والمشاكل
                4. التوصيات والخطط المستقبلية
                5. الملاحق والبيانات التفصيلية

                ⏰ الجدول الزمني:
                • جمع البيانات: 3 أيام
                • التحليل والكتابة: 2 يوم
                • المراجعة والتدقيق: 1 يوم
            """.trimIndent()

            priority.contains("عالية") || priority.contains("عاجل") -> """
                ⚡ وصف مهمة عالية الأولوية:

                🚨 سبب الأولوية العالية:
                • موعد نهائي قريب
                • تأثير كبير على المشروع
                • مطلوبة لاستكمال مهام أخرى

                🎯 الأهداف المحددة:
                • إنجاز المهمة في الوقت المحدد
                • ضمان الجودة العالية
                • تجنب التأخير في المهام التابعة

                📋 خطة العمل:
                1. البدء فوراً دون تأجيل
                2. تخصيص الوقت الكافي
                3. طلب المساعدة عند الحاجة
                4. المتابعة المستمرة للتقدم

                ⚠️ المخاطر المحتملة:
                • ضيق الوقت
                • تعارض مع مهام أخرى
                • نقص الموارد المطلوبة
            """.trimIndent()

            else -> """
                📝 وصف تفصيلي للمهمة:

                🎯 الهدف من المهمة:
                • تحقيق النتيجة المطلوبة بكفاءة
                • تطوير المهارات اللازمة
                • المساهمة في الأهداف العامة

                📋 الخطوات الأساسية:
                1. التخطيط والإعداد الأولي
                2. جمع المعلومات والموارد
                3. التنفيذ المرحلي
                4. المراجعة والتقييم
                5. التسليم النهائي

                💡 نصائح للنجاح:
                • تحديد أولويات واضحة
                • تقسيم المهمة لأجزاء صغيرة
                • المتابعة المنتظمة للتقدم
                • طلب التغذية الراجعة

                📊 معايير النجاح:
                • إنجاز المهمة في الوقت المحدد
                • تحقيق الجودة المطلوبة
                • الالتزام بالميزانية المحددة
            """.trimIndent()
        }
    }

    private fun generateTaskGoals(): String {
        val taskName = binding.taskNameEditText.text.toString().trim()
        val category = binding.taskCategoryText.text.toString()

        return when {
            taskName.contains("اجتماع", ignoreCase = true) -> """
                🎯 أهداف الاجتماع:

                🎯 الهدف الرئيسي:
                • تحقيق التواصل الفعال بين أعضاء الفريق

                🎯 الأهداف الفرعية:
                • مراجعة التقدم المحرز في المشروع
                • تحديد المشاكل والتحديات الحالية
                • وضع خطة عمل واضحة للمرحلة القادمة
                • توزيع المهام والمسؤوليات
                • تحديد المواعيد النهائية الجديدة

                🎯 النتائج المتوقعة:
                • قرارات واضحة ومحددة
                • خطة عمل مفصلة
                • التزام جماعي بالأهداف
                • تحسين التعاون والتنسيق
            """.trimIndent()

            category.contains("دراسة") -> """
                🎯 أهداف الدراسة:

                🎯 الهدف الأكاديمي:
                • إتقان المادة العلمية المطلوبة

                🎯 الأهداف التعليمية:
                • فهم المفاهيم الأساسية بعمق
                • تطبيق المعرفة النظرية عملياً
                • تطوير مهارات التفكير النقدي
                • تحسين قدرات حل المشكلات
                • الاستعداد الجيد للامتحانات

                🎯 الأهداف الشخصية:
                • بناء الثقة بالنفس
                • تطوير عادات دراسية جيدة
                • تحسين إدارة الوقت
                • زيادة التركيز والانضباط
            """.trimIndent()

            category.contains("عمل") -> """
                🎯 أهداف العمل:

                🎯 الهدف المهني:
                • تحقيق التميز في الأداء الوظيفي

                🎯 الأهداف التشغيلية:
                • إنجاز المهام في الوقت المحدد
                • تحقيق معايير الجودة المطلوبة
                • تحسين الكفاءة والإنتاجية
                • تطوير المهارات المهنية
                • بناء علاقات عمل إيجابية

                🎯 الأهداف الاستراتيجية:
                • المساهمة في نجاح المؤسسة
                • تطوير الخبرات والمعرفة
                • بناء سمعة مهنية قوية
                • تحقيق النمو الوظيفي
            """.trimIndent()

            else -> """
                🎯 أهداف المهمة:

                🎯 الهدف الرئيسي:
                • تحقيق النتيجة المطلوبة بأعلى جودة ممكنة

                🎯 الأهداف الفرعية:
                • التخطيط الدقيق والمنظم
                • الاستخدام الأمثل للموارد المتاحة
                • تطوير المهارات والقدرات الشخصية
                • تحقيق التوازن بين الجودة والوقت
                • التعلم من التجربة للمستقبل

                🎯 الأهداف طويلة المدى:
                • بناء الخبرة والمعرفة
                • تطوير الكفاءات الشخصية
                • تحسين الأداء العام
                • تحقيق الرضا والإنجاز الشخصي
            """.trimIndent()
        }
    }

    private fun generateWorkSteps(): String {
        val taskName = binding.taskNameEditText.text.toString().trim()
        val category = binding.taskCategoryText.text.toString()

        return when {
            taskName.contains("اجتماع", ignoreCase = true) -> """
                📋 خطوات تنظيم الاجتماع:

                📅 المرحلة الأولى - التحضير (يوم واحد قبل):
                1. تحديد موضوع وأهداف الاجتماع
                2. إعداد جدول الأعمال التفصيلي
                3. تحديد قائمة المدعوين
                4. حجز قاعة الاجتماع أو إعداد الرابط الإلكتروني
                5. إرسال الدعوات مع جدول الأعمال

                📋 المرحلة الثانية - التنفيذ (يوم الاجتماع):
                6. تجهيز المواد والعروض التقديمية
                7. التأكد من جاهزية التقنيات المطلوبة
                8. بدء الاجتماع في الوقت المحدد
                9. إدارة النقاش وفقاً لجدول الأعمال
                10. تسجيل القرارات والنقاط المهمة

                📝 المرحلة الثالثة - المتابعة (بعد الاجتماع):
                11. كتابة محضر الاجتماع
                12. توزيع المحضر على المشاركين
                13. متابعة تنفيذ القرارات المتخذة
                14. تحديد موعد الاجتماع التالي إذا لزم الأمر
            """.trimIndent()

            taskName.contains("تقرير", ignoreCase = true) -> """
                📋 خطوات إعداد التقرير:

                📊 المرحلة الأولى - جمع البيانات:
                1. تحديد نوع ونطاق التقرير المطلوب
                2. تحديد مصادر البيانات المطلوبة
                3. جمع البيانات من الأقسام المختلفة
                4. التحقق من دقة وصحة البيانات
                5. تنظيم البيانات في جداول ومخططات

                📈 المرحلة الثانية - التحليل والكتابة:
                6. تحليل البيانات واستخراج النتائج
                7. تحديد الاتجاهات والأنماط المهمة
                8. كتابة الملخص التنفيذي
                9. كتابة الأقسام الرئيسية للتقرير
                10. إضافة الرسوم البيانية والجداول

                ✅ المرحلة الثالثة - المراجعة والإنهاء:
                11. مراجعة المحتوى والتأكد من الدقة
                12. تدقيق اللغة والإملاء
                13. تنسيق التقرير وإضافة الفهارس
                14. الحصول على الموافقات النهائية
                15. توزيع التقرير على المعنيين
            """.trimIndent()

            category.contains("دراسة") -> """
                📋 خطوات الدراسة الفعالة:

                📚 المرحلة الأولى - التحضير:
                1. تحديد المواد والموضوعات المطلوب دراستها
                2. وضع جدول زمني للدراسة
                3. تجهيز مكان الدراسة المناسب
                4. جمع الكتب والمراجع المطلوبة
                5. إعداد الأدوات اللازمة (أقلام، دفاتر، إلخ)

                📖 المرحلة الثانية - الدراسة النشطة:
                6. قراءة المادة قراءة سريعة أولاً
                7. القراءة التفصيلية مع أخذ الملاحظات
                8. تلخيص النقاط المهمة
                9. حل التمارين والأسئلة العملية
                10. مراجعة ما تم دراسته

                🧠 المرحلة الثالثة - التثبيت والمراجعة:
                11. مراجعة الملاحظات والملخصات
                12. حل أسئلة إضافية للتأكد من الفهم
                13. مناقشة المادة مع الزملاء أو المدرس
                14. المراجعة النهائية قبل الامتحان
            """.trimIndent()

            else -> """
                📋 خطوات تنفيذ المهمة:

                🎯 المرحلة الأولى - التخطيط والإعداد:
                1. تحديد الهدف النهائي بوضوح
                2. تحليل المتطلبات والموارد المطلوبة
                3. تقسيم المهمة إلى مهام فرعية صغيرة
                4. وضع جدول زمني واقعي للتنفيذ
                5. تحديد المعايير والمؤشرات للنجاح

                ⚡ المرحلة الثانية - التنفيذ النشط:
                6. البدء بالمهام الأساسية والمهمة
                7. متابعة التقدم بانتظام
                8. حل المشاكل والتحديات أولاً بأول
                9. طلب المساعدة عند الحاجة
                10. توثيق التقدم والإنجازات

                ✅ المرحلة الثالثة - المراجعة والإنهاء:
                11. مراجعة جودة العمل المنجز
                12. التأكد من تحقيق جميع المتطلبات
                13. إجراء التعديلات النهائية إذا لزم الأمر
                14. توثيق الدروس المستفادة
                15. تسليم المهمة والحصول على التغذية الراجعة
            """.trimIndent()
        }
    }

    private fun generateQuickIdeas(): String {
        val taskName = binding.taskNameEditText.text.toString().trim()
        val category = binding.taskCategoryText.text.toString()

        return when {
            taskName.contains("اجتماع", ignoreCase = true) -> """
                💡 أفكار سريعة للاجتماع:

                🚀 أفكار إبداعية:
                • استخدام تقنية العصف الذهني لتوليد حلول مبتكرة
                • تطبيق قاعدة "لا للانتقاد" أثناء طرح الأفكار
                • استخدام الخرائط الذهنية لتنظيم الأفكار

                ⚡ أفكار لتحسين الكفاءة:
                • تحديد وقت محدد لكل نقطة في جدول الأعمال
                • استخدام تقنية "الوقوف" لاجتماعات أقصر
                • تسجيل النقاط المهمة على لوحة مرئية للجميع

                🎯 أفكار للمتابعة:
                • إنشاء قائمة مهام واضحة مع مسؤوليات محددة
                • تحديد مواعيد نهائية واقعية لكل مهمة
                • جدولة اجتماع متابعة قصير بعد أسبوع
            """.trimIndent()

            category.contains("دراسة") -> """
                💡 أفكار سريعة للدراسة:

                🧠 تقنيات الحفظ والفهم:
                • استخدام تقنية "البومودورو" (25 دقيقة دراسة + 5 دقائق راحة)
                • تطبيق طريقة "التعليم للآخرين" لتثبيت المعلومات
                • استخدام البطاقات التعليمية للمراجعة السريعة

                📱 استخدام التكنولوجيا:
                • تطبيقات الهاتف للتذكير بأوقات الدراسة
                • مقاطع فيديو تعليمية لتوضيح المفاهيم الصعبة
                • تطبيقات الخرائط الذهنية لتنظيم المعلومات

                👥 الدراسة الجماعية:
                • تكوين مجموعة دراسة مع الزملاء
                • تبادل الملاحظات والملخصات
                • إجراء مناقشات حول المواضيع المعقدة
            """.trimIndent()

            category.contains("عمل") -> """
                💡 أفكار سريعة للعمل:

                ⚡ تحسين الإنتاجية:
                • تطبيق مبدأ "أهم مهمة أولاً" في بداية اليوم
                • استخدام تقنية "تجميع المهام المتشابهة"
                • تحديد أوقات محددة للرد على الإيميلات

                🤝 التعاون والتواصل:
                • إنشاء قنوات تواصل سريعة مع الفريق
                • تنظيم اجتماعات قصيرة يومية للمتابعة
                • مشاركة التقدم والإنجازات مع الزملاء

                📈 التطوير المهني:
                • تخصيص وقت أسبوعي لتعلم مهارة جديدة
                • طلب التغذية الراجعة من المدير والزملاء
                • توثيق الإنجازات والدروس المستفادة
            """.trimIndent()

            else -> """
                💡 أفكار سريعة عامة:

                🎯 تحسين التركيز:
                • تطبيق تقنية "الهاتف بعيداً" أثناء العمل
                • استخدام الموسيقى الهادئة لتحسين التركيز
                • تحديد فترات راحة منتظمة لتجديد الطاقة

                📊 تتبع التقدم:
                • استخدام تطبيقات إدارة المهام
                • إنشاء قوائم مرجعية للمهام المعقدة
                • الاحتفال بالإنجازات الصغيرة

                🚀 الابتكار والإبداع:
                • تجربة طرق جديدة لحل المشاكل
                • طلب آراء الآخرين للحصول على منظور مختلف
                • تخصيص وقت للتفكير الإبداعي والعصف الذهني
            """.trimIndent()
        }
    }
}
