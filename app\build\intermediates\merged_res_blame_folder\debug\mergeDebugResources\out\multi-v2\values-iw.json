{"logs": [{"outputFile": "com.example.kpitrackerapp-mergeDebugResources-54:/values-iw/values-iw.xml", "map": [{"source": "C:\\Gradle\\gradle-8.4\\caches\\8.11.1\\transforms\\1fb6da15b525de990376081401abcb58\\transformed\\appcompat-1.6.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "361,466,566,674,758,860,976,1055,1133,1224,1318,1412,1506,1606,1699,1794,1887,1978,2070,2151,2256,2359,2457,2562,2664,2766,2920,11050", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "461,561,669,753,855,971,1050,1128,1219,1313,1407,1501,1601,1694,1789,1882,1973,2065,2146,2251,2354,2452,2557,2659,2761,2915,3012,11127"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\8.11.1\\transforms\\d69c07a7d8d82aa3a4b3977e5b803b90\\transformed\\core-1.12.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "39,40,41,42,43,44,45,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3437,3531,3633,3730,3827,3928,4028,11132", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "3526,3628,3725,3822,3923,4023,4129,11228"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\8.11.1\\transforms\\44fac508c06d1fa71634b2a2e87bd97f\\transformed\\jetified-play-services-basement-18.1.0\\res\\values-iw\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "113", "endOffsets": "308"}, "to": {"startLines": "57", "startColumns": "4", "startOffsets": "5385", "endColumns": "117", "endOffsets": "5498"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\8.11.1\\transforms\\32f7978bc74fb055e711be763c16e3bd\\transformed\\jetified-play-services-base-18.1.0\\res\\values-iw\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,442,563,663,798,919,1027,1126,1258,1358,1499,1618,1748,1889,1945,2001", "endColumns": "98,149,120,99,134,120,107,98,131,99,140,118,129,140,55,55,76", "endOffsets": "291,441,562,662,797,918,1026,1125,1257,1357,1498,1617,1747,1888,1944,2000,2077"}, "to": {"startLines": "49,50,51,52,53,54,55,56,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4420,4523,4677,4802,4906,5045,5170,5282,5503,5639,5743,5888,6011,6145,6290,6350,6410", "endColumns": "102,153,124,103,138,124,111,102,135,103,144,122,133,144,59,59,80", "endOffsets": "4518,4672,4797,4901,5040,5165,5277,5380,5634,5738,5883,6006,6140,6285,6345,6405,6486"}}, {"source": "C:\\Gradle\\gradle-8.4\\caches\\8.11.1\\transforms\\36b4f88c4f8ab3af336856b4860e45b2\\transformed\\material-1.11.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,311,388,463,540,640,731,824,937,1017,1082,1170,1240,1303,1395,1458,1518,1577,1640,1701,1755,1857,1914,1973,2027,2095,2206,2287,2369,2501,2572,2645,2769,2857,2933,2986,3040,3106,3179,3255,3341,3419,3489,3564,3646,3714,3815,3900,3970,4060,4151,4225,4298,4387,4438,4519,4586,4668,4753,4815,4879,4942,5010,5104,5199,5289,5386,5443,5501", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,76,74,76,99,90,92,112,79,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,81,131,70,72,123,87,75,52,53,65,72,75,85,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74", "endOffsets": "306,383,458,535,635,726,819,932,1012,1077,1165,1235,1298,1390,1453,1513,1572,1635,1696,1750,1852,1909,1968,2022,2090,2201,2282,2364,2496,2567,2640,2764,2852,2928,2981,3035,3101,3174,3250,3336,3414,3484,3559,3641,3709,3810,3895,3965,4055,4146,4220,4293,4382,4433,4514,4581,4663,4748,4810,4874,4937,5005,5099,5194,5284,5381,5438,5496,5571"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3017,3094,3169,3246,3346,4134,4227,4340,6491,6556,6644,6714,6777,6869,6932,6992,7051,7114,7175,7229,7331,7388,7447,7501,7569,7680,7761,7843,7975,8046,8119,8243,8331,8407,8460,8514,8580,8653,8729,8815,8893,8963,9038,9120,9188,9289,9374,9444,9534,9625,9699,9772,9861,9912,9993,10060,10142,10227,10289,10353,10416,10484,10578,10673,10763,10860,10917,10975", "endLines": "6,34,35,36,37,38,46,47,48,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126", "endColumns": "12,76,74,76,99,90,92,112,79,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,81,131,70,72,123,87,75,52,53,65,72,75,85,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74", "endOffsets": "356,3089,3164,3241,3341,3432,4222,4335,4415,6551,6639,6709,6772,6864,6927,6987,7046,7109,7170,7224,7326,7383,7442,7496,7564,7675,7756,7838,7970,8041,8114,8238,8326,8402,8455,8509,8575,8648,8724,8810,8888,8958,9033,9115,9183,9284,9369,9439,9529,9620,9694,9767,9856,9907,9988,10055,10137,10222,10284,10348,10411,10479,10573,10668,10758,10855,10912,10970,11045"}}]}]}