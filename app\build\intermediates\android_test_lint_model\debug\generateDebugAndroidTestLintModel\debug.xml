<variant
    name="debug"
    package="com.example.kpitrackerapp"
    minSdkVersion="26"
    targetSdkVersion="34"
    debuggable="true"
    mergedManifest="build\intermediates\merged_manifest\debug\processDebugMainManifest\AndroidManifest.xml"
    manifestMergeReport="build\outputs\logs\manifest-merger-debug-report.txt"
    partialResultsDir="build\intermediates\android_test_lint_partial_results\debug\lintAnalyzeDebugAndroidTest\out">
  <buildFeatures
      viewBinding="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
  </sourceProviders>
  <testSourceProviders>
    <sourceProvider
        manifests="src\androidTest\AndroidManifest.xml"
        javaDirectories="src\androidTest\java;src\androidTestDebug\java;src\androidTest\kotlin;src\androidTestDebug\kotlin"
        resDirectories="src\androidTest\res;src\androidTestDebug\res"
        assetsDirectories="src\androidTest\assets;src\androidTestDebug\assets"
        androidTest="true"/>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      type="INSTRUMENTATION_TEST"
      applicationId="com.example.kpitrackerapp.test"
      generatedResourceFolders="build\generated\res\resValues\androidTest\debug"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31dc57fa5fb6b1e784be3fa06f5411c2\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
