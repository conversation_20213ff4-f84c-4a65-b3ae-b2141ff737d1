package com.example.kpitrackerapp.ui

import android.app.Activity
import android.app.DatePickerDialog
import android.app.TimePickerDialog
import android.os.Bundle
import android.widget.ArrayAdapter
import android.widget.AutoCompleteTextView
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.example.kpitrackerapp.R
import com.example.kpitrackerapp.databinding.ActivityAddEditKpiBinding
import com.example.kpitrackerapp.models.Task // Changed from Kpi
import com.example.kpitrackerapp.viewmodels.TaskViewModel // Changed from KpiViewModel
import com.example.kpitrackerapp.viewmodels.TaskViewModelFactory // Changed
import com.example.kpitrackerapp.persistence.AppDatabase // Added import
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.OutOfQuotaPolicy
import androidx.work.WorkManager
import com.example.kpitrackerapp.workers.TaskReminderWorker
import com.example.kpitrackerapp.utils.AlarmClockManager
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.concurrent.TimeUnit
import java.util.Date // Added import
import java.util.Locale
import java.util.UUID

// Renaming this class to AddEditTaskActivity would be more appropriate,
// but for now, we'll adapt it to work with the new "Add Task" UI.
class AddEditKpiActivity : AppCompatActivity() {

    private lateinit var binding: ActivityAddEditKpiBinding
    private val taskViewModel: TaskViewModel by viewModels {
        val database = AppDatabase.getDatabase(this)
        TaskViewModelFactory(
            application,
            database.taskDao(),
            database.taskCategoryDao(),
            database.subtaskDao()
        )
    }

    private var currentTaskId: Int? = null // Changed to Int?
    private var isEditingTask = false
    private val calendar = Calendar.getInstance()

    // مدير المنبهات
    private lateinit var alarmClockManager: AlarmClockManager

    companion object {
        const val EXTRA_TASK_ID = "com.example.kpitrackerapp.TASK_ID"
        const val EXTRA_EDIT_KPI_ID = "com.example.kpitrackerapp.EDIT_KPI_ID" // Added back for compatibility
        const val EXTRA_PREFILL_USER_ID = "com.example.kpitrackerapp.PREFILL_USER_ID" // Ensure present
        const val EXTRA_PREFILL_USER_NAME = "com.example.kpitrackerapp.PREFILL_USER_NAME" // Ensure present
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAddEditKpiBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // تهيئة مدير المنبهات
        alarmClockManager = AlarmClockManager(this)

        currentTaskId = intent.getStringExtra(EXTRA_TASK_ID)?.toIntOrNull() // Parse String to Int?
        isEditingTask = currentTaskId != null

        setupToolbar() // Changed from setupActionBar
        setupFormFields()
        setupSaveButton()

        if (isEditingTask) {
            // title = getString(R.string.edit_task_title) // Add this string if needed
            binding.toolbar.title = "Edit Task" // Placeholder, use string resource
            loadTaskData()
        } else {
            // title = getString(R.string.add_task_title) // This is set in XML
            // binding.toolbar.title = getString(R.string.add_task_title) // Already set by layout
        }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        // Title is set in the XML layout: app:title="@string/add_task_title"
    }

    private fun setupFormFields() {
        // Expiration Date Picker
        binding.taskExpirationDateEditText.setOnClickListener {
            showDatePickerDialog()
        }

        // Expiration Time Picker
        binding.taskExpirationTimeEditText.setOnClickListener {
            showTimePickerDialog()
        }

        // Priority Selector with Dialog
        val priorityOptions = arrayOf(
            "🔴 عاجلة - يجب إنجازها فوراً",
            "🟠 عالية - مهمة جداً",
            "🟡 متوسطة - مهمة عادية",
            "🟢 منخفضة - يمكن تأجيلها"
        )

        var selectedPriorityIndex = 2 // Default to متوسطة
        binding.taskPriorityText.text = priorityOptions[selectedPriorityIndex]

        binding.taskPrioritySelector.setOnClickListener {
            showPriorityDialog(priorityOptions, selectedPriorityIndex) { newIndex ->
                selectedPriorityIndex = newIndex
                binding.taskPriorityText.text = priorityOptions[newIndex]

                val priorityLabels = arrayOf("عاجلة", "عالية", "متوسطة", "منخفضة")
                val priorityColors = arrayOf("#F44336", "#FF5722", "#FF9800", "#4CAF50")
                val priorityIcons = arrayOf("🔴", "🟠", "🟡", "🟢")

                val selectedPriority = priorityLabels[newIndex]
                val selectedColor = priorityColors[newIndex]
                val selectedIcon = priorityIcons[newIndex]

                Toast.makeText(this, "$selectedIcon تم اختيار الأولوية: $selectedPriority", Toast.LENGTH_SHORT).show()
            }
        }

        // Category Selector with Dialog
        val categoryOptions = arrayOf(
            "💼 عمل - مهام العمل والوظيفة",
            "👤 شخصي - أمور شخصية",
            "📚 دراسة - تعليم وتطوير",
            "🏥 صحة - صحة ولياقة",
            "💰 مالية - أمور مالية",
            "👨‍👩‍👧‍👦 عائلة - أمور عائلية",
            "🛒 تسوق - مشتريات",
            "✈️ سفر - سفر ورحلات",
            "🏠 منزل - أعمال منزلية",
            "🎯 مشروع - مشاريع خاصة",
            "📞 اتصالات - مكالمات ومراسلات",
            "🎨 إبداع - أنشطة إبداعية",
            "🤝 اجتماعي - أنشطة اجتماعية",
            "⚙️ صيانة - صيانة وإصلاح",
            "📋 إداري - أعمال إدارية"
        )

        var selectedCategoryIndex = 0 // Default to عمل
        binding.taskCategoryText.text = categoryOptions[selectedCategoryIndex]

        binding.taskCategorySelector.setOnClickListener {
            showCategoryDialog(categoryOptions, selectedCategoryIndex) { newIndex ->
                selectedCategoryIndex = newIndex
                binding.taskCategoryText.text = categoryOptions[newIndex]

                val categoryLabels = arrayOf("عمل", "شخصي", "دراسة", "صحة", "مالية", "عائلة", "تسوق", "سفر", "منزل", "مشروع", "اتصالات", "إبداع", "اجتماعي", "صيانة", "إداري")
                val categoryIcons = arrayOf("💼", "👤", "📚", "🏥", "💰", "👨‍👩‍👧‍👦", "🛒", "✈️", "🏠", "🎯", "📞", "🎨", "🤝", "⚙️", "📋")

                val selectedCategory = categoryLabels[newIndex]
                val selectedIcon = categoryIcons[newIndex]

                Toast.makeText(this, "$selectedIcon تم اختيار الفئة: $selectedCategory", Toast.LENGTH_SHORT).show()
            }
        }

        // Reminder Dropdown
        val reminderOptions = resources.getStringArray(R.array.reminder_options)
        val reminderAdapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, reminderOptions)
        (binding.taskReminderInputLayout.editText as? AutoCompleteTextView)?.setAdapter(reminderAdapter)

        if (!isEditingTask) {
            // Default to "No reminder" for new tasks
            (binding.taskReminderInputLayout.editText as? AutoCompleteTextView)?.setText(getString(R.string.reminder_option_no_reminder), false)
        }
    }

    private fun showDatePickerDialog() {
        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH)
        val day = calendar.get(Calendar.DAY_OF_MONTH)

        DatePickerDialog(this, { _, selectedYear, selectedMonth, selectedDay ->
            val selectedDate = Calendar.getInstance()
            selectedDate.set(selectedYear, selectedMonth, selectedDay)
            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            binding.taskExpirationDateEditText.setText(dateFormat.format(selectedDate.time))
        }, year, month, day).show()
    }

    private fun showTimePickerDialog() {
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        val minute = calendar.get(Calendar.MINUTE)

        TimePickerDialog(this, { _, selectedHour, selectedMinute ->
            val selectedTime = String.format(Locale.getDefault(), "%02d:%02d", selectedHour, selectedMinute)
            binding.taskExpirationTimeEditText.setText(selectedTime)
        }, hour, minute, true).show()
    }


    private fun loadTaskData() {
        // Placeholder for loading existing task data if isEditingTask is true
        // This would involve fetching the Task by currentTaskId from taskViewModel
        // and populating the fields.
        // For example:
        taskViewModel.getTaskById(currentTaskId ?: 0).observe(this) { task -> // Pass Int
            task?.let {
                binding.taskNameEditText.setText(it.name)
                val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                binding.taskExpirationDateEditText.setText(dateFormat.format(it.expirationDate))
                // Expiration time is not in the model, so we can't load it.
                // binding.taskExpirationTimeEditText.setText(it.expirationTime ?: "")

                val reminderOptions = resources.getStringArray(R.array.reminder_options)
                val reminderValueToSet: String = when (it.reminderDaysBefore) {
                    null -> getString(R.string.reminder_option_no_reminder)
                    0 -> getString(R.string.reminder_option_on_due_date)
                    1 -> getString(R.string.reminder_option_1_day)
                    2 -> getString(R.string.reminder_option_2_days)
                    3 -> getString(R.string.reminder_option_3_days)
                    7 -> getString(R.string.reminder_option_1_week)
                    else -> getString(R.string.reminder_option_no_reminder) // Default to "No reminder"
                }
                (binding.taskReminderInputLayout.editText as? AutoCompleteTextView)?.setText(reminderValueToSet, false)
            }
        }
    }

    private fun setupSaveButton() {
        binding.addTaskButton.setOnClickListener {
            saveTask()
        }
    }

    private fun saveTask() {
        val taskName = binding.taskNameEditText.text.toString().trim()
        val expirationDateStr = binding.taskExpirationDateEditText.text.toString().trim()
        // val expirationTime = binding.taskExpirationTimeEditText.text.toString().trim() // Not used in model
        val reminderDaysText = (binding.taskReminderInputLayout.editText as? AutoCompleteTextView)?.text.toString()

        var isValid = true
        if (taskName.isEmpty()) {
            binding.taskNameInputLayout.error = "Task name is required" // Use string resource
            isValid = false
        } else {
            binding.taskNameInputLayout.error = null
        }

        var parsedExpirationDate: Date? = null
        if (expirationDateStr.isEmpty()) {
            binding.taskExpirationDateInputLayout.error = getString(R.string.error_date_required)
            isValid = false
        } else {
            try {
                val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                dateFormat.isLenient = false
                parsedExpirationDate = dateFormat.parse(expirationDateStr)
                binding.taskExpirationDateInputLayout.error = null
            } catch (e: Exception) {
                binding.taskExpirationDateInputLayout.error = "Invalid date format (YYYY-MM-DD)" // Use string resource
                isValid = false
            }
        }

        if (!isValid) {
            return
        }

        // Convert reminderDaysText to an integer for reminderDaysBefore
        val reminderValue = when(reminderDaysText) {
            getString(R.string.reminder_option_no_reminder) -> -1 // Special value for null
            getString(R.string.reminder_option_on_due_date) -> 0
            getString(R.string.reminder_option_1_day) -> 1
            getString(R.string.reminder_option_2_days) -> 2
            getString(R.string.reminder_option_3_days) -> 3
            getString(R.string.reminder_option_1_week) -> 7
            else -> -1 // Default to no reminder
        }
        val finalReminderDaysBefore = if(reminderValue == -1) null else reminderValue


        if (parsedExpirationDate == null && isValid) { // Should not happen if validation passed, but as a safeguard
            Toast.makeText(this, getString(R.string.error_invalid_expiration_date), Toast.LENGTH_SHORT).show()
            return
        }

        val taskToSave = if (isEditingTask) {
            Task(
                id = currentTaskId!!,
                name = taskName,
                expirationDate = parsedExpirationDate!!,
                reminderDaysBefore = finalReminderDaysBefore,
                isCompleted = false // Or load existing status
            )
        } else {
            Task(
                name = taskName,
                expirationDate = parsedExpirationDate!!,
                reminderDaysBefore = finalReminderDaysBefore,
                isCompleted = false
            )
        }

        if (isEditingTask) {
            taskViewModel.update(taskToSave)

            // Schedule a one-time worker if "On due date" is selected and date is today
            if (taskToSave.reminderDaysBefore == 0 && isTaskDueToday(taskToSave.expirationDate)) {
                scheduleImmediateTaskCheckWorker()
            }

            Toast.makeText(this, getString(R.string.task_updated_success, taskName), Toast.LENGTH_SHORT).show()
            setResult(Activity.RESULT_OK)
            finish()
        } else {
            taskViewModel.insert(taskToSave)

            // Schedule a one-time worker if "On due date" is selected and date is today
            if (taskToSave.reminderDaysBefore == 0 && isTaskDueToday(taskToSave.expirationDate)) {
                scheduleImmediateTaskCheckWorker()
            }

            Toast.makeText(this, getString(R.string.task_added_success, taskName), Toast.LENGTH_SHORT).show()

            // إضافة المهمة تلقائياً إلى منبه الهاتف للمهام الجديدة فقط
            addTaskToPhoneClock(taskToSave)
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        finish()
        return true
    }

    /**
     * إضافة المهمة تلقائياً إلى منبه الهاتف
     */
    private fun addTaskToPhoneClock(task: Task) {
        try {
            // إظهار خيارات إضافة المنبه للمستخدم
            showAlarmOptionsDialog(task)
        } catch (e: Exception) {
            // في حالة حدوث خطأ، لا نريد أن نوقف عملية حفظ المهمة
            Toast.makeText(this, "تم حفظ المهمة، لكن حدث خطأ في إضافة المنبه", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * إظهار خيارات إضافة المنبه
     */
    private fun showAlarmOptionsDialog(task: Task) {
        val options = arrayOf(
            "⏰ إضافة منبه تلقائي",
            "🕘 إضافة منبه مخصص",
            "⚡ منبه سريع (بعد 30 دقيقة)",
            "❌ بدون منبه"
        )

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("📱 إضافة المهمة لمنبه الهاتف")
            .setMessage("هل تريد إضافة منبه لتذكيرك بهذه المهمة؟")
            .setSingleChoiceItems(options, -1) { _, _ ->
                // لا نفعل شيء هنا، سننتظر الضغط على موافق
            }
            .setPositiveButton("موافق") { dialog, _ ->
                val selectedPosition = (dialog as androidx.appcompat.app.AlertDialog).listView.checkedItemPosition
                when (selectedPosition) {
                    0 -> {
                        // منبه تلقائي
                        alarmClockManager.addTaskToAlarmClock(task)
                        finishActivity()
                    }
                    1 -> {
                        // منبه مخصص
                        dialog.dismiss()
                        showCustomAlarmTimeDialog(task)
                    }
                    2 -> {
                        // منبه سريع
                        alarmClockManager.addQuickReminder(task)
                        finishActivity()
                    }
                    3 -> {
                        // بدون منبه
                        Toast.makeText(this, "تم حفظ المهمة بدون منبه", Toast.LENGTH_SHORT).show()
                        finishActivity()
                    }
                    else -> {
                        // لم يتم اختيار شيء، افتراضي بدون منبه
                        Toast.makeText(this, "تم حفظ المهمة بدون منبه", Toast.LENGTH_SHORT).show()
                        finishActivity()
                    }
                }
            }
            .setNegativeButton("إلغاء") { _, _ ->
                Toast.makeText(this, "تم حفظ المهمة بدون منبه", Toast.LENGTH_SHORT).show()
                finishActivity()
            }
            .show()
    }

    /**
     * إظهار حوار اختيار وقت المنبه المخصص
     */
    private fun showCustomAlarmTimeDialog(task: Task) {
        val calendar = Calendar.getInstance()

        TimePickerDialog(
            this,
            { _, hourOfDay, minute ->
                // إضافة المنبه بالوقت المحدد
                alarmClockManager.addCustomAlarm(task, hourOfDay, minute)
                finishActivity()
            },
            calendar.get(Calendar.HOUR_OF_DAY),
            calendar.get(Calendar.MINUTE),
            true // استخدام تنسيق 24 ساعة
        ).show()
    }

    /**
     * إنهاء النشاط بعد حفظ المهمة
     */
    private fun finishActivity() {
        setResult(Activity.RESULT_OK)
        finish()
    }

    private fun isTaskDueToday(expirationDate: Date): Boolean {
        val todayCal = Calendar.getInstance()
        val expirationCal = Calendar.getInstance().apply { time = expirationDate }

        return todayCal.get(Calendar.YEAR) == expirationCal.get(Calendar.YEAR) &&
               todayCal.get(Calendar.DAY_OF_YEAR) == expirationCal.get(Calendar.DAY_OF_YEAR)
    }

    private fun scheduleImmediateTaskCheckWorker() {
        val workRequest = OneTimeWorkRequestBuilder<TaskReminderWorker>()
            .setInitialDelay(1, TimeUnit.MINUTES) // Run in 1 minute
            .setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
            .addTag(TaskReminderWorker.WORK_NAME + "_immediate_check") // Optional tag
            .build()
        WorkManager.getInstance(applicationContext).enqueue(workRequest)
        android.util.Log.i("AddEditKpiActivity", "Scheduled immediate TaskReminderWorker for same-day reminder.")
    }

    private fun showPriorityDialog(options: Array<String>, currentIndex: Int, onSelected: (Int) -> Unit) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("⭐ اختيار الأولوية")
            .setSingleChoiceItems(options, currentIndex) { dialog, which ->
                onSelected(which)
                dialog.dismiss()
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun showCategoryDialog(options: Array<String>, currentIndex: Int, onSelected: (Int) -> Unit) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("📂 اختيار الفئة")
            .setSingleChoiceItems(options, currentIndex) { dialog, which ->
                onSelected(which)
                dialog.dismiss()
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }
}
