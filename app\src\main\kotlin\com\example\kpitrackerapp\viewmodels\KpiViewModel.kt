package com.example.kpitrackerapp.viewmodels

import android.app.Application // Import Application
import android.content.Context // Import Context
import android.util.Log // Import Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.* // Import Lifecycle components
import com.example.kpitrackerapp.models.Kpi
import com.example.kpitrackerapp.models.KpiProgressEntry
import com.example.kpitrackerapp.models.OcrResultItem // Import OcrResultItem
import com.example.kpitrackerapp.models.User // Import User
import com.example.kpitrackerapp.models.UserKpiAssignment // Import UserKpiAssignment
import com.example.kpitrackerapp.persistence.KpiDao
import com.example.kpitrackerapp.persistence.KpiProgressEntryDao
import com.example.kpitrackerapp.persistence.UserDao // Import UserDao
import com.example.kpitrackerapp.persistence.UserKpiAssignmentDao // Import UserKpiAssignmentDao
import com.example.kpitrackerapp.ui.ExcelReviewActivity // Import for constants
import com.example.kpitrackerapp.ui.KpiReportData // Import the report data class
import com.example.kpitrackerapp.ui.UserSummaryItem // Import UserSummaryItem
import com.example.kpitrackerapp.ui.KpiSummaryDetail // Import KpiSummaryDetail
import kotlinx.coroutines.Dispatchers // Import Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview // Import FlowPreview
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.* // Import Flow operators
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext // Import withContext
import java.util.* // Import Calendar, Date, TimeZone
import java.util.concurrent.TimeUnit
import com.example.kpitrackerapp.util.NotificationHelper // Import NotificationHelper


// --- Data Classes ---

// Data class for holding aggregated Master Card values
data class MasterCardData(
    val totalMonthlyTarget: Double,
    val totalMonthlyAchieved: Double,
    val totalAnnualTarget: Double,
    val totalAnnualAchieved: Double,
    val monthlyPercentage: Int,
    val annualPercentage: Int
)

// Helper data class for combining four values
data class Quad<A, B, C, D>(val first: A, val second: B, val third: C, val fourth: D)

// Data class for holding summary details (reusing existing one for now)
// data class KpiSummaryDetail(val kpiName: String, val monthlyPercentage: Int, val annualPercentage: Int)
// Already imported: import com.example.kpitrackerapp.ui.KpiSummaryDetail

// Data class combining KPI with its progress details (ensure it's defined if not imported)


@OptIn(ExperimentalCoroutinesApi::class) // Needed for flatMapLatest
class KpiViewModel(
    private val application: Application, // Add application context
    private val kpiDao: KpiDao,
    private val progressEntryDao: KpiProgressEntryDao,
    private val userDao: UserDao, // Add UserDao
    private val userKpiAssignmentDao: UserKpiAssignmentDao // Add UserKpiAssignmentDao
) : ViewModel() {

    private val context: Context = application.applicationContext // Store context

    // --- Expose all users as a StateFlow ---
    val allUsersList: StateFlow<List<User>> = userDao.getAllUsers()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000), // Keep subscribed for 5s after last observer
            initialValue = emptyList() // Start with an empty list
        )

    // --- Temporary Data Holder for Excel Import (Using LiveData) ---
    private val _temporaryExcelData = MutableLiveData<List<OcrResultItem>?>(null)
    val temporaryExcelData: LiveData<List<OcrResultItem>?> = _temporaryExcelData

    // --- Filters ---
    // Removed user filter logic

    // --- Filter for Main Screen Monthly Calculation ---
    private val _selectedFilterMonth = MutableStateFlow<Calendar?>(Calendar.getInstance()) // Default to current month
    val selectedFilterMonth: StateFlow<Calendar?> = _selectedFilterMonth

    // --- Filter for User KPI List Screen (Remains single user) ---
    private val _selectedUserIdForList = MutableStateFlow<String?>(null)
    val selectedUserIdForList: StateFlow<String?> = _selectedUserIdForList

    // --- Selected KPI for Report ---
    private val _selectedKpiId = MutableStateFlow<String?>(null)
    val selectedKpiId: StateFlow<String?> = _selectedKpiId

    // --- Report Date Range (Combined) ---
    private val _reportDateRange = MutableStateFlow<Pair<Long?, Long?>>(
        getStartOfYearMillis(System.currentTimeMillis()) to getEndOfDayMillis(System.currentTimeMillis()) // Pass current time to getStartOfYearMillis
    )
    val reportDateRange: StateFlow<Pair<Long?, Long?>> = _reportDateRange

    // --- Flow for the last entry date ---
    val lastEntryDate: StateFlow<Long?> = progressEntryDao.getLastEntryDate()
        .stateIn(viewModelScope, SharingStarted.Lazily, null) // Convert Flow<Long?> to StateFlow<Long?>

    // --- NEW: Flow for KPIs available in the ReportActivity dropdown ---
    // This should list all KPIs that are assigned to at least one user.
    val allAssignedKpisForReport: StateFlow<List<Kpi>> = combine(
        kpiDao.getAllKpis(),
        userKpiAssignmentDao.getAllAssignments()
    ) { allKpis, allAssignments ->
        val assignedKpiIds = allAssignments.map { it.kpiId }.toSet()
        allKpis.filter { it.id in assignedKpiIds } // Filter KPIs to only those assigned
               .distinctBy { it.name } // Add this line to get unique KPIs by name
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyList()
    )
    // --- End NEW Flow ---


    // --- Flow for all KPIs (for template selection in AddEditKpiActivity) ---
    val allKpisForTemplateSelection: StateFlow<List<Kpi>> = combine(
        kpiDao.getAllKpis(),
        userKpiAssignmentDao.getAllAssignments()
    ) { allKpis, allAssignments ->
        val assignedKpiIds = allAssignments.map { it.kpiId }.toSet()
        allKpis.filter { it.id in assignedKpiIds }
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000), emptyList())


    // --- Data Flows ---
    private val allUsers: Flow<List<User>> = userDao.getAllUsers() // Flow for all users

    // --- Flow for Unique Months with Data ---
    val uniqueMonthsWithData: Flow<List<Calendar>> = progressEntryDao.getAllEntries() // Get all entries
        .map { entries -> // entries is List<KpiProgressEntry>
            entries
                .map { entry -> // entry is KpiProgressEntry
                    val calendar = Calendar.getInstance() // Get instance
                    calendar.time = entry.date          // Set time using Date object
                    calendar                            // Return the calendar instance
                }
                .distinctBy { cal -> cal.get(Calendar.YEAR) * 100 + cal.get(Calendar.MONTH) } // Use cal here
                .sorted() // Sort chronologically
        }
        .stateIn(viewModelScope, SharingStarted.Lazily, emptyList()) // Make it a StateFlow

    // Simplified base KPI flow - just get all KPIs
    // Removed filteredKpisBase as filtering logic is now inside kpisWithProgress combine

    // Combine base filtered KPIs with the selected month filter AND user filter
    val kpisWithProgress: Flow<List<KpiWithProgress>> = combine(
        kpiDao.getAllKpis(), // Get all KPIs
        userKpiAssignmentDao.getAllAssignments(), // Get all assignments
        _selectedUserIdForList, // Use the single user ID filter for this list screen flow
        _selectedFilterMonth // Get the month filter
    ) { allKpis, allAssignments, userIdForList, filterMonthCalendar ->
        // Filter KPIs based on the selected user ID for the list screen
        val filteredKpis = if (userIdForList == null) {
             // For UserKpiListActivity, if no user is selected, show nothing.
             emptyList()
        } else {
            val assignedKpiIds = allAssignments.filter { it.userId == userIdForList }.map { it.kpiId }.toSet()
            allKpis.filter { it.id in assignedKpiIds }
        }
        Log.d("KpiViewModel.kpisWithProgress", "Combine triggered. UserIDForList: $userIdForList, Month: ${filterMonthCalendar?.time}, All KPIs: ${allKpis.size}, Filtered KPIs: ${filteredKpis.size}")
        Triple(filteredKpis, filterMonthCalendar, userIdForList) // Pass userIdForList along
    }.flatMapLatest { (filteredKpis, filterMonthCalendar, userIdForList) -> // Destructure userIdForList here
        val today = Calendar.getInstance(TimeZone.getTimeZone("UTC")) // Define today here
        if (filteredKpis.isEmpty() || userIdForList == null) { // Check if list is empty or userIdForList is null
            Log.d("KpiViewModel.kpisWithProgress", "flatMapLatest: Filtered KPI list empty or userIdForList null ($userIdForList).")
            flowOf(emptyList())
        } else {
            Log.d("KpiViewModel.kpisWithProgress", "flatMapLatest: Processing ${filteredKpis.size} filtered KPIs for User: $userIdForList.")
            val progressFlows: List<Flow<KpiWithProgress>> = filteredKpis.map { kpi ->
                // Determine the year and month to use for monthly calculation based on the filter
                val calculationCalendar = filterMonthCalendar ?: Calendar.getInstance() // Use current month if filter is null ("All")
                val calculationYear = calculationCalendar.get(Calendar.YEAR)
                val calculationMonth = calculationCalendar.get(Calendar.MONTH) + 1 // Calendar.MONTH is 0-based

                // REMOVED: isSystemMasterKpi check based on ownerName.
                // Simplifying: Calculate progress individually for this KPI and USER (userIdForList).

                // Fetch individual progress data for this KPI and USER (userIdForList)
                val totalProgressFlow = progressEntryDao.getTotalValueForKpi(kpi.id, userIdForList) // Pass userIdForList
                val monthlyProgressFlow = progressEntryDao.getSumForMonth(
                    kpi.id,
                    userIdForList, // Pass userIdForList
                    calculationYear.toString(),
                    String.format("%02d", calculationMonth)
                )
                val allEntriesFlow = progressEntryDao.getEntriesForKpi(kpi.id, userIdForList) // Pass userIdForList

                // Fetch the current month's sum for this user
                val currentMonthSumFlow = progressEntryDao.getSumForMonth(
                    kpi.id,
                    userIdForList, // Pass userIdForList
                    today.get(Calendar.YEAR).toString(),
                    String.format("%02d", today.get(Calendar.MONTH) + 1)
                )
                // The actual current month sum will be provided by the combine parameter

                // Combine the individual flows for this user/kpi, including the current month sum flow
                combine(
                    totalProgressFlow,
                    monthlyProgressFlow,
                    allEntriesFlow,
                    currentMonthSumFlow // Include the current month sum flow here
                ) { totalProgressValue, monthlySumValue, allEntries, currentMonthSumValue -> // Add currentMonthSumValue parameter
                    // Process results from all flows (logic remains similar, but values are aggregated for masters)
                    val totalProgress = totalProgressValue ?: 0.0
                    val monthlyProgress = monthlySumValue ?: 0.0 // This is now aggregated for masters
                    val entries = allEntries // These are aggregated entries for masters
                    val monthlyTarget = kpi.monthlyTarget // Master KPI uses its own target for percentage calculation

                    // --- Calculate additional analysis values (using current date for remaining days etc.) ---
                    val today = Calendar.getInstance(TimeZone.getTimeZone("UTC")) // Use today for remaining days calculation
                    val currentDay = today.get(Calendar.DAY_OF_MONTH)
                    val daysInMonth = today.getActualMaximum(Calendar.DAY_OF_MONTH) // Days in *current* month

                    var remainingValue: Double? = null
                    // Removed unused remainingPercentage variable
                    var remainingDays: Int? = null
                    var dailyRate: Double? = null
                    var daysSinceUpdate: Long? = null

                    // Calculate days since last update (remains the same)
                    entries.maxByOrNull { it.date }?.let { lastEntry ->
                        val diffMillis = today.timeInMillis - lastEntry.date.time // Use today's calendar
                        daysSinceUpdate = TimeUnit.MILLISECONDS.toDays(diffMillis)
                    }

                    val actualCurrentMonthSum = currentMonthSumValue ?: 0.0 // Use the value from the combine parameter

                    // Calculate monthly percentage based on the *filtered/calculated month's* progress (monthlyProgress)
                    val monthlyPercentage = monthlyTarget?.takeIf { it > 0.0 }?.let {
                        ((monthlyProgress / it) * 100.0).toInt().coerceIn(0, 1000) // Use monthlyProgress here
                    }

                    // The 'remainingValue' and 'dailyRate' should still be based on the *actual current month's* progress
                    // as these reflect the current reality for the user.
                    if (monthlyTarget != null && monthlyTarget > 0) {
                        remainingValue = (monthlyTarget - actualCurrentMonthSum).coerceAtLeast(0.0) // Use actualCurrentMonthSum for remaining
                        remainingDays = (daysInMonth - currentDay).coerceAtLeast(0) // Use today's calendar

                        if (remainingDays > 0) {
                            dailyRate = remainingValue / remainingDays
                        }
                    }

                    // --- Calculate Current Period Target and Achievement ---
                    var currentPeriodTarget: Double? = null
                    var currentPeriodAchievement: Int? = null

                    if (monthlyTarget != null && monthlyTarget > 0) {
                        // Calculate current period target: (Monthly Target × Days Elapsed) ÷ Days in Month
                        currentPeriodTarget = (monthlyTarget * currentDay) / daysInMonth

                        // Debug log to check values
                        Log.d("KpiViewModel", "Period Target Calculation: monthlyTarget=$monthlyTarget, currentDay=$currentDay, daysInMonth=$daysInMonth, result=$currentPeriodTarget")

                        // Calculate current period achievement: (Current Month Progress ÷ Current Period Target) × 100
                        // Use actualCurrentMonthSum (current month progress only)
                        if (currentPeriodTarget > 0) {
                            currentPeriodAchievement = ((actualCurrentMonthSum / currentPeriodTarget) * 100).toInt().coerceIn(0, 1000)
                            Log.d("KpiViewModel", "Period Achievement Calculation: actualCurrentMonthSum=$actualCurrentMonthSum, currentPeriodTarget=$currentPeriodTarget, result=$currentPeriodAchievement%")
                        }
                    }
                    // --- End additional analysis calculation ---

                    // Create KpiWithProgress with all necessary data, including userIdForList
                    KpiWithProgress(
                        kpi = kpi,
                        userId = userIdForList, // Pass the userIdForList from the outer scope
                        currentProgressValue = totalProgress,       // Overall total progress
                        monthlyTargetValue = monthlyTarget,         // Monthly target
                        currentMonthProgressValue = monthlyProgress,// Progress for the *calculated* month
                        actualCurrentMonthProgressValue = actualCurrentMonthSum, // Assign actual current month sum
                        monthlyPercentage = monthlyPercentage,      // Percentage for the *calculated* month
                        progressEntries = entries,                  // All entries for this KPI
                        remainingMonthlyValue = remainingValue,     // Remaining based on *current* month progress
                        remainingMonthlyPercentage = null,          // Set to null as it might be confusing
                        remainingDaysInMonth = remainingDays,       // Remaining days in *current* month
                        requiredDailyRate = dailyRate,              // Required rate based on *current* month
                        daysSinceLastUpdate = daysSinceUpdate,      // Days since last update
                        currentPeriodTarget = currentPeriodTarget,  // Target that should be achieved by current date
                        currentPeriodAchievement = currentPeriodAchievement // Percentage of current period target achieved
                    )
                }
            }
            // Use the top-level combine function to combine the list of flows
            combine(progressFlows) { results ->
                results.toList()
            }
        }
    }.stateIn(viewModelScope, SharingStarted.Lazily, emptyList()) // Convert to StateFlow


    // --- User Summaries Flow ---
    val userSummaries: StateFlow<List<UserSummaryItem>> = combine(
        allUsers,
        userKpiAssignmentDao.getAllAssignments(), // Corrected DAO function call
        kpiDao.getAllKpis(),
        progressEntryDao.getAllEntries(),
        _selectedFilterMonth // Include the month filter
    ) { users: List<User>, assignments: List<UserKpiAssignment>, kpis: List<Kpi>, entries: List<KpiProgressEntry>, selectedMonth: Calendar? -> // Explicit types for lambda params
        Log.d("KpiViewModel.userSummaries", "Combine triggered. Users: ${users.size}, Assignments: ${assignments.size}, KPIs: ${kpis.size}, Entries: ${entries.size}, Month: ${selectedMonth?.time}")
        // Create maps for efficient lookup
        val kpiMap = kpis.associateBy { kpi -> kpi.id } // Explicit lambda param
        val assignmentsByUser = assignments.groupBy { assignment -> assignment.userId } // Explicit lambda param
        val entriesByKpi = entries.groupBy { entry -> entry.kpiId } // Explicit lambda param

        // Determine the calculation period based on the selected month filter
        // Ensure calculationCalendar is consistently UTC if selectedMonth is null, matching helpers
        val baseCalendarForCalc = selectedMonth ?: Calendar.getInstance(TimeZone.getTimeZone("UTC"))

        // Calculate start and end of the calculation month using UTC helpers
        val monthStartMillis = getStartOfMonth(baseCalendarForCalc.timeInMillis).timeInMillis
        val monthEndMillis = getEndOfMonth(baseCalendarForCalc.timeInMillis).timeInMillis

        // Calculate start and end of the calculation year using UTC helpers
        val yearStartMillis = getStartOfYearMillis(baseCalendarForCalc.timeInMillis)
        val yearEndMillis = getEndOfYearMillis(baseCalendarForCalc.timeInMillis)
        // Removed orphaned lines from previous incorrect edit


        users.map { user ->
            val userAssignments = assignmentsByUser[user.id] ?: emptyList()
            val userKpiIds = userAssignments.map { it.kpiId }
            val userKpis = userKpiIds.mapNotNull { kpiMap[it] }

            val summaryDetails = mutableListOf<KpiSummaryDetail>()
            var totalMonthlyAchieved = 0.0
            var totalMonthlyTarget = 0.0
            var totalAnnualAchieved = 0.0 // This variable is calculated but not used in the final UserSummaryItem

            userKpis.forEach { kpi: Kpi -> // Explicit type for lambda param
                val allEntriesForThisKpi = entriesByKpi[kpi.id] ?: emptyList()
                // Filter these entries for the current user in the loop
                val userSpecificKpiEntries = allEntriesForThisKpi.filter { it.userId == user.id }


                // Calculate Monthly Progress for the selected/current month using user-specific entries
                val monthlyEntries = userSpecificKpiEntries.filter { entry -> entry.date.time in monthStartMillis..monthEndMillis }
                val kpiMonthlySum = monthlyEntries.sumOf { entry -> entry.value }
                val kpiMonthlyTarget = kpi.monthlyTarget ?: 0.0
                val kpiMonthlyPerc = if (kpiMonthlyTarget > 0.0) ((kpiMonthlySum / kpiMonthlyTarget) * 100.0).toInt().coerceIn(0, 1000) else 0

                totalMonthlyAchieved += kpiMonthlySum // This is for the user's overall monthly, not used in KpiSummaryDetail
                totalMonthlyTarget += kpiMonthlyTarget // This is for the user's overall monthly, not used in KpiSummaryDetail

                // Calculate Annual Progress (based on the calculation year) using user-specific entries
                val annualEntries = userSpecificKpiEntries.filter { entry -> entry.date.time in yearStartMillis..yearEndMillis }
                val kpiAnnualSum = annualEntries.sumOf { entry -> entry.value }
                val kpiAnnualTarget = kpi.annualTarget
                val kpiAnnualPerc = if (kpiAnnualTarget > 0.0) ((kpiAnnualSum / kpiAnnualTarget) * 100.0).toInt().coerceIn(0, 1000) else 0

                totalAnnualAchieved += kpiAnnualSum // This is for the user's overall annual, not used in KpiSummaryDetail

                summaryDetails.add(
                    KpiSummaryDetail(
                        kpiName = kpi.name,
                        monthlyPercentage = kpiMonthlyPerc,
                        annualPercentage = kpiAnnualPerc // Individual annual %
                    )
                )
            }

            // Calculate overall annual percentage for the user - THESE VARIABLES ARE NOT USED
            // val userTotalAnnualTarget = userKpis.sumOf { it.annualTarget } // Removed unused variable
            // Sum up all progress entries within the *calculation year* for the user's KPIs
            // val userTotalAnnualAchieved = userKpis.sumOf { kpi: Kpi -> // Explicit type for lambda param // Removed unused variable
            //     (entriesByKpi[kpi.id] ?: emptyList())
            //         .filter { entry -> entry.date.time in yearStartMillis..yearEndMillis } // Explicit lambda param
            //         .sumOf { entry -> entry.value } // Explicit lambda param
            // }

            // Calculate overall monthly percentage for the user - LOGIC MISSING IF NEEDED

            // --- Get Owner Image URI from one of the user's KPIs ---
            // Find the first KPI associated with this user that has a non-null ownerImageUri
            val userImageUriFromKpi = userKpis.firstOrNull { !it.ownerImageUri.isNullOrBlank() }?.ownerImageUri
            Log.d("KpiViewModel", "User: ${user.name}, Found image URI from KPI: $userImageUriFromKpi") // Add log

            // Corrected UserSummaryItem constructor call - Use image URI from KPI
            UserSummaryItem(
                userId = user.id,
                userName = user.name,
                userImagePath = userImageUriFromKpi, // Use the URI found from the KPI
                topColor = user.topColor, // Pass user's top color
                bottomColor = user.bottomColor, // Pass user's bottom color
                kpiCount = userKpis.size, // Corrected parameter name
                kpiDetails = summaryDetails
            )
        }.also { resultList ->
            Log.d("KpiViewModel.userSummaries", "Combine finished. Emitting ${resultList.size} summary items.")
            // Log details of the first few items if needed for debugging
            // resultList.take(3).forEach { Log.v("KpiViewModel.userSummaries", "  - User: ${it.userName}, KPIs: ${it.kpiCount}, Details: ${it.kpiDetails.size}") }
        }
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000), emptyList()) // Changed to WhileSubscribed
    // --- End User Summaries Flow ---


    // --- Master Card Data Flow ---
    val masterCardData: StateFlow<MasterCardData?> = combine(
        kpiDao.getAllKpis(),
        progressEntryDao.getAllEntries(),
        userKpiAssignmentDao.getAllAssignments(), // Add assignments flow
        _selectedFilterMonth
    ) { allKpis, allEntries, allAssignments, selectedMonth -> // Add allAssignments parameter
        Log.d("KpiViewModel.masterCardData", "Combine triggered. KPIs: ${allKpis.size}, Entries: ${allEntries.size}, Assignments: ${allAssignments.size}, Month: ${selectedMonth?.time}")

        // Get the set of assigned KPI IDs
        val assignedKpiIds = allAssignments.map { it.kpiId }.toSet()
        // Filter KPIs to include only those assigned to at least one user
        val assignedKpis = allKpis.filter { it.id in assignedKpiIds }

        if (assignedKpis.isEmpty()) { // Check if there are any *assigned* KPIs
            Log.d("KpiViewModel.masterCardData", "No assigned KPIs found, emitting null.")
            null // Return null if there are no assigned KPIs
        } else {
            val calculationCalendar = selectedMonth ?: Calendar.getInstance() // Use current if "All Months"

            // Calculate start and end of the calculation month
            val monthStartMillis = Calendar.getInstance().apply {
                timeInMillis = calculationCalendar.timeInMillis
                set(Calendar.DAY_OF_MONTH, 1)
                set(Calendar.HOUR_OF_DAY, 0); set(Calendar.MINUTE, 0); set(Calendar.SECOND, 0); set(Calendar.MILLISECOND, 0)
            }.timeInMillis
            val monthEndMillis = Calendar.getInstance().apply {
                timeInMillis = calculationCalendar.timeInMillis
                set(Calendar.DAY_OF_MONTH, getActualMaximum(Calendar.DAY_OF_MONTH))
                set(Calendar.HOUR_OF_DAY, 23); set(Calendar.MINUTE, 59); set(Calendar.SECOND, 59); set(Calendar.MILLISECOND, 999)
            }.timeInMillis

            // Calculate start and end of the calculation year
            val yearStartMillis = Calendar.getInstance().apply {
                timeInMillis = calculationCalendar.timeInMillis
                set(Calendar.DAY_OF_YEAR, 1)
                set(Calendar.HOUR_OF_DAY, 0); set(Calendar.MINUTE, 0); set(Calendar.SECOND, 0); set(Calendar.MILLISECOND, 0)
            }.timeInMillis
            val yearEndMillis = Calendar.getInstance().apply {
                timeInMillis = calculationCalendar.timeInMillis
                set(Calendar.MONTH, 11) // December
                set(Calendar.DAY_OF_MONTH, 31)
                set(Calendar.HOUR_OF_DAY, 23); set(Calendar.MINUTE, 59); set(Calendar.SECOND, 59); set(Calendar.MILLISECOND, 999)
            }.timeInMillis

            // Aggregate targets ONLY from assigned KPIs
            val totalMonthlyTarget = assignedKpis.sumOf { it.monthlyTarget ?: 0.0 }
            val totalAnnualTarget = assignedKpis.sumOf { it.annualTarget } // annualTarget is non-nullable

            // Aggregate achieved values from all entries within the respective periods (remains the same)
            val totalMonthlyAchieved = allEntries
                .filter { it.date.time in monthStartMillis..monthEndMillis }
                .sumOf { it.value }

            val totalAnnualAchieved = allEntries
                .filter { it.date.time in yearStartMillis..yearEndMillis }
                .sumOf { it.value }

            // Calculate percentages
            val monthlyPercentage = if (totalMonthlyTarget > 0.0) ((totalMonthlyAchieved / totalMonthlyTarget) * 100.0).toInt().coerceIn(0, 1000) else 0
            val annualPercentage = if (totalAnnualTarget > 0.0) ((totalAnnualAchieved / totalAnnualTarget) * 100.0).toInt().coerceIn(0, 1000) else 0

            Log.d("KpiViewModel.masterCardData", "Calculated: Monthly $totalMonthlyAchieved/$totalMonthlyTarget ($monthlyPercentage%), Annual $totalAnnualAchieved/$totalAnnualTarget ($annualPercentage%)")

            MasterCardData(
                totalMonthlyTarget = totalMonthlyTarget,
                totalMonthlyAchieved = totalMonthlyAchieved,
                totalAnnualTarget = totalAnnualTarget,
                totalAnnualAchieved = totalAnnualAchieved,
                monthlyPercentage = monthlyPercentage,
                annualPercentage = annualPercentage
            )
        }
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000), null) // Start with null, emit data when calculated
    // --- End Master Card Data Flow ---


    // --- REVISED: Overall Aggregated KPI Summaries Flow (Groups by KPI Name) ---
    val overallAggregatedKpiSummaries: StateFlow<List<KpiSummaryDetail>> = combine(
        kpiDao.getAllKpis(),
        progressEntryDao.getAllEntries(),
        userKpiAssignmentDao.getAllAssignments(), // ADDED: Source for all assignments
        _selectedFilterMonth
    ) { allKpisFromDb, allEntries, allAssignments, selectedMonth -> // ADDED: allAssignments parameter
        Log.d("KpiViewModel.overallAggregated", "Combine triggered. DB KPIs: ${allKpisFromDb.size}, Entries: ${allEntries.size}, Assignments: ${allAssignments.size}, Month: ${selectedMonth?.time}")

        // Determine the set of KPI IDs that are currently assigned to at least one user
        val assignedKpiIds = allAssignments.map { it.kpiId }.toSet()
        Log.d("KpiViewModel.overallAggregated", "Found ${assignedKpiIds.size} unique assigned KPI IDs.")

        // Filter the KPIs from the database to include only those that are currently assigned
        val activeKpisFromDb = allKpisFromDb.filter { it.id in assignedKpiIds }
        Log.d("KpiViewModel.overallAggregated", "Filtered to ${activeKpisFromDb.size} active (assigned) KPIs from DB.")

        if (activeKpisFromDb.isEmpty()) { // Check active (assigned) KPIs
            Log.d("KpiViewModel.overallAggregated", "No active (assigned) KPIs found, emitting empty list.")
            emptyList<KpiSummaryDetail>()
        } else {
            // 1. Group active KPIs from DB by their name
            val kpisGroupedByName = activeKpisFromDb.groupBy { it.name } // Use activeKpisFromDb
            Log.d("KpiViewModel.overallAggregated", "Grouped active KPIs by name. Unique names: ${kpisGroupedByName.keys.size}")

            // Define the names to exclude (Removed "Wellness Card" as filtering by active assignments should handle it)
            val excludedKpiNames = setOf("A", "T", "Tes")

            // Filter out the excluded names from the grouped map
            val filteredKpisGroupedByName = kpisGroupedByName.filterKeys { kpiName ->
                kpiName !in excludedKpiNames
            }
            Log.d("KpiViewModel.overallAggregated", "Filtered KPI groups. Remaining unique names: ${filteredKpisGroupedByName.keys.size}")

            val calculationCalendar = selectedMonth ?: Calendar.getInstance() // Use current if "All Months"

            // Calculate start and end of the calculation month (using helpers)
            val monthStartMillis = getStartOfMonth(calculationCalendar.timeInMillis).timeInMillis
            val monthEndMillis = getEndOfMonth(calculationCalendar.timeInMillis).timeInMillis

            // Calculate start and end of the calculation year
            val yearStartMillis = getStartOfYearMillis(calculationCalendar.timeInMillis) // Helper needed or inline
            val yearEndMillis = getEndOfYearMillis(calculationCalendar.timeInMillis) // Helper needed or inline

            // 2. Process each unique KPI name group from the filtered map
            filteredKpisGroupedByName.mapNotNull { (kpiName, kpisInGroup) -> // kpiName: String, kpisInGroup: List<Kpi>
                // Use the first KPI in the group as the representative KPI for targets
                val representativeKpi = kpisInGroup.firstOrNull() ?: return@mapNotNull null
                Log.v("KpiViewModel.overallAggregated", "Processing group for name: '$kpiName'")

                // 3. Get all kpiIds for this group name
                val kpiIdsInGroup = kpisInGroup.map { it.id }.toSet()

                // 4. Filter all progress entries to get only those belonging to this group
                val groupEntries = allEntries.filter { it.kpiId in kpiIdsInGroup }
                Log.v("KpiViewModel.overallAggregated", "Found ${groupEntries.size} entries for group '$kpiName'")

                // 5. Calculate aggregated percentages using groupEntries and representativeKpi targets

                // Calculate AGGREGATED Monthly Progress for this KPI NAME for the selected/current month
                val monthlyEntries = groupEntries.filter { entry -> entry.date.time in monthStartMillis..monthEndMillis }
                val groupMonthlySum = monthlyEntries.sumOf { entry -> entry.value }
                // Calculate aggregated monthly target from all KPIs in the group
                val aggregatedMonthlyTargetForGroup = kpisInGroup.sumOf { it.monthlyTarget ?: 0.0 }
                val groupMonthlyPerc = if (aggregatedMonthlyTargetForGroup > 0.0) ((groupMonthlySum / aggregatedMonthlyTargetForGroup) * 100.0).toInt().coerceIn(0, 1000) else 0

                // Calculate AGGREGATED Annual Progress for this KPI NAME for the calculation year
                val annualEntries = groupEntries.filter { entry -> entry.date.time in yearStartMillis..yearEndMillis }
                val groupAnnualSum = annualEntries.sumOf { entry -> entry.value }
                // Calculate aggregated annual target from all KPIs in the group
                val aggregatedAnnualTargetForGroup = kpisInGroup.sumOf { it.annualTarget } // annualTarget is non-nullable
                val groupAnnualPerc = if (aggregatedAnnualTargetForGroup > 0.0) ((groupAnnualSum / aggregatedAnnualTargetForGroup) * 100.0).toInt().coerceIn(0, 1000) else 0

                // Create ONE KpiSummaryDetail per group name
                KpiSummaryDetail(
                    kpiName = kpiName, // Use the group's name
                    monthlyPercentage = groupMonthlyPerc,
                    annualPercentage = groupAnnualPerc
                )
            }.also {
                Log.d("KpiViewModel.overallAggregated", "Calculated ${it.size} truly aggregated summary details after exclusion.")
            }
        }
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000), emptyList())
    // --- End REVISED Overall Aggregated KPI Summaries Flow ---


    // --- REVISED: Aggregated KpiWithProgress Flow (Groups by KPI Name) ---
    val aggregatedKpisWithProgress: StateFlow<List<KpiWithProgress>> = combine(
        kpiDao.getAllKpis(),
        progressEntryDao.getAllEntries(),
        userKpiAssignmentDao.getAllAssignments(), // Add assignments flow
        _selectedFilterMonth // Use the same month filter
    ) { allKpisFromDb, allEntries, allAssignments, filterMonthCalendar -> // Add allAssignments parameter
        Log.d("KpiViewModel.aggregatedKpi", "Combine triggered. DB KPIs: ${allKpisFromDb.size}, Entries: ${allEntries.size}, Assignments: ${allAssignments.size}, Month: ${filterMonthCalendar?.time}")

        // Get the set of all assigned KPI IDs once
        val allAssignedKpiIds = allAssignments.map { it.kpiId }.toSet()

        if (allKpisFromDb.isEmpty()) {
            Log.d("KpiViewModel.aggregatedKpi", "No KPIs found in DB, emitting empty list.")
            emptyList()
        } else {
            // 1. Group KPIs from DB by their name
            val kpisGroupedByName = allKpisFromDb.groupBy { it.name }
            Log.d("KpiViewModel.aggregatedKpi", "Grouped KPIs by name. Unique names: ${kpisGroupedByName.keys.size}")

            // 2. Process each unique KPI name group
            kpisGroupedByName.mapNotNull { (kpiName, kpisInGroup) -> // kpiName: String, kpisInGroup: List<Kpi>
                // Use the first KPI in the group as the representative KPI for details like unit, name etc.
                val representativeKpi = kpisInGroup.firstOrNull() ?: return@mapNotNull null // Skip if group is somehow empty
                Log.v("KpiViewModel.aggregatedKpi", "Processing group for name: '$kpiName'. Representative ID: ${representativeKpi.id}")

                // 3. Filter KPIs in this group to include only those assigned to users
                val assignedKpisInGroup = kpisInGroup.filter { it.id in allAssignedKpiIds }
                if (assignedKpisInGroup.isEmpty()) {
                    Log.v("KpiViewModel.aggregatedKpi", "Skipping group '$kpiName' as no KPIs in it are assigned to users.")
                    return@mapNotNull null // Skip this group if none of its KPIs are assigned
                }

                // 3b. Calculate the aggregated monthly, annual, and daily targets for this group from assigned KPIs
                val aggregatedMonthlyTarget = assignedKpisInGroup.sumOf { it.monthlyTarget ?: 0.0 }
                val aggregatedAnnualTarget = assignedKpisInGroup.sumOf { it.annualTarget } // annualTarget is not nullable
                val aggregatedDailyTarget = assignedKpisInGroup.sumOf { it.dailyTarget ?: 0.0 }
                Log.v("KpiViewModel.aggregatedKpi", "Group '$kpiName': Aggregated Targets from ${assignedKpisInGroup.size} assigned KPIs - Monthly: $aggregatedMonthlyTarget, Annual: $aggregatedAnnualTarget, Daily: $aggregatedDailyTarget")

                // Use the first ASSIGNED KPI in the group as the representative for certain individual values
                val representativeAssignedKpi = assignedKpisInGroup.first()


                // 4. Get all kpiIds for this group name (original logic, needed for filtering entries)
                val kpiIdsInGroup = kpisInGroup.map { it.id }.toSet() // Still need all IDs in group to fetch all relevant entries

                // 5. Filter all progress entries to get only those belonging to this group name
                val groupEntries = allEntries.filter { it.kpiId in kpiIdsInGroup }
                Log.v("KpiViewModel.aggregatedKpi", "Found ${groupEntries.size} entries for group '$kpiName'")


                // 6. Calculate aggregated progress values using groupEntries
                // Determine the calculation period based on the filter
                val calculationCalendar = filterMonthCalendar ?: Calendar.getInstance()
                val calculationYear = calculationCalendar.get(Calendar.YEAR)
                val calculationMonth = calculationCalendar.get(Calendar.MONTH) + 1

                // Calculate AGGREGATED total progress (sum of all entries for this KPI)
                val totalProgress = groupEntries.sumOf { it.value } // Corrected: Use groupEntries

                // Calculate AGGREGATED monthly progress for the *calculated* month
                val monthlyProgress = groupEntries.filter { // Corrected: Use groupEntries
                    val entryCal = Calendar.getInstance().apply { time = it.date }
                    entryCal.get(Calendar.YEAR) == calculationYear && (entryCal.get(Calendar.MONTH) + 1) == calculationMonth
                }.sumOf { it.value }

                // Calculate remaining, required daily based on *current* month's AGGREGATED progress
                val today = Calendar.getInstance(TimeZone.getTimeZone("UTC"))
                val currentDay = today.get(Calendar.DAY_OF_MONTH)
                val daysInMonth = today.getActualMaximum(Calendar.DAY_OF_MONTH)
                var remainingValue: Double?
                var remainingDays: Int?
                var dailyRate: Double?
                var daysSinceUpdate: Long? = null
                groupEntries.maxByOrNull { it.date }?.let { lastEntry ->
                    val diffMillis = today.timeInMillis - lastEntry.date.time
                    daysSinceUpdate = TimeUnit.MILLISECONDS.toDays(diffMillis)
                }
                val currentMonthSumForAllUsers = groupEntries.filter {
                    val entryCal = Calendar.getInstance().apply { time = it.date }
                    entryCal.get(Calendar.YEAR) == today.get(Calendar.YEAR) && (entryCal.get(Calendar.MONTH) + 1) == (today.get(Calendar.MONTH) + 1)
                }.sumOf { it.value }

                    // Calculate the percentage using the progress for the *filtered/calculation month*
                    // and the representative KPI's monthly target (or aggregated if that's intended for display)
                    // For aggregatedKpisWithProgress, monthlyTargetValue is aggregatedMonthlyTarget
                    val calculatedMonthlyPercentage = aggregatedMonthlyTarget.takeIf { it > 0.0 }?.let {
                        ((monthlyProgress / it) * 100.0).toInt().coerceIn(0, 1000) // Use monthlyProgress (sum for filtered month)
                    }


                    // Values for KpiWithProgress (General logic for all KPIs)
                    val finalMonthlyTargetValue = aggregatedMonthlyTarget
                    val finalActualCurrentMonthProgressValue = currentMonthSumForAllUsers // Still useful for other calculations
                    var finalMonthlyPercentage = calculatedMonthlyPercentage // Now based on filtered month's progress
                    val finalMonthlyProgressForFilteredMonth = monthlyProgress // progress for filtered month
                    var finalCurrentProgressValue = totalProgress // Default to total sum of values

                    val kpiForDisplay = representativeKpi.copy(
                        name = (representativeKpi.name ?: "Unknown KPI")!!, // Provide default name if null, force non-null
                        annualTarget = aggregatedAnnualTarget,
                        monthlyTarget = aggregatedMonthlyTarget,
                        dailyTarget = aggregatedDailyTarget
                    )

                    // --- Special handling for "Loyalty" KPI with PERCENTAGE unit ---
                    if (kpiName.equals("Loyalty", ignoreCase = true) && representativeKpi.unit == com.example.kpitrackerapp.models.KpiUnit.PERCENTAGE) {
                        Log.d("KpiViewModel.aggregatedKpi", "Special handling for Loyalty KPI (Percentage).")
                        // Calculate the average of individual user percentages for the current calculation month.
                        val userIdsForThisKpiGroup = allAssignments.filter { it.kpiId in kpiIdsInGroup }.map { it.userId }.toSet()
                        val individualUserPercentages = mutableListOf<Double>()

                        userIdsForThisKpiGroup.forEach { userId ->
                            // Get this user's progress for any KPI within the "Loyalty" group for the calculation month
                            val userMonthlyProgressForGroup = allEntries.filter {
                                it.userId == userId &&
                                it.kpiId in kpiIdsInGroup && // kpiIdsInGroup are all KPIs named "Loyalty"
                                Calendar.getInstance().apply { time = it.date }.get(Calendar.YEAR) == calculationYear &&
                                (Calendar.getInstance().apply { time = it.date }.get(Calendar.MONTH) + 1) == calculationMonth
                            }.sumOf { it.value }

                            // Get the specific "Loyalty" KPI IDs this user is assigned to
                            val userSpecificLoyaltyKpiIds = allAssignments
                                .filter { it.userId == userId && it.kpiId in kpiIdsInGroup }
                                .map { it.kpiId }
                                .toSet()

                            // Get the actual KPI objects for these specific assignments from the full DB list
                            val userActualAssignedLoyaltyKpis = allKpisFromDb.filter { it.id in userSpecificLoyaltyKpiIds }

                            if (userActualAssignedLoyaltyKpis.isNotEmpty()) {
                                val userTotalMonthlyTargetForLoyalty = userActualAssignedLoyaltyKpis.sumOf { it.monthlyTarget ?: 0.0 }

                                if (userTotalMonthlyTargetForLoyalty > 0.0) {
                                    val percentage = (userMonthlyProgressForGroup / userTotalMonthlyTargetForLoyalty) * 100.0
                                    individualUserPercentages.add(percentage)
                                    Log.v("KpiViewModel.aggregatedKpi", "Loyalty - User $userId: Progress $userMonthlyProgressForGroup, Combined Target $userTotalMonthlyTargetForLoyalty, Percentage $percentage")
                                } else {
                                    // If total target is 0, user's percentage contribution is 0, unless progress is also 0 (then 0/0 -> 0)
                                    // or progress is non-zero (then progress/0 -> effectively infinite, but we'll cap or treat as 0 for averaging).
                                    individualUserPercentages.add(0.0) // Add 0% if target is 0 or less.
                                    Log.v("KpiViewModel.aggregatedKpi", "Loyalty - User $userId: Progress $userMonthlyProgressForGroup, Combined Target $userTotalMonthlyTargetForLoyalty (<=0). Adding 0% to average.")
                                }
                            } else {
                                // This should not happen if userIdsForThisKpiGroup is correctly derived from users who have assignments to kpiIdsInGroup.
                                Log.w("KpiViewModel.aggregatedKpi", "Loyalty - User $userId is in userIdsForThisKpiGroup but no specific Loyalty KPI objects found for them from allKpisFromDb.")
                            }
                        }

                        if (individualUserPercentages.isNotEmpty()) {
                            val averagePercentage = individualUserPercentages.average()
                            finalCurrentProgressValue = averagePercentage // This will be formatted with '%' by the adapter
                            // The monthlyPercentage (for the progress circle) might also need to be this average
                            // For now, the circle uses 'calculatedMonthlyPercentage' which is based on aggregated sum / aggregated target.
                            // If the circle also needs to show this average, then:
                            // finalMonthlyPercentage = averagePercentage.toInt().coerceIn(0, 1000)
                            Log.d("KpiViewModel.aggregatedKpi", "Loyalty - Average User Percentage: $averagePercentage. Set as currentProgressValue.")
                        } else {
                            Log.d("KpiViewModel.aggregatedKpi", "Loyalty - No individual user percentages to average.")
                            finalCurrentProgressValue = 0.0 // Or handle as appropriate
                        }
                    }
                    // --- End Special handling ---

                    // Recalculate remaining and daily rate based on the AGGREGATED monthly target
                    // and the ACTUAL CURRENT month's aggregated progress (currentMonthSumForAllUsers)
                    // This part is for the detailed card view's "remaining" and "daily req" fields,
                    // which should reflect current reality, not filtered month reality.
                    if (aggregatedMonthlyTarget > 0) { // Only calculate if there's a target
                        remainingValue = (aggregatedMonthlyTarget - currentMonthSumForAllUsers).coerceAtLeast(0.0)
                        remainingDays = (daysInMonth - currentDay).coerceAtLeast(0)
                        if (remainingDays > 0) {
                            dailyRate = remainingValue / remainingDays
                        } else {
                            dailyRate = if (remainingValue > 0) Double.POSITIVE_INFINITY else 0.0 // or null
                        }
                    } else {
                        remainingValue = null
                        remainingDays = null
                        dailyRate = null
                    }

                    // --- Calculate Current Period Target and Achievement for Aggregated KPI ---
                    var currentPeriodTarget: Double? = null
                    var currentPeriodAchievement: Int? = null

                    if (aggregatedMonthlyTarget > 0) {
                        // Calculate current period target: (Monthly Target × Days Elapsed) ÷ Days in Month
                        currentPeriodTarget = (aggregatedMonthlyTarget * currentDay) / daysInMonth

                        // Calculate current period achievement: (Current Month Progress ÷ Current Period Target) × 100
                        if (currentPeriodTarget > 0) {
                            currentPeriodAchievement = ((currentMonthSumForAllUsers / currentPeriodTarget) * 100).toInt().coerceIn(0, 1000)
                        }
                    }

                    KpiWithProgress(
                        kpi = kpiForDisplay,
                        userId = "Aggregated", // Provide a default non-null String for aggregated view
                        currentProgressValue = finalCurrentProgressValue, // Use the potentially adjusted value
                        monthlyTargetValue = finalMonthlyTargetValue, // Aggregated monthly target
                        currentMonthProgressValue = finalMonthlyProgressForFilteredMonth, // Sum for the *filtered* month
                        actualCurrentMonthProgressValue = finalActualCurrentMonthProgressValue, // Sum for the *actual current* month
                        monthlyPercentage = finalMonthlyPercentage, // Percentage based on *filtered* month's progress (or average if changed above)
                        progressEntries = groupEntries,
                        remainingMonthlyValue = remainingValue, // Based on *actual current* month
                    remainingMonthlyPercentage = null, // This was always null
                remainingDaysInMonth = remainingDays ?: 0, // Provide default value for nullable Int
                requiredDailyRate = dailyRate,
                daysSinceLastUpdate = daysSinceUpdate,
                currentPeriodTarget = currentPeriodTarget,  // Target that should be achieved by current date
                currentPeriodAchievement = currentPeriodAchievement // Percentage of current period target achieved
            )
        }.also {
             Log.d("KpiViewModel.aggregatedKpi", "Calculated ${it.size} aggregated KpiWithProgress items.")
        }
    }
}.stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000), emptyList())
    // --- End Aggregated KpiWithProgress Flow ---


    // --- Function to update the selected filter month ---
    fun setSelectedFilterMonth(calendar: Calendar?) {
        _selectedFilterMonth.value = calendar
    }

    // --- Function to update the selected user ID filter for the LIST screen ---
    fun setSelectedUserIdForList(userId: String?) {
        Log.d("KpiViewModel", "Setting selected user ID filter for LIST to: $userId")
        _selectedUserIdForList.value = userId
    }


    // --- Report Data Flow ---
    // Report data for the selected KPI and date range (Aggregated across all users)
    @OptIn(FlowPreview::class)
    val reportData: Flow<List<KpiReportData>> = combine(
        _selectedKpiId,
        _reportDateRange
    ) { kpiId, dateRange ->
        // This combine emits a Pair<String?, Pair<Long?, Long?>> whenever selectedKpiId or reportDateRange changes.
        // This Pair will be the input to flatMapLatest.
        Pair(kpiId, dateRange)
    }.flatMapLatest { (kpiId, dateRange) -> // Destructure the Pair
        val startDateMillis = dateRange.first
        val endDateMillis = dateRange.second

        // Check if KPI ID is present AND date range is valid
        if (kpiId == null || startDateMillis == null || endDateMillis == null) {
            Log.d("KpiViewModel.reportData", "Missing kpiId or date range null. Emitting empty list.")
            flowOf(emptyList()) // Emit empty list as a Flow
        } else {
            Log.d("KpiViewModel.reportData", "Fetching aggregated report data for KPI: $kpiId, Range: $startDateMillis-$endDateMillis")

            // Fetch the specific KPI - this returns a Flow<Kpi?>
            kpiDao.getKpiById(kpiId).flatMapLatest { kpi: Kpi? ->
                if (kpi == null) {
                    Log.w("KpiViewModel.reportData", "KPI with ID $kpiId not found. Emitting empty list.")
                    flowOf(emptyList()) // KPI not found, emit empty list as a Flow
                } else {
                    // Get all KPIs with the same name
                    kpiDao.getKpisByName(kpi.name).flatMapLatest { kpisWithSameName ->
                        // Log the number of KPIs with the same name for debugging
                        Log.d("KpiViewModel.reportData", "Found ${kpisWithSameName.size} KPIs with name '${kpi.name}'")

                        // Get all user IDs that have these KPIs assigned
                        val kpiIds = kpisWithSameName.map { it.id }
                        userKpiAssignmentDao.getUserIdsForKpis(kpiIds).flatMapLatest { userIds ->
                            // Log the number of users for debugging
                            Log.d("KpiViewModel.reportData", "Found ${userIds.size} users with KPIs named '${kpi.name}'")
                            userIds.forEach { userId ->
                                Log.d("KpiViewModel.reportData", "User $userId has KPI '${kpi.name}' assigned")
                            }

                            // Get user data for each user separately
                            userDao.getAllUsers().flatMapLatest { users ->
                                Log.d("KpiViewModel.reportData", "Found ${users.size} users")

                                // Process each user and collect their report data
                                val reportDataList = mutableListOf<KpiReportData>()

                                // Process users one by one
                                for (user in users) {
                                    Log.d("KpiViewModel.reportData", "Processing user: ${user.name} (${user.id})")

                                    // Check if this user is assigned to ANY KPI with the same name
                                    var isUserAssigned = false
                                    for (kpiWithSameName in kpisWithSameName) {
                                        val assignment = userKpiAssignmentDao.getAssignment(user.id, kpiWithSameName.id).first()
                                        if (assignment != null) {
                                            isUserAssigned = true
                                            break
                                        }
                                    }

                                    if (isUserAssigned) {
                                        // User is assigned to at least one KPI with this name, get their data
                                        // We'll use the selected KPI for targets, but aggregate data across all KPIs with the same name
                                        var daily = 0.0
                                        var monthly = 0.0
                                        var quarterly = 0.0
                                        var annual = 0.0

                                        // Aggregate data across all KPIs with the same name
                                        for (kpiWithSameName in kpisWithSameName) {
                                            daily += progressEntryDao.getSumForDate(kpiWithSameName.id, user.id, endDateMillis).first() ?: 0.0
                                            monthly += progressEntryDao.getSumForDateRange(kpiWithSameName.id, user.id, getStartOfMonth(endDateMillis).timeInMillis, endDateMillis).first() ?: 0.0
                                            quarterly += progressEntryDao.getSumForDateRange(kpiWithSameName.id, user.id, getStartOfQuarter(endDateMillis).timeInMillis, endDateMillis).first() ?: 0.0
                                            annual += progressEntryDao.getSumForDateRange(kpiWithSameName.id, user.id, startDateMillis, endDateMillis).first() ?: 0.0
                                        }

                                        // Calculate percentages
                                        val dailyPerc = kpi.dailyTarget?.takeIf { it > 0 }?.let { (daily / it * 100).toInt().coerceIn(0, 1000) }
                                        val monthlyPerc = kpi.monthlyTarget?.takeIf { it > 0 }?.let { (monthly / it * 100).toInt().coerceIn(0, 1000) }
                                        val quarterlyPerc = kpi.quarterlyTarget?.takeIf { it > 0 }?.let { (quarterly / it * 100).toInt().coerceIn(0, 1000) }
                                        val annualPerc = if (kpi.annualTarget > 0) (annual / kpi.annualTarget * 100).toInt().coerceIn(0, 1000) else 0

                                        Log.d("KpiViewModel.reportData", "User ${user.name} (${user.id}) Sums for KPI ${kpi.name}: Daily: $daily, Monthly: $monthly, Quarterly: $quarterly, Annual: $annual")

                                        // Create a KpiReportData object for this user
                                        val reportData = KpiReportData(
                                            userId = user.id,
                                            userName = user.name,
                                            kpiId = kpi.id,
                                            kpiName = kpi.name,
                                            kpiUnit = kpi.unit,
                                            dailyTarget = kpi.dailyTarget,
                                            dailyAchieved = daily,
                                            dailyPercentage = dailyPerc,
                                            monthlyTarget = kpi.monthlyTarget,
                                            monthlyAchieved = monthly,
                                            monthlyPercentage = monthlyPerc,
                                            quarterlyTarget = kpi.quarterlyTarget,
                                            quarterlyAchieved = quarterly,
                                            quarterlyPercentage = quarterlyPerc,
                                            annualTarget = kpi.annualTarget,
                                            annualAchieved = annual,
                                            annualPercentage = annualPerc
                                        )

                                        reportDataList.add(reportData)
                                    }
                                }

                                // Return the list of report data
                                flowOf(reportDataList)
                            }
                        } // This inner combine returns Flow<List<KpiReportData>>
                    }
                }
            } // This flatMapLatest returns Flow<List<KpiReportData>>
        }
    }

    // Trend data for the selected KPI and date range (Aggregated across all users)
    val trendData: Flow<Map<String, List<KpiProgressEntry>>> = combine(
        _selectedKpiId,
        _reportDateRange
    ) { kpiId, dateRange ->
        val startDateMillis = dateRange.first
        val endDateMillis = dateRange.second
        Log.d("KpiViewModel", "trendData combine triggered: kpiId=$kpiId, start=$startDateMillis, end=$endDateMillis")
        Pair(kpiId, dateRange) // Use a Pair
    }.flatMapLatest { (kpiId, dateRangePair) -> // Corrected Destructure Pair
        val startDateMillis = dateRangePair.first
        val endDateMillis = dateRangePair.second

        if (kpiId == null || startDateMillis == null || endDateMillis == null) {
            Log.d("KpiViewModel", "trendData flatMapLatest: Missing parameters, emitting emptyMap")
            flowOf(emptyMap()) // Emit empty map if any required parameter is missing
        } else {
            Log.d("KpiViewModel", "trendData flatMapLatest: Fetching entries for KPI: $kpiId, Range: $startDateMillis-$endDateMillis")

            // First get the KPI to get its name
            kpiDao.getKpiById(kpiId).flatMapLatest { kpi ->
                if (kpi == null) {
                    flowOf(emptyMap())
                } else {
                    // Fetch all entries for all KPIs with this name across ALL users
                    progressEntryDao.getEntriesForKpiNameAggregated(kpi.name).map { allEntriesForKpi: List<KpiProgressEntry> ->
                        // Filter by date range and group by user ID (still group by user ID for the chart)
                        val filteredEntries: List<KpiProgressEntry> = allEntriesForKpi.filter { entry: KpiProgressEntry ->
                            entry.date.time in startDateMillis..endDateMillis
                        }

                        // Log the number of entries and unique user IDs for debugging
                        val userIds: List<String> = filteredEntries.mapNotNull { entry: KpiProgressEntry -> entry.userId }.distinct()
                        Log.d("KpiViewModel", "trendData: Found ${filteredEntries.size} entries for ${userIds.size} users in date range")

                        // Log details for each user
                        for (userId in userIds) {
                            val entriesForUser: List<KpiProgressEntry> = filteredEntries.filter { entry: KpiProgressEntry -> entry.userId == userId }
                            Log.d("KpiViewModel", "trendData: User $userId has ${entriesForUser.size} entries")
                        }

                        // Group by user ID for the chart
                        filteredEntries.groupBy { entry: KpiProgressEntry -> entry.userId ?: "Unassigned" } // Handle nullable userId
                    }
                }
            }
        }
    }


    // --- Actions ---

    // Removed insertUser function (User management might be handled elsewhere or removed)
    // Removed getUserIdsForKpi function

    // Simple insert function
    fun insertKpi(kpi: Kpi) = viewModelScope.launch {
        kpiDao.insertKpi(kpi)
        Log.d("KpiViewModel", "Inserted KPI ${kpi.id}")
    }

    // Simple update function
    fun updateKpi(kpi: Kpi) = viewModelScope.launch {
        kpiDao.updateKpi(kpi)
        Log.d("KpiViewModel", "Updated KPI ${kpi.id}")
    }

    // Updated to require userId and remove optional userIdentifier from signature
    fun addProgressEntry(kpiId: String, userId: String, value: Double, date: Date = Date(System.currentTimeMillis())) = viewModelScope.launch {
        Log.d("KpiViewModel", "Adding progress entry for KPI ID: $kpiId, User ID: $userId, Value: $value, Date: $date") // Add logging for added entry details
        val entry = KpiProgressEntry(
            kpiId = kpiId,
            userId = userId, // Use the provided userId
            value = value,
            date = date
            // userIdentifier is kept in the model but not set here unless needed for import logic specifically
        )
        progressEntryDao.insertEntry(entry)

        // --- Check if target is achieved and send notification ---
        // Get the KPI details
        val kpi = kpiDao.getKpiById(kpiId).firstOrNull() // Use firstOrNull for safety
        val monthlyTarget = kpi?.monthlyTarget // Assign to local val first
        if (kpi != null && monthlyTarget != null && monthlyTarget > 0) { // Use the local val in the check
            // Get the current month and year
            val calendar = Calendar.getInstance()
            val currentYear = calendar.get(Calendar.YEAR)
            val currentMonth = calendar.get(Calendar.MONTH) + 1 // Calendar.MONTH is 0-based

            // Get the updated sum for the current month for THIS USER AFTER adding the entry
            val monthlySum = progressEntryDao.getSumForMonth(
                kpiId,
                userId, // Pass userId
                currentYear.toString(),
                String.format("%02d", currentMonth)
            ).firstOrNull() ?: 0.0 // Get the latest sum for this user

            // Check if the target is met
            // TODO: Add logic to prevent sending notification repeatedly if target was already met this month
            if (monthlySum >= monthlyTarget) { // Use the local val here
                NotificationHelper.showTargetAchievedNotification(
                    context, // Use the stored context
                    kpi.id,
                    kpi.name,
                    monthlyTarget, // Use the local val here
                    monthlySum // Show the actual achieved value
                )
            } // Closes inner if (monthlySum >= monthlyTarget)
        } // Closes outer if (kpi != null && ...)
    } // Closes launch

    // --- REMOVED: updateOwnerImagePath ---
    // This operated based on ownerName, which is removed. User image paths should be updated via UserDao.

    // --- REMOVED: updateUserCardColors ---
    // This operated based on ownerName. Card colors are per-KPI now.

    // --- Actions ---
    // Updated delete function - needs to delete assignments AND user-specific progress
    fun delete(kpi: Kpi) = viewModelScope.launch {
        // 1. Find all users assigned to this KPI
        val assignedUserIds = userKpiAssignmentDao.getUserIdsForKpi(kpi.id).first() // Get user IDs

        // 2. Delete progress entries for each assigned user for this KPI
        assignedUserIds.forEach { userId ->
            progressEntryDao.deleteEntriesForKpi(kpi.id, userId) // Pass userId
            Log.d("KpiViewModel", "Deleted progress for KPI ${kpi.id} for User $userId")
        }

        // 3. Delete all assignments for this KPI
        userKpiAssignmentDao.deleteAssignmentsForKpi(kpi.id)
        Log.d("KpiViewModel", "Deleted assignments for KPI ${kpi.id}")

        // 4. Delete the KPI itself
        kpiDao.deleteKpi(kpi)
        Log.d("KpiViewModel", "Deleted KPI definition ${kpi.id}")
    }

    // Removed deleteUser function (User management might be handled elsewhere or removed)

    // Function to delete a single progress entry
    fun deleteProgressEntry(entry: KpiProgressEntry) = viewModelScope.launch {
        progressEntryDao.deleteEntry(entry) // Assuming DAO has a deleteEntry function
    }

    // Function to update a single progress entry
    fun updateProgressEntry(entry: KpiProgressEntry) = viewModelScope.launch {
        progressEntryDao.updateEntry(entry) // Assuming DAO has an updateEntry function
    }

    // Function to clear all progress entries for a specific KPI and USER
    fun clearProgressForKpi(kpiId: String, userId: String) = viewModelScope.launch {
        progressEntryDao.deleteEntriesForKpi(kpiId, userId) // Pass userId
        Log.d("KpiViewModel", "Cleared progress entries for KPI ID: $kpiId, User ID: $userId")
    }

    // Function to clear progress entries for a specific KPI, USER, and month
    fun clearProgressForMonth(kpiId: String, userId: String, year: Int, month: Int) = viewModelScope.launch {
        val yearStr = year.toString()
        val monthStr = String.format("%02d", month) // Ensure month is two digits (e.g., "01", "07")
        progressEntryDao.deleteEntriesForMonth(kpiId, userId, yearStr, monthStr) // Pass userId
        Log.d("KpiViewModel", "Cleared progress entries for KPI ID: $kpiId, User ID: $userId, Month: $yearStr-$monthStr")
    }

    // Removed setUserFilter function

    fun setSelectedKpiIdForReport(kpiId: String?) {
        _selectedKpiId.value = kpiId
    }

    // --- Report Date Range Update ---
    fun setReportStartDate(dateInMillis: Long) {
        val currentEnd = _reportDateRange.value.second
        _reportDateRange.value = getStartOfDayMillis(dateInMillis) to currentEnd
    }

    fun setReportEndDate(dateInMillis: Long) {
        val currentStart = _reportDateRange.value.first
        _reportDateRange.value = currentStart to getEndOfDayMillis(dateInMillis)
    }

    // --- Aggregation Functions (Used by Detail Screen - Now require userId) ---
    fun getMonthlySum(kpiId: String, userId: String, year: Int, month: Int): Flow<Double?> {
        val yearStr = year.toString()
        val monthStr = String.format("%02d", month)
        return progressEntryDao.getSumForMonth(kpiId, userId, yearStr, monthStr) // Pass userId
    }

    fun getQuarterlySum(kpiId: String, userId: String, year: Int, quarter: Int): Flow<Double?> {
        val yearStr = year.toString()
        val (startMonth, endMonth) = when (quarter) {
            1 -> 1 to 3
            2 -> 4 to 6
            3 -> 7 to 9
            4 -> 10 to 12
            else -> throw IllegalArgumentException("Invalid quarter: $quarter")
        }
        return progressEntryDao.getSumForQuarter(kpiId, userId, yearStr, startMonth, endMonth) // Pass userId
    }

    fun getYearlySum(kpiId: String, userId: String, year: Int): Flow<Double?> {
        val yearStr = year.toString()
        return progressEntryDao.getSumForYear(kpiId, userId, yearStr) // Pass userId
    }

    fun getDailySum(kpiId: String, userId: String, date: Date): Flow<Double?> {
        return progressEntryDao.getSumForDate(kpiId, userId, getStartOfDayMillis(date.time)) // Pass userId
    }

    // Get the overall total progress for a KPI and User
    fun getTotalProgress(kpiId: String, userId: String): Flow<Double?> {
        return progressEntryDao.getTotalValueForKpi(kpiId, userId) // Pass userId
    }

    // Get a single entry for a specific date and User
    fun getEntryForDate(kpiId: String, userId: String, date: Date): Flow<KpiProgressEntry?> {
        // Ensure we compare only the date part by getting the start of the day
        val startOfDayMillis = getStartOfDayMillis(date.time)
        return progressEntryDao.getEntryForDate(kpiId, userId, startOfDayMillis) // Pass userId
    }

    // Get a single entry by its ID
    fun getEntryById(entryId: String): Flow<KpiProgressEntry?> {
        return progressEntryDao.getEntryById(entryId)
    }

    // Get progress between two dates
    fun getProgressBetweenDates(kpiId: String, userId: String, startDate: Date, endDate: Date): Flow<Double?> {
        val startMillis = getStartOfDayMillis(startDate.time)
        val endMillis = getEndOfDayMillis(endDate.time)
        return progressEntryDao.getSumForDateRange(kpiId, userId, startMillis, endMillis)
    }

    // --- Date Helper Functions (Using UTC consistently) ---
    private fun getStartOfDayMillis(millis: Long): Long {
        val calendar = Calendar.getInstance(TimeZone.getTimeZone("UTC")).apply {
            timeInMillis = millis
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }
        return calendar.timeInMillis
    }

    private fun getEndOfDayMillis(millis: Long): Long {
        val calendar = Calendar.getInstance(TimeZone.getTimeZone("UTC")).apply {
            timeInMillis = millis
            set(Calendar.HOUR_OF_DAY, 23)
            set(Calendar.MINUTE, 59)
            set(Calendar.SECOND, 59)
            set(Calendar.MILLISECOND, 999)
        }
        return calendar.timeInMillis
    }

     // Overloaded helper for specific calendar
     private fun getStartOfYearMillis(millis: Long): Long {
        val calendar = Calendar.getInstance(TimeZone.getTimeZone("UTC")).apply {
            timeInMillis = millis
            set(Calendar.DAY_OF_YEAR, 1)
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }
        return calendar.timeInMillis
    }

     // Helper to get end of year
     private fun getEndOfYearMillis(millis: Long): Long {
        val calendar = Calendar.getInstance(TimeZone.getTimeZone("UTC")).apply {
            timeInMillis = millis
            set(Calendar.MONTH, 11) // December
            set(Calendar.DAY_OF_YEAR, getActualMaximum(Calendar.DAY_OF_YEAR)) // Last day of year
            set(Calendar.HOUR_OF_DAY, 23)
            set(Calendar.MINUTE, 59)
            set(Calendar.SECOND, 59)
            set(Calendar.MILLISECOND, 999)
        }
        return calendar.timeInMillis
     }


    private fun getStartOfMonth(millis: Long): Calendar {
         return Calendar.getInstance(TimeZone.getTimeZone("UTC")).apply {
            timeInMillis = millis
            set(Calendar.DAY_OF_MONTH, 1)
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }
    }

    private fun getEndOfMonth(millis: Long): Calendar {
         return Calendar.getInstance(TimeZone.getTimeZone("UTC")).apply {
            timeInMillis = millis
            set(Calendar.DAY_OF_MONTH, getActualMaximum(Calendar.DAY_OF_MONTH))
            set(Calendar.HOUR_OF_DAY, 23)
            set(Calendar.MINUTE, 59)
            set(Calendar.SECOND, 59)
            set(Calendar.MILLISECOND, 999)
        }
    }

     private fun getStartOfQuarter(millis: Long): Calendar {
         val calendar = Calendar.getInstance(TimeZone.getTimeZone("UTC")).apply { timeInMillis = millis }
         val currentMonth = calendar.get(Calendar.MONTH)
         val startMonth = (currentMonth / 3) * 3
         calendar.set(Calendar.MONTH, startMonth)
         calendar.set(Calendar.DAY_OF_MONTH, 1)
         calendar.set(Calendar.HOUR_OF_DAY, 0)
         calendar.set(Calendar.MINUTE, 0)
         calendar.set(Calendar.SECOND, 0)
         calendar.set(Calendar.MILLISECOND, 0)
         return calendar
    }

     private fun getEndOfQuarter(millis: Long): Calendar {
         val calendar = Calendar.getInstance(TimeZone.getTimeZone("UTC")).apply { timeInMillis = millis }
         val currentMonth = calendar.get(Calendar.MONTH)
         val endMonth = (currentMonth / 3) * 3 + 2 // Last month of the quarter
         calendar.set(Calendar.MONTH, endMonth)
         calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
         calendar.set(Calendar.HOUR_OF_DAY, 23)
         calendar.set(Calendar.MINUTE, 59)
         calendar.set(Calendar.SECOND, 59)
         calendar.set(Calendar.MILLISECOND, 999)
         return calendar
    }

    // --- Excel Import Data Handling (Using LiveData) ---
    fun setTemporaryExcelData(data: List<OcrResultItem>?) {
        Log.d("KpiViewModel", "Setting temporary LiveData for Excel. Count: ${data?.size ?: "null"}")
        // Use postValue if called from background thread, or value if on main thread.
        // Since ExcelImportActivity calls this after IO, postValue is safer.
        _temporaryExcelData.postValue(data)
    }

    fun clearTemporaryExcelData() {
        Log.d("KpiViewModel", "Clearing temporary LiveData for Excel.")
        _temporaryExcelData.postValue(null) // Use postValue here too for consistency
    }

    // Handles the actual import logic, called from ExcelReviewActivity
    fun importReviewedExcelData(
        kpiId: String,
        itemsToImport: List<OcrResultItem>,
        aggregationChoice: String,
        filteredUser: String
    ) = viewModelScope.launch {
        Log.d("KpiViewModel.importReviewedExcelData", "Entering import. KPI ID: $kpiId, Aggregation: $aggregationChoice, User Filter: $filteredUser") // Added log
        var importedCount = 0
        var processedCount = 0
        val errors = mutableListOf<String>()

        Log.d("KpiViewModel", "Importing reviewed data. KPI: $kpiId, Items: ${itemsToImport.size}, Choice: $aggregationChoice, User Filter: $filteredUser")

        if (aggregationChoice == ExcelReviewActivity.AGGREGATION_AVERAGE && itemsToImport.isNotEmpty()) {
            // --- Calculate Average ---
            // Average only makes sense if importing for a SINGLE user.
            if (filteredUser == ExcelReviewActivity.ALL_USERS_FILTER) {
                val errorMsg = "Cannot import average for 'All Users'. Select a specific user."
                Log.e("KpiViewModel", errorMsg)
                errors.add(errorMsg)
                // TODO: Return error status to UI
            } else {
                // Assuming filteredUser is the actual userId when a specific user is selected for average
                val averageValue = itemsToImport.map { it.value }.average()
                val latestDate = itemsToImport.maxByOrNull { it.date }?.date ?: Date(System.currentTimeMillis())
                // val userIdForAverage = filteredUser // Assume filteredUser is the userId // Removed intermediate variable

                Log.d("KpiViewModel", "Calculated Average: $averageValue for User: $filteredUser on Date: $latestDate") // Use filteredUser in log
                // Ensure filteredUser is not null or blank before attempting to add entry
                if (!filteredUser.isNullOrBlank()) {
                    filteredUser.let { nonNullUserId ->
                        try {
                            Log.d("KpiViewModel.importReviewedExcelData", "Attempting to add average progress entry for user: $nonNullUserId. KPI ID: $kpiId, Value: $averageValue, Date: $latestDate")
                            addProgressEntry(kpiId = kpiId, userId = nonNullUserId, value = averageValue, date = latestDate)
                            importedCount = 1
                            processedCount = itemsToImport.size
                            Log.d("KpiViewModel", "Imported single average entry via ViewModel for User $nonNullUserId.")
                        } catch (e: Exception) {
                            val errorMsg = "Error importing calculated average via ViewModel for User $nonNullUserId: ${e.message}"
                            Log.e("KpiViewModel", errorMsg, e)
                            errors.add(errorMsg)
                        }
                    }
                } else {
                    val errorMsg = "Cannot import average: User ID (filteredUser) is null or blank." // Updated error message
                    Log.e("KpiViewModel", errorMsg)
                    errors.add(errorMsg)
                    // Ensure processedCount is incremented even if import fails due to blank userId
                    if (itemsToImport.isNotEmpty()) processedCount = itemsToImport.size
                }
            }
        } else {
            // --- Import Individually ---
            itemsToImport.forEach { item ->
                processedCount++ // Increment processed count for each item attempted
                val targetUserId: String? = when {
                    filteredUser != ExcelReviewActivity.ALL_USERS_FILTER -> filteredUser
                    item.userIdentifier?.isNotBlank() == true -> { // Use safe call ?. and check for true
                        // Placeholder for actual user lookup by item.userIdentifier (name/email etc.)
                        // For now, if it's "All Users" and userIdentifier is present, we'd try to find the ID.
                        // This example assumes item.userIdentifier *could* be a direct ID or needs lookup.
                        // As lookup isn't implemented, we'll log and potentially skip or use a default if applicable.
                        Log.w("KpiViewModel", "User lookup by item.userIdentifier ('${item.userIdentifier}') for 'All Users' not fully implemented. This might lead to issues if identifier is not a direct User ID.")
                        item.userIdentifier // Tentatively use it, assuming it might be an ID in some cases.
                    }
                    else -> {
                        Log.w("KpiViewModel", "Skipping entry for Date=${item.date} - Missing user identifier or blank when importing for 'All Users'.")
                        null
                    }
                }

                if (targetUserId != null && targetUserId.isNotBlank()) {
                    try {
                        // Use let to ensure targetUserId is non-null String within this block
                        targetUserId.let { userId ->
                            Log.d("KpiViewModel.importReviewedExcelData", "Attempting to add individual progress entry for user: $userId. KPI ID: $kpiId, Value: ${item.value}, Date: ${item.date}")
                            addProgressEntry(kpiId = kpiId, userId = userId, value = item.value, date = item.date)
                            importedCount++
                        }
                    } catch (e: Exception) {
                        val errorMsg = "Error importing individual entry for Date=${item.date}, User=$targetUserId: ${e.message}"
                        Log.e("KpiViewModel", errorMsg, e)
                        errors.add(errorMsg)
                    }
                } else {
                    val reason = if (targetUserId == null) "is null" else "is blank"
                    Log.w("KpiViewModel", "Skipping individual import for Date=${item.date}. Reason: targetUserId $reason.")
                    if (targetUserId != null) { // Only add error if it was non-null but blank
                        errors.add("User ID for entry Date=${item.date} is blank.")
                    }
                }
            } // End of forEach loop
        } // End of else for individual import

        // Log final status
        if (errors.isNotEmpty()) {
            Log.w("KpiViewModel", "Import finished with ${errors.size} errors.")
        } else {
            Log.i("KpiViewModel", "Import finished successfully. Imported: $importedCount, Processed: $processedCount")
        }
        // Clear the temp data after import attempt
        clearTemporaryExcelData() // Call within the class
    }
    // --- End Excel Import Data Handling ---


     // Function to get a single KPI by ID (needed for Review Activity)
     fun getKpiById(kpiId: String): Flow<Kpi?> {
         return kpiDao.getKpiById(kpiId)
     }

    // Removed getMasterKpis function reference

    // --- KPI Duplication ---
    fun duplicateKpi(originalKpi: Kpi) = viewModelScope.launch {
        try {
            // 1. Generate a new unique ID for the duplicated KPI
            val newKpiId = UUID.randomUUID().toString()

            // 2. Create the duplicated KPI object with the new ID and modified name
            val duplicatedKpi = originalKpi.copy(
                id = newKpiId, // Assign the newly generated ID
                name = "${originalKpi.name} (Copy)" // Append "(Copy)" to the name
                // Other fields are copied directly
            )

            // 3. Insert the new KPI definition
            kpiDao.insertKpi(duplicatedKpi)
            Log.d("KpiViewModel", "Inserted duplicated KPI definition with ID: $newKpiId")

            // 4. REMOVED: Fetching and duplicating progress entries.
            // Duplicating progress is complex due to user context.

            Log.i("KpiViewModel", "Successfully duplicated KPI definition '${originalKpi.name}' (New ID: $newKpiId). Progress entries were NOT duplicated.")

        } catch (e: Exception) {
            Log.e("KpiViewModel", "Error duplicating KPI: ${originalKpi.name}", e)
            // Optionally, provide feedback to the user via a LiveData/StateFlow event
        }
    }

    // --- REMOVED: copyKpiToOwner function ---

    // --- REMOVED: assignKpiToUser function ---

    // --- NEW: Function to update User card colors ---
    fun updateUserCardColors(userId: String, topColor: Int?, bottomColor: Int?) = viewModelScope.launch {
        try {
            userDao.updateUserColors(userId, topColor, bottomColor)
            Log.d("KpiViewModel", "Updated card colors for user ID: $userId (Top: $topColor, Bottom: $bottomColor)")
        } catch (e: Exception) {
            Log.e("KpiViewModel", "Error updating card colors for user ID: $userId", e)
            // Optionally notify UI of error
        }
    }
    // --- End Function to update User card colors ---

    // --- Function to get all KPIs as a List (for one-time operations) ---
    suspend fun getAllKpisList(): List<Kpi> {
        return kpiDao.getAllKpis().first() // Collect the first emission from the Flow
    }
    // --- End Function to get all KPIs as a List ---

    // --- REMOVED: Old function to update individual KPI card color ---
    /*
    fun updateKpiColor(kpiId: String, colorHex: String?) = viewModelScope.launch {
        try {
            // This function is removed as individual_card_color_hex is removed from Kpi model
            // kpiDao.updateKpiColor(kpiId, colorHex) // Call DAO function
            Log.d("KpiViewModel", "Updated individual card color to $colorHex for KPI ID: $kpiId")
        } catch (e: Exception) {
            Log.e("KpiViewModel", "Error updating individual card color for KPI ID: $kpiId", e)
            // Optionally notify UI of error
        }
    }
    */
    // --- End REMOVED Function ---

    // --- REMOVED: Function to update individual KPI card gradient colors ---
    /*
    fun updateKpiGradientColors(kpiId: String, topColorHex: String?, bottomColorHex: String?) = viewModelScope.launch {
        try {
            kpiDao.updateKpiGradientColors(kpiId, topColorHex, bottomColorHex) // Call NEW DAO function
            Log.d("KpiViewModel", "Updated gradient colors (Top: $topColorHex, Bottom: $bottomColorHex) for KPI ID: $kpiId")
        } catch (e: Exception) {
            Log.e("KpiViewModel", "Error updating gradient colors for KPI ID: $kpiId", e)
            // Optionally notify UI of error
        }
    }
    */
    // --- End REMOVED Function ---

    // --- NEW: Function to update single KPI card color ---
    fun updateKpiCardColor(kpiId: String, colorHex: String?) = viewModelScope.launch {
        try {
            // Assuming KpiDao will have a function like this:
            kpiDao.updateKpiCardColor(kpiId, colorHex)
            Log.d("KpiViewModel", "Updated card color to $colorHex for KPI ID: $kpiId")
        } catch (e: Exception) {
            Log.e("KpiViewModel", "Error updating card color for KPI ID: $kpiId", e)
            // Optionally notify UI of error
        }
    }
    // --- End Function to update single card color ---

    // --- NEW: Function to assign a KPI to a user based on the user's name (Creates user if not found) ---
    fun assignKpiToUserByName(kpiId: String, ownerName: String) = viewModelScope.launch(Dispatchers.IO) {
        try {
            // 1. Find the user by name (case-insensitive)
            var user = userDao.getUserByName(ownerName).firstOrNull()
            var userIdToAssign: String

            if (user == null) {
                // 2a. User not found - Create a new user
                Log.i("KpiViewModel", "User '$ownerName' not found. Creating new user.")
                val newUserId = UUID.randomUUID().toString()
                val newUser = User(
                    id = newUserId,
                    name = ownerName,
                    imagePath = null, // Explicitly set null
                    topColor = null,  // Explicitly set null
                    bottomColor = null // Explicitly set null
                )
                userDao.insertUser(newUser) // Insert the new user
                userIdToAssign = newUserId // Use the new user's ID for assignment
                Log.i("KpiViewModel", "Created and inserted new user '$ownerName' with ID: $newUserId")
            } else {
                // 2b. User found - Use existing user's ID
                userIdToAssign = user.id
                Log.i("KpiViewModel", "Found existing user '$ownerName' with ID: $userIdToAssign")
            }

            // 3. Create the assignment object using the determined userId
            val assignment = UserKpiAssignment(
                userId = userIdToAssign,
                kpiId = kpiId
                // id is auto-generated by Room
            )

            // 4. Insert the assignment
            userKpiAssignmentDao.insertAssignment(assignment)
            Log.i("KpiViewModel", "Successfully assigned KPI $kpiId to user ID $userIdToAssign (Name: '$ownerName')")

            // Optional: Handle potential existing assignments if needed (e.g., delete old ones if owner changes during edit)

        } catch (e: Exception) {
            Log.e("KpiViewModel", "Error assigning KPI $kpiId to user '$ownerName'", e)
        }
    }
    // --- End Function to assign KPI to user by name ---


    // --- REMOVED: updateImagePathForOwner ---
    // User images are handled via UserDao now.

    // --- NEW: Function to delete a user and all their assignments ---
    fun deleteUserAndAssignments(userId: String) = viewModelScope.launch(Dispatchers.IO) { // Use IO dispatcher for DB operations
        try {
            // 1. Get all assignments and filter for the current user to find their KPI IDs
            // This must be done BEFORE deleting the assignments.
            val allAssignments = userKpiAssignmentDao.getAllAssignments().first() // Get current list of all assignments
            val kpiIdsForUser = allAssignments.filter { it.userId == userId }.map { it.kpiId }
            Log.d("KpiViewModel", "Found ${kpiIdsForUser.size} KPIs assigned to user ID: $userId for progress deletion.")

            // 2. Delete progress entries for each of these KPIs for this specific user
            kpiIdsForUser.forEach { kpiId ->
                progressEntryDao.deleteEntriesForKpi(kpiId, userId) // Uses existing DAO method
                Log.d("KpiViewModel", "Deleted progress entries for KPI ID: $kpiId, User ID: $userId")
            }

            // 3. Delete all assignments associated with the user
            userKpiAssignmentDao.deleteAssignmentsForUser(userId)
            Log.d("KpiViewModel", "Deleted assignments for user ID: $userId")

            // 4. Delete the user themselves
            userDao.deleteUser(userId) // Assuming this method in UserDao takes the userId String
            Log.i("KpiViewModel", "Successfully deleted user, assignments, and progress for user ID: $userId")
        } catch (e: Exception) {
            Log.e("KpiViewModel", "Error deleting user data for user ID: $userId", e)
            // Optionally notify the UI about the error using a LiveData or StateFlow event
        }
    }
    // --- End Function to delete user and assignments ---


    // --- NEW: Function to get data needed for user export ---
    // Mark function as suspend as it calls suspend functions (.first())
    suspend fun getUserProgressEntriesForExport(userId: String): List<Pair<Kpi, KpiProgressEntry>> {
        // Run database operations on IO dispatcher
        return withContext(Dispatchers.IO) {
            try {
                // 1. Get all KPI definitions (needed for names)
                val allKpis = kpiDao.getAllKpis().first()
                val kpiMap = allKpis.associateBy { it.id }

                // 2. Get all assignments for the specific user
                val allAssignments = userKpiAssignmentDao.getAllAssignments().first()
                val userKpiIds = allAssignments.filter { it.userId == userId }.map { it.kpiId }.toSet()

                // 3. Get all progress entries for the specific user
                val allEntries = progressEntryDao.getAllEntries().first()
                val userEntries = allEntries.filter { it.userId == userId && it.kpiId in userKpiIds }

                // 4. Combine entries with their KPI definitions
                userEntries.mapNotNull { entry ->
                    kpiMap[entry.kpiId]?.let { kpi ->
                        Pair(kpi, entry) // Pair the Kpi definition with the Progress Entry
                    }
                }
            } catch (e: Exception) {
                Log.e("KpiViewModel", "Error fetching data for export for user $userId", e)
                emptyList() // Return empty list on error
            }
        }
    }
    // --- End Function for user export data ---

    // --- Login and User Management Functions ---

    suspend fun getUserByUsername(username: String): User? {
        return withContext(Dispatchers.IO) {
            try {
                userDao.getUserByUsername(username)
            } catch (e: Exception) {
                Log.e("KpiViewModel", "Error getting user by username: $username", e)
                null
            }
        }
    }

    suspend fun getUserByEmail(email: String): User? {
        return withContext(Dispatchers.IO) {
            try {
                userDao.getUserByEmail(email)
            } catch (e: Exception) {
                Log.e("KpiViewModel", "Error getting user by email: $email", e)
                null
            }
        }
    }

    suspend fun insertUser(user: User) {
        withContext(Dispatchers.IO) {
            try {
                userDao.insertUser(user)
                Log.i("KpiViewModel", "Successfully inserted user: ${user.name}")
            } catch (e: Exception) {
                Log.e("KpiViewModel", "Error inserting user: ${user.name}", e)
                throw e
            }
        }
    }

    suspend fun updateUserLastLogin(userId: String, timestamp: Long) {
        withContext(Dispatchers.IO) {
            try {
                userDao.updateLastLogin(userId, timestamp)
                Log.d("KpiViewModel", "Updated last login for user: $userId")
            } catch (e: Exception) {
                Log.e("KpiViewModel", "Error updating last login for user: $userId", e)
            }
        }
    }

    // --- End Login and User Management Functions ---

    // --- Function to refresh user summaries ---
    fun refreshUserSummaries() {
        // Since userSummaries is a StateFlow that automatically updates when underlying data changes,
        // we can trigger a refresh by updating the filter month to the same value
        val currentMonth = _selectedFilterMonth.value
        _selectedFilterMonth.value = currentMonth
        Log.d("KpiViewModel", "refreshUserSummaries called - triggering data refresh")
    }

} // This is the correct closing brace for the KpiViewModel class
