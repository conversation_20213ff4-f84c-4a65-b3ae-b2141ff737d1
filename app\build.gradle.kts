plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.jetbrains.kotlin.android)
    alias(libs.plugins.google.ksp) // Apply KSP plugin
    id("kotlin-parcelize") // Add Parcelize plugin
    id("com.google.gms.google-services") // Add Firebase plugin
}

android {
    namespace = "com.example.kpitrackerapp"
    compileSdk = 34

    defaultConfig {
        applicationId = "com.example.kpitrackerapp"
        minSdk = 26 // Raised minSdk to 26 (Android 8.0 Oreo) to support Apache POI requirements
        targetSdk = 34
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        create("release") {
            // For production, these should be stored securely (not in code)
            // Use gradle.properties or environment variables
            storeFile = file("release-keystore.jks")
            storePassword = System.getenv("KEYSTORE_PASSWORD") ?: "kpi_tracker_2025"
            keyAlias = System.getenv("KEY_ALIAS") ?: "kpi_tracker_key"
            keyPassword = System.getenv("KEY_PASSWORD") ?: "kpi_tracker_2025"
        }
    }

    buildTypes {
        debug {
            // Remove applicationIdSuffix to avoid Firebase configuration issues
            // applicationIdSuffix = ".debug"
            versionNameSuffix = "-debug"
            isDebuggable = true
        }
        release {
            // Temporarily disable minification due to Apache POI issues
            isMinifyEnabled = false
            isShrinkResources = false
            isDebuggable = false
            // Temporarily disable signing for testing - enable when keystore is ready
            // signingConfig = signingConfigs.getByName("release")
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    // Removed compileOptions block for desugaring
    // Remove compileOptions, use toolchain instead
    kotlinOptions {
        jvmTarget = "17" // Target JVM 17 for Kotlin
    }
    java {
        toolchain {
            languageVersion.set(JavaLanguageVersion.of(17)) // Set Java Toolchain to 17
        }
    }
    buildFeatures {
        viewBinding = true // Enable View Binding
        buildConfig = true // Enable BuildConfig
    }

    bundle {
        language {
            enableSplit = true
        }
        density {
            enableSplit = true
        }
        abi {
            enableSplit = true
        }
    }
}

// Provide schema location argument to KSP for Room
ksp {
    arg("room.schemaLocation", "$projectDir/schemas")
}

dependencies {
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    implementation(libs.androidx.constraintlayout)
    implementation(libs.androidx.activity.ktx) // Activity KTX for viewModels delegate
    implementation(libs.androidx.fragment.ktx) // Fragment KTX for activityViewModels delegate

    // Add dependencies for charts later (e.g., MPAndroidChart or Compose Charts)
    // Add dependencies for authentication later (e.g., Firebase Auth)
    // Room dependencies
    implementation(libs.androidx.room.runtime)
    implementation(libs.androidx.room.ktx) // Kotlin Extensions for Room
    ksp(libs.androidx.room.compiler) // Use ksp for annotation processing

    // Lifecycle components (ViewModel)
    implementation(libs.androidx.lifecycle.viewmodel.ktx)
    // implementation(libs.androidx.lifecycle.livedata.ktx) // Uncomment if using LiveData

    // UI components
    implementation(libs.androidx.recyclerview)
    implementation(libs.mpandroidchart) // Add MPAndroidChart
    implementation("com.google.code.gson:gson:2.10.1") // Add Gson for JSON parsing
    implementation("com.github.dhaval2404:colorpicker:2.3") // Add Color Picker library
    implementation("org.jsoup:jsoup:1.17.2") // Add Jsoup for HTML parsing

    // WorkManager for background tasks
    implementation(libs.androidx.work.runtime.ktx) // Add this line

    // ML Kit Text Recognition (Reverting to V1 play-services lib to fix build)
    implementation("com.google.android.gms:play-services-mlkit-text-recognition:19.0.0")

    // Removed Apache POI dependency

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)

    // Removed Core Library Desugaring dependency

    // Apache POI for Excel Reading (add required dependencies)
    // Note: This adds significant size to the app. Consider alternatives if size is critical.
    // Use the Android-compatible versions if available, otherwise standard versions.
    // Need to ensure compatibility with minSdk 26.
    implementation("org.apache.poi:poi:5.2.5") // For .xls files (Updated)
    implementation("org.apache.poi:poi-ooxml:5.2.5") // For .xlsx files (Updated)
    // Required for XML processing within POI
    implementation("org.apache.xmlbeans:xmlbeans:5.2.0") // (Updated)
    implementation("org.apache.commons:commons-compress:1.26.0") // Required by POI (Updated)
    implementation("org.apache.commons:commons-collections4:4.4") // Required by POI (Seems current)
    // May need additional logging dependencies depending on POI version/usage

    // Glide for image loading
    implementation(libs.glide)
    ksp(libs.glide.compiler) // Use ksp for Glide annotation processing

    // Firebase dependencies for messaging
    implementation(platform("com.google.firebase:firebase-bom:32.7.0"))
    implementation("com.google.firebase:firebase-database-ktx")
    implementation("com.google.firebase:firebase-messaging-ktx")
    implementation("com.google.firebase:firebase-analytics-ktx")
}
