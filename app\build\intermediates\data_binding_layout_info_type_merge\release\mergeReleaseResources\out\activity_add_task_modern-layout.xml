<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_add_task_modern" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_add_task_modern.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_add_task_modern_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="456" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="10" startOffset="4" endLine="27" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="18" startOffset="8" endLine="25" endOffset="46"/></Target><Target id="@+id/templateMeeting" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="58" startOffset="16" endLine="97" endOffset="67"/></Target><Target id="@+id/templateStudy" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="99" startOffset="16" endLine="139" endOffset="67"/></Target><Target id="@+id/templateCall" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="141" startOffset="16" endLine="180" endOffset="67"/></Target><Target id="@+id/taskNameInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="195" startOffset="12" endLine="216" endOffset="67"/></Target><Target id="@+id/taskNameEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="209" startOffset="16" endLine="215" endOffset="45"/></Target><Target id="@+id/taskDescriptionInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="219" startOffset="12" endLine="241" endOffset="67"/></Target><Target id="@+id/taskDescriptionEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="233" startOffset="16" endLine="240" endOffset="45"/></Target><Target id="@+id/dueDateInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="244" startOffset="12" endLine="269" endOffset="67"/></Target><Target id="@+id/dueDateEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="260" startOffset="16" endLine="268" endOffset="45"/></Target><Target id="@+id/dueTimeInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="272" startOffset="12" endLine="297" endOffset="67"/></Target><Target id="@+id/dueTimeEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="288" startOffset="16" endLine="296" endOffset="45"/></Target><Target id="@+id/priorityLow" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="315" startOffset="16" endLine="350" endOffset="67"/></Target><Target id="@+id/priorityMedium" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="352" startOffset="16" endLine="389" endOffset="67"/></Target><Target id="@+id/priorityHigh" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="391" startOffset="16" endLine="426" endOffset="67"/></Target><Target id="@+id/fabSave" view="com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton"><Expressions/><location startLine="440" startOffset="4" endLine="454" endOffset="33"/></Target></Targets></Layout>