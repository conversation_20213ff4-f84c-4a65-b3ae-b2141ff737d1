package com.example.kpitrackerapp.ui

import android.Manifest
import android.app.Activity
import android.app.DatePickerDialog
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.provider.CalendarContract
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import android.widget.CheckBox
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.kpitrackerapp.R
import com.example.kpitrackerapp.databinding.ActivityTaskManagementBinding
import com.example.kpitrackerapp.models.Task
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.textfield.TextInputEditText
import com.example.kpitrackerapp.persistence.AppDatabase
import com.example.kpitrackerapp.viewmodels.TaskViewModel
import com.example.kpitrackerapp.viewmodels.TaskViewModelFactory
import com.example.kpitrackerapp.utils.AlarmClockManager
import android.widget.DatePicker
import android.widget.TimePicker
import com.google.android.material.textfield.TextInputEditText
import java.text.SimpleDateFormat
import com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity
import java.util.Calendar
import java.util.Date
import java.util.Locale

class TaskManagementActivity : AppCompatActivity(), EnhancedTaskActionsListener {

    private lateinit var binding: ActivityTaskManagementBinding
    private lateinit var taskAdapter: EnhancedTaskAdapter
    private var taskToAddToCalendar: Task? = null

    private val requestCalendarPermissionLauncher: ActivityResultLauncher<Array<String>> =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { permissions ->
            val granted = permissions.entries.all { it.value }
            if (granted) {
                taskToAddToCalendar?.let { addTaskToDeviceCalendar(it) }
            } else {
                Toast.makeText(this, "Calendar permission denied.", Toast.LENGTH_LONG).show()
            }
            taskToAddToCalendar = null
        }

    private val taskViewModel: TaskViewModel by viewModels {
        val database = AppDatabase.getDatabase(this)
        TaskViewModelFactory(
            application,
            database.taskDao(),
            database.taskCategoryDao(),
            database.subtaskDao()
        )
    }

    // Activity result launcher for AddEditKpiActivity
    private val addTaskResultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            // Task added or edited, list will be updated by observer
            // Optionally show a success message or refresh explicitly if needed
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTaskManagementBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupRecyclerView()
        setupFab()
        setupSearchAndFilters()
        setupEisenhowerMatrix()
        setupSummaryCardInteractions()
        observeTasks()
        observeQuickStats()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbarTaskManagement)
        // Title is set in activity_task_management.xml (app:title="@string/my_tasks_title")
        // If you want to set it programmatically:
        // supportActionBar?.title = getString(R.string.my_tasks_title)
        // For back navigation, if this is not the main screen:
        // supportActionBar?.setDisplayHomeAsUpEnabled(true)
        // supportActionBar?.setDisplayShowHomeEnabled(true)
        // binding.toolbarTaskManagement.setNavigationOnClickListener {
        //     onBackPressedDispatcher.onBackPressed()
        // }
    }

    private fun setupFab() {
        binding.fabAddTask.setOnClickListener {
            val intent = Intent(this, AddEditAdvancedTaskActivity::class.java)
            addTaskResultLauncher.launch(intent)
        }
    }

    private fun setupRecyclerView() {
        taskAdapter = EnhancedTaskAdapter(this)
        binding.rvTasks.apply {
            adapter = taskAdapter
            layoutManager = LinearLayoutManager(this@TaskManagementActivity)
        }
    }

    private fun setupSearchAndFilters() {
        // Search button click
        binding.btnSearch.setOnClickListener {
            toggleSearchView()
        }

        // Filter button click
        binding.btnFilter.setOnClickListener {
            showFilterDialog()
        }

        // View mode button click
        binding.btnViewMode.setOnClickListener {
            toggleViewMode()
        }

        // Search functionality
        binding.searchView.setOnQueryTextListener(object : androidx.appcompat.widget.SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                query?.let { taskViewModel.setSearchQuery(it) }
                return true
            }

            override fun onQueryTextChange(newText: String?): Boolean {
                taskViewModel.setSearchQuery(newText ?: "")
                return true
            }
        })

        // Filter chips
        setupFilterChips()
    }

    private fun setupFilterChips() {
        binding.chipAll.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                taskViewModel.clearFilters()
                uncheckOtherChips(binding.chipAll)
            }
        }

        binding.chipToday.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                // Filter today's tasks - implement custom logic
                filterTodayTasks()
                uncheckOtherChips(binding.chipToday)
            }
        }

        binding.chipThisWeek.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                // Filter this week's tasks - implement custom logic
                filterThisWeekTasks()
                uncheckOtherChips(binding.chipThisWeek)
            }
        }

        binding.chipUrgent.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                taskViewModel.setSelectedPriority(com.example.kpitrackerapp.models.TaskPriority.URGENT)
                uncheckOtherChips(binding.chipUrgent)
            }
        }

        binding.chipCompleted.setOnCheckedChangeListener { _, isChecked ->
            taskViewModel.setShowCompleted(isChecked)
        }

        binding.chipOverdue.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                // Filter overdue tasks - implement custom logic
                filterOverdueTasks()
                uncheckOtherChips(binding.chipOverdue)
            }
        }
    }

    private fun uncheckOtherChips(selectedChip: com.google.android.material.chip.Chip) {
        val chips = listOf(
            binding.chipAll,
            binding.chipToday,
            binding.chipThisWeek,
            binding.chipUrgent,
            binding.chipOverdue
        )

        chips.forEach { chip ->
            if (chip != selectedChip && chip != binding.chipCompleted) {
                chip.isChecked = false
            }
        }
    }

    private fun toggleSearchView() {
        if (binding.searchView.visibility == android.view.View.GONE) {
            binding.searchView.visibility = android.view.View.VISIBLE
            binding.searchView.requestFocus()
        } else {
            binding.searchView.visibility = android.view.View.GONE
            binding.searchView.setQuery("", false)
            taskViewModel.setSearchQuery("")
        }
    }

    private fun setupEisenhowerMatrix() {
        binding.switchMatrixView.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                // إظهار المصفوفة مع تأثير انيميشن
                binding.layoutEisenhowerMatrix.visibility = android.view.View.VISIBLE
                binding.layoutEisenhowerMatrix.alpha = 0f
                binding.layoutEisenhowerMatrix.animate()
                    .alpha(1f)
                    .setDuration(300)
                    .start()

                showMatrixIntroduction()
                android.widget.Toast.makeText(this, "🎯 تم تفعيل مصفوفة أيزنهاور", android.widget.Toast.LENGTH_SHORT).show()
            } else {
                // إخفاء المصفوفة مع تأثير انيميشن
                binding.layoutEisenhowerMatrix.animate()
                    .alpha(0f)
                    .setDuration(300)
                    .withEndAction {
                        binding.layoutEisenhowerMatrix.visibility = android.view.View.GONE
                    }
                    .start()
                android.widget.Toast.makeText(this, "📋 تم إخفاء مصفوفة أيزنهاور", android.widget.Toast.LENGTH_SHORT).show()
            }
        }

        // Setup matrix RecyclerViews
        setupMatrixRecyclerViews()

        // Setup click listeners for each quadrant
        setupMatrixClickListeners()
    }

    private fun setupMatrixRecyclerViews() {
        // Setup each quadrant's RecyclerView
        // This would need separate adapters for each quadrant
        // For now, we'll keep it simple

        // إضافة مراقبة للمهام وتحديث المصفوفة
        taskViewModel.allTasks.observe(this) { tasks ->
            tasks?.let { updateEisenhowerMatrix(it) }
        }
    }

    private fun updateEisenhowerMatrix(tasks: List<com.example.kpitrackerapp.models.Task>) {
        val groupedTasks = com.example.kpitrackerapp.utils.EisenhowerMatrixHelper.groupTasksByQuadrant(tasks)
        val stats = com.example.kpitrackerapp.utils.EisenhowerMatrixHelper.getMatrixStats(tasks)

        // تحديث عدد المهام في كل ربع
        updateQuadrantCounts(groupedTasks)

        // عرض الإحصائيات إذا كانت المصفوفة مرئية
        if (binding.layoutEisenhowerMatrix.visibility == android.view.View.VISIBLE) {
            showMatrixStats(stats)
        }
    }

    private fun updateQuadrantCounts(groupedTasks: Map<com.example.kpitrackerapp.utils.EisenhowerQuadrant, List<com.example.kpitrackerapp.models.Task>>) {
        // تحديث النصوص لإظهار عدد المهام في كل ربع
        val urgentImportantCount = groupedTasks[com.example.kpitrackerapp.utils.EisenhowerQuadrant.URGENT_IMPORTANT]?.size ?: 0
        val notUrgentImportantCount = groupedTasks[com.example.kpitrackerapp.utils.EisenhowerQuadrant.NOT_URGENT_IMPORTANT]?.size ?: 0
        val urgentNotImportantCount = groupedTasks[com.example.kpitrackerapp.utils.EisenhowerQuadrant.URGENT_NOT_IMPORTANT]?.size ?: 0
        val notUrgentNotImportantCount = groupedTasks[com.example.kpitrackerapp.utils.EisenhowerQuadrant.NOT_URGENT_NOT_IMPORTANT]?.size ?: 0

        // تحديث النصوص في واجهة المستخدم
        try {
            binding.tvUrgentImportantCount?.text = "$urgentImportantCount مهام"
            binding.tvNotUrgentImportantCount?.text = "$notUrgentImportantCount مهام"
            binding.tvUrgentNotImportantCount?.text = "$urgentNotImportantCount مهام"
            binding.tvNotUrgentNotImportantCount?.text = "$notUrgentNotImportantCount مهام"
        } catch (e: Exception) {
            android.util.Log.e("EisenhowerMatrix", "Error updating quadrant counts", e)
        }

        android.util.Log.d("EisenhowerMatrix", "Updated counts - Urgent+Important: $urgentImportantCount, Important: $notUrgentImportantCount, Urgent: $urgentNotImportantCount, Neither: $notUrgentNotImportantCount")
    }

    private fun showMatrixStats(stats: com.example.kpitrackerapp.utils.EisenhowerMatrixStats) {
        val recommendations = com.example.kpitrackerapp.utils.EisenhowerMatrixHelper.getMatrixRecommendations(stats)

        // عرض الإحصائيات في Toast أو Dialog حسب الحاجة
        if (recommendations.isNotEmpty()) {
            android.util.Log.d("EisenhowerMatrix", "Recommendations: ${recommendations.joinToString(", ")}")
        }
    }

    private fun setupMatrixClickListeners() {
        // الربع الأول: عاجل ومهم
        findViewById<androidx.recyclerview.widget.RecyclerView>(R.id.rvUrgentImportant)?.parent?.parent?.let { cardView ->
            (cardView as? com.google.android.material.card.MaterialCardView)?.setOnClickListener {
                showQuadrantInfo(
                    title = "🔥 الربع الأول: عاجل ومهم",
                    description = """
                        🎯 **الهدف:** التعامل مع الأزمات والطوارئ

                        📋 **أمثلة:**
                        • المواعيد النهائية الحرجة
                        • الأزمات والطوارئ
                        • المشاكل الملحة
                        • المهام المتأخرة

                        ⚡ **الاستراتيجية:**
                        • تعامل معها فوراً
                        • أعطها الأولوية القصوى
                        • لا تؤجلها أبداً

                        ⚠️ **تحذير:**
                        إذا كان معظم وقتك في هذا الربع، فأنت تعيش في حالة ضغط مستمر!

                        💡 **نصيحة:**
                        حاول تقليل المهام في هذا الربع بالتخطيط المسبق والوقاية
                    """.trimIndent(),
                    color = "#D32F2F"
                )
            }
        }

        // الربع الثاني: مهم وغير عاجل
        findViewById<androidx.recyclerview.widget.RecyclerView>(R.id.rvNotUrgentImportant)?.parent?.parent?.let { cardView ->
            (cardView as? com.google.android.material.card.MaterialCardView)?.setOnClickListener {
                showQuadrantInfo(
                    title = "🎯 الربع الثاني: مهم وغير عاجل",
                    description = """
                        🎯 **الهدف:** التطوير والنمو طويل المدى

                        📋 **أمثلة:**
                        • التخطيط الاستراتيجي
                        • التعلم والتطوير
                        • ممارسة الرياضة
                        • بناء العلاقات
                        • الوقاية والصيانة

                        ⚡ **الاستراتيجية:**
                        • خصص وقت منتظم لها
                        • اجعلها جزء من روتينك
                        • لا تتجاهلها لصالح العاجل

                        🌟 **الفائدة:**
                        هذا هو الربع الذهبي! كلما زاد وقتك هنا، قل وقتك في الربع الأول

                        💡 **نصيحة:**
                        اهدف لقضاء 65-70% من وقتك في هذا الربع للحصول على حياة متوازنة ومنتجة
                    """.trimIndent(),
                    color = "#1976D2"
                )
            }
        }

        // الربع الثالث: عاجل وغير مهم
        findViewById<androidx.recyclerview.widget.RecyclerView>(R.id.rvUrgentNotImportant)?.parent?.parent?.let { cardView ->
            (cardView as? com.google.android.material.card.MaterialCardView)?.setOnClickListener {
                showQuadrantInfo(
                    title = "⚡ الربع الثالث: عاجل وغير مهم",
                    description = """
                        🎯 **الهدف:** التقليل والتفويض

                        📋 **أمثلة:**
                        • المقاطعات غير المهمة
                        • بعض المكالمات والرسائل
                        • الاجتماعات غير الضرورية
                        • طلبات الآخرين غير المهمة

                        ⚡ **الاستراتيجية:**
                        • فوّض ما يمكن تفويضه
                        • قل "لا" بأدب
                        • حدد أوقات محددة للرد
                        • قلل من الوقت المخصص لها

                        ⚠️ **خطر:**
                        هذا الربع يخدع! يبدو مهماً لأنه عاجل، لكنه يسرق وقتك من المهم حقاً

                        💡 **نصيحة:**
                        تعلم الفرق بين "العاجل" و "المهم" - ليس كل عاجل مهم!
                    """.trimIndent(),
                    color = "#F57C00"
                )
            }
        }

        // الربع الرابع: غير عاجل وغير مهم
        findViewById<androidx.recyclerview.widget.RecyclerView>(R.id.rvNotUrgentNotImportant)?.parent?.parent?.let { cardView ->
            (cardView as? com.google.android.material.card.MaterialCardView)?.setOnClickListener {
                showQuadrantInfo(
                    title = "📝 الربع الرابع: غير عاجل وغير مهم",
                    description = """
                        🎯 **الهدف:** التخلص منها أو تقليلها للحد الأدنى

                        📋 **أمثلة:**
                        • تصفح وسائل التواصل بلا هدف
                        • مشاهدة التلفزيون المفرطة
                        • الألعاب المضيعة للوقت
                        • القيل والقال
                        • التسوق العشوائي

                        ⚡ **الاستراتيجية:**
                        • احذفها من جدولك
                        • استبدلها بأنشطة مفيدة
                        • استخدمها كمكافآت قصيرة فقط

                        ⚠️ **تحذير:**
                        هذا الربع مدمر للإنتاجية! يسرق وقتك دون أي فائدة

                        💡 **نصيحة:**
                        إذا كنت تقضي وقتاً كثيراً هنا، فأنت تحتاج لإعادة تقييم أولوياتك

                        🎯 **استثناء:**
                        الراحة والاستجمام المخطط لهما مهمان، لكن ضعهما في الربع الثاني!
                    """.trimIndent(),
                    color = "#616161"
                )
            }
        }
    }

    private fun showQuadrantInfo(title: String, description: String, color: String) {
        val dialog = com.google.android.material.dialog.MaterialAlertDialogBuilder(this)
            .setTitle(title)
            .setMessage(description)
            .setPositiveButton("فهمت ✅") { dialog, _ ->
                dialog.dismiss()
            }
            .setNeutralButton("نصائح إضافية 💡") { _, _ ->
                showAdditionalTips(title)
            }
            .create()

        dialog.show()

        // تخصيص لون العنوان
        dialog.findViewById<android.widget.TextView>(androidx.appcompat.R.id.alertTitle)?.let { titleView ->
            try {
                titleView.setTextColor(android.graphics.Color.parseColor(color))
            } catch (e: Exception) {
                // استخدام لون افتراضي في حالة الخطأ
            }
        }
    }

    private fun showAdditionalTips(quadrantTitle: String) {
        val tips = when {
            quadrantTitle.contains("الأول") -> """
                🔥 **نصائح للربع الأول (عاجل ومهم):**

                📅 **للوقاية:**
                • خطط مسبقاً لتجنب الأزمات
                • ضع خطط طوارئ
                • راجع المهام بانتظام

                ⏰ **للتعامل:**
                • ركز على مهمة واحدة فقط
                • اطلب المساعدة عند الحاجة
                • لا تتردد في إعادة جدولة المهام الأقل أهمية

                🎯 **الهدف:**
                تقليل الوقت في هذا الربع من 25% إلى 15% أو أقل
            """.trimIndent()

            quadrantTitle.contains("الثاني") -> """
                🎯 **نصائح للربع الثاني (مهم وغير عاجل):**

                📅 **للتخطيط:**
                • خصص 2-3 ساعات يومياً لهذا الربع
                • ضع أهداف أسبوعية وشهرية
                • استخدم تقنية الـ Time Blocking

                🌱 **للنمو:**
                • اقرأ 30 دقيقة يومياً
                • تعلم مهارة جديدة شهرياً
                • مارس الرياضة بانتظام

                🎯 **الهدف:**
                زيادة الوقت في هذا الربع إلى 65-70%
            """.trimIndent()

            quadrantTitle.contains("الثالث") -> """
                ⚡ **نصائح للربع الثالث (عاجل وغير مهم):**

                🚫 **للرفض:**
                • تعلم قول "لا" بأدب
                • "شكراً لك، لكن لدي التزامات أخرى"
                • "دعني أتحقق من جدولي وأعود إليك"

                📱 **للتحكم:**
                • أغلق الإشعارات غير المهمة
                • حدد أوقات محددة للرد على الرسائل
                • استخدم وضع "عدم الإزعاج"

                🎯 **الهدف:**
                تقليل الوقت هنا إلى 15% أو أقل
            """.trimIndent()

            else -> """
                📝 **نصائح للربع الرابع (غير مهم وغير عاجل):**

                🗑️ **للتخلص:**
                • احذف التطبيقات المضيعة للوقت
                • ألغ الاشتراكات غير المفيدة
                • تجنب الأخبار السلبية المفرطة

                🔄 **للاستبدال:**
                • استبدل مشاهدة التلفزيون بالقراءة
                • استبدل تصفح الإنترنت بالتعلم
                • استبدل الألعاب بالرياضة

                🎯 **الهدف:**
                تقليل الوقت هنا إلى 5% أو أقل (للراحة المخططة فقط)
            """.trimIndent()
        }

        com.google.android.material.dialog.MaterialAlertDialogBuilder(this)
            .setTitle("💡 نصائح إضافية")
            .setMessage(tips)
            .setPositiveButton("ممتاز! 🚀", null)
            .show()
    }

    private fun showMatrixIntroduction() {
        val sharedPrefs = getSharedPreferences("matrix_prefs", android.content.Context.MODE_PRIVATE)
        val hasSeenIntro = sharedPrefs.getBoolean("has_seen_matrix_intro", false)

        if (!hasSeenIntro) {
            com.google.android.material.dialog.MaterialAlertDialogBuilder(this)
                .setTitle("🎯 مرحباً بك في مصفوفة أيزنهاور!")
                .setMessage("""
                    📊 **ما هي مصفوفة أيزنهاور؟**

                    أداة قوية لإدارة الوقت تقسم مهامك إلى 4 أرباع:

                    🔥 **عاجل ومهم** - افعل الآن
                    🎯 **مهم وغير عاجل** - خطط له (الربع الذهبي!)
                    ⚡ **عاجل وغير مهم** - فوّض أو قلل
                    📝 **غير مهم وغير عاجل** - احذف أو أجل

                    💡 **كيف تستخدمها؟**
                    • اضغط على أي ربع لمعرفة تفاصيله
                    • ستصنف مهامك تلقائياً حسب الأولوية والموعد النهائي
                    • احصل على توصيات لتحسين إنتاجيتك

                    🎯 **الهدف:** قضاء 65-70% من وقتك في الربع الثاني!
                """.trimIndent())
                .setPositiveButton("فهمت! 🚀") { _, _ ->
                    sharedPrefs.edit().putBoolean("has_seen_matrix_intro", true).apply()
                    showCurrentMatrixStats()
                }
                .setNeutralButton("تخطي") { _, _ ->
                    sharedPrefs.edit().putBoolean("has_seen_matrix_intro", true).apply()
                }
                .show()
        } else {
            showCurrentMatrixStats()
        }
    }

    private fun showCurrentMatrixStats() {
        taskViewModel.allTasks.value?.let { tasks ->
            if (tasks.isNotEmpty()) {
                val stats = com.example.kpitrackerapp.utils.EisenhowerMatrixHelper.getMatrixStats(tasks)
                val recommendations = com.example.kpitrackerapp.utils.EisenhowerMatrixHelper.getMatrixRecommendations(stats)

                val statsMessage = """
                    📊 **إحصائيات مهامك الحالية:**

                    🔥 عاجل ومهم: ${stats.urgentImportantCount} مهام (${stats.urgentImportantPercentage}%)
                    🎯 مهم وغير عاجل: ${stats.notUrgentImportantCount} مهام (${stats.notUrgentImportantPercentage}%)
                    ⚡ عاجل وغير مهم: ${stats.urgentNotImportantCount} مهام (${stats.urgentNotImportantPercentage}%)
                    📝 غير مهم وغير عاجل: ${stats.notUrgentNotImportantCount} مهام (${stats.notUrgentNotImportantPercentage}%)

                    💡 **التوصيات:**
                    ${recommendations.joinToString("\n")}
                """.trimIndent()

                com.google.android.material.dialog.MaterialAlertDialogBuilder(this)
                    .setTitle("📈 تحليل مهامك")
                    .setMessage(statsMessage)
                    .setPositiveButton("ممتاز! 📊", null)
                    .show()
            }
        }
    }

    private fun observeTasks() {
        try {
            // Observe filtered tasks instead of all tasks
            taskViewModel.filteredTasks.observe(this) { tasks ->
                try {
                    tasks?.let {
                        taskAdapter.submitList(it)
                        binding.layoutEmptyState.visibility =
                            if (it.isEmpty()) android.view.View.VISIBLE else android.view.View.GONE
                    }
                } catch (e: Exception) {
                    android.util.Log.e("TaskManagement", "Error updating tasks list", e)
                }
            }

            // TODO: Implement Eisenhower Matrix observers when needed
            // These LiveData properties need to be added to TaskViewModel first
        } catch (e: Exception) {
            android.util.Log.e("TaskManagement", "Error setting up task observers", e)
        }
    }

    private fun observeQuickStats() {
        taskViewModel.quickStats.observe(this) { stats ->
            stats?.let {
                updateQuickStatsUI(it)
            }
        }
    }

    private fun updateQuickStatsUI(stats: com.example.kpitrackerapp.models.QuickStats) {
        try {
            binding.tvTotalTasks.text = stats.todayTasks.toString()
            binding.tvTodayTasks.text = stats.todayCompleted.toString()
            binding.tvUrgentTasks.text = stats.urgentTasks.toString()
            binding.tvCompletionRate.text = "${stats.completionRate}%"
            binding.progressOverall.progress = stats.completionRate
        } catch (e: Exception) {
            android.util.Log.e("TaskManagement", "Error updating quick stats UI", e)
        }
    }

    private fun showFilterDialog() {
        val filterOptions = arrayOf(
            "🔴 المهام العاجلة فقط",
            "📅 مهام اليوم",
            "📆 مهام هذا الأسبوع",
            "⏰ المهام المتأخرة",
            "✅ المهام المكتملة",
            "📋 جميع المهام",
            "🎯 حسب الأولوية",
            "📂 حسب الفئة"
        )

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🔍 فلترة المهام")
            .setItems(filterOptions) { _, which ->
                when (which) {
                    0 -> {
                        binding.chipUrgent.isChecked = true
                        taskViewModel.setSelectedPriority(com.example.kpitrackerapp.models.TaskPriority.URGENT)
                    }
                    1 -> {
                        binding.chipToday.isChecked = true
                        filterTodayTasks()
                    }
                    2 -> {
                        binding.chipThisWeek.isChecked = true
                        filterThisWeekTasks()
                    }
                    3 -> {
                        binding.chipOverdue.isChecked = true
                        filterOverdueTasks()
                    }
                    4 -> {
                        binding.chipCompleted.isChecked = true
                        taskViewModel.setShowCompleted(true)
                    }
                    5 -> {
                        binding.chipAll.isChecked = true
                        taskViewModel.clearFilters()
                    }
                    6 -> showPriorityFilterDialog()
                    7 -> showCategoryFilterDialog()
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun showPriorityFilterDialog() {
        val priorities = arrayOf(
            "🔴 عاجلة",
            "🟠 عالية",
            "🟡 متوسطة",
            "🟢 منخفضة"
        )

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("⭐ اختيار الأولوية")
            .setItems(priorities) { _, which ->
                val priority = when (which) {
                    0 -> com.example.kpitrackerapp.models.TaskPriority.URGENT
                    1 -> com.example.kpitrackerapp.models.TaskPriority.HIGH
                    2 -> com.example.kpitrackerapp.models.TaskPriority.MEDIUM
                    else -> com.example.kpitrackerapp.models.TaskPriority.LOW
                }
                taskViewModel.setSelectedPriority(priority)
                android.widget.Toast.makeText(this, "تم فلترة المهام حسب الأولوية: ${priorities[which]}", android.widget.Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun showCategoryFilterDialog() {
        val categories = arrayOf(
            "💼 عمل",
            "👤 شخصي",
            "📚 دراسة",
            "🏥 صحة",
            "💰 مالية",
            "👨‍👩‍👧‍👦 عائلة",
            "🛒 تسوق",
            "✈️ سفر"
        )

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("📂 اختيار الفئة")
            .setItems(categories) { _, which ->
                val category = categories[which].substringAfter(" ")
                taskViewModel.setSelectedCategory(category)
                android.widget.Toast.makeText(this, "تم فلترة المهام حسب الفئة: ${categories[which]}", android.widget.Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private var isGridView = false

    private fun toggleViewMode() {
        isGridView = !isGridView

        if (isGridView) {
            // Switch to grid view
            binding.rvTasks.layoutManager = androidx.recyclerview.widget.GridLayoutManager(this, 2)
            binding.btnViewMode.setImageResource(R.drawable.ic_baseline_view_list_24)
            android.widget.Toast.makeText(this, "📊 عرض الشبكة", android.widget.Toast.LENGTH_SHORT).show()
        } else {
            // Switch to list view
            binding.rvTasks.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(this)
            binding.btnViewMode.setImageResource(R.drawable.ic_dashboard_24)
            android.widget.Toast.makeText(this, "📋 عرض القائمة", android.widget.Toast.LENGTH_SHORT).show()
        }
    }

    private fun setupSummaryCardInteractions() {
        // إضافة تفاعلات للبطاقة العلوية
        binding.cardQuickStats?.setOnClickListener {
            showDetailedStatsDialog()
        }

        // إضافة تفاعلات لكل عنصر في البطاقة
        binding.tvTotalTasks?.setOnClickListener {
            filterTodayTasks()
            binding.chipToday.isChecked = true
            android.widget.Toast.makeText(this, "عرض مهام اليوم", android.widget.Toast.LENGTH_SHORT).show()
        }

        binding.tvUrgentTasks?.setOnClickListener {
            taskViewModel.setSelectedPriority(com.example.kpitrackerapp.models.TaskPriority.URGENT)
            binding.chipUrgent.isChecked = true
            android.widget.Toast.makeText(this, "عرض المهام العاجلة", android.widget.Toast.LENGTH_SHORT).show()
        }

        binding.tvTodayTasks?.setOnClickListener {
            taskViewModel.setShowCompleted(true)
            binding.chipCompleted.isChecked = true
            android.widget.Toast.makeText(this, "عرض المهام المكتملة", android.widget.Toast.LENGTH_SHORT).show()
        }

        binding.tvCompletionRate?.setOnClickListener {
            showProductivityTipsDialog()
        }

        // إضافة تأثيرات بصرية للبطاقة
        binding.cardQuickStats?.setOnTouchListener { view, event ->
            when (event.action) {
                android.view.MotionEvent.ACTION_DOWN -> {
                    view.animate().scaleX(0.95f).scaleY(0.95f).setDuration(100).start()
                }
                android.view.MotionEvent.ACTION_UP, android.view.MotionEvent.ACTION_CANCEL -> {
                    view.animate().scaleX(1.0f).scaleY(1.0f).setDuration(100).start()
                }
            }
            false
        }
    }

    private fun showProductivityTipsDialog() {
        val tips = arrayOf(
            "🎯 ركز على مهمة واحدة في كل مرة",
            "⏰ استخدم تقنية البومودورو (25 دقيقة عمل + 5 دقائق راحة)",
            "📅 خطط ليومك في المساء السابق",
            "🚫 تعلم قول 'لا' للمهام غير المهمة",
            "🔄 راجع أهدافك أسبوعياً",
            "💪 ابدأ بالمهام الصعبة في بداية اليوم",
            "📱 أغلق الإشعارات أثناء العمل المركز",
            "🎉 احتفل بإنجازاتك الصغيرة"
        )

        val randomTip = tips.random()

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("💡 نصيحة إنتاجية")
            .setMessage(randomTip)
            .setPositiveButton("شكراً! 🙏") { _, _ ->
                android.widget.Toast.makeText(this, "استمر في التقدم! 🚀", android.widget.Toast.LENGTH_SHORT).show()
            }
            .setNeutralButton("نصيحة أخرى") { _, _ ->
                showProductivityTipsDialog()
            }
            .show()
    }

    private fun showDetailedStatsDialog() {
        taskViewModel.quickStats.value?.let { stats ->
            val message = """
                📊 **إحصائيات مفصلة:**

                📅 مهام اليوم: ${stats.todayTasks}
                ✅ مكتملة اليوم: ${stats.todayCompleted}
                🔴 مهام عاجلة: ${stats.urgentTasks}
                📈 معدل الإنجاز: ${stats.completionRate}%

                💡 **نصائح:**
                • حاول إنجاز 80% من مهام اليوم
                • قلل المهام العاجلة بالتخطيط المسبق
                • استخدم مصفوفة أيزنهاور لتحسين الإنتاجية
            """.trimIndent()

            androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("📈 إحصائيات مفصلة")
                .setMessage(message)
                .setPositiveButton("ممتاز! 🚀", null)
                .setNeutralButton("عرض مصفوفة أيزنهاور") { _, _ ->
                    binding.switchMatrixView.isChecked = true
                }
                .show()
        }
    }

    // Filter functions
    private fun filterTodayTasks() {
        // Use existing ViewModel method to filter today's tasks
        val today = java.util.Date()
        taskViewModel.getTasksForDate(today).observe(this) { tasks ->
            tasks?.let {
                taskAdapter.submitList(it)
                binding.layoutEmptyState.visibility =
                    if (it.isEmpty()) android.view.View.VISIBLE else android.view.View.GONE
            }
        }
        android.widget.Toast.makeText(this, "📅 عرض مهام اليوم", android.widget.Toast.LENGTH_SHORT).show()
    }

    private fun filterThisWeekTasks() {
        // Filter tasks for this week
        val calendar = java.util.Calendar.getInstance()
        calendar.set(java.util.Calendar.DAY_OF_WEEK, calendar.firstDayOfWeek)
        val startOfWeek = calendar.time

        calendar.add(java.util.Calendar.DAY_OF_WEEK, 6)
        val endOfWeek = calendar.time

        taskViewModel.getTasksInDateRange(startOfWeek, endOfWeek).observe(this) { tasks ->
            tasks?.let {
                taskAdapter.submitList(it)
                binding.layoutEmptyState.visibility =
                    if (it.isEmpty()) android.view.View.VISIBLE else android.view.View.GONE
            }
        }
        android.widget.Toast.makeText(this, "📆 عرض مهام هذا الأسبوع", android.widget.Toast.LENGTH_SHORT).show()
    }

    private fun filterOverdueTasks() {
        // Use existing ViewModel method to filter overdue tasks
        taskViewModel.getOverdueTasks().observe(this) { tasks ->
            tasks?.let {
                taskAdapter.submitList(it)
                binding.layoutEmptyState.visibility =
                    if (it.isEmpty()) android.view.View.VISIBLE else android.view.View.GONE
            }
        }
        android.widget.Toast.makeText(this, "⏰ عرض المهام المتأخرة", android.widget.Toast.LENGTH_SHORT).show()
    }

    override fun onEditTask(task: Task) {
        // Option 1: Launch AddEditAdvancedTaskActivity for editing
        val intent = Intent(this, AddEditAdvancedTaskActivity::class.java).apply {
            putExtra(AddEditAdvancedTaskActivity.EXTRA_TASK_ID, task.id.toString()) // Pass task ID as String
        }
        addTaskResultLauncher.launch(intent)

        // Option 2: Show a dialog (current implementation)
        // showEditTaskDialog(task) // Keep this if you prefer dialog for quick edits
    }

    private fun showEditTaskDialog(taskToEdit: Task) {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_edit_task, null)
        val etEditTaskName = dialogView.findViewById<TextInputEditText>(R.id.etEditTaskName)
        val etEditTaskExpirationDate = dialogView.findViewById<TextInputEditText>(R.id.etEditTaskExpirationDate)
        // Expiration time field might not be in dialog_edit_task.xml or needed if model doesn't support it
        // val etEditTaskExpirationTime = dialogView.findViewById<TextInputEditText>(R.id.etEditTaskExpirationTime)
        val etEditTaskReminderDays = dialogView.findViewById<TextInputEditText>(R.id.etEditTaskReminderDays)
        val cbTaskCompleted = dialogView.findViewById<CheckBox>(R.id.cbTaskCompleted)

        val sdfDate = SimpleDateFormat("yyyy-MM-dd", Locale.US)
        val editCalendar = Calendar.getInstance().apply { time = taskToEdit.expirationDate }

        etEditTaskName.setText(taskToEdit.name)
        etEditTaskExpirationDate.setText(sdfDate.format(editCalendar.time))
        etEditTaskReminderDays.setText(taskToEdit.reminderDaysBefore?.toString() ?: "0") // Changed to "0" for null reminder
        cbTaskCompleted.isChecked = taskToEdit.isCompleted

        etEditTaskExpirationDate.setOnClickListener {
            val dateSetListener = DatePickerDialog.OnDateSetListener { _, year, monthOfYear, dayOfMonth ->
                editCalendar.set(Calendar.YEAR, year)
                editCalendar.set(Calendar.MONTH, monthOfYear)
                editCalendar.set(Calendar.DAY_OF_MONTH, dayOfMonth)
                etEditTaskExpirationDate.setText(sdfDate.format(editCalendar.time))
            }
            DatePickerDialog(this, dateSetListener, editCalendar.get(Calendar.YEAR), editCalendar.get(Calendar.MONTH), editCalendar.get(Calendar.DAY_OF_MONTH)
            ).apply {
                datePicker.minDate = System.currentTimeMillis() - 1000
                show()
            }
        }

        MaterialAlertDialogBuilder(this)
            .setTitle(getString(R.string.edit_task_dialog_title))
            .setView(dialogView)
            .setNegativeButton(getString(R.string.dialog_cancel), null)
            .setPositiveButton(getString(R.string.save_button)) { _, _ ->
                val newName = etEditTaskName.text.toString().trim()
                val newReminderDaysStr = etEditTaskReminderDays.text.toString()
                val isCompleted = cbTaskCompleted.isChecked

                if (newName.isEmpty()) {
                    Toast.makeText(this, "Task name cannot be empty", Toast.LENGTH_SHORT).show()
                    return@setPositiveButton
                }

                val newReminderDays = if (newReminderDaysStr.isNotEmpty()) newReminderDaysStr.toIntOrNull() else null
                if (newReminderDaysStr.isNotEmpty() && newReminderDays == null) {
                    Toast.makeText(this, "Invalid reminder days", Toast.LENGTH_SHORT).show()
                    return@setPositiveButton
                }

                val updatedTask = taskToEdit.copy(
                    name = newName,
                    expirationDate = editCalendar.time, // Date is from editCalendar
                    reminderDaysBefore = newReminderDays,
                    isCompleted = isCompleted
                )
                taskViewModel.update(updatedTask)
                Toast.makeText(this, getString(R.string.task_updated_success, newName), Toast.LENGTH_SHORT).show()
            }
            .show()
    }

    override fun onDeleteTask(task: Task) {
        MaterialAlertDialogBuilder(this)
            .setTitle(getString(R.string.confirm_delete_task_title))
            .setMessage(getString(R.string.confirm_delete_task_message, task.name))
            .setNegativeButton(getString(R.string.dialog_cancel), null)
            .setPositiveButton(getString(R.string.action_delete)) { _, _ ->
                taskViewModel.delete(task)
                Toast.makeText(this, getString(R.string.task_deleted_success, task.name), Toast.LENGTH_SHORT).show()
            }
            .show()
    }

    override fun onAddTaskToCalendar(task: Task) {
        taskToAddToCalendar = task
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_CALENDAR) == PackageManager.PERMISSION_GRANTED &&
            ContextCompat.checkSelfPermission(this, Manifest.permission.READ_CALENDAR) == PackageManager.PERMISSION_GRANTED) {
            addTaskToDeviceCalendar(task)
        } else {
            requestCalendarPermissionLauncher.launch(arrayOf(Manifest.permission.WRITE_CALENDAR, Manifest.permission.READ_CALENDAR))
        }
    }

    private fun addTaskToDeviceCalendar(task: Task) {
        val calIntent = Intent(Intent.ACTION_INSERT).apply {
            data = CalendarContract.Events.CONTENT_URI
            putExtra(CalendarContract.Events.TITLE, task.name)

            val beginTime = Calendar.getInstance().apply { time = task.expirationDate }
            // For tasks, expirationDate usually means the end of that day or a specific time on that day.
            // If we want it to be an all-day event for the expirationDate:
            putExtra(CalendarContract.Events.ALL_DAY, true)
            putExtra(CalendarContract.EXTRA_EVENT_BEGIN_TIME, beginTime.timeInMillis)
            putExtra(CalendarContract.EXTRA_EVENT_END_TIME, beginTime.timeInMillis) // For single all-day event
        }

        try {
            startActivity(calIntent)
        } catch (e: Exception) {
            Toast.makeText(this, "Could not open calendar app.", Toast.LENGTH_SHORT).show()
        }
        taskToAddToCalendar = null
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.task_management_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_view_task_report -> {
                startActivity(Intent(this, TaskReportActivity::class.java))
                true
            }
            R.id.action_reminder_settings -> {
                startActivity(Intent(this, TaskReminderSettingsActivity::class.java))
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    // Enhanced interface implementations
    override fun onCompleteTask(task: Task) {
        taskViewModel.completeTask(task.id)
        showSnackbar("تم إكمال المهمة ✅")
    }

    override fun onUncompleteTask(task: Task) {
        taskViewModel.uncompleteTask(task.id)
        showSnackbar("تم إلغاء إكمال المهمة")
    }

    override fun onUpdateProgress(task: Task) {
        // Show progress update dialog
        showProgressUpdateDialog(task)
    }

    override fun onAddSubtask(task: Task) {
        // Show add subtask dialog
        showAddSubtaskDialog(task)
    }

    override fun onSetReminder(task: Task) {
        // Show reminder settings dialog
        showReminderDialog(task)
    }

    override fun onShareTask(task: Task) {
        // Share task details
        shareTask(task)
    }

    override fun onStartTask(task: Task) {
        // Start focus session for task
        startFocusSession(task)
    }

    override fun onScheduleTask(task: Task) {
        // Show scheduling dialog
        showSchedulingDialog(task)
    }

    override fun onShowSuggestions(task: Task) {
        // Show AI suggestions for task
        showTaskSuggestions(task)
    }

    override fun onTaskClick(task: Task) {
        // Show task details or edit
        onEditTask(task)
    }

    // Helper methods for enhanced functionality
    private fun showProgressUpdateDialog(task: Task) {
        val builder = androidx.appcompat.app.AlertDialog.Builder(this)
        val view = layoutInflater.inflate(android.R.layout.select_dialog_item, null)

        // Create a simple progress dialog for now
        val progressOptions = arrayOf("0%", "25%", "50%", "75%", "100%")
        val progressValues = arrayOf(0, 25, 50, 75, 100)

        builder.setTitle("تحديث تقدم المهمة")
            .setItems(progressOptions) { _, which ->
                taskViewModel.updateTaskProgress(task.id, progressValues[which])
                showSnackbar("تم تحديث التقدم إلى ${progressValues[which]}%")
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun showAddSubtaskDialog(task: Task) {
        val builder = androidx.appcompat.app.AlertDialog.Builder(this)
        val editText = android.widget.EditText(this)
        editText.hint = "اسم المهمة الفرعية"

        builder.setTitle("إضافة مهمة فرعية")
            .setView(editText)
            .setPositiveButton("إضافة") { _, _ ->
                val subtaskName = editText.text.toString().trim()
                if (subtaskName.isNotEmpty()) {
                    val subtask = com.example.kpitrackerapp.models.Subtask(
                        parentTaskId = task.id,
                        name = subtaskName
                    )
                    taskViewModel.insertSubtask(subtask)
                    showSnackbar("تم إضافة المهمة الفرعية")
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun showReminderDialog(task: Task) {
        val options = arrayOf("قبل ساعة", "قبل يوم", "قبل أسبوع", "مخصص")

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("تعيين تذكير")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> setTaskReminder(task, 1) // 1 hour
                    1 -> setTaskReminder(task, 24) // 1 day
                    2 -> setTaskReminder(task, 168) // 1 week
                    3 -> showCustomReminderDialog(task)
                }
            }
            .show()
    }

    private fun showCustomReminderDialog(task: Task) {
        val dialogView = layoutInflater.inflate(R.layout.dialog_custom_reminder, null)
        val datePicker = dialogView.findViewById<DatePicker>(R.id.datePicker)
        val timePicker = dialogView.findViewById<TimePicker>(R.id.timePicker)

        // Set minimum date to today
        datePicker.minDate = System.currentTimeMillis()

        MaterialAlertDialogBuilder(this)
            .setTitle("⏰ تعيين تذكير مخصص")
            .setView(dialogView)
            .setPositiveButton("تعيين") { _, _ ->
                val calendar = Calendar.getInstance().apply {
                    set(Calendar.YEAR, datePicker.year)
                    set(Calendar.MONTH, datePicker.month)
                    set(Calendar.DAY_OF_MONTH, datePicker.dayOfMonth)
                    set(Calendar.HOUR_OF_DAY, timePicker.hour)
                    set(Calendar.MINUTE, timePicker.minute)
                    set(Calendar.SECOND, 0)
                }

                setCustomTaskReminder(task, calendar.time)
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun setTaskReminder(task: Task, hoursBeforeDue: Int) {
        val reminderTime = Calendar.getInstance().apply {
            time = task.expirationDate
            add(Calendar.HOUR_OF_DAY, -hoursBeforeDue)
        }

        // Use AlarmClockManager to set the reminder
        val alarmManager = AlarmClockManager(this)
        val success = alarmManager.addTaskToAlarmClock(task, reminderTime)

        if (success) {
            showSnackbar("✅ تم تعيين التذكير قبل $hoursBeforeDue ساعة")
        } else {
            showSnackbar("❌ فشل في تعيين التذكير")
        }
    }

    private fun setCustomTaskReminder(task: Task, reminderDateTime: Date) {
        val alarmManager = AlarmClockManager(this)
        val reminderCalendar = Calendar.getInstance().apply { time = reminderDateTime }
        val success = alarmManager.addTaskToAlarmClock(task, reminderCalendar)

        val dateFormat = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault())
        val formattedDate = dateFormat.format(reminderDateTime)

        if (success) {
            showSnackbar("✅ تم تعيين التذكير في $formattedDate")
        } else {
            showSnackbar("❌ فشل في تعيين التذكير")
        }
    }

    private fun shareTask(task: Task) {
        val shareText = buildString {
            append("📋 ${task.name}\n")
            if (!task.description.isNullOrBlank()) {
                append("📝 ${task.description}\n")
            }
            append("📅 موعد الانتهاء: ${java.text.SimpleDateFormat("dd/MM/yyyy", java.util.Locale.getDefault()).format(task.expirationDate)}\n")
            append("⚡ الأولوية: ${task.priority.displayName}\n")
            append("📊 التقدم: ${task.progress}%")
        }

        val shareIntent = Intent().apply {
            action = Intent.ACTION_SEND
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, shareText)
            putExtra(Intent.EXTRA_SUBJECT, "مهمة: ${task.name}")
        }

        startActivity(Intent.createChooser(shareIntent, "مشاركة المهمة"))
    }

    private fun startFocusSession(task: Task) {
        showPomodoroDialog(task)
    }

    private fun showPomodoroDialog(task: Task) {
        val options = arrayOf(
            "🍅 بومودورو قصير (25 دقيقة)",
            "🍅 بومودورو طويل (50 دقيقة)",
            "⏰ وقت مخصص"
        )

        MaterialAlertDialogBuilder(this)
            .setTitle("🍅 جلسة التركيز")
            .setMessage("اختر مدة جلسة التركيز للمهمة: ${task.name}")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> startPomodoroTimer(task, 25)
                    1 -> startPomodoroTimer(task, 50)
                    2 -> showCustomPomodoroDialog(task)
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun showCustomPomodoroDialog(task: Task) {
        val dialogView = layoutInflater.inflate(R.layout.dialog_custom_pomodoro, null)
        val minutesInput = dialogView.findViewById<TextInputEditText>(R.id.minutesInput)

        MaterialAlertDialogBuilder(this)
            .setTitle("⏰ وقت مخصص")
            .setView(dialogView)
            .setPositiveButton("بدء") { _, _ ->
                val minutes = minutesInput.text.toString().toIntOrNull() ?: 25
                if (minutes in 1..180) {
                    startPomodoroTimer(task, minutes)
                } else {
                    showSnackbar("⚠️ يرجى إدخال وقت بين 1-180 دقيقة")
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun startPomodoroTimer(task: Task, minutes: Int) {
        val intent = Intent(this, PomodoroTimerActivity::class.java).apply {
            putExtra("TASK_ID", task.id)
            putExtra("TASK_NAME", task.name)
            putExtra("TIMER_MINUTES", minutes)
        }
        startActivity(intent)
        showSnackbar("🍅 بدء جلسة التركيز لمدة $minutes دقيقة")
    }

    private fun showSchedulingDialog(task: Task) {
        val options = arrayOf(
            "📅 جدولة لليوم التالي",
            "📆 جدولة لنهاية الأسبوع",
            "🗓️ جدولة مخصصة",
            "⏰ جدولة ذكية (حسب الأولوية)"
        )

        MaterialAlertDialogBuilder(this)
            .setTitle("📋 جدولة المهمة")
            .setMessage("كيف تريد جدولة المهمة: ${task.name}؟")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> scheduleTaskForTomorrow(task)
                    1 -> scheduleTaskForWeekend(task)
                    2 -> showCustomSchedulingDialog(task)
                    3 -> scheduleTaskSmartly(task)
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun scheduleTaskForTomorrow(task: Task) {
        val tomorrow = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_YEAR, 1)
            set(Calendar.HOUR_OF_DAY, 9) // 9 AM
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
        }

        updateTaskSchedule(task, tomorrow.time, "📅 تم جدولة المهمة لغداً في 9:00 صباحاً")
    }

    private fun scheduleTaskForWeekend(task: Task) {
        val weekend = Calendar.getInstance().apply {
            // Find next Saturday
            while (get(Calendar.DAY_OF_WEEK) != Calendar.SATURDAY) {
                add(Calendar.DAY_OF_YEAR, 1)
            }
            set(Calendar.HOUR_OF_DAY, 10) // 10 AM
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
        }

        updateTaskSchedule(task, weekend.time, "📆 تم جدولة المهمة لنهاية الأسبوع")
    }

    private fun showCustomSchedulingDialog(task: Task) {
        val dialogView = layoutInflater.inflate(R.layout.dialog_custom_reminder, null)
        val datePicker = dialogView.findViewById<DatePicker>(R.id.datePicker)
        val timePicker = dialogView.findViewById<TimePicker>(R.id.timePicker)

        // Set minimum date to today
        datePicker.minDate = System.currentTimeMillis()

        MaterialAlertDialogBuilder(this)
            .setTitle("🗓️ جدولة مخصصة")
            .setView(dialogView)
            .setPositiveButton("جدولة") { _, _ ->
                val calendar = Calendar.getInstance().apply {
                    set(Calendar.YEAR, datePicker.year)
                    set(Calendar.MONTH, datePicker.month)
                    set(Calendar.DAY_OF_MONTH, datePicker.dayOfMonth)
                    set(Calendar.HOUR_OF_DAY, timePicker.hour)
                    set(Calendar.MINUTE, timePicker.minute)
                    set(Calendar.SECOND, 0)
                }

                val dateFormat = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault())
                updateTaskSchedule(task, calendar.time, "🗓️ تم جدولة المهمة في ${dateFormat.format(calendar.time)}")
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun scheduleTaskSmartly(task: Task) {
        val smartTime = when (task.priority) {
            TaskPriority.URGENT, TaskPriority.HIGH -> {
                // High priority: schedule for next available morning
                Calendar.getInstance().apply {
                    if (get(Calendar.HOUR_OF_DAY) >= 17) {
                        add(Calendar.DAY_OF_YEAR, 1)
                    }
                    set(Calendar.HOUR_OF_DAY, 8)
                    set(Calendar.MINUTE, 0)
                    set(Calendar.SECOND, 0)
                }
            }
            TaskPriority.MEDIUM -> {
                // Medium priority: schedule for afternoon
                Calendar.getInstance().apply {
                    if (get(Calendar.HOUR_OF_DAY) >= 15) {
                        add(Calendar.DAY_OF_YEAR, 1)
                    }
                    set(Calendar.HOUR_OF_DAY, 14)
                    set(Calendar.MINUTE, 0)
                    set(Calendar.SECOND, 0)
                }
            }
            else -> {
                // Low priority: schedule for weekend
                Calendar.getInstance().apply {
                    while (get(Calendar.DAY_OF_WEEK) != Calendar.SATURDAY) {
                        add(Calendar.DAY_OF_YEAR, 1)
                    }
                    set(Calendar.HOUR_OF_DAY, 15)
                    set(Calendar.MINUTE, 0)
                    set(Calendar.SECOND, 0)
                }
            }
        }

        updateTaskSchedule(task, smartTime.time, "🤖 تم جدولة المهمة ذكياً حسب الأولوية")
    }

    private fun updateTaskSchedule(task: Task, newDate: Date, message: String) {
        val updatedTask = task.copy(expirationDate = newDate)
        taskViewModel.update(updatedTask)
        showSnackbar(message)

        // Set reminder for the new scheduled time
        setTaskReminder(updatedTask, 24) // 24 hours before
    }

    private fun showTaskSuggestions(task: Task) {
        val suggestions = listOf(
            "💡 قسم هذه المهمة إلى مهام فرعية أصغر",
            "⏰ أفضل وقت لتنفيذ هذه المهمة: 9-11 صباحاً",
            "🔋 هذه المهمة تحتاج طاقة عالية",
            "📚 ابحث عن موارد إضافية لهذا الموضوع"
        )

        val randomSuggestion = suggestions.random()

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🤖 اقتراح ذكي")
            .setMessage(randomSuggestion)
            .setPositiveButton("شكراً", null)
            .setNegativeButton("اقتراح آخر") { _, _ ->
                showTaskSuggestions(task)
            }
            .show()
    }

    private fun showSnackbar(message: String) {
        com.google.android.material.snackbar.Snackbar.make(
            binding.root,
            message,
            com.google.android.material.snackbar.Snackbar.LENGTH_SHORT
        ).show()
    }
}
