<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F8F9FA"
    tools:context=".ui.PomodoroTimerActivity">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp"
            android:gravity="center">

            <!-- Task Info Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp"
                    android:gravity="center">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🍅"
                        android:textSize="32sp"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/taskNameText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="اسم المهمة"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="#1A1A1A"
                        android:textAlignment="center"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/timerDurationText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="25 دقيقة"
                        android:textSize="14sp"
                        android:textColor="#6B7280" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Timer Display -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="280dp"
                android:layout_height="280dp"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="140dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="@color/white">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="20dp">

                    <ProgressBar
                        android:id="@+id/progressBar"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_centerInParent="true"
                        android:indeterminate="false"
                        android:max="100"
                        android:progress="0"
                        android:progressDrawable="@drawable/circular_progress_bar" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/timerDisplay"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="25:00"
                            android:textSize="36sp"
                            android:textStyle="bold"
                            android:textColor="#1A1A1A"
                            android:fontFamily="monospace" />

                        <TextView
                            android:id="@+id/progressText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0%"
                            android:textSize="14sp"
                            android:textColor="#6B7280"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                </RelativeLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Status Text -->
            <TextView
                android:id="@+id/timerStatusText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="جاهز للبدء"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#6B7280"
                android:layout_marginBottom="32dp" />

            <!-- Control Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_marginBottom="16dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/startPauseButton"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="2"
                    android:layout_marginEnd="8dp"
                    android:text="▶️ بدء"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:cornerRadius="16dp"
                    app:backgroundTint="#10B981" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/stopButton"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="⏹️"
                    android:textSize="18sp"
                    app:cornerRadius="16dp"
                    app:strokeColor="#EF4444"
                    android:textColor="#EF4444" />

            </LinearLayout>

            <!-- Additional Controls -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/addTimeButton"
                    style="@style/Widget.MaterialComponents.Button.TextButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="⏰ +5 دقائق"
                    android:textColor="#3B82F6"
                    app:icon="@drawable/ic_add_time"
                    app:iconTint="#3B82F6" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/skipBreakButton"
                    style="@style/Widget.MaterialComponents.Button.TextButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="⏭️ تخطي الاستراحة"
                    android:textColor="#F59E0B"
                    android:visibility="gone"
                    app:icon="@drawable/ic_skip"
                    app:iconTint="#F59E0B" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
