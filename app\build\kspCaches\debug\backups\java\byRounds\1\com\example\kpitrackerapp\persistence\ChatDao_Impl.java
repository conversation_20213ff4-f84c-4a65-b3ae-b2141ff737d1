package com.example.kpitrackerapp.persistence;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.kpitrackerapp.models.AttachmentType;
import com.example.kpitrackerapp.models.ChatMessage;
import com.example.kpitrackerapp.models.Conversation;
import com.example.kpitrackerapp.models.MessageType;
import com.example.kpitrackerapp.models.User;
import com.example.kpitrackerapp.models.UserRole;
import java.lang.Class;
import java.lang.Exception;
import java.lang.IllegalArgumentException;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class ChatDao_Impl implements ChatDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<ChatMessage> __insertionAdapterOfChatMessage;

  private final EntityInsertionAdapter<Conversation> __insertionAdapterOfConversation;

  private final EntityDeletionOrUpdateAdapter<ChatMessage> __deletionAdapterOfChatMessage;

  private final EntityDeletionOrUpdateAdapter<Conversation> __deletionAdapterOfConversation;

  private final EntityDeletionOrUpdateAdapter<ChatMessage> __updateAdapterOfChatMessage;

  private final EntityDeletionOrUpdateAdapter<Conversation> __updateAdapterOfConversation;

  private final SharedSQLiteStatement __preparedStmtOfDeleteMessageById;

  private final SharedSQLiteStatement __preparedStmtOfMarkMessagesAsRead;

  private final SharedSQLiteStatement __preparedStmtOfMarkMessagesAsDelivered;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllMessagesInConversation;

  private final SharedSQLiteStatement __preparedStmtOfClearUnreadCountForParticipant1;

  private final SharedSQLiteStatement __preparedStmtOfClearUnreadCountForParticipant2;

  private final SharedSQLiteStatement __preparedStmtOfUpdateConversationWithNewMessage;

  private final SharedSQLiteStatement __preparedStmtOfSetArchiveStatusForParticipant1;

  private final SharedSQLiteStatement __preparedStmtOfSetArchiveStatusForParticipant2;

  private final SharedSQLiteStatement __preparedStmtOfSetMuteStatusForParticipant1;

  private final SharedSQLiteStatement __preparedStmtOfSetMuteStatusForParticipant2;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldMessages;

  public ChatDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfChatMessage = new EntityInsertionAdapter<ChatMessage>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `chat_messages` (`id`,`conversationId`,`senderId`,`receiverId`,`message`,`messageType`,`attachmentPath`,`attachmentType`,`timestamp`,`isRead`,`isDelivered`,`replyToMessageId`,`isEdited`,`editedAt`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ChatMessage entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getConversationId());
        statement.bindString(3, entity.getSenderId());
        statement.bindString(4, entity.getReceiverId());
        statement.bindString(5, entity.getMessage());
        statement.bindString(6, __MessageType_enumToString(entity.getMessageType()));
        if (entity.getAttachmentPath() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getAttachmentPath());
        }
        if (entity.getAttachmentType() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, __AttachmentType_enumToString(entity.getAttachmentType()));
        }
        statement.bindLong(9, entity.getTimestamp());
        final int _tmp = entity.isRead() ? 1 : 0;
        statement.bindLong(10, _tmp);
        final int _tmp_1 = entity.isDelivered() ? 1 : 0;
        statement.bindLong(11, _tmp_1);
        if (entity.getReplyToMessageId() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getReplyToMessageId());
        }
        final int _tmp_2 = entity.isEdited() ? 1 : 0;
        statement.bindLong(13, _tmp_2);
        if (entity.getEditedAt() == null) {
          statement.bindNull(14);
        } else {
          statement.bindLong(14, entity.getEditedAt());
        }
      }
    };
    this.__insertionAdapterOfConversation = new EntityInsertionAdapter<Conversation>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `conversations` (`id`,`participant1Id`,`participant2Id`,`lastMessage`,`lastMessageTime`,`lastMessageSenderId`,`unreadCount1`,`unreadCount2`,`isArchived1`,`isArchived2`,`isMuted1`,`isMuted2`,`createdAt`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Conversation entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getParticipant1Id());
        statement.bindString(3, entity.getParticipant2Id());
        if (entity.getLastMessage() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getLastMessage());
        }
        statement.bindLong(5, entity.getLastMessageTime());
        if (entity.getLastMessageSenderId() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getLastMessageSenderId());
        }
        statement.bindLong(7, entity.getUnreadCount1());
        statement.bindLong(8, entity.getUnreadCount2());
        final int _tmp = entity.isArchived1() ? 1 : 0;
        statement.bindLong(9, _tmp);
        final int _tmp_1 = entity.isArchived2() ? 1 : 0;
        statement.bindLong(10, _tmp_1);
        final int _tmp_2 = entity.isMuted1() ? 1 : 0;
        statement.bindLong(11, _tmp_2);
        final int _tmp_3 = entity.isMuted2() ? 1 : 0;
        statement.bindLong(12, _tmp_3);
        statement.bindLong(13, entity.getCreatedAt());
      }
    };
    this.__deletionAdapterOfChatMessage = new EntityDeletionOrUpdateAdapter<ChatMessage>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `chat_messages` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ChatMessage entity) {
        statement.bindString(1, entity.getId());
      }
    };
    this.__deletionAdapterOfConversation = new EntityDeletionOrUpdateAdapter<Conversation>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `conversations` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Conversation entity) {
        statement.bindString(1, entity.getId());
      }
    };
    this.__updateAdapterOfChatMessage = new EntityDeletionOrUpdateAdapter<ChatMessage>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `chat_messages` SET `id` = ?,`conversationId` = ?,`senderId` = ?,`receiverId` = ?,`message` = ?,`messageType` = ?,`attachmentPath` = ?,`attachmentType` = ?,`timestamp` = ?,`isRead` = ?,`isDelivered` = ?,`replyToMessageId` = ?,`isEdited` = ?,`editedAt` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ChatMessage entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getConversationId());
        statement.bindString(3, entity.getSenderId());
        statement.bindString(4, entity.getReceiverId());
        statement.bindString(5, entity.getMessage());
        statement.bindString(6, __MessageType_enumToString(entity.getMessageType()));
        if (entity.getAttachmentPath() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getAttachmentPath());
        }
        if (entity.getAttachmentType() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, __AttachmentType_enumToString(entity.getAttachmentType()));
        }
        statement.bindLong(9, entity.getTimestamp());
        final int _tmp = entity.isRead() ? 1 : 0;
        statement.bindLong(10, _tmp);
        final int _tmp_1 = entity.isDelivered() ? 1 : 0;
        statement.bindLong(11, _tmp_1);
        if (entity.getReplyToMessageId() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getReplyToMessageId());
        }
        final int _tmp_2 = entity.isEdited() ? 1 : 0;
        statement.bindLong(13, _tmp_2);
        if (entity.getEditedAt() == null) {
          statement.bindNull(14);
        } else {
          statement.bindLong(14, entity.getEditedAt());
        }
        statement.bindString(15, entity.getId());
      }
    };
    this.__updateAdapterOfConversation = new EntityDeletionOrUpdateAdapter<Conversation>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `conversations` SET `id` = ?,`participant1Id` = ?,`participant2Id` = ?,`lastMessage` = ?,`lastMessageTime` = ?,`lastMessageSenderId` = ?,`unreadCount1` = ?,`unreadCount2` = ?,`isArchived1` = ?,`isArchived2` = ?,`isMuted1` = ?,`isMuted2` = ?,`createdAt` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Conversation entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getParticipant1Id());
        statement.bindString(3, entity.getParticipant2Id());
        if (entity.getLastMessage() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getLastMessage());
        }
        statement.bindLong(5, entity.getLastMessageTime());
        if (entity.getLastMessageSenderId() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getLastMessageSenderId());
        }
        statement.bindLong(7, entity.getUnreadCount1());
        statement.bindLong(8, entity.getUnreadCount2());
        final int _tmp = entity.isArchived1() ? 1 : 0;
        statement.bindLong(9, _tmp);
        final int _tmp_1 = entity.isArchived2() ? 1 : 0;
        statement.bindLong(10, _tmp_1);
        final int _tmp_2 = entity.isMuted1() ? 1 : 0;
        statement.bindLong(11, _tmp_2);
        final int _tmp_3 = entity.isMuted2() ? 1 : 0;
        statement.bindLong(12, _tmp_3);
        statement.bindLong(13, entity.getCreatedAt());
        statement.bindString(14, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteMessageById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM chat_messages WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfMarkMessagesAsRead = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE chat_messages SET isRead = 1 WHERE conversationId = ? AND receiverId = ? AND isRead = 0";
        return _query;
      }
    };
    this.__preparedStmtOfMarkMessagesAsDelivered = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE chat_messages SET isDelivered = 1 WHERE conversationId = ? AND senderId != ? AND isDelivered = 0";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllMessagesInConversation = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM chat_messages WHERE conversationId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearUnreadCountForParticipant1 = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE conversations SET unreadCount1 = 0 WHERE id = ? AND participant1Id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearUnreadCountForParticipant2 = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE conversations SET unreadCount2 = 0 WHERE id = ? AND participant2Id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateConversationWithNewMessage = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        UPDATE conversations\n"
                + "        SET unreadCount1 = CASE WHEN participant1Id = ? THEN unreadCount1 + 1 ELSE unreadCount1 END,\n"
                + "            unreadCount2 = CASE WHEN participant2Id = ? THEN unreadCount2 + 1 ELSE unreadCount2 END,\n"
                + "            lastMessage = ?,\n"
                + "            lastMessageTime = ?,\n"
                + "            lastMessageSenderId = ?\n"
                + "        WHERE id = ?\n"
                + "    ";
        return _query;
      }
    };
    this.__preparedStmtOfSetArchiveStatusForParticipant1 = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE conversations SET isArchived1 = ? WHERE id = ? AND participant1Id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfSetArchiveStatusForParticipant2 = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE conversations SET isArchived2 = ? WHERE id = ? AND participant2Id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfSetMuteStatusForParticipant1 = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE conversations SET isMuted1 = ? WHERE id = ? AND participant1Id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfSetMuteStatusForParticipant2 = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE conversations SET isMuted2 = ? WHERE id = ? AND participant2Id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteOldMessages = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM chat_messages WHERE timestamp < ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertMessage(final ChatMessage message,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfChatMessage.insertAndReturnId(message);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertMessages(final List<ChatMessage> messages,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfChatMessage.insert(messages);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertConversation(final Conversation conversation,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfConversation.insertAndReturnId(conversation);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteMessage(final ChatMessage message,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfChatMessage.handle(message);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteConversation(final Conversation conversation,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfConversation.handle(conversation);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateMessage(final ChatMessage message,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfChatMessage.handle(message);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateConversation(final Conversation conversation,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfConversation.handle(conversation);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteMessageById(final String messageId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteMessageById.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, messageId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteMessageById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object markMessagesAsRead(final String conversationId, final String userId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfMarkMessagesAsRead.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, conversationId);
        _argIndex = 2;
        _stmt.bindString(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfMarkMessagesAsRead.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object markMessagesAsDelivered(final String conversationId, final String userId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfMarkMessagesAsDelivered.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, conversationId);
        _argIndex = 2;
        _stmt.bindString(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfMarkMessagesAsDelivered.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllMessagesInConversation(final String conversationId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllMessagesInConversation.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, conversationId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllMessagesInConversation.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearUnreadCountForParticipant1(final String conversationId, final String userId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearUnreadCountForParticipant1.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, conversationId);
        _argIndex = 2;
        _stmt.bindString(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearUnreadCountForParticipant1.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearUnreadCountForParticipant2(final String conversationId, final String userId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearUnreadCountForParticipant2.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, conversationId);
        _argIndex = 2;
        _stmt.bindString(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearUnreadCountForParticipant2.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateConversationWithNewMessage(final String conversationId,
      final String lastMessage, final long timestamp, final String senderId,
      final String receiverId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateConversationWithNewMessage.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, receiverId);
        _argIndex = 2;
        _stmt.bindString(_argIndex, receiverId);
        _argIndex = 3;
        _stmt.bindString(_argIndex, lastMessage);
        _argIndex = 4;
        _stmt.bindLong(_argIndex, timestamp);
        _argIndex = 5;
        _stmt.bindString(_argIndex, senderId);
        _argIndex = 6;
        _stmt.bindString(_argIndex, conversationId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateConversationWithNewMessage.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object setArchiveStatusForParticipant1(final String conversationId, final String userId,
      final boolean isArchived, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfSetArchiveStatusForParticipant1.acquire();
        int _argIndex = 1;
        final int _tmp = isArchived ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 2;
        _stmt.bindString(_argIndex, conversationId);
        _argIndex = 3;
        _stmt.bindString(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfSetArchiveStatusForParticipant1.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object setArchiveStatusForParticipant2(final String conversationId, final String userId,
      final boolean isArchived, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfSetArchiveStatusForParticipant2.acquire();
        int _argIndex = 1;
        final int _tmp = isArchived ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 2;
        _stmt.bindString(_argIndex, conversationId);
        _argIndex = 3;
        _stmt.bindString(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfSetArchiveStatusForParticipant2.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object setMuteStatusForParticipant1(final String conversationId, final String userId,
      final boolean isMuted, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfSetMuteStatusForParticipant1.acquire();
        int _argIndex = 1;
        final int _tmp = isMuted ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 2;
        _stmt.bindString(_argIndex, conversationId);
        _argIndex = 3;
        _stmt.bindString(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfSetMuteStatusForParticipant1.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object setMuteStatusForParticipant2(final String conversationId, final String userId,
      final boolean isMuted, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfSetMuteStatusForParticipant2.acquire();
        int _argIndex = 1;
        final int _tmp = isMuted ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 2;
        _stmt.bindString(_argIndex, conversationId);
        _argIndex = 3;
        _stmt.bindString(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfSetMuteStatusForParticipant2.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteOldMessages(final long cutoffTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldMessages.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, cutoffTime);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldMessages.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<ChatMessage>> getMessagesForConversation(final String conversationId) {
    final String _sql = "SELECT * FROM chat_messages WHERE conversationId = ? ORDER BY timestamp ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, conversationId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"chat_messages"}, new Callable<List<ChatMessage>>() {
      @Override
      @NonNull
      public List<ChatMessage> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfConversationId = CursorUtil.getColumnIndexOrThrow(_cursor, "conversationId");
          final int _cursorIndexOfSenderId = CursorUtil.getColumnIndexOrThrow(_cursor, "senderId");
          final int _cursorIndexOfReceiverId = CursorUtil.getColumnIndexOrThrow(_cursor, "receiverId");
          final int _cursorIndexOfMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "message");
          final int _cursorIndexOfMessageType = CursorUtil.getColumnIndexOrThrow(_cursor, "messageType");
          final int _cursorIndexOfAttachmentPath = CursorUtil.getColumnIndexOrThrow(_cursor, "attachmentPath");
          final int _cursorIndexOfAttachmentType = CursorUtil.getColumnIndexOrThrow(_cursor, "attachmentType");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfIsRead = CursorUtil.getColumnIndexOrThrow(_cursor, "isRead");
          final int _cursorIndexOfIsDelivered = CursorUtil.getColumnIndexOrThrow(_cursor, "isDelivered");
          final int _cursorIndexOfReplyToMessageId = CursorUtil.getColumnIndexOrThrow(_cursor, "replyToMessageId");
          final int _cursorIndexOfIsEdited = CursorUtil.getColumnIndexOrThrow(_cursor, "isEdited");
          final int _cursorIndexOfEditedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "editedAt");
          final List<ChatMessage> _result = new ArrayList<ChatMessage>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ChatMessage _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpConversationId;
            _tmpConversationId = _cursor.getString(_cursorIndexOfConversationId);
            final String _tmpSenderId;
            _tmpSenderId = _cursor.getString(_cursorIndexOfSenderId);
            final String _tmpReceiverId;
            _tmpReceiverId = _cursor.getString(_cursorIndexOfReceiverId);
            final String _tmpMessage;
            _tmpMessage = _cursor.getString(_cursorIndexOfMessage);
            final MessageType _tmpMessageType;
            _tmpMessageType = __MessageType_stringToEnum(_cursor.getString(_cursorIndexOfMessageType));
            final String _tmpAttachmentPath;
            if (_cursor.isNull(_cursorIndexOfAttachmentPath)) {
              _tmpAttachmentPath = null;
            } else {
              _tmpAttachmentPath = _cursor.getString(_cursorIndexOfAttachmentPath);
            }
            final AttachmentType _tmpAttachmentType;
            if (_cursor.isNull(_cursorIndexOfAttachmentType)) {
              _tmpAttachmentType = null;
            } else {
              _tmpAttachmentType = __AttachmentType_stringToEnum(_cursor.getString(_cursorIndexOfAttachmentType));
            }
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final boolean _tmpIsRead;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsRead);
            _tmpIsRead = _tmp != 0;
            final boolean _tmpIsDelivered;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDelivered);
            _tmpIsDelivered = _tmp_1 != 0;
            final String _tmpReplyToMessageId;
            if (_cursor.isNull(_cursorIndexOfReplyToMessageId)) {
              _tmpReplyToMessageId = null;
            } else {
              _tmpReplyToMessageId = _cursor.getString(_cursorIndexOfReplyToMessageId);
            }
            final boolean _tmpIsEdited;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsEdited);
            _tmpIsEdited = _tmp_2 != 0;
            final Long _tmpEditedAt;
            if (_cursor.isNull(_cursorIndexOfEditedAt)) {
              _tmpEditedAt = null;
            } else {
              _tmpEditedAt = _cursor.getLong(_cursorIndexOfEditedAt);
            }
            _item = new ChatMessage(_tmpId,_tmpConversationId,_tmpSenderId,_tmpReceiverId,_tmpMessage,_tmpMessageType,_tmpAttachmentPath,_tmpAttachmentType,_tmpTimestamp,_tmpIsRead,_tmpIsDelivered,_tmpReplyToMessageId,_tmpIsEdited,_tmpEditedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getMessagesForConversationPaged(final String conversationId, final int limit,
      final int offset, final Continuation<? super List<ChatMessage>> $completion) {
    final String _sql = "SELECT * FROM chat_messages WHERE conversationId = ? ORDER BY timestamp DESC LIMIT ? OFFSET ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    _statement.bindString(_argIndex, conversationId);
    _argIndex = 2;
    _statement.bindLong(_argIndex, limit);
    _argIndex = 3;
    _statement.bindLong(_argIndex, offset);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<ChatMessage>>() {
      @Override
      @NonNull
      public List<ChatMessage> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfConversationId = CursorUtil.getColumnIndexOrThrow(_cursor, "conversationId");
          final int _cursorIndexOfSenderId = CursorUtil.getColumnIndexOrThrow(_cursor, "senderId");
          final int _cursorIndexOfReceiverId = CursorUtil.getColumnIndexOrThrow(_cursor, "receiverId");
          final int _cursorIndexOfMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "message");
          final int _cursorIndexOfMessageType = CursorUtil.getColumnIndexOrThrow(_cursor, "messageType");
          final int _cursorIndexOfAttachmentPath = CursorUtil.getColumnIndexOrThrow(_cursor, "attachmentPath");
          final int _cursorIndexOfAttachmentType = CursorUtil.getColumnIndexOrThrow(_cursor, "attachmentType");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfIsRead = CursorUtil.getColumnIndexOrThrow(_cursor, "isRead");
          final int _cursorIndexOfIsDelivered = CursorUtil.getColumnIndexOrThrow(_cursor, "isDelivered");
          final int _cursorIndexOfReplyToMessageId = CursorUtil.getColumnIndexOrThrow(_cursor, "replyToMessageId");
          final int _cursorIndexOfIsEdited = CursorUtil.getColumnIndexOrThrow(_cursor, "isEdited");
          final int _cursorIndexOfEditedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "editedAt");
          final List<ChatMessage> _result = new ArrayList<ChatMessage>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ChatMessage _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpConversationId;
            _tmpConversationId = _cursor.getString(_cursorIndexOfConversationId);
            final String _tmpSenderId;
            _tmpSenderId = _cursor.getString(_cursorIndexOfSenderId);
            final String _tmpReceiverId;
            _tmpReceiverId = _cursor.getString(_cursorIndexOfReceiverId);
            final String _tmpMessage;
            _tmpMessage = _cursor.getString(_cursorIndexOfMessage);
            final MessageType _tmpMessageType;
            _tmpMessageType = __MessageType_stringToEnum(_cursor.getString(_cursorIndexOfMessageType));
            final String _tmpAttachmentPath;
            if (_cursor.isNull(_cursorIndexOfAttachmentPath)) {
              _tmpAttachmentPath = null;
            } else {
              _tmpAttachmentPath = _cursor.getString(_cursorIndexOfAttachmentPath);
            }
            final AttachmentType _tmpAttachmentType;
            if (_cursor.isNull(_cursorIndexOfAttachmentType)) {
              _tmpAttachmentType = null;
            } else {
              _tmpAttachmentType = __AttachmentType_stringToEnum(_cursor.getString(_cursorIndexOfAttachmentType));
            }
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final boolean _tmpIsRead;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsRead);
            _tmpIsRead = _tmp != 0;
            final boolean _tmpIsDelivered;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDelivered);
            _tmpIsDelivered = _tmp_1 != 0;
            final String _tmpReplyToMessageId;
            if (_cursor.isNull(_cursorIndexOfReplyToMessageId)) {
              _tmpReplyToMessageId = null;
            } else {
              _tmpReplyToMessageId = _cursor.getString(_cursorIndexOfReplyToMessageId);
            }
            final boolean _tmpIsEdited;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsEdited);
            _tmpIsEdited = _tmp_2 != 0;
            final Long _tmpEditedAt;
            if (_cursor.isNull(_cursorIndexOfEditedAt)) {
              _tmpEditedAt = null;
            } else {
              _tmpEditedAt = _cursor.getLong(_cursorIndexOfEditedAt);
            }
            _item = new ChatMessage(_tmpId,_tmpConversationId,_tmpSenderId,_tmpReceiverId,_tmpMessage,_tmpMessageType,_tmpAttachmentPath,_tmpAttachmentType,_tmpTimestamp,_tmpIsRead,_tmpIsDelivered,_tmpReplyToMessageId,_tmpIsEdited,_tmpEditedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getMessageById(final String messageId,
      final Continuation<? super ChatMessage> $completion) {
    final String _sql = "SELECT * FROM chat_messages WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, messageId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<ChatMessage>() {
      @Override
      @Nullable
      public ChatMessage call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfConversationId = CursorUtil.getColumnIndexOrThrow(_cursor, "conversationId");
          final int _cursorIndexOfSenderId = CursorUtil.getColumnIndexOrThrow(_cursor, "senderId");
          final int _cursorIndexOfReceiverId = CursorUtil.getColumnIndexOrThrow(_cursor, "receiverId");
          final int _cursorIndexOfMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "message");
          final int _cursorIndexOfMessageType = CursorUtil.getColumnIndexOrThrow(_cursor, "messageType");
          final int _cursorIndexOfAttachmentPath = CursorUtil.getColumnIndexOrThrow(_cursor, "attachmentPath");
          final int _cursorIndexOfAttachmentType = CursorUtil.getColumnIndexOrThrow(_cursor, "attachmentType");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfIsRead = CursorUtil.getColumnIndexOrThrow(_cursor, "isRead");
          final int _cursorIndexOfIsDelivered = CursorUtil.getColumnIndexOrThrow(_cursor, "isDelivered");
          final int _cursorIndexOfReplyToMessageId = CursorUtil.getColumnIndexOrThrow(_cursor, "replyToMessageId");
          final int _cursorIndexOfIsEdited = CursorUtil.getColumnIndexOrThrow(_cursor, "isEdited");
          final int _cursorIndexOfEditedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "editedAt");
          final ChatMessage _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpConversationId;
            _tmpConversationId = _cursor.getString(_cursorIndexOfConversationId);
            final String _tmpSenderId;
            _tmpSenderId = _cursor.getString(_cursorIndexOfSenderId);
            final String _tmpReceiverId;
            _tmpReceiverId = _cursor.getString(_cursorIndexOfReceiverId);
            final String _tmpMessage;
            _tmpMessage = _cursor.getString(_cursorIndexOfMessage);
            final MessageType _tmpMessageType;
            _tmpMessageType = __MessageType_stringToEnum(_cursor.getString(_cursorIndexOfMessageType));
            final String _tmpAttachmentPath;
            if (_cursor.isNull(_cursorIndexOfAttachmentPath)) {
              _tmpAttachmentPath = null;
            } else {
              _tmpAttachmentPath = _cursor.getString(_cursorIndexOfAttachmentPath);
            }
            final AttachmentType _tmpAttachmentType;
            if (_cursor.isNull(_cursorIndexOfAttachmentType)) {
              _tmpAttachmentType = null;
            } else {
              _tmpAttachmentType = __AttachmentType_stringToEnum(_cursor.getString(_cursorIndexOfAttachmentType));
            }
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final boolean _tmpIsRead;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsRead);
            _tmpIsRead = _tmp != 0;
            final boolean _tmpIsDelivered;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDelivered);
            _tmpIsDelivered = _tmp_1 != 0;
            final String _tmpReplyToMessageId;
            if (_cursor.isNull(_cursorIndexOfReplyToMessageId)) {
              _tmpReplyToMessageId = null;
            } else {
              _tmpReplyToMessageId = _cursor.getString(_cursorIndexOfReplyToMessageId);
            }
            final boolean _tmpIsEdited;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsEdited);
            _tmpIsEdited = _tmp_2 != 0;
            final Long _tmpEditedAt;
            if (_cursor.isNull(_cursorIndexOfEditedAt)) {
              _tmpEditedAt = null;
            } else {
              _tmpEditedAt = _cursor.getLong(_cursorIndexOfEditedAt);
            }
            _result = new ChatMessage(_tmpId,_tmpConversationId,_tmpSenderId,_tmpReceiverId,_tmpMessage,_tmpMessageType,_tmpAttachmentPath,_tmpAttachmentType,_tmpTimestamp,_tmpIsRead,_tmpIsDelivered,_tmpReplyToMessageId,_tmpIsEdited,_tmpEditedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getUnreadMessageCount(final String userId,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM chat_messages WHERE receiverId = ? AND isRead = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getUnreadMessageCountForConversation(final String conversationId,
      final String userId, final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM chat_messages WHERE conversationId = ? AND receiverId = ? AND isRead = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, conversationId);
    _argIndex = 2;
    _statement.bindString(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getLastMessageForConversation(final String conversationId,
      final Continuation<? super ChatMessage> $completion) {
    final String _sql = "SELECT * FROM chat_messages WHERE conversationId = ? ORDER BY timestamp DESC LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, conversationId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<ChatMessage>() {
      @Override
      @Nullable
      public ChatMessage call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfConversationId = CursorUtil.getColumnIndexOrThrow(_cursor, "conversationId");
          final int _cursorIndexOfSenderId = CursorUtil.getColumnIndexOrThrow(_cursor, "senderId");
          final int _cursorIndexOfReceiverId = CursorUtil.getColumnIndexOrThrow(_cursor, "receiverId");
          final int _cursorIndexOfMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "message");
          final int _cursorIndexOfMessageType = CursorUtil.getColumnIndexOrThrow(_cursor, "messageType");
          final int _cursorIndexOfAttachmentPath = CursorUtil.getColumnIndexOrThrow(_cursor, "attachmentPath");
          final int _cursorIndexOfAttachmentType = CursorUtil.getColumnIndexOrThrow(_cursor, "attachmentType");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfIsRead = CursorUtil.getColumnIndexOrThrow(_cursor, "isRead");
          final int _cursorIndexOfIsDelivered = CursorUtil.getColumnIndexOrThrow(_cursor, "isDelivered");
          final int _cursorIndexOfReplyToMessageId = CursorUtil.getColumnIndexOrThrow(_cursor, "replyToMessageId");
          final int _cursorIndexOfIsEdited = CursorUtil.getColumnIndexOrThrow(_cursor, "isEdited");
          final int _cursorIndexOfEditedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "editedAt");
          final ChatMessage _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpConversationId;
            _tmpConversationId = _cursor.getString(_cursorIndexOfConversationId);
            final String _tmpSenderId;
            _tmpSenderId = _cursor.getString(_cursorIndexOfSenderId);
            final String _tmpReceiverId;
            _tmpReceiverId = _cursor.getString(_cursorIndexOfReceiverId);
            final String _tmpMessage;
            _tmpMessage = _cursor.getString(_cursorIndexOfMessage);
            final MessageType _tmpMessageType;
            _tmpMessageType = __MessageType_stringToEnum(_cursor.getString(_cursorIndexOfMessageType));
            final String _tmpAttachmentPath;
            if (_cursor.isNull(_cursorIndexOfAttachmentPath)) {
              _tmpAttachmentPath = null;
            } else {
              _tmpAttachmentPath = _cursor.getString(_cursorIndexOfAttachmentPath);
            }
            final AttachmentType _tmpAttachmentType;
            if (_cursor.isNull(_cursorIndexOfAttachmentType)) {
              _tmpAttachmentType = null;
            } else {
              _tmpAttachmentType = __AttachmentType_stringToEnum(_cursor.getString(_cursorIndexOfAttachmentType));
            }
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final boolean _tmpIsRead;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsRead);
            _tmpIsRead = _tmp != 0;
            final boolean _tmpIsDelivered;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDelivered);
            _tmpIsDelivered = _tmp_1 != 0;
            final String _tmpReplyToMessageId;
            if (_cursor.isNull(_cursorIndexOfReplyToMessageId)) {
              _tmpReplyToMessageId = null;
            } else {
              _tmpReplyToMessageId = _cursor.getString(_cursorIndexOfReplyToMessageId);
            }
            final boolean _tmpIsEdited;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsEdited);
            _tmpIsEdited = _tmp_2 != 0;
            final Long _tmpEditedAt;
            if (_cursor.isNull(_cursorIndexOfEditedAt)) {
              _tmpEditedAt = null;
            } else {
              _tmpEditedAt = _cursor.getLong(_cursorIndexOfEditedAt);
            }
            _result = new ChatMessage(_tmpId,_tmpConversationId,_tmpSenderId,_tmpReceiverId,_tmpMessage,_tmpMessageType,_tmpAttachmentPath,_tmpAttachmentType,_tmpTimestamp,_tmpIsRead,_tmpIsDelivered,_tmpReplyToMessageId,_tmpIsEdited,_tmpEditedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object searchMessagesInConversation(final String conversationId, final String query,
      final Continuation<? super List<ChatMessage>> $completion) {
    final String _sql = "SELECT * FROM chat_messages WHERE conversationId = ? AND message LIKE '%' || ? || '%' ORDER BY timestamp DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, conversationId);
    _argIndex = 2;
    _statement.bindString(_argIndex, query);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<ChatMessage>>() {
      @Override
      @NonNull
      public List<ChatMessage> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfConversationId = CursorUtil.getColumnIndexOrThrow(_cursor, "conversationId");
          final int _cursorIndexOfSenderId = CursorUtil.getColumnIndexOrThrow(_cursor, "senderId");
          final int _cursorIndexOfReceiverId = CursorUtil.getColumnIndexOrThrow(_cursor, "receiverId");
          final int _cursorIndexOfMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "message");
          final int _cursorIndexOfMessageType = CursorUtil.getColumnIndexOrThrow(_cursor, "messageType");
          final int _cursorIndexOfAttachmentPath = CursorUtil.getColumnIndexOrThrow(_cursor, "attachmentPath");
          final int _cursorIndexOfAttachmentType = CursorUtil.getColumnIndexOrThrow(_cursor, "attachmentType");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfIsRead = CursorUtil.getColumnIndexOrThrow(_cursor, "isRead");
          final int _cursorIndexOfIsDelivered = CursorUtil.getColumnIndexOrThrow(_cursor, "isDelivered");
          final int _cursorIndexOfReplyToMessageId = CursorUtil.getColumnIndexOrThrow(_cursor, "replyToMessageId");
          final int _cursorIndexOfIsEdited = CursorUtil.getColumnIndexOrThrow(_cursor, "isEdited");
          final int _cursorIndexOfEditedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "editedAt");
          final List<ChatMessage> _result = new ArrayList<ChatMessage>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ChatMessage _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpConversationId;
            _tmpConversationId = _cursor.getString(_cursorIndexOfConversationId);
            final String _tmpSenderId;
            _tmpSenderId = _cursor.getString(_cursorIndexOfSenderId);
            final String _tmpReceiverId;
            _tmpReceiverId = _cursor.getString(_cursorIndexOfReceiverId);
            final String _tmpMessage;
            _tmpMessage = _cursor.getString(_cursorIndexOfMessage);
            final MessageType _tmpMessageType;
            _tmpMessageType = __MessageType_stringToEnum(_cursor.getString(_cursorIndexOfMessageType));
            final String _tmpAttachmentPath;
            if (_cursor.isNull(_cursorIndexOfAttachmentPath)) {
              _tmpAttachmentPath = null;
            } else {
              _tmpAttachmentPath = _cursor.getString(_cursorIndexOfAttachmentPath);
            }
            final AttachmentType _tmpAttachmentType;
            if (_cursor.isNull(_cursorIndexOfAttachmentType)) {
              _tmpAttachmentType = null;
            } else {
              _tmpAttachmentType = __AttachmentType_stringToEnum(_cursor.getString(_cursorIndexOfAttachmentType));
            }
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final boolean _tmpIsRead;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsRead);
            _tmpIsRead = _tmp != 0;
            final boolean _tmpIsDelivered;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDelivered);
            _tmpIsDelivered = _tmp_1 != 0;
            final String _tmpReplyToMessageId;
            if (_cursor.isNull(_cursorIndexOfReplyToMessageId)) {
              _tmpReplyToMessageId = null;
            } else {
              _tmpReplyToMessageId = _cursor.getString(_cursorIndexOfReplyToMessageId);
            }
            final boolean _tmpIsEdited;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsEdited);
            _tmpIsEdited = _tmp_2 != 0;
            final Long _tmpEditedAt;
            if (_cursor.isNull(_cursorIndexOfEditedAt)) {
              _tmpEditedAt = null;
            } else {
              _tmpEditedAt = _cursor.getLong(_cursorIndexOfEditedAt);
            }
            _item = new ChatMessage(_tmpId,_tmpConversationId,_tmpSenderId,_tmpReceiverId,_tmpMessage,_tmpMessageType,_tmpAttachmentPath,_tmpAttachmentType,_tmpTimestamp,_tmpIsRead,_tmpIsDelivered,_tmpReplyToMessageId,_tmpIsEdited,_tmpEditedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object searchAllMessages(final String userId, final String query,
      final Continuation<? super List<ChatMessage>> $completion) {
    final String _sql = "SELECT * FROM chat_messages WHERE (senderId = ? OR receiverId = ?) AND message LIKE '%' || ? || '%' ORDER BY timestamp DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    _argIndex = 2;
    _statement.bindString(_argIndex, userId);
    _argIndex = 3;
    _statement.bindString(_argIndex, query);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<ChatMessage>>() {
      @Override
      @NonNull
      public List<ChatMessage> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfConversationId = CursorUtil.getColumnIndexOrThrow(_cursor, "conversationId");
          final int _cursorIndexOfSenderId = CursorUtil.getColumnIndexOrThrow(_cursor, "senderId");
          final int _cursorIndexOfReceiverId = CursorUtil.getColumnIndexOrThrow(_cursor, "receiverId");
          final int _cursorIndexOfMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "message");
          final int _cursorIndexOfMessageType = CursorUtil.getColumnIndexOrThrow(_cursor, "messageType");
          final int _cursorIndexOfAttachmentPath = CursorUtil.getColumnIndexOrThrow(_cursor, "attachmentPath");
          final int _cursorIndexOfAttachmentType = CursorUtil.getColumnIndexOrThrow(_cursor, "attachmentType");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfIsRead = CursorUtil.getColumnIndexOrThrow(_cursor, "isRead");
          final int _cursorIndexOfIsDelivered = CursorUtil.getColumnIndexOrThrow(_cursor, "isDelivered");
          final int _cursorIndexOfReplyToMessageId = CursorUtil.getColumnIndexOrThrow(_cursor, "replyToMessageId");
          final int _cursorIndexOfIsEdited = CursorUtil.getColumnIndexOrThrow(_cursor, "isEdited");
          final int _cursorIndexOfEditedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "editedAt");
          final List<ChatMessage> _result = new ArrayList<ChatMessage>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ChatMessage _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpConversationId;
            _tmpConversationId = _cursor.getString(_cursorIndexOfConversationId);
            final String _tmpSenderId;
            _tmpSenderId = _cursor.getString(_cursorIndexOfSenderId);
            final String _tmpReceiverId;
            _tmpReceiverId = _cursor.getString(_cursorIndexOfReceiverId);
            final String _tmpMessage;
            _tmpMessage = _cursor.getString(_cursorIndexOfMessage);
            final MessageType _tmpMessageType;
            _tmpMessageType = __MessageType_stringToEnum(_cursor.getString(_cursorIndexOfMessageType));
            final String _tmpAttachmentPath;
            if (_cursor.isNull(_cursorIndexOfAttachmentPath)) {
              _tmpAttachmentPath = null;
            } else {
              _tmpAttachmentPath = _cursor.getString(_cursorIndexOfAttachmentPath);
            }
            final AttachmentType _tmpAttachmentType;
            if (_cursor.isNull(_cursorIndexOfAttachmentType)) {
              _tmpAttachmentType = null;
            } else {
              _tmpAttachmentType = __AttachmentType_stringToEnum(_cursor.getString(_cursorIndexOfAttachmentType));
            }
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final boolean _tmpIsRead;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsRead);
            _tmpIsRead = _tmp != 0;
            final boolean _tmpIsDelivered;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDelivered);
            _tmpIsDelivered = _tmp_1 != 0;
            final String _tmpReplyToMessageId;
            if (_cursor.isNull(_cursorIndexOfReplyToMessageId)) {
              _tmpReplyToMessageId = null;
            } else {
              _tmpReplyToMessageId = _cursor.getString(_cursorIndexOfReplyToMessageId);
            }
            final boolean _tmpIsEdited;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsEdited);
            _tmpIsEdited = _tmp_2 != 0;
            final Long _tmpEditedAt;
            if (_cursor.isNull(_cursorIndexOfEditedAt)) {
              _tmpEditedAt = null;
            } else {
              _tmpEditedAt = _cursor.getLong(_cursorIndexOfEditedAt);
            }
            _item = new ChatMessage(_tmpId,_tmpConversationId,_tmpSenderId,_tmpReceiverId,_tmpMessage,_tmpMessageType,_tmpAttachmentPath,_tmpAttachmentType,_tmpTimestamp,_tmpIsRead,_tmpIsDelivered,_tmpReplyToMessageId,_tmpIsEdited,_tmpEditedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getConversationById(final String conversationId,
      final Continuation<? super Conversation> $completion) {
    final String _sql = "SELECT * FROM conversations WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, conversationId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Conversation>() {
      @Override
      @Nullable
      public Conversation call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfParticipant1Id = CursorUtil.getColumnIndexOrThrow(_cursor, "participant1Id");
          final int _cursorIndexOfParticipant2Id = CursorUtil.getColumnIndexOrThrow(_cursor, "participant2Id");
          final int _cursorIndexOfLastMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "lastMessage");
          final int _cursorIndexOfLastMessageTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastMessageTime");
          final int _cursorIndexOfLastMessageSenderId = CursorUtil.getColumnIndexOrThrow(_cursor, "lastMessageSenderId");
          final int _cursorIndexOfUnreadCount1 = CursorUtil.getColumnIndexOrThrow(_cursor, "unreadCount1");
          final int _cursorIndexOfUnreadCount2 = CursorUtil.getColumnIndexOrThrow(_cursor, "unreadCount2");
          final int _cursorIndexOfIsArchived1 = CursorUtil.getColumnIndexOrThrow(_cursor, "isArchived1");
          final int _cursorIndexOfIsArchived2 = CursorUtil.getColumnIndexOrThrow(_cursor, "isArchived2");
          final int _cursorIndexOfIsMuted1 = CursorUtil.getColumnIndexOrThrow(_cursor, "isMuted1");
          final int _cursorIndexOfIsMuted2 = CursorUtil.getColumnIndexOrThrow(_cursor, "isMuted2");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final Conversation _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpParticipant1Id;
            _tmpParticipant1Id = _cursor.getString(_cursorIndexOfParticipant1Id);
            final String _tmpParticipant2Id;
            _tmpParticipant2Id = _cursor.getString(_cursorIndexOfParticipant2Id);
            final String _tmpLastMessage;
            if (_cursor.isNull(_cursorIndexOfLastMessage)) {
              _tmpLastMessage = null;
            } else {
              _tmpLastMessage = _cursor.getString(_cursorIndexOfLastMessage);
            }
            final long _tmpLastMessageTime;
            _tmpLastMessageTime = _cursor.getLong(_cursorIndexOfLastMessageTime);
            final String _tmpLastMessageSenderId;
            if (_cursor.isNull(_cursorIndexOfLastMessageSenderId)) {
              _tmpLastMessageSenderId = null;
            } else {
              _tmpLastMessageSenderId = _cursor.getString(_cursorIndexOfLastMessageSenderId);
            }
            final int _tmpUnreadCount1;
            _tmpUnreadCount1 = _cursor.getInt(_cursorIndexOfUnreadCount1);
            final int _tmpUnreadCount2;
            _tmpUnreadCount2 = _cursor.getInt(_cursorIndexOfUnreadCount2);
            final boolean _tmpIsArchived1;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsArchived1);
            _tmpIsArchived1 = _tmp != 0;
            final boolean _tmpIsArchived2;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsArchived2);
            _tmpIsArchived2 = _tmp_1 != 0;
            final boolean _tmpIsMuted1;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsMuted1);
            _tmpIsMuted1 = _tmp_2 != 0;
            final boolean _tmpIsMuted2;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsMuted2);
            _tmpIsMuted2 = _tmp_3 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _result = new Conversation(_tmpId,_tmpParticipant1Id,_tmpParticipant2Id,_tmpLastMessage,_tmpLastMessageTime,_tmpLastMessageSenderId,_tmpUnreadCount1,_tmpUnreadCount2,_tmpIsArchived1,_tmpIsArchived2,_tmpIsMuted1,_tmpIsMuted2,_tmpCreatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getConversationBetweenUsers(final String user1Id, final String user2Id,
      final Continuation<? super Conversation> $completion) {
    final String _sql = "SELECT * FROM conversations WHERE (participant1Id = ? AND participant2Id = ?) OR (participant1Id = ? AND participant2Id = ?)";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 4);
    int _argIndex = 1;
    _statement.bindString(_argIndex, user1Id);
    _argIndex = 2;
    _statement.bindString(_argIndex, user2Id);
    _argIndex = 3;
    _statement.bindString(_argIndex, user2Id);
    _argIndex = 4;
    _statement.bindString(_argIndex, user1Id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Conversation>() {
      @Override
      @Nullable
      public Conversation call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfParticipant1Id = CursorUtil.getColumnIndexOrThrow(_cursor, "participant1Id");
          final int _cursorIndexOfParticipant2Id = CursorUtil.getColumnIndexOrThrow(_cursor, "participant2Id");
          final int _cursorIndexOfLastMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "lastMessage");
          final int _cursorIndexOfLastMessageTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastMessageTime");
          final int _cursorIndexOfLastMessageSenderId = CursorUtil.getColumnIndexOrThrow(_cursor, "lastMessageSenderId");
          final int _cursorIndexOfUnreadCount1 = CursorUtil.getColumnIndexOrThrow(_cursor, "unreadCount1");
          final int _cursorIndexOfUnreadCount2 = CursorUtil.getColumnIndexOrThrow(_cursor, "unreadCount2");
          final int _cursorIndexOfIsArchived1 = CursorUtil.getColumnIndexOrThrow(_cursor, "isArchived1");
          final int _cursorIndexOfIsArchived2 = CursorUtil.getColumnIndexOrThrow(_cursor, "isArchived2");
          final int _cursorIndexOfIsMuted1 = CursorUtil.getColumnIndexOrThrow(_cursor, "isMuted1");
          final int _cursorIndexOfIsMuted2 = CursorUtil.getColumnIndexOrThrow(_cursor, "isMuted2");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final Conversation _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpParticipant1Id;
            _tmpParticipant1Id = _cursor.getString(_cursorIndexOfParticipant1Id);
            final String _tmpParticipant2Id;
            _tmpParticipant2Id = _cursor.getString(_cursorIndexOfParticipant2Id);
            final String _tmpLastMessage;
            if (_cursor.isNull(_cursorIndexOfLastMessage)) {
              _tmpLastMessage = null;
            } else {
              _tmpLastMessage = _cursor.getString(_cursorIndexOfLastMessage);
            }
            final long _tmpLastMessageTime;
            _tmpLastMessageTime = _cursor.getLong(_cursorIndexOfLastMessageTime);
            final String _tmpLastMessageSenderId;
            if (_cursor.isNull(_cursorIndexOfLastMessageSenderId)) {
              _tmpLastMessageSenderId = null;
            } else {
              _tmpLastMessageSenderId = _cursor.getString(_cursorIndexOfLastMessageSenderId);
            }
            final int _tmpUnreadCount1;
            _tmpUnreadCount1 = _cursor.getInt(_cursorIndexOfUnreadCount1);
            final int _tmpUnreadCount2;
            _tmpUnreadCount2 = _cursor.getInt(_cursorIndexOfUnreadCount2);
            final boolean _tmpIsArchived1;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsArchived1);
            _tmpIsArchived1 = _tmp != 0;
            final boolean _tmpIsArchived2;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsArchived2);
            _tmpIsArchived2 = _tmp_1 != 0;
            final boolean _tmpIsMuted1;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsMuted1);
            _tmpIsMuted1 = _tmp_2 != 0;
            final boolean _tmpIsMuted2;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsMuted2);
            _tmpIsMuted2 = _tmp_3 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _result = new Conversation(_tmpId,_tmpParticipant1Id,_tmpParticipant2Id,_tmpLastMessage,_tmpLastMessageTime,_tmpLastMessageSenderId,_tmpUnreadCount1,_tmpUnreadCount2,_tmpIsArchived1,_tmpIsArchived2,_tmpIsMuted1,_tmpIsMuted2,_tmpCreatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<ConversationWithDetails>> getConversationsForUser(final String userId) {
    final String _sql = "\n"
            + "        SELECT c.*,\n"
            + "               CASE\n"
            + "                   WHEN c.participant1Id = ? THEN u2.name\n"
            + "                   ELSE u1.name\n"
            + "               END as otherParticipantName,\n"
            + "               CASE\n"
            + "                   WHEN c.participant1Id = ? THEN u2.imagePath\n"
            + "                   ELSE u1.imagePath\n"
            + "               END as otherParticipantImage,\n"
            + "               CASE\n"
            + "                   WHEN c.participant1Id = ? THEN c.unreadCount1\n"
            + "                   ELSE c.unreadCount2\n"
            + "               END as unreadCount\n"
            + "        FROM conversations c\n"
            + "        INNER JOIN users u1 ON c.participant1Id = u1.id\n"
            + "        INNER JOIN users u2 ON c.participant2Id = u2.id\n"
            + "        WHERE c.participant1Id = ? OR c.participant2Id = ?\n"
            + "        ORDER BY c.lastMessageTime DESC\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 5);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    _argIndex = 2;
    _statement.bindString(_argIndex, userId);
    _argIndex = 3;
    _statement.bindString(_argIndex, userId);
    _argIndex = 4;
    _statement.bindString(_argIndex, userId);
    _argIndex = 5;
    _statement.bindString(_argIndex, userId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"conversations",
        "users"}, new Callable<List<ConversationWithDetails>>() {
      @Override
      @NonNull
      public List<ConversationWithDetails> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfParticipant1Id = CursorUtil.getColumnIndexOrThrow(_cursor, "participant1Id");
          final int _cursorIndexOfParticipant2Id = CursorUtil.getColumnIndexOrThrow(_cursor, "participant2Id");
          final int _cursorIndexOfLastMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "lastMessage");
          final int _cursorIndexOfLastMessageTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastMessageTime");
          final int _cursorIndexOfLastMessageSenderId = CursorUtil.getColumnIndexOrThrow(_cursor, "lastMessageSenderId");
          final int _cursorIndexOfUnreadCount1 = CursorUtil.getColumnIndexOrThrow(_cursor, "unreadCount1");
          final int _cursorIndexOfUnreadCount2 = CursorUtil.getColumnIndexOrThrow(_cursor, "unreadCount2");
          final int _cursorIndexOfIsArchived1 = CursorUtil.getColumnIndexOrThrow(_cursor, "isArchived1");
          final int _cursorIndexOfIsArchived2 = CursorUtil.getColumnIndexOrThrow(_cursor, "isArchived2");
          final int _cursorIndexOfIsMuted1 = CursorUtil.getColumnIndexOrThrow(_cursor, "isMuted1");
          final int _cursorIndexOfIsMuted2 = CursorUtil.getColumnIndexOrThrow(_cursor, "isMuted2");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfOtherParticipantName = CursorUtil.getColumnIndexOrThrow(_cursor, "otherParticipantName");
          final int _cursorIndexOfOtherParticipantImage = CursorUtil.getColumnIndexOrThrow(_cursor, "otherParticipantImage");
          final int _cursorIndexOfUnreadCount = CursorUtil.getColumnIndexOrThrow(_cursor, "unreadCount");
          final List<ConversationWithDetails> _result = new ArrayList<ConversationWithDetails>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ConversationWithDetails _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpParticipant1Id;
            _tmpParticipant1Id = _cursor.getString(_cursorIndexOfParticipant1Id);
            final String _tmpParticipant2Id;
            _tmpParticipant2Id = _cursor.getString(_cursorIndexOfParticipant2Id);
            final String _tmpLastMessage;
            if (_cursor.isNull(_cursorIndexOfLastMessage)) {
              _tmpLastMessage = null;
            } else {
              _tmpLastMessage = _cursor.getString(_cursorIndexOfLastMessage);
            }
            final long _tmpLastMessageTime;
            _tmpLastMessageTime = _cursor.getLong(_cursorIndexOfLastMessageTime);
            final String _tmpLastMessageSenderId;
            if (_cursor.isNull(_cursorIndexOfLastMessageSenderId)) {
              _tmpLastMessageSenderId = null;
            } else {
              _tmpLastMessageSenderId = _cursor.getString(_cursorIndexOfLastMessageSenderId);
            }
            final int _tmpUnreadCount1;
            _tmpUnreadCount1 = _cursor.getInt(_cursorIndexOfUnreadCount1);
            final int _tmpUnreadCount2;
            _tmpUnreadCount2 = _cursor.getInt(_cursorIndexOfUnreadCount2);
            final boolean _tmpIsArchived1;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsArchived1);
            _tmpIsArchived1 = _tmp != 0;
            final boolean _tmpIsArchived2;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsArchived2);
            _tmpIsArchived2 = _tmp_1 != 0;
            final boolean _tmpIsMuted1;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsMuted1);
            _tmpIsMuted1 = _tmp_2 != 0;
            final boolean _tmpIsMuted2;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsMuted2);
            _tmpIsMuted2 = _tmp_3 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final String _tmpOtherParticipantName;
            _tmpOtherParticipantName = _cursor.getString(_cursorIndexOfOtherParticipantName);
            final String _tmpOtherParticipantImage;
            if (_cursor.isNull(_cursorIndexOfOtherParticipantImage)) {
              _tmpOtherParticipantImage = null;
            } else {
              _tmpOtherParticipantImage = _cursor.getString(_cursorIndexOfOtherParticipantImage);
            }
            final int _tmpUnreadCount;
            _tmpUnreadCount = _cursor.getInt(_cursorIndexOfUnreadCount);
            _item = new ConversationWithDetails(_tmpId,_tmpParticipant1Id,_tmpParticipant2Id,_tmpLastMessage,_tmpLastMessageTime,_tmpLastMessageSenderId,_tmpUnreadCount1,_tmpUnreadCount2,_tmpIsArchived1,_tmpIsArchived2,_tmpIsMuted1,_tmpIsMuted2,_tmpCreatedAt,_tmpOtherParticipantName,_tmpOtherParticipantImage,_tmpUnreadCount);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<ConversationWithDetails>> getActiveConversationsForUser(final String userId) {
    final String _sql = "\n"
            + "        SELECT c.*,\n"
            + "               CASE\n"
            + "                   WHEN c.participant1Id = ? THEN u2.name\n"
            + "                   ELSE u1.name\n"
            + "               END as otherParticipantName,\n"
            + "               CASE\n"
            + "                   WHEN c.participant1Id = ? THEN u2.imagePath\n"
            + "                   ELSE u1.imagePath\n"
            + "               END as otherParticipantImage,\n"
            + "               CASE\n"
            + "                   WHEN c.participant1Id = ? THEN c.unreadCount1\n"
            + "                   ELSE c.unreadCount2\n"
            + "               END as unreadCount\n"
            + "        FROM conversations c\n"
            + "        INNER JOIN users u1 ON c.participant1Id = u1.id\n"
            + "        INNER JOIN users u2 ON c.participant2Id = u2.id\n"
            + "        WHERE (c.participant1Id = ? OR c.participant2Id = ?)\n"
            + "        AND ((c.participant1Id = ? AND c.isArchived1 = 0) OR (c.participant2Id = ? AND c.isArchived2 = 0))\n"
            + "        ORDER BY c.lastMessageTime DESC\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 7);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    _argIndex = 2;
    _statement.bindString(_argIndex, userId);
    _argIndex = 3;
    _statement.bindString(_argIndex, userId);
    _argIndex = 4;
    _statement.bindString(_argIndex, userId);
    _argIndex = 5;
    _statement.bindString(_argIndex, userId);
    _argIndex = 6;
    _statement.bindString(_argIndex, userId);
    _argIndex = 7;
    _statement.bindString(_argIndex, userId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"conversations",
        "users"}, new Callable<List<ConversationWithDetails>>() {
      @Override
      @NonNull
      public List<ConversationWithDetails> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfParticipant1Id = CursorUtil.getColumnIndexOrThrow(_cursor, "participant1Id");
          final int _cursorIndexOfParticipant2Id = CursorUtil.getColumnIndexOrThrow(_cursor, "participant2Id");
          final int _cursorIndexOfLastMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "lastMessage");
          final int _cursorIndexOfLastMessageTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastMessageTime");
          final int _cursorIndexOfLastMessageSenderId = CursorUtil.getColumnIndexOrThrow(_cursor, "lastMessageSenderId");
          final int _cursorIndexOfUnreadCount1 = CursorUtil.getColumnIndexOrThrow(_cursor, "unreadCount1");
          final int _cursorIndexOfUnreadCount2 = CursorUtil.getColumnIndexOrThrow(_cursor, "unreadCount2");
          final int _cursorIndexOfIsArchived1 = CursorUtil.getColumnIndexOrThrow(_cursor, "isArchived1");
          final int _cursorIndexOfIsArchived2 = CursorUtil.getColumnIndexOrThrow(_cursor, "isArchived2");
          final int _cursorIndexOfIsMuted1 = CursorUtil.getColumnIndexOrThrow(_cursor, "isMuted1");
          final int _cursorIndexOfIsMuted2 = CursorUtil.getColumnIndexOrThrow(_cursor, "isMuted2");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfOtherParticipantName = CursorUtil.getColumnIndexOrThrow(_cursor, "otherParticipantName");
          final int _cursorIndexOfOtherParticipantImage = CursorUtil.getColumnIndexOrThrow(_cursor, "otherParticipantImage");
          final int _cursorIndexOfUnreadCount = CursorUtil.getColumnIndexOrThrow(_cursor, "unreadCount");
          final List<ConversationWithDetails> _result = new ArrayList<ConversationWithDetails>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ConversationWithDetails _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpParticipant1Id;
            _tmpParticipant1Id = _cursor.getString(_cursorIndexOfParticipant1Id);
            final String _tmpParticipant2Id;
            _tmpParticipant2Id = _cursor.getString(_cursorIndexOfParticipant2Id);
            final String _tmpLastMessage;
            if (_cursor.isNull(_cursorIndexOfLastMessage)) {
              _tmpLastMessage = null;
            } else {
              _tmpLastMessage = _cursor.getString(_cursorIndexOfLastMessage);
            }
            final long _tmpLastMessageTime;
            _tmpLastMessageTime = _cursor.getLong(_cursorIndexOfLastMessageTime);
            final String _tmpLastMessageSenderId;
            if (_cursor.isNull(_cursorIndexOfLastMessageSenderId)) {
              _tmpLastMessageSenderId = null;
            } else {
              _tmpLastMessageSenderId = _cursor.getString(_cursorIndexOfLastMessageSenderId);
            }
            final int _tmpUnreadCount1;
            _tmpUnreadCount1 = _cursor.getInt(_cursorIndexOfUnreadCount1);
            final int _tmpUnreadCount2;
            _tmpUnreadCount2 = _cursor.getInt(_cursorIndexOfUnreadCount2);
            final boolean _tmpIsArchived1;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsArchived1);
            _tmpIsArchived1 = _tmp != 0;
            final boolean _tmpIsArchived2;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsArchived2);
            _tmpIsArchived2 = _tmp_1 != 0;
            final boolean _tmpIsMuted1;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsMuted1);
            _tmpIsMuted1 = _tmp_2 != 0;
            final boolean _tmpIsMuted2;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsMuted2);
            _tmpIsMuted2 = _tmp_3 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final String _tmpOtherParticipantName;
            _tmpOtherParticipantName = _cursor.getString(_cursorIndexOfOtherParticipantName);
            final String _tmpOtherParticipantImage;
            if (_cursor.isNull(_cursorIndexOfOtherParticipantImage)) {
              _tmpOtherParticipantImage = null;
            } else {
              _tmpOtherParticipantImage = _cursor.getString(_cursorIndexOfOtherParticipantImage);
            }
            final int _tmpUnreadCount;
            _tmpUnreadCount = _cursor.getInt(_cursorIndexOfUnreadCount);
            _item = new ConversationWithDetails(_tmpId,_tmpParticipant1Id,_tmpParticipant2Id,_tmpLastMessage,_tmpLastMessageTime,_tmpLastMessageSenderId,_tmpUnreadCount1,_tmpUnreadCount2,_tmpIsArchived1,_tmpIsArchived2,_tmpIsMuted1,_tmpIsMuted2,_tmpCreatedAt,_tmpOtherParticipantName,_tmpOtherParticipantImage,_tmpUnreadCount);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getConversationCountForUser(final String userId,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM conversations WHERE participant1Id = ? OR participant2Id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    _argIndex = 2;
    _statement.bindString(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalUnreadCountForUser(final String userId,
      final Continuation<? super Integer> $completion) {
    final String _sql = "\n"
            + "        SELECT SUM(\n"
            + "            CASE\n"
            + "                WHEN participant1Id = ? THEN unreadCount1\n"
            + "                WHEN participant2Id = ? THEN unreadCount2\n"
            + "                ELSE 0\n"
            + "            END\n"
            + "        ) as totalUnread\n"
            + "        FROM conversations\n"
            + "        WHERE participant1Id = ? OR participant2Id = ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 4);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    _argIndex = 2;
    _statement.bindString(_argIndex, userId);
    _argIndex = 3;
    _statement.bindString(_argIndex, userId);
    _argIndex = 4;
    _statement.bindString(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getUsersWithConversations(final String userId,
      final Continuation<? super List<User>> $completion) {
    final String _sql = "\n"
            + "        SELECT DISTINCT u.* FROM users u\n"
            + "        INNER JOIN conversations c ON (u.id = c.participant1Id OR u.id = c.participant2Id)\n"
            + "        WHERE (c.participant1Id = ? OR c.participant2Id = ?) AND u.id != ?\n"
            + "        ORDER BY u.name ASC\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    _argIndex = 2;
    _statement.bindString(_argIndex, userId);
    _argIndex = 3;
    _statement.bindString(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<User>>() {
      @Override
      @NonNull
      public List<User> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "imagePath");
          final int _cursorIndexOfTopColor = CursorUtil.getColumnIndexOrThrow(_cursor, "topColor");
          final int _cursorIndexOfBottomColor = CursorUtil.getColumnIndexOrThrow(_cursor, "bottomColor");
          final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfUsername = CursorUtil.getColumnIndexOrThrow(_cursor, "username");
          final int _cursorIndexOfDepartment = CursorUtil.getColumnIndexOrThrow(_cursor, "department");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfLastLoginAt = CursorUtil.getColumnIndexOrThrow(_cursor, "lastLoginAt");
          final int _cursorIndexOfRole = CursorUtil.getColumnIndexOrThrow(_cursor, "role");
          final int _cursorIndexOfPermissions = CursorUtil.getColumnIndexOrThrow(_cursor, "permissions");
          final List<User> _result = new ArrayList<User>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final User _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpImagePath;
            if (_cursor.isNull(_cursorIndexOfImagePath)) {
              _tmpImagePath = null;
            } else {
              _tmpImagePath = _cursor.getString(_cursorIndexOfImagePath);
            }
            final Integer _tmpTopColor;
            if (_cursor.isNull(_cursorIndexOfTopColor)) {
              _tmpTopColor = null;
            } else {
              _tmpTopColor = _cursor.getInt(_cursorIndexOfTopColor);
            }
            final Integer _tmpBottomColor;
            if (_cursor.isNull(_cursorIndexOfBottomColor)) {
              _tmpBottomColor = null;
            } else {
              _tmpBottomColor = _cursor.getInt(_cursorIndexOfBottomColor);
            }
            final String _tmpEmail;
            if (_cursor.isNull(_cursorIndexOfEmail)) {
              _tmpEmail = null;
            } else {
              _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpUsername;
            if (_cursor.isNull(_cursorIndexOfUsername)) {
              _tmpUsername = null;
            } else {
              _tmpUsername = _cursor.getString(_cursorIndexOfUsername);
            }
            final String _tmpDepartment;
            if (_cursor.isNull(_cursorIndexOfDepartment)) {
              _tmpDepartment = null;
            } else {
              _tmpDepartment = _cursor.getString(_cursorIndexOfDepartment);
            }
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpLastLoginAt;
            if (_cursor.isNull(_cursorIndexOfLastLoginAt)) {
              _tmpLastLoginAt = null;
            } else {
              _tmpLastLoginAt = _cursor.getLong(_cursorIndexOfLastLoginAt);
            }
            final UserRole _tmpRole;
            _tmpRole = __UserRole_stringToEnum(_cursor.getString(_cursorIndexOfRole));
            final String _tmpPermissions;
            if (_cursor.isNull(_cursorIndexOfPermissions)) {
              _tmpPermissions = null;
            } else {
              _tmpPermissions = _cursor.getString(_cursorIndexOfPermissions);
            }
            _item = new User(_tmpId,_tmpName,_tmpImagePath,_tmpTopColor,_tmpBottomColor,_tmpEmail,_tmpPhone,_tmpUsername,_tmpDepartment,_tmpIsActive,_tmpCreatedAt,_tmpLastLoginAt,_tmpRole,_tmpPermissions);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object archiveConversation(final String conversationId, final String userId,
      final Continuation<? super Unit> $completion) {
    return ChatDao.DefaultImpls.archiveConversation(ChatDao_Impl.this, conversationId, userId, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }

  private String __MessageType_enumToString(@NonNull final MessageType _value) {
    switch (_value) {
      case TEXT: return "TEXT";
      case IMAGE: return "IMAGE";
      case FILE: return "FILE";
      case VOICE: return "VOICE";
      case SYSTEM: return "SYSTEM";
      case KPI_SHARE: return "KPI_SHARE";
      case PROGRESS_SHARE: return "PROGRESS_SHARE";
      default: throw new IllegalArgumentException("Can't convert enum to string, unknown enum value: " + _value);
    }
  }

  private String __AttachmentType_enumToString(@NonNull final AttachmentType _value) {
    switch (_value) {
      case IMAGE: return "IMAGE";
      case DOCUMENT: return "DOCUMENT";
      case AUDIO: return "AUDIO";
      case VIDEO: return "VIDEO";
      case KPI_DATA: return "KPI_DATA";
      case PROGRESS_DATA: return "PROGRESS_DATA";
      default: throw new IllegalArgumentException("Can't convert enum to string, unknown enum value: " + _value);
    }
  }

  private MessageType __MessageType_stringToEnum(@NonNull final String _value) {
    switch (_value) {
      case "TEXT": return MessageType.TEXT;
      case "IMAGE": return MessageType.IMAGE;
      case "FILE": return MessageType.FILE;
      case "VOICE": return MessageType.VOICE;
      case "SYSTEM": return MessageType.SYSTEM;
      case "KPI_SHARE": return MessageType.KPI_SHARE;
      case "PROGRESS_SHARE": return MessageType.PROGRESS_SHARE;
      default: throw new IllegalArgumentException("Can't convert value to enum, unknown value: " + _value);
    }
  }

  private AttachmentType __AttachmentType_stringToEnum(@NonNull final String _value) {
    switch (_value) {
      case "IMAGE": return AttachmentType.IMAGE;
      case "DOCUMENT": return AttachmentType.DOCUMENT;
      case "AUDIO": return AttachmentType.AUDIO;
      case "VIDEO": return AttachmentType.VIDEO;
      case "KPI_DATA": return AttachmentType.KPI_DATA;
      case "PROGRESS_DATA": return AttachmentType.PROGRESS_DATA;
      default: throw new IllegalArgumentException("Can't convert value to enum, unknown value: " + _value);
    }
  }

  private UserRole __UserRole_stringToEnum(@NonNull final String _value) {
    switch (_value) {
      case "USER": return UserRole.USER;
      case "ADMIN": return UserRole.ADMIN;
      case "SUPER_ADMIN": return UserRole.SUPER_ADMIN;
      default: throw new IllegalArgumentException("Can't convert value to enum, unknown value: " + _value);
    }
  }
}
