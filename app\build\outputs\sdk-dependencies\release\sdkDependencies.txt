# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "viewbinding"
    version: "8.9.1"
  }
  digests {
    sha256: "Y\330\207Nj\027\310\200\227\224\030\240\b\215\355\023\276X\351A\234\t\334\227\326\327\206O\236w\325\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.6.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.6.0"
  }
  digests {
    sha256: "`\261\v^\365v\233yW\001r\340\025\270\025\224\005\311/\003K\250\213\223\221\251wX\234\235\353N"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.9.22"
  }
  digests {
    sha256: "j\276\024l\'\206A8\270t\314\314\376_SN>\271#\311\232\033{]EIN\345iO>\n"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.22"
  }
  digests {
    sha256: "\005_\\\262B\207\372\020a\000\231Z{G\253\222\022k\201\3502\350u\365\372,\360\275Ui=\v"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.22"
  }
  digests {
    sha256: "A\230\260\352\360\220\244\362[o~ZYX\037C\024\272\214\237l\321\321>\351\323H\346^\330\367\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.9.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.12.0"
  }
  digests {
    sha256: "\225\263\355\257x\"{|\310\330\002\321\037\207\223\251\256\322\222\345+\217\277\214\b\326\023L\313\027\026\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.12.0"
  }
  digests {
    sha256: "B\377\247\312G\327\272\217\341\330t\305~\371\307\021\033\304\032+\f\f!Q\2129\340}\"-\355\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.3.0"
  }
  digests {
    sha256: "\253\375)\310Un[\3202Z\237v\232\271\351\321T\377JU\025\304v\315\325\242\250([\033\031\334"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\201\306\373\035\273j<\327\334\202}{\b\341\310\024.\323\244\000\243B$A\020\204\200\342/\2117\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.7.0"
  }
  digests {
    sha256: "S=\355\216\340dZ\030\366e=\245?\341\354Z\025C\016\1776\2477\324^A\0051\363\0173\030"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.1"
  }
  digests {
    sha256: "\020s\023v\f\030\370\332\027N\215\201\003PJF\216\200n\210\367\265Z\204\275\034\016\256\352\021\216\232"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.1"
  }
  digests {
    sha256: "t\226\317\375\323\353\020\020\232\315\332\0342\022\366\254x\025x\236\t8\r\311\342\314\336\304\226\333\243\374"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-play-services"
    version: "1.7.1"
  }
  digests {
    sha256: "\316\241\246\303\221\354Z8\362\365P\235\000l]\003k\355q\002\"u\341\366\247\256\334s^?8a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.0.2"
  }
  digests {
    sha256: "\003F\\\3656\200\332#\323\324\243\231\\\003)\267\367;!\0358y\036\3136\345\221\376\273\211fd"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.1.0"
  }
  digests {
    sha256: "\003\272\025\363\206\317&\v\336r\374@\020\373\211\250 \304}Q\316c\274\t)~\253\346\314\340\233\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.6.2"
  }
  digests {
    sha256: "\337{\247?7}\275|\315(\261\022\257\320\215(\266\016\302\306\274\221\354e\206\t\310ED*\316\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.2"
  }
  digests {
    sha256: "\354\301\031&%0\246\342\371\363s ~G-Gc\006\276\323\324\262\026\376a\261\352B\343\276\366\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.7.0"
  }
  digests {
    sha256: "N\035\222\342\211\222\f\327\265\0166q\271\031\033\324\023@sI\315\246\366\002\260(Td\364\034\034\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.7.0"
  }
  digests {
    sha256: "\257\216\021\270~\003\rn1\a\3559\255\317\001\372\020\326%\236\261\\C\313\246\347\264\343\377\256\337\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.7.0"
  }
  digests {
    sha256: "\232\377\242La`\334\214\255\252\311B-OqNP\tS}\002C\257\304\274t\265\267\317\r\344\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\251\177L!\221\353\3352\371\232\302)Y{\321\251$F\260\034\321\240\210\3214\224\314\005\312\277\r\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.7.0"
  }
  digests {
    sha256: "o\263=\224s\244\223=\251\331\212\v\022\266\006\022~\200h\033\331\271\003\t\314\322\302#\bc\2719"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\357p3\261]\262uiw\034<\n\353o\2229+VT!a\225\376t\023n\243\341\b\221*\324"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\264\222\333\374\205\222dq6q\022\366\320\345\322\311\231\036\205\\T!\25379\223\226\314\001#\342\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.7.0"
  }
  digests {
    sha256: "W\305x\206.!\2366\fiW\377\v\312o\026\227\a\261\345X-O\245\223\342\204\367\366>3\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.7.0"
  }
  digests {
    sha256: "\343\033\320\351+\320\263\0336\004H\312Z[\200\374\037cP{\264\356\216\233\323\000CtI\321x\233"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.0"
  }
  digests {
    sha256: "4\350\262\277\307N#\301R^=\251\003\256D\233\177\033D\n\357E\341\201Y\356G\016\221\231\177H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.8.2"
  }
  digests {
    sha256: "\\x(=\031V\261K\"\317j\327\fq*s\252\bA\026\267SU&\027l;\205\346\251 Z"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.6.2"
  }
  digests {
    sha256: "F\f\2206\266M\247\316z\006j\f\261\204\340\024Q\317\304I)\252\033b\376\006\211Y\177#C\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.1.0"
  }
  digests {
    sha256: "+\374TG\\\004q1\2213a\365m\017\177\001\234n[\356S\356\260\353}\224\247\304\231\240R\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.6.1"
  }
  digests {
    sha256: "~\245W;\223\253\253\323\27521$Q\306\352H\246b\260:\024\r\332\201\256\276uwj \244\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.2.0"
  }
  digests {
    sha256: "\363\032\006\301P\354\2600s\365Zo{\vt\242@\246\250\327\'\301L\347g&\320 W\r\372\214"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.2.0"
  }
  digests {
    sha256: "\177\372MFM\235\262Y\374\240\315\265\017\275J\266=hr\274\332YF\213\237uUPL}Z\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.11.0"
  }
  digests {
    sha256: "\216\257\1779\236\340\224\334\023I\\6\347\324\357G\036\247\357\2129\236My\311kP\375O\202E\f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.15.0"
  }
  digests {
    sha256: "\006pGqCI\347x\232[\333\372\331\321\300\257\237:\036\262\214U\240\356?h\346\202\371\005\304\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.1.4"
  }
  digests {
    sha256: "\r\367\024\300\265\036Tq\016\277tn\264i\3233\027k\273<\262\237\200w]\303\312N\263\026%\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.0.4"
  }
  digests {
    sha256: ">G\177M\3421\345\213%\365\251\222\363\276E\351}3,4\243\232\236>}Kx\256\n\302%o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.3.2"
  }
  digests {
    sha256: "\000\\\365\025\020I:$\372H\272\256\032EZRXQS\2215\032qq}\323<\272\225!\031f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.1.0-beta02"
  }
  digests {
    sha256: "\272\372\303\312\231\036\326\212,|\246\3775)f\322\000\261.f\243B\321\017I|\270\026\217Y\005J"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.2.0"
  }
  digests {
    sha256: "\241\340Y\263\274\vC\245\215\354\016\376\315\312\250\234\202\322\274\245R\352[\254\366elF\350S\025~"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.6.1"
  }
  digests {
    sha256: "ibO\327\255\326\316[\374\301#b\315BsA\322\221\016\'~\325\246\374\304a2\244\211\221\024\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.6.1"
  }
  digests {
    sha256: "\2312m>\354\244\246Fq\274\210\002\361\344_Nk4\216\214\177\253\2420\303\205M1h\t\377\317"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-ktx"
    version: "2.6.1"
  }
  digests {
    sha256: "\332L\f\272~\374\257\242\237\276\253\035\264\031\204#\217%\301\2436\022\301\326\rc\271\225\226\215p\312"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.4.0"
  }
  digests {
    sha256: "\273\177\241\023\021/~HWIn\".0Q\327:\221\n\335t\277@v\036\033\332\345[\002\026\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.4.0"
  }
  digests {
    sha256: "\312nP3\"\262\346\003t\302\263l\225\305\v\026p\235\223\210\3766\350\017\262=\341\373\367\246\353\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.github.PhilJay"
    artifactId: "MPAndroidChart"
    version: "v3.1.0"
  }
  digests {
    sha256: "V\315\021<1\2330\034e\a\216\310\242o/\217g\252N\354\215i\023\311\360\n\326,5C\v\321"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.10.1"
  }
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.dhaval2404"
    artifactId: "colorpicker"
    version: "2.3"
  }
  digests {
    sha256: "\207\3411y\375N\366\211.a\224@\243\272\245\212m|!\273}\307\316\267\326zO\326!\315\'\360"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-android-extensions-runtime"
    version: "1.9.22"
  }
  digests {
    sha256: "\360\0057\307\002>`I3\366\251V;\033\263\270>a\373\375,\303\320\351\220\rak\253hb\244"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.flexbox"
    artifactId: "flexbox"
    version: "3.0.0"
  }
  digests {
    sha256: "^\031P\004\206\375|\216.\214z\255k\273\251\310\320\255\247\005|k2\271\265\306\0249\201Nut"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jsoup"
    artifactId: "jsoup"
    version: "1.17.2"
  }
  digests {
    sha256: "\366\v3\263\216\235z\311>\252\246\212lp\367\006\273\231\003d\224\262\342\255\322\277\356\021\320\232\306\365"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime-ktx"
    version: "2.9.0"
  }
  digests {
    sha256: "</\232\nQ\202o\033\247Gu\035\211\235\230W\336\371\227l\t\370\352\220\006k6J\274\262J\031"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime"
    version: "2.9.0"
  }
  digests {
    sha256: "\213\205\363\212\250&\331\002\350\250\215*\371\334s\245\364?f\030r\004\205\361nt&\222\305\241\217o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-mlkit-text-recognition"
    version: "19.0.0"
  }
  digests {
    sha256: "\r$\362\227+\006\266v\033\264x/\202*w\357\0247\2102\215n\225\252\330-H\255\276\206\3662"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.1.0"
  }
  digests {
    sha256: "N\312V\316\354\3242Z7l\330C\257V7~#v\316(M\fo\005\245\320\250/L\033\370\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-mlkit-text-recognition-common"
    version: "19.0.0"
  }
  digests {
    sha256: "\206ocr\233CmM \311\346\236\210\313\213\231\345\370\310\t\276\021h\233\rND\311B$\213\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "3.1.0"
  }
  digests {
    sha256: "}\257\303\237\016\2505G3f\254\303F\367\346\177\310D?Ld]\231\234>\230{\312\326\270\214{"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "3.1.8"
  }
  digests {
    sha256: "\341~\335\036\367\375G\\\220\272\244\343\224\"3/\'\b}4\274\264l\264\214\350j\371\245Ja."
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "3.1.8"
  }
  digests {
    sha256: "\313\223S\357\027\221\256\027\t}\207\214\247\021\342Z\2342\316\311\004*\334I\260\f\255\376\341\247)\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "17.0.0"
  }
  digests {
    sha256: "(*Zp?\233~\265e\b\335\351~\251\030\351]s1\213\025pP\364W\367\250m\312u\001P"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-proto"
    version: "16.0.0"
  }
  digests {
    sha256: ")=\271j\r\035C\3603\026x\201\2668\330\375\350D\344\345I_Q\001\317R)We)^\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "18.0.0"
  }
  digests {
    sha256: "\200\256\316~\036\365\211W\312/\301\225{\311 \216\311*:\225( \0231\323\306>1\202W\017\227"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.odml"
    artifactId: "image"
    version: "1.0.0-beta1"
  }
  digests {
    sha256: ".q\2521\370:\224\025\'\177\021\235\346q\225ro\a\321v\016\225B\301\021w\2142\016:\241\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "17.1.5"
  }
  digests {
    sha256: "\344\3013\370\005[\030\224\201J\321i\002\216\027\366\262\024\203\215\vg\207&\215\316\375\300\235N\235\277"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "common"
    version: "18.8.0"
  }
  digests {
    sha256: "\235\3371\237s+!\371i\370\022\245\216\035\245$\363\033& \255\272~\256\177o\326\352#\356\221\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "vision-common"
    version: "17.3.0"
  }
  digests {
    sha256: "\300\b\\\245\373\240\017\021R\234\n\203\261(Q\352lY\233\274\310\257\375\222~\221K)\247CE\237"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.6"
  }
  digests {
    sha256: "\030\004\020^\236\005\375\330\367`A;\255]\344\230\303\201\2522\237O\235\224\310Q\274\211\032\306T\306"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "vision-interfaces"
    version: "16.2.0"
  }
  digests {
    sha256: "*\320\364xM\255$J\250\205\332\341}P\213\330T\301\331v\276 Z\222\332\030R\350\237\256\036\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.apache.poi"
    artifactId: "poi"
    version: "5.2.5"
  }
  digests {
    sha256: "5.\033D\245wz\362\337=}\304\b\315\251\367_\223-\016\001%\372\032}3j\023\300\246c\247"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "commons-codec"
    artifactId: "commons-codec"
    version: "1.16.0"
  }
  digests {
    sha256: "VY_\262\v\v\205\274\221\320\325\003\332\325\v\267\361\271\257\300\356\325\337\372l\273%\222\220\000HM"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.commons"
    artifactId: "commons-collections4"
    version: "4.4"
  }
  digests {
    sha256: "\035\370\271C\v\\\216\321C\327\201^@>3\357Sq\262@\n\255\276\233\332\b\203v.\bF\321"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.commons"
    artifactId: "commons-math3"
    version: "3.6.1"
  }
  digests {
    sha256: "\036V\327\260X\322\213e\253\322V\270E\2168\205\266t\301\325\210\372C\315}\034\273\234~\362\263\b"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "commons-io"
    artifactId: "commons-io"
    version: "2.15.1"
  }
  digests {
    sha256: "\245\212\361.\341\266\214\375.\273\f\'\312\357\026O\bC\201\240\016\310\032H\314\'_\327\352T\341T"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.zaxxer"
    artifactId: "SparseBitSet"
    version: "1.3"
  }
  digests {
    sha256: "\367k\205\255\260\300\a!\256&{|\375\344\332\177q\323\022\034\302\026\f\237\300\f\f\211\370\305<\212"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.logging.log4j"
    artifactId: "log4j-api"
    version: "2.21.1"
  }
  digests {
    sha256: "\035\264\216\030\b\201\276\361\336\265\002\002 \006\240%\242H\330\366\242a\206x\233\f|\344\207\306\002\326"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.poi"
    artifactId: "poi-ooxml"
    version: "5.2.5"
  }
  digests {
    sha256: "\306\255\001\241\f\357\241\322\251\301\265R\275B\223\243\354\242\233Q3S\361\354\201@A\323\215\251\354["
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.poi"
    artifactId: "poi-ooxml-lite"
    version: "5.2.5"
  }
  digests {
    sha256: "mL\346slB,R\344\177\373\372\b\307\204\341\270\301\210\357\266\225\203\254\000\032\n\261`\"\327t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.xmlbeans"
    artifactId: "xmlbeans"
    version: "5.2.0"
  }
  digests {
    sha256: "Z4O2<\004\233p$\355\323\233\275X\243\216w\353D!\212\255\'\224\207\236\177J\303\312\035\341"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.commons"
    artifactId: "commons-compress"
    version: "1.26.0"
  }
  digests {
    sha256: "\005\032\316\270\273\314b\320\365\262\270\254r\3057g\371\305\233\373\320P\025\036e\276\366\365\034\216\331\311"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.commons"
    artifactId: "commons-lang3"
    version: "3.14.0"
  }
  digests {
    sha256: "{\226\277>\346\211I\253\265\274FUY\254\'\016\005QYo\243E#\375\337\211\016\304\030\335\341<"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.virtuald"
    artifactId: "curvesapi"
    version: "1.08"
  }
  digests {
    sha256: "\255\225\260\213\213\277\235}\027\345\340\b\024\211\217\2423$\363+\305\266/\0327\200\036jV\316\000y"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "glide"
    version: "4.16.0"
  }
  digests {
    sha256: "\211\201\034c\335&jHQ\375\033y\306\374\f\230)\226\354\3445\365\377H?\2700\312l\265<\324"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "gifdecoder"
    version: "4.16.0"
  }
  digests {
    sha256: "\225_\207*\364\322\243!\376\242\243F\374G\"%\242\271\225$\254\304g\201I\027\022\343\233j!O"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "disklrucache"
    version: "4.16.0"
  }
  digests {
    sha256: "\242\'\366U\234\020J\245\245\310\213\np\351\307\343\333hY\343\vGt\363\202\221!\337\236bst"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "annotations"
    version: "4.16.0"
  }
  digests {
    sha256: "\305\2369\261\263\033MU\222\'|\331\000\022\024\235t\vV\235C8\365dC\312\310,?\275\243\321"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-bom"
    version: "32.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-analytics-ktx"
    version: "21.5.0"
  }
  digests {
    sha256: "\317\206\026\235\361\031\035\240e\316M\302)\370\000\270m\310B\310\320\2519\341\220\373\363\353\354\020\272\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-analytics"
    version: "21.5.0"
  }
  digests {
    sha256: "\025\230\346\252q\231\302\370k\210R\326\212Nx\f-%\221\002b\353\255)\221\236\232r?\200\212Y"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement"
    version: "21.5.0"
  }
  digests {
    sha256: "\333k\274\222\365\342\311~^\273\320\263\3335`S\363p\311\377w\354\366\017\242\216\240\2356A\366\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-identifier"
    version: "18.0.0"
  }
  digests {
    sha256: "s\f\233g\344\370]\2738I\364\363\344*PG\316\341\365\234#&F\235\001\331m\017\275!\030\032"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-base"
    version: "21.5.0"
  }
  digests {
    sha256: "\321\'\334D-\031\"OE\234\032o\002\367\365!\356M\236*Lc\306\037y\337\252F\253\236\316\253"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-impl"
    version: "21.5.0"
  }
  digests {
    sha256: "\345E\352J>\257\306}\025U{\330\221\277i\207\254\351\344\300A\302\362\220\200\377D\243R\200\256\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices"
    version: "1.0.0-beta05"
  }
  digests {
    sha256: "\001\'W\214\334\355\255|\216\316\027H\024\306\364\343\002x\216\217{\321N\203=\221\234\033vY\331}"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices-java"
    version: "1.0.0-beta05"
  }
  digests {
    sha256: "\227\352S+F\274\203\365\254\344\a\342B\247\254\315\330+\204\2407u\331n\257\\\341`\316\275\273\234"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "31.1-android"
  }
  digests {
    sha256: "2\254.\327\t\331m\'\213].>\\\352\027\217\244\223\2319\305%\373du2\360\0230\215\263\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.12.0"
  }
  digests {
    sha256: "\377\020xZ\302\243W\354]\351\302\223\313\230*,\273`\\\003\t\352L\301\313\233\233\306\333\347\363\313"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.j2objc"
    artifactId: "j2objc-annotations"
    version: "1.3"
  }
  digests {
    sha256: "!\2570\311\"g\275a\"\300\340\264\322\f\314\266d\0327\352\371V\306T\016\304q\325\204\346J{"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-stats"
    version: "17.0.2"
  }
  digests {
    sha256: "\335C\024\245?I\243x\354\024a\003\323b2\271luEM)Rc6\314\275\3612\224\027d\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-api"
    version: "21.5.0"
  }
  digests {
    sha256: "hL\225\312\377K\005>9\237\265\0275\351\364\310\334.\2307\212I\b\003/\310\256qy\v3\260"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk-api"
    version: "21.5.0"
  }
  digests {
    sha256: "\354\364\356\210\351to\371\ff\312\247\a\363\356\216\275;\254G\325\203!\034\264\316\256\2158\341x\203"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "20.4.2"
  }
  digests {
    sha256: "7x.?6\033lBKT9\243\363=V\2243\327\342f\005X\001\365\037\374\240\272\335\250\230\332"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common-ktx"
    version: "20.4.2"
  }
  digests {
    sha256: "\027\vDi\016H\r\035\336y\250\321cXj\020%\253UB\356\n\2548t\267n}\034\'q\217"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations"
    version: "17.2.0"
  }
  digests {
    sha256: "\336\001\036j;ya\336c\217\027/,\266k\243\004\352\251\211f*\236+\017H\337\367t\314\301f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations-interop"
    version: "17.1.1"
  }
  digests {
    sha256: "\372\306Ph\017y!\364\253\222\360\273!\240\2224\261,\332\357\253,\367\001\210(\035~\245+\215\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-measurement-connector"
    version: "19.0.0"
  }
  digests {
    sha256: "\333\247Mk\371FG\3569{\367\257\262\253\a\366\376\215\023\025~Vx_\245@\242\241>\330,\231"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk"
    version: "21.5.0"
  }
  digests {
    sha256: "\233\325\323\025\031\212^\200\377\\T\2008\206\325\203H\025\366bM\002\331N7\262\353\257#\375\000\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-database-ktx"
    version: "20.3.0"
  }
  digests {
    sha256: "l\017\337VrH\314j\033\316\202<\364\317T\345a\314\021\320\253\'\317\027\006\215&\206\346\0263;"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-database"
    version: "20.3.0"
  }
  digests {
    sha256: "\256\276\347\357\317Cv\023\3446\347\003Eg\006\242u\200\354\367\025\374E\035\326\377\031\312\264\363\257\276"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-appcheck-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\036\241\020\250\3266\3042`\241\360}zE\372\005H\210Y\372\2637;\023\004/\201\022\005\266\376<"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth-interop"
    version: "20.0.0"
  }
  digests {
    sha256: "\300\237\332\337\240WI\315\177]h_\367\237<$\370\273\325+\241\310\033\216k\277M\000\230\301\312I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-database-collection"
    version: "18.0.1"
  }
  digests {
    sha256: "\373\222`M\363[\370\031\347\006C/\366\343\312\235G\224\314\2054\215\224\310\207b+\251;TP\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-messaging-ktx"
    version: "23.4.0"
  }
  digests {
    sha256: "\346j3bRA`\253\350\241\270\275\3615\000\273pB\203\177\241q\371\211\017\313\2029\234\363\"\205"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-messaging"
    version: "23.4.0"
  }
  digests {
    sha256: "\366\211(O\215\336\324[\277\266T=x\262\032y\272\344\375\256G[It\006\201\247i\257;\214\353"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-iid-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\v|7!\310Kb\347\004\0250r9\355J\177\231\211\211\bK\362\203?\220\271\365\276\243\tZ\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-cloud-messaging"
    version: "17.1.0"
  }
  digests {
    sha256: "\362\005 f\307L\203\032\343V\263t\"|\273\237\351\304\207~\252Z\032\311\032W$U\240\363F\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-datatransport"
    version: "18.1.7"
  }
  digests {
    sha256: "_#\325u\n\342H\334\2421,Yc\252\211\370Q\2766\023/\270\237\025\021L5\205\346\005\377\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-parcelize-runtime"
    version: "1.9.22"
  }
  digests {
    sha256: "\360\a\1775\354\202\216\024\212\311-\fj\241A\2472\265\353zJ9\026y\256\357\245\237\251\241\245\314"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 5
  library_dep_index: 3
}
library_dependencies {
  library_index: 6
  library_dep_index: 3
  library_dep_index: 5
}
library_dependencies {
  library_index: 7
  library_dep_index: 3
}
library_dependencies {
  library_index: 8
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 3
  library_dep_index: 9
}
library_dependencies {
  library_index: 9
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 48
  library_dep_index: 3
  library_dep_index: 8
}
library_dependencies {
  library_index: 10
  library_dep_index: 3
}
library_dependencies {
  library_index: 11
  library_dep_index: 1
}
library_dependencies {
  library_index: 12
  library_dep_index: 1
  library_dep_index: 13
}
library_dependencies {
  library_index: 14
  library_dep_index: 1
}
library_dependencies {
  library_index: 15
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 35
  library_dep_index: 28
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 30
  library_dep_index: 40
}
library_dependencies {
  library_index: 16
  library_dep_index: 1
}
library_dependencies {
  library_index: 17
  library_dep_index: 1
  library_dep_index: 16
}
library_dependencies {
  library_index: 18
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 15
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 28
  library_dep_index: 36
  library_dep_index: 37
}
library_dependencies {
  library_index: 19
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 6
}
library_dependencies {
  library_index: 20
  library_dep_index: 21
}
library_dependencies {
  library_index: 21
  library_dep_index: 4
  library_dep_index: 22
  library_dep_index: 7
  library_dep_index: 6
}
library_dependencies {
  library_index: 22
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 23
  library_dep_index: 21
}
library_dependencies {
  library_index: 23
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 6
}
library_dependencies {
  library_index: 24
  library_dep_index: 25
}
library_dependencies {
  library_index: 25
  library_dep_index: 11
  library_dep_index: 9
  library_dep_index: 26
}
library_dependencies {
  library_index: 26
  library_dep_index: 27
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 11
  library_dep_index: 8
  library_dep_index: 29
  library_dep_index: 15
  library_dep_index: 28
  library_dep_index: 37
  library_dep_index: 43
  library_dep_index: 41
  library_dep_index: 38
  library_dep_index: 44
  library_dep_index: 3
  library_dep_index: 46
}
library_dependencies {
  library_index: 27
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 9
  library_dep_index: 15
  library_dep_index: 28
  library_dep_index: 37
  library_dep_index: 41
  library_dep_index: 38
  library_dep_index: 34
  library_dep_index: 3
  library_dep_index: 42
}
library_dependencies {
  library_index: 28
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 15
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 18
  library_dep_index: 30
  library_dep_index: 40
}
library_dependencies {
  library_index: 29
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 15
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 28
  library_dep_index: 36
  library_dep_index: 37
}
library_dependencies {
  library_index: 30
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 3
  library_dep_index: 20
  library_dep_index: 18
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 15
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 28
  library_dep_index: 36
  library_dep_index: 37
}
library_dependencies {
  library_index: 31
  library_dep_index: 29
  library_dep_index: 3
  library_dep_index: 29
  library_dep_index: 32
  library_dep_index: 15
  library_dep_index: 35
  library_dep_index: 28
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 18
  library_dep_index: 30
  library_dep_index: 40
}
library_dependencies {
  library_index: 32
  library_dep_index: 1
  library_dep_index: 15
  library_dep_index: 33
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 15
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 28
  library_dep_index: 36
  library_dep_index: 37
}
library_dependencies {
  library_index: 33
  library_dep_index: 1
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 1
}
library_dependencies {
  library_index: 35
  library_dep_index: 1
  library_dep_index: 15
  library_dep_index: 3
  library_dep_index: 19
  library_dep_index: 18
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 15
  library_dep_index: 28
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 30
  library_dep_index: 40
}
library_dependencies {
  library_index: 36
  library_dep_index: 28
  library_dep_index: 3
  library_dep_index: 19
  library_dep_index: 31
  library_dep_index: 15
  library_dep_index: 35
  library_dep_index: 28
  library_dep_index: 32
  library_dep_index: 37
  library_dep_index: 29
  library_dep_index: 18
  library_dep_index: 30
  library_dep_index: 40
}
library_dependencies {
  library_index: 37
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 29
  library_dep_index: 28
  library_dep_index: 38
  library_dep_index: 3
  library_dep_index: 19
  library_dep_index: 18
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 15
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 28
  library_dep_index: 36
}
library_dependencies {
  library_index: 38
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 18
  library_dep_index: 3
  library_dep_index: 39
}
library_dependencies {
  library_index: 39
  library_dep_index: 38
  library_dep_index: 3
  library_dep_index: 38
}
library_dependencies {
  library_index: 40
  library_dep_index: 15
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 15
  library_dep_index: 35
  library_dep_index: 28
  library_dep_index: 36
  library_dep_index: 37
}
library_dependencies {
  library_index: 41
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 33
  library_dep_index: 13
}
library_dependencies {
  library_index: 42
  library_dep_index: 27
  library_dep_index: 8
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 39
  library_dep_index: 3
  library_dep_index: 27
}
library_dependencies {
  library_index: 43
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 30
  library_dep_index: 28
}
library_dependencies {
  library_index: 44
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 45
}
library_dependencies {
  library_index: 45
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 11
}
library_dependencies {
  library_index: 46
  library_dep_index: 42
  library_dep_index: 47
  library_dep_index: 8
  library_dep_index: 26
  library_dep_index: 31
  library_dep_index: 36
  library_dep_index: 39
  library_dep_index: 3
  library_dep_index: 26
}
library_dependencies {
  library_index: 47
  library_dep_index: 3
  library_dep_index: 11
}
library_dependencies {
  library_index: 48
  library_dep_index: 1
  library_dep_index: 11
}
library_dependencies {
  library_index: 49
  library_dep_index: 27
  library_dep_index: 1
  library_dep_index: 50
  library_dep_index: 11
  library_dep_index: 9
  library_dep_index: 8
  library_dep_index: 53
  library_dep_index: 54
  library_dep_index: 55
  library_dep_index: 56
  library_dep_index: 26
  library_dep_index: 15
  library_dep_index: 28
  library_dep_index: 57
  library_dep_index: 38
  library_dep_index: 3
  library_dep_index: 50
}
library_dependencies {
  library_index: 50
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 9
  library_dep_index: 51
  library_dep_index: 52
  library_dep_index: 49
}
library_dependencies {
  library_index: 51
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 11
}
library_dependencies {
  library_index: 52
  library_dep_index: 51
  library_dep_index: 14
  library_dep_index: 11
}
library_dependencies {
  library_index: 53
  library_dep_index: 1
}
library_dependencies {
  library_index: 54
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 45
}
library_dependencies {
  library_index: 55
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 9
  library_dep_index: 32
  library_dep_index: 33
}
library_dependencies {
  library_index: 56
  library_dep_index: 11
  library_dep_index: 9
  library_dep_index: 55
}
library_dependencies {
  library_index: 57
  library_dep_index: 1
}
library_dependencies {
  library_index: 58
  library_dep_index: 59
  library_dep_index: 60
  library_dep_index: 27
  library_dep_index: 1
  library_dep_index: 49
  library_dep_index: 61
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 9
  library_dep_index: 54
  library_dep_index: 65
  library_dep_index: 10
  library_dep_index: 26
  library_dep_index: 15
  library_dep_index: 70
  library_dep_index: 57
  library_dep_index: 73
  library_dep_index: 51
  library_dep_index: 72
}
library_dependencies {
  library_index: 59
  library_dep_index: 3
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 61
  library_dep_index: 1
}
library_dependencies {
  library_index: 62
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 45
  library_dep_index: 11
}
library_dependencies {
  library_index: 63
  library_dep_index: 49
  library_dep_index: 9
  library_dep_index: 64
}
library_dependencies {
  library_index: 65
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 66
}
library_dependencies {
  library_index: 66
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 67
  library_dep_index: 43
  library_dep_index: 68
  library_dep_index: 69
}
library_dependencies {
  library_index: 67
  library_dep_index: 1
}
library_dependencies {
  library_index: 68
  library_dep_index: 1
}
library_dependencies {
  library_index: 69
  library_dep_index: 1
}
library_dependencies {
  library_index: 70
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 9
  library_dep_index: 45
  library_dep_index: 71
  library_dep_index: 72
}
library_dependencies {
  library_index: 71
  library_dep_index: 8
  library_dep_index: 3
}
library_dependencies {
  library_index: 72
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 11
  library_dep_index: 9
  library_dep_index: 26
  library_dep_index: 70
}
library_dependencies {
  library_index: 73
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 11
}
library_dependencies {
  library_index: 74
  library_dep_index: 10
  library_dep_index: 17
  library_dep_index: 75
  library_dep_index: 77
  library_dep_index: 78
  library_dep_index: 75
  library_dep_index: 76
}
library_dependencies {
  library_index: 75
  library_dep_index: 1
  library_dep_index: 6
  library_dep_index: 76
  library_dep_index: 74
}
library_dependencies {
  library_index: 76
  library_dep_index: 75
  library_dep_index: 74
  library_dep_index: 3
  library_dep_index: 19
  library_dep_index: 75
  library_dep_index: 74
}
library_dependencies {
  library_index: 77
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 78
}
library_dependencies {
  library_index: 78
  library_dep_index: 1
  library_dep_index: 77
  library_dep_index: 3
  library_dep_index: 77
}
library_dependencies {
  library_index: 79
  library_dep_index: 1
}
library_dependencies {
  library_index: 81
  library_dep_index: 82
  library_dep_index: 5
  library_dep_index: 49
  library_dep_index: 8
  library_dep_index: 58
  library_dep_index: 83
}
library_dependencies {
  library_index: 82
  library_dep_index: 3
}
library_dependencies {
  library_index: 83
  library_dep_index: 3
}
library_dependencies {
  library_index: 85
  library_dep_index: 86
  library_dep_index: 86
}
library_dependencies {
  library_index: 86
  library_dep_index: 10
  library_dep_index: 9
  library_dep_index: 30
  library_dep_index: 40
  library_dep_index: 76
  library_dep_index: 78
  library_dep_index: 33
  library_dep_index: 13
  library_dep_index: 3
  library_dep_index: 19
  library_dep_index: 85
}
library_dependencies {
  library_index: 87
  library_dep_index: 88
  library_dep_index: 25
  library_dep_index: 89
  library_dep_index: 100
}
library_dependencies {
  library_index: 88
  library_dep_index: 11
  library_dep_index: 9
  library_dep_index: 26
  library_dep_index: 25
  library_dep_index: 24
}
library_dependencies {
  library_index: 89
  library_dep_index: 90
  library_dep_index: 91
  library_dep_index: 92
  library_dep_index: 88
  library_dep_index: 25
  library_dep_index: 24
  library_dep_index: 97
  library_dep_index: 98
  library_dep_index: 93
  library_dep_index: 96
  library_dep_index: 100
  library_dep_index: 101
  library_dep_index: 103
}
library_dependencies {
  library_index: 90
  library_dep_index: 1
}
library_dependencies {
  library_index: 91
  library_dep_index: 1
  library_dep_index: 90
  library_dep_index: 92
  library_dep_index: 93
  library_dep_index: 96
}
library_dependencies {
  library_index: 92
  library_dep_index: 1
  library_dep_index: 90
  library_dep_index: 93
  library_dep_index: 94
  library_dep_index: 95
}
library_dependencies {
  library_index: 93
  library_dep_index: 1
}
library_dependencies {
  library_index: 94
  library_dep_index: 1
  library_dep_index: 93
}
library_dependencies {
  library_index: 96
  library_dep_index: 1
  library_dep_index: 93
}
library_dependencies {
  library_index: 98
  library_dep_index: 99
  library_dep_index: 1
  library_dep_index: 60
}
library_dependencies {
  library_index: 99
  library_dep_index: 95
}
library_dependencies {
  library_index: 100
  library_dep_index: 9
  library_dep_index: 90
  library_dep_index: 91
  library_dep_index: 92
  library_dep_index: 88
  library_dep_index: 25
  library_dep_index: 24
  library_dep_index: 98
  library_dep_index: 93
  library_dep_index: 96
}
library_dependencies {
  library_index: 101
  library_dep_index: 102
  library_dep_index: 90
  library_dep_index: 91
  library_dep_index: 92
  library_dep_index: 88
  library_dep_index: 25
  library_dep_index: 24
  library_dep_index: 97
  library_dep_index: 98
  library_dep_index: 93
  library_dep_index: 96
  library_dep_index: 100
}
library_dependencies {
  library_index: 102
  library_dep_index: 1
}
library_dependencies {
  library_index: 103
  library_dep_index: 25
  library_dep_index: 24
}
library_dependencies {
  library_index: 104
  library_dep_index: 105
  library_dep_index: 106
  library_dep_index: 107
  library_dep_index: 108
  library_dep_index: 109
  library_dep_index: 110
}
library_dependencies {
  library_index: 111
  library_dep_index: 104
  library_dep_index: 112
  library_dep_index: 113
  library_dep_index: 114
  library_dep_index: 108
  library_dep_index: 116
  library_dep_index: 110
  library_dep_index: 106
}
library_dependencies {
  library_index: 112
  library_dep_index: 113
}
library_dependencies {
  library_index: 113
  library_dep_index: 110
}
library_dependencies {
  library_index: 114
  library_dep_index: 108
  library_dep_index: 115
}
library_dependencies {
  library_index: 117
  library_dep_index: 118
  library_dep_index: 119
  library_dep_index: 120
  library_dep_index: 26
  library_dep_index: 52
  library_dep_index: 102
  library_dep_index: 34
}
library_dependencies {
  library_index: 118
  library_dep_index: 1
}
library_dependencies {
  library_index: 121
  library_dep_index: 122
  library_dep_index: 144
  library_dep_index: 149
  library_dep_index: 123
  library_dep_index: 138
  library_dep_index: 139
  library_dep_index: 145
  library_dep_index: 150
  library_dep_index: 93
  library_dep_index: 140
}
library_dependencies {
  library_index: 122
  library_dep_index: 123
  library_dep_index: 138
  library_dep_index: 139
  library_dep_index: 98
  library_dep_index: 5
}
library_dependencies {
  library_index: 123
  library_dep_index: 124
  library_dep_index: 136
  library_dep_index: 143
}
library_dependencies {
  library_index: 124
  library_dep_index: 11
  library_dep_index: 66
  library_dep_index: 125
  library_dep_index: 25
  library_dep_index: 126
  library_dep_index: 127
  library_dep_index: 135
}
library_dependencies {
  library_index: 125
  library_dep_index: 25
}
library_dependencies {
  library_index: 126
  library_dep_index: 25
}
library_dependencies {
  library_index: 127
  library_dep_index: 11
  library_dep_index: 9
  library_dep_index: 128
  library_dep_index: 129
  library_dep_index: 125
  library_dep_index: 25
  library_dep_index: 126
  library_dep_index: 135
  library_dep_index: 130
}
library_dependencies {
  library_index: 128
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 3
  library_dep_index: 20
  library_dep_index: 129
}
library_dependencies {
  library_index: 129
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 8
  library_dep_index: 128
  library_dep_index: 130
  library_dep_index: 13
  library_dep_index: 3
  library_dep_index: 20
  library_dep_index: 128
}
library_dependencies {
  library_index: 130
  library_dep_index: 131
  library_dep_index: 13
  library_dep_index: 132
  library_dep_index: 133
  library_dep_index: 60
  library_dep_index: 134
}
library_dependencies {
  library_index: 135
  library_dep_index: 66
  library_dep_index: 25
}
library_dependencies {
  library_index: 136
  library_dep_index: 125
  library_dep_index: 25
  library_dep_index: 126
  library_dep_index: 137
  library_dep_index: 24
  library_dep_index: 138
  library_dep_index: 139
  library_dep_index: 98
  library_dep_index: 140
  library_dep_index: 141
  library_dep_index: 142
  library_dep_index: 130
  library_dep_index: 5
}
library_dependencies {
  library_index: 137
  library_dep_index: 25
  library_dep_index: 126
}
library_dependencies {
  library_index: 138
  library_dep_index: 23
  library_dep_index: 98
  library_dep_index: 99
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 3
  library_dep_index: 25
  library_dep_index: 24
}
library_dependencies {
  library_index: 139
  library_dep_index: 138
  library_dep_index: 6
  library_dep_index: 98
  library_dep_index: 99
}
library_dependencies {
  library_index: 140
  library_dep_index: 141
  library_dep_index: 3
  library_dep_index: 24
  library_dep_index: 99
  library_dep_index: 138
  library_dep_index: 139
  library_dep_index: 98
}
library_dependencies {
  library_index: 141
  library_dep_index: 24
  library_dep_index: 99
}
library_dependencies {
  library_index: 142
  library_dep_index: 25
  library_dep_index: 99
}
library_dependencies {
  library_index: 143
  library_dep_index: 11
  library_dep_index: 25
  library_dep_index: 126
  library_dep_index: 127
}
library_dependencies {
  library_index: 144
  library_dep_index: 138
  library_dep_index: 139
  library_dep_index: 145
  library_dep_index: 6
  library_dep_index: 98
}
library_dependencies {
  library_index: 145
  library_dep_index: 146
  library_dep_index: 138
  library_dep_index: 139
  library_dep_index: 98
  library_dep_index: 147
  library_dep_index: 148
  library_dep_index: 1
  library_dep_index: 88
  library_dep_index: 25
  library_dep_index: 24
  library_dep_index: 3
  library_dep_index: 20
}
library_dependencies {
  library_index: 146
  library_dep_index: 88
  library_dep_index: 24
}
library_dependencies {
  library_index: 147
  library_dep_index: 25
  library_dep_index: 24
  library_dep_index: 99
}
library_dependencies {
  library_index: 148
  library_dep_index: 88
}
library_dependencies {
  library_index: 149
  library_dep_index: 138
  library_dep_index: 139
  library_dep_index: 150
  library_dep_index: 6
  library_dep_index: 98
}
library_dependencies {
  library_index: 150
  library_dep_index: 25
  library_dep_index: 24
  library_dep_index: 60
  library_dep_index: 94
  library_dep_index: 151
  library_dep_index: 3
  library_dep_index: 1
  library_dep_index: 90
  library_dep_index: 91
  library_dep_index: 92
  library_dep_index: 88
  library_dep_index: 152
  library_dep_index: 135
  library_dep_index: 96
  library_dep_index: 93
  library_dep_index: 153
  library_dep_index: 141
  library_dep_index: 142
  library_dep_index: 138
  library_dep_index: 139
  library_dep_index: 98
  library_dep_index: 140
}
library_dependencies {
  library_index: 151
  library_dep_index: 25
  library_dep_index: 24
}
library_dependencies {
  library_index: 152
  library_dep_index: 25
  library_dep_index: 24
}
library_dependencies {
  library_index: 153
  library_dep_index: 1
  library_dep_index: 90
  library_dep_index: 91
  library_dep_index: 92
}
library_dependencies {
  library_index: 154
  library_dep_index: 3
  library_dep_index: 82
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 3
  dependency_index: 8
  dependency_index: 49
  dependency_index: 58
  dependency_index: 63
  dependency_index: 42
  dependency_index: 46
  dependency_index: 74
  dependency_index: 76
  dependency_index: 36
  dependency_index: 70
  dependency_index: 79
  dependency_index: 80
  dependency_index: 81
  dependency_index: 84
  dependency_index: 85
  dependency_index: 87
  dependency_index: 104
  dependency_index: 111
  dependency_index: 113
  dependency_index: 114
  dependency_index: 106
  dependency_index: 117
  dependency_index: 121
  dependency_index: 144
  dependency_index: 149
  dependency_index: 122
  dependency_index: 154
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jitpack.io"
  }
}
