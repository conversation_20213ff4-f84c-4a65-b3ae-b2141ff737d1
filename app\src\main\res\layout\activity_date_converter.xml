<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F8F9FA"
    tools:context=".ui.DateConverterActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:elevation="0dp"
        app:elevation="0dp">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="محول التاريخ"
            app:titleTextColor="#1A1A1A"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:navigationIconTint="#1A1A1A" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- Conversion Type Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="نوع التحويل :"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#1A1A1A"
                android:layout_marginBottom="16dp" />

            <!-- Radio Group for Conversion Type -->
            <RadioGroup
                android:id="@+id/conversionTypeGroup"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="24dp">

                <RadioButton
                    android:id="@+id/radioMiladiToHijri"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="ميلادي الى هجري"
                    android:textSize="14sp"
                    android:checked="true"
                    android:buttonTint="#3B82F6" />

                <RadioButton
                    android:id="@+id/radioHijriToMiladi"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="هجري الى ميلادي"
                    android:textSize="14sp"
                    android:buttonTint="#3B82F6" />

            </RadioGroup>

            <!-- Input Method Selection -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp"
                android:gravity="center">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/buttonPickerMode"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="اختيار"
                    android:textSize="12sp"
                    app:cornerRadius="20dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/buttonInputMode"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="كتابة"
                    android:textSize="12sp"
                    app:cornerRadius="20dp" />

            </LinearLayout>

            <!-- Date Selection Cards (Picker Mode) -->
            <LinearLayout
                android:id="@+id/pickerModeLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="20dp"
                android:visibility="visible">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/dayCard"
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="6dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="#E5E7EB"
                    app:cardBackgroundColor="@color/white"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="اليوم"
                            android:textSize="11sp"
                            android:textColor="#6B7280"
                            android:layout_marginBottom="2dp" />

                        <TextView
                            android:id="@+id/dayText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#1A1A1A" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/monthCard"
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    android:layout_weight="1"
                    android:layout_marginStart="3dp"
                    android:layout_marginEnd="3dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="#E5E7EB"
                    app:cardBackgroundColor="@color/white"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="الشهر"
                            android:textSize="11sp"
                            android:textColor="#6B7280"
                            android:layout_marginBottom="2dp" />

                        <TextView
                            android:id="@+id/monthText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#1A1A1A" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/yearCard"
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    android:layout_weight="1"
                    android:layout_marginStart="6dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="#E5E7EB"
                    app:cardBackgroundColor="@color/white"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="السنة"
                            android:textSize="11sp"
                            android:textColor="#6B7280"
                            android:layout_marginBottom="2dp" />

                        <TextView
                            android:id="@+id/yearText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="2024"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#1A1A1A" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- Date Input Fields (Input Mode) -->
            <LinearLayout
                android:id="@+id/inputModeLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="20dp"
                android:visibility="gone">

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="6dp"
                    android:hint="اليوم"
                    app:boxCornerRadiusTopStart="12dp"
                    app:boxCornerRadiusTopEnd="12dp"
                    app:boxCornerRadiusBottomStart="12dp"
                    app:boxCornerRadiusBottomEnd="12dp"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/dayInput"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="2"
                        android:textAlignment="center"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="3dp"
                    android:layout_marginEnd="3dp"
                    android:hint="الشهر"
                    app:boxCornerRadiusTopStart="12dp"
                    app:boxCornerRadiusTopEnd="12dp"
                    app:boxCornerRadiusBottomStart="12dp"
                    app:boxCornerRadiusBottomEnd="12dp"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/monthInput"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="2"
                        android:textAlignment="center"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="6dp"
                    android:hint="السنة"
                    app:boxCornerRadiusTopStart="12dp"
                    app:boxCornerRadiusTopEnd="12dp"
                    app:boxCornerRadiusBottomStart="12dp"
                    app:boxCornerRadiusBottomEnd="12dp"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/yearInput"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="4"
                        android:textAlignment="center"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

            <!-- Convert Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/convertButton"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:text="تحويل التاريخ"
                android:textSize="15sp"
                android:textStyle="bold"
                android:textColor="@color/white"
                app:cornerRadius="16dp"
                app:backgroundTint="#4A90A4"
                android:layout_marginBottom="24dp" />

            <!-- Results Table -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="12dp">

                    <!-- Table Rows -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="10dp"
                        android:background="#F8F9FA">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="التاريخ بالميلادي"
                            android:textSize="13sp"
                            android:textStyle="bold"
                            android:textColor="#1A1A1A"
                            android:gravity="end" />

                        <TextView
                            android:id="@+id/resultMiladiDate"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#6B7280"
                            android:gravity="start"
                            android:layout_marginStart="16dp" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E5E7EB" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="التاريخ بالهجري"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="#1A1A1A"
                            android:gravity="end" />

                        <TextView
                            android:id="@+id/resultHijriDate"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#6B7280"
                            android:gravity="start"
                            android:layout_marginStart="16dp" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E5E7EB" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp"
                        android:background="#F8F9FA">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="اليوم"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="#1A1A1A"
                            android:gravity="end" />

                        <TextView
                            android:id="@+id/resultDayName"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#6B7280"
                            android:gravity="start"
                            android:layout_marginStart="16dp" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E5E7EB" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="الشهر بالهجري"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="#1A1A1A"
                            android:gravity="end" />

                        <TextView
                            android:id="@+id/resultHijriMonth"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#6B7280"
                            android:gravity="start"
                            android:layout_marginStart="16dp" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E5E7EB" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp"
                        android:background="#F8F9FA">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="الشهر بالميلادي"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="#1A1A1A"
                            android:gravity="end" />

                        <TextView
                            android:id="@+id/resultMiladiMonth"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#6B7280"
                            android:gravity="start"
                            android:layout_marginStart="16dp" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E5E7EB" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="الشهر بالسرياني"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="#1A1A1A"
                            android:gravity="end" />

                        <TextView
                            android:id="@+id/resultSyrianiMonth"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#6B7280"
                            android:gravity="start"
                            android:layout_marginStart="16dp" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E5E7EB" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp"
                        android:background="#F8F9FA">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="عدد أيام الشهر بالهجري"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="#1A1A1A"
                            android:gravity="end" />

                        <TextView
                            android:id="@+id/resultHijriMonthDays"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#6B7280"
                            android:gravity="start"
                            android:layout_marginStart="16dp" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E5E7EB" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="عدد أيام الشهر بالميلادي"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="#1A1A1A"
                            android:gravity="end" />

                        <TextView
                            android:id="@+id/resultMiladiMonthDays"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#6B7280"
                            android:gravity="start"
                            android:layout_marginStart="16dp" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
