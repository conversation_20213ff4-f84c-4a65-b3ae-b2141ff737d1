package com.example.kpitrackerapp.ui

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.kpitrackerapp.R
import com.example.kpitrackerapp.adapters.ChatMessageAdapter
import com.example.kpitrackerapp.adapters.SearchResultsAdapter
import com.example.kpitrackerapp.databinding.ActivityChatBinding
import com.example.kpitrackerapp.models.ChatMessage
import com.example.kpitrackerapp.models.MessageType
import com.example.kpitrackerapp.models.User
import com.example.kpitrackerapp.utils.SessionManager
import com.example.kpitrackerapp.viewmodels.ChatViewModel
import com.example.kpitrackerapp.viewmodels.ChatViewModelFactory
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.textfield.TextInputEditText
import kotlinx.coroutines.launch

class ChatActivity : AppCompatActivity() {

    private lateinit var binding: ActivityChatBinding
    private lateinit var chatAdapter: ChatMessageAdapter
    private lateinit var otherUser: User
    private lateinit var conversationId: String

    private val chatViewModel: ChatViewModel by viewModels {
        ChatViewModelFactory(application)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityChatBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Get other user from intent
        val otherUserId = intent.getStringExtra("OTHER_USER_ID")
        val otherUserName = intent.getStringExtra("OTHER_USER_NAME")
        val otherUserImage = intent.getStringExtra("OTHER_USER_IMAGE")

        if (otherUserId == null || otherUserName == null) {
            Toast.makeText(this, "Error: Invalid user data", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        otherUser = User(
            id = otherUserId,
            name = otherUserName,
            email = "", // Not needed for chat
            imagePath = otherUserImage
        )

        setupToolbar()
        setupRecyclerView()
        setupClickListeners()
        observeViewModel()

        // Initialize conversation
        initializeConversation()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = otherUser.name

        // Show online status (placeholder)
        supportActionBar?.subtitle = "Online" // Will be dynamic later
    }

    private fun setupRecyclerView() {
        chatAdapter = ChatMessageAdapter(
            onMessageClick = { message ->
                // Handle message click (show details, etc.)
            },
            onMessageLongClick = { message ->
                showMessageOptions(message.message.id, message.isFromCurrentUser)
            },
            onReplyClick = { message ->
                // Handle reply to message
                binding.messageEditText.hint = "Reply to: ${message.message.message.take(30)}..."
            }
        )

        binding.messagesRecyclerView.apply {
            adapter = chatAdapter
            layoutManager = LinearLayoutManager(this@ChatActivity).apply {
                stackFromEnd = true
            }
        }
    }

    private fun setupClickListeners() {
        binding.sendButton.setOnClickListener {
            sendMessage()
        }

        binding.attachButton.setOnClickListener {
            showAttachmentOptions()
        }

        // Auto-scroll to bottom when keyboard appears
        binding.messagesRecyclerView.addOnLayoutChangeListener { _, _, _, _, bottom, _, _, _, oldBottom ->
            if (bottom < oldBottom) {
                binding.messagesRecyclerView.scrollToPosition(chatAdapter.itemCount - 1)
            }
        }
    }

    private fun observeViewModel() {
        // Observe messages
        chatViewModel.messages.observe(this) { messages ->
            chatAdapter.submitList(messages) {
                // Scroll to bottom when new messages arrive
                if (messages.isNotEmpty()) {
                    binding.messagesRecyclerView.scrollToPosition(messages.size - 1)
                }
            }
        }

        // Observe loading state
        chatViewModel.isLoading.observe(this) { isLoading ->
            // Show/hide loading indicator if needed
        }

        // Observe errors
        chatViewModel.error.observe(this) { error ->
            if (error != null) {
                Toast.makeText(this, error, Toast.LENGTH_SHORT).show()
                chatViewModel.clearError()
            }
        }
    }

    private fun initializeConversation() {
        val currentUser = SessionManager.getCurrentUser()
        if (currentUser != null) {
            lifecycleScope.launch {
                conversationId = chatViewModel.getOrCreateConversation(currentUser.id, otherUser.id)
                chatViewModel.loadMessages(conversationId)
                chatViewModel.markMessagesAsRead(conversationId)
            }
        }
    }

    private fun sendMessage() {
        val messageText = binding.messageEditText.text?.toString()?.trim() ?: ""
        if (messageText.isEmpty()) return

        lifecycleScope.launch {
            try {
                chatViewModel.sendMessage(
                    receiverId = otherUser.id,
                    message = messageText,
                    messageType = MessageType.TEXT
                )
                binding.messageEditText.text?.clear()
            } catch (e: Exception) {
                Toast.makeText(this@ChatActivity, "Failed to send message", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun showMessageOptions(messageId: String, isFromCurrentUser: Boolean) {
        val options = if (isFromCurrentUser) {
            arrayOf("Copy", "Edit", "Delete", "Reply")
        } else {
            arrayOf("Copy", "Reply")
        }

        MaterialAlertDialogBuilder(this)
            .setTitle("Message Options")
            .setItems(options) { _, which ->
                when (options[which]) {
                    "Copy" -> copyMessage(messageId)
                    "Edit" -> editMessage(messageId)
                    "Delete" -> deleteMessage(messageId)
                    "Reply" -> replyToMessage(messageId)
                }
            }
            .show()
    }

    private fun copyMessage(messageId: String) {
        // Find message and copy to clipboard
        val message = chatAdapter.currentList.find { it.message.id == messageId }
        if (message != null) {
            val clipboard = getSystemService(CLIPBOARD_SERVICE) as android.content.ClipboardManager
            val clip = android.content.ClipData.newPlainText("Message", message.message.message)
            clipboard.setPrimaryClip(clip)
            Toast.makeText(this, "Message copied", Toast.LENGTH_SHORT).show()
        }
    }

    private fun editMessage(messageId: String) {
        val message = chatAdapter.currentList.find { it.message.id == messageId }
        if (message != null) {
            binding.messageEditText.setText(message.message.message)
            binding.messageEditText.text?.length?.let { binding.messageEditText.setSelection(it) }
            // TODO: Implement edit mode
        }
    }

    private fun deleteMessage(messageId: String) {
        MaterialAlertDialogBuilder(this)
            .setTitle("Delete Message")
            .setMessage("Are you sure you want to delete this message?")
            .setPositiveButton("Delete") { _, _ ->
                lifecycleScope.launch {
                    chatViewModel.deleteMessage(messageId)
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun replyToMessage(messageId: String) {
        val message = chatAdapter.currentList.find { it.message.id == messageId }
        if (message != null) {
            binding.messageEditText.hint = "Reply to: ${message.message.message.take(30)}..."
            binding.messageEditText.requestFocus()
            // TODO: Implement reply functionality
        }
    }

    private fun showAttachmentOptions() {
        val options = arrayOf(
            "📷 Camera",
            "🖼️ Gallery",
            "📁 File",
            "📊 Share KPI",
            "📈 Share Progress"
        )

        MaterialAlertDialogBuilder(this)
            .setTitle("Attach")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> openCamera()
                    1 -> openGallery()
                    2 -> openFileChooser()
                    3 -> shareKpi()
                    4 -> shareProgress()
                }
            }
            .show()
    }

    private fun openCamera() {
        Toast.makeText(this, "Camera feature coming soon!", Toast.LENGTH_SHORT).show()
        // TODO: Implement camera functionality
    }

    private fun openGallery() {
        Toast.makeText(this, "Gallery feature coming soon!", Toast.LENGTH_SHORT).show()
        // TODO: Implement gallery functionality
    }

    private fun openFileChooser() {
        Toast.makeText(this, "File chooser coming soon!", Toast.LENGTH_SHORT).show()
        // TODO: Implement file chooser
    }

    private fun shareKpi() {
        Toast.makeText(this, "KPI sharing coming soon!", Toast.LENGTH_SHORT).show()
        // TODO: Implement KPI sharing
    }

    private fun shareProgress() {
        Toast.makeText(this, "Progress sharing coming soon!", Toast.LENGTH_SHORT).show()
        // TODO: Implement progress sharing
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.chat_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            R.id.action_call -> {
                Toast.makeText(this, "Call feature coming soon!", Toast.LENGTH_SHORT).show()
                true
            }
            R.id.action_video_call -> {
                Toast.makeText(this, "Video call feature coming soon!", Toast.LENGTH_SHORT).show()
                true
            }
            R.id.action_search -> {
                showSearchDialog()
                true
            }
            R.id.action_chat_info -> {
                showChatInfo()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun showChatInfo() {
        val options = arrayOf(
            "🔇 Mute Conversation",
            "📁 Archive Conversation",
            "🗑️ Delete Conversation",
            "🚫 Block User"
        )

        MaterialAlertDialogBuilder(this)
            .setTitle("Chat Info")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> muteConversation()
                    1 -> archiveConversation()
                    2 -> deleteConversation()
                    3 -> blockUser()
                }
            }
            .show()
    }

    private fun muteConversation() {
        lifecycleScope.launch {
            chatViewModel.muteConversation(conversationId)
            Toast.makeText(this@ChatActivity, "Conversation muted", Toast.LENGTH_SHORT).show()
        }
    }

    private fun archiveConversation() {
        lifecycleScope.launch {
            chatViewModel.archiveConversation(conversationId)
            Toast.makeText(this@ChatActivity, "Conversation archived", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    private fun deleteConversation() {
        MaterialAlertDialogBuilder(this)
            .setTitle("Delete Conversation")
            .setMessage("Are you sure you want to delete this entire conversation? This action cannot be undone.")
            .setPositiveButton("Delete") { _, _ ->
                lifecycleScope.launch {
                    chatViewModel.deleteConversation(conversationId)
                    Toast.makeText(this@ChatActivity, "Conversation deleted", Toast.LENGTH_SHORT).show()
                    finish()
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun showSearchDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_search_messages, null)
        val searchInput = dialogView.findViewById<com.google.android.material.textfield.TextInputEditText>(R.id.searchInput)
        val searchResults = dialogView.findViewById<androidx.recyclerview.widget.RecyclerView>(R.id.searchResults)

        // Setup RecyclerView for search results
        val searchAdapter = SearchResultsAdapter { message ->
            // Scroll to message in main chat
            scrollToMessage(message)
        }
        searchResults.adapter = searchAdapter
        searchResults.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(this)

        val dialog = MaterialAlertDialogBuilder(this)
            .setTitle("🔍 Search Messages")
            .setView(dialogView)
            .setNegativeButton("Close", null)
            .create()

        // Setup search functionality
        searchInput.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: android.text.Editable?) {
                val query = s.toString().trim()
                if (query.length >= 2) {
                    performSearch(query, searchAdapter)
                } else {
                    searchAdapter.submitList(emptyList())
                }
            }
        })

        dialog.show()
    }

    private fun performSearch(query: String, adapter: SearchResultsAdapter) {
        lifecycleScope.launch {
            try {
                val results = chatViewModel.searchMessages(query, conversationId)
                adapter.submitList(results)
            } catch (e: Exception) {
                Toast.makeText(this@ChatActivity, "Search failed: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun scrollToMessage(message: ChatMessage) {
        // Find message position in current list and scroll to it
        val currentMessages = (binding.messagesRecyclerView.adapter as? ChatMessageAdapter)?.currentList
        val position = currentMessages?.indexOfFirst { it.id == message.id }
        if (position != null && position >= 0) {
            binding.messagesRecyclerView.smoothScrollToPosition(position)
            // Highlight the message briefly
            binding.messagesRecyclerView.postDelayed({
                binding.messagesRecyclerView.findViewHolderForAdapterPosition(position)?.itemView?.let { view ->
                    highlightView(view)
                }
            }, 500)
        }
    }

    private fun highlightView(view: android.view.View) {
        val originalBackground = view.background
        view.setBackgroundColor(ContextCompat.getColor(this, R.color.highlight_yellow))
        view.postDelayed({
            view.background = originalBackground
        }, 2000)
    }

    private fun blockUser() {
        MaterialAlertDialogBuilder(this)
            .setTitle("🚫 Block User")
            .setMessage("Are you sure you want to block ${otherUser.name}? They won't be able to send you messages and you won't receive notifications from them.")
            .setPositiveButton("Block") { _, _ ->
                lifecycleScope.launch {
                    try {
                        chatViewModel.blockUser(otherUser.id)
                        Toast.makeText(this@ChatActivity, "✅ ${otherUser.name} has been blocked", Toast.LENGTH_SHORT).show()
                        finish() // Close chat activity
                    } catch (e: Exception) {
                        Toast.makeText(this@ChatActivity, "❌ Failed to block user", Toast.LENGTH_SHORT).show()
                    }
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    override fun onResume() {
        super.onResume()
        // Mark messages as read when user returns to chat
        if (::conversationId.isInitialized) {
            lifecycleScope.launch {
                chatViewModel.markMessagesAsRead(conversationId)
            }
        }
    }
}
