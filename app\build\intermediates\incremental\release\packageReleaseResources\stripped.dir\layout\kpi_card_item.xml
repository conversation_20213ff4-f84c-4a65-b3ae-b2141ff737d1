<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="16dp"
    android:layout_marginTop="8dp"
    android:layout_marginEnd="16dp"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="6dp"
    app:strokeColor="#E0E0E0"
    app:strokeWidth="1dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- KPI Title -->
        <TextView
            android:id="@+id/kpiTitleTextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:textAppearance="?attr/textAppearanceHeadline6"
            android:textColor="?android:attr/textColorPrimary"
            app:layout_constraintEnd_toStartOf="@+id/starRatingTextView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="KPI Title (e.g., T1)" />

        <!-- Star Rating -->
        <TextView
            android:id="@+id/starRatingTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:textAppearance="?attr/textAppearanceBody1"
            android:textColor="#FFC107"
            app:layout_constraintBottom_toBottomOf="@+id/kpiTitleTextView"
            app:layout_constraintEnd_toStartOf="@+id/ownerChip"
            app:layout_constraintTop_toTopOf="@+id/kpiTitleTextView"
            tools:text="🌟🌟🌟" />

        <!-- Owner Chip/Status -->
        <com.google.android.material.chip.Chip
            android:id="@+id/ownerChip"
            style="@style/Widget.App.Chip"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:clickable="false"
            android:focusable="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/kpiTitleTextView"
            app:layout_constraintBottom_toBottomOf="@+id/kpiTitleTextView"
            tools:text="Default" />

        <!-- Subtitle (Optional, could be owner name if chip isn't used) -->
         <TextView
            android:id="@+id/kpiSubtitleTextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textAppearance="?attr/textAppearanceCaption"
            android:textColor="?android:attr/textColorSecondary"
            app:layout_constraintStart_toStartOf="@+id/kpiTitleTextView"
            app:layout_constraintEnd_toEndOf="@+id/kpiTitleTextView"
            app:layout_constraintTop_toBottomOf="@+id/kpiTitleTextView"
            tools:text="Subtitle / Owner Name" />


        <!-- Target Icon -->
        <ImageView
            android:id="@+id/targetIcon"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginTop="12dp"
            android:src="@drawable/ic_baseline_assessment_24"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/kpiSubtitleTextView"
            app:tint="?android:attr/textColorSecondary"
            android:contentDescription="@string/target_label" />

        <!-- Target Label & Value -->
        <TextView
            android:id="@+id/targetTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="@string/kpi_card_item_target_label"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?android:attr/textColorSecondary"
            app:layout_constraintBottom_toBottomOf="@+id/targetIcon"
            app:layout_constraintStart_toEndOf="@+id/targetIcon"
            app:layout_constraintTop_toTopOf="@+id/targetIcon" />

        <TextView
            android:id="@+id/targetValueTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?android:attr/textColorPrimary"
            android:textStyle="bold"
            app:layout_constraintBaseline_toBaselineOf="@+id/targetTextView"
            app:layout_constraintStart_toEndOf="@+id/targetTextView"
            tools:text="10,000" />

        <!-- Current Icon -->
        <ImageView
            android:id="@+id/currentIcon"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginTop="8dp"
            android:src="@android:drawable/ic_menu_info_details"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/targetIcon"
            app:tint="?android:attr/textColorSecondary"
            android:contentDescription="@string/current_label" />

        <!-- Current Label & Value -->
        <TextView
            android:id="@+id/currentTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="@string/kpi_card_item_current_label"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?android:attr/textColorSecondary"
            app:layout_constraintBottom_toBottomOf="@+id/currentIcon"
            app:layout_constraintStart_toEndOf="@+id/currentIcon"
            app:layout_constraintTop_toTopOf="@+id/currentIcon" />

        <TextView
            android:id="@+id/currentValueTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?android:attr/textColorPrimary"
            android:textStyle="bold"
            app:layout_constraintBaseline_toBaselineOf="@+id/currentTextView"
            app:layout_constraintStart_toEndOf="@+id/currentTextView"
            tools:text="849" />

        <!-- Current Period Target Icon -->
        <ImageView
            android:id="@+id/currentPeriodTargetIcon"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginTop="8dp"
            android:src="@drawable/ic_baseline_timer_24"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/currentIcon"
            app:tint="?android:attr/textColorSecondary"
            android:contentDescription="@string/kpi_card_item_current_period_target_label" />

        <!-- Current Period Target Label & Value -->
        <TextView
            android:id="@+id/currentPeriodTargetTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="@string/kpi_card_item_current_period_target_label"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?android:attr/textColorSecondary"
            app:layout_constraintBottom_toBottomOf="@+id/currentPeriodTargetIcon"
            app:layout_constraintStart_toEndOf="@+id/currentPeriodTargetIcon"
            app:layout_constraintTop_toTopOf="@+id/currentPeriodTargetIcon" />

        <TextView
            android:id="@+id/currentPeriodTargetValueTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?android:attr/textColorPrimary"
            android:textStyle="bold"
            app:layout_constraintBaseline_toBaselineOf="@+id/currentPeriodTargetTextView"
            app:layout_constraintStart_toEndOf="@+id/currentPeriodTargetTextView"
            tools:text="15,000" />

        <!-- Current Period Achievement Icon -->
        <ImageView
            android:id="@+id/currentPeriodAchievementIcon"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginTop="8dp"
            android:src="@drawable/ic_performance_24"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/currentPeriodTargetIcon"
            app:tint="?android:attr/textColorSecondary"
            android:contentDescription="@string/kpi_card_item_current_period_achievement_label" />

        <!-- Current Period Achievement Label & Value -->
        <TextView
            android:id="@+id/currentPeriodAchievementTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="@string/kpi_card_item_current_period_achievement_label"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?android:attr/textColorSecondary"
            app:layout_constraintBottom_toBottomOf="@+id/currentPeriodAchievementIcon"
            app:layout_constraintStart_toEndOf="@+id/currentPeriodAchievementIcon"
            app:layout_constraintTop_toTopOf="@+id/currentPeriodAchievementIcon" />

        <TextView
            android:id="@+id/currentPeriodAchievementValueTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?android:attr/textColorPrimary"
            android:textStyle="bold"
            app:layout_constraintBaseline_toBaselineOf="@+id/currentPeriodAchievementTextView"
            app:layout_constraintStart_toEndOf="@+id/currentPeriodAchievementTextView"
            tools:text="120%" />

        <!-- Trend Indicator Icon Removed -->

        <!-- Remaining Icon -->
        <ImageView
            android:id="@+id/remainingIcon"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginTop="8dp"
            android:src="@drawable/ic_baseline_event_busy_24"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/currentPeriodAchievementIcon"
            app:tint="?android:attr/textColorSecondary"
            android:contentDescription="@string/kpi_card_item_remaining_to_target_label" />

        <!-- Remaining Label & Value -->
        <TextView
            android:id="@+id/remainingTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="@string/kpi_card_item_remaining_to_target_label"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?android:attr/textColorSecondary"
            app:layout_constraintBottom_toBottomOf="@+id/remainingIcon"
            app:layout_constraintStart_toEndOf="@+id/remainingIcon"
            app:layout_constraintTop_toTopOf="@+id/remainingIcon" />

        <TextView
            android:id="@+id/remainingValueTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?android:attr/textColorPrimary"
            android:textStyle="bold"
            app:layout_constraintBaseline_toBaselineOf="@+id/remainingTextView"
            app:layout_constraintStart_toEndOf="@+id/remainingTextView"
            tools:text="50" />

        <!-- Required Daily Icon -->
        <ImageView
            android:id="@+id/requiredDailyIcon"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginTop="8dp"
            android:src="@drawable/ic_baseline_calendar_today_24"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/remainingIcon"
            app:tint="?android:attr/textColorSecondary"
            android:contentDescription="@string/kpi_card_item_required_daily_label" />

        <!-- Required Daily Label & Value -->
        <TextView
            android:id="@+id/requiredDailyTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="@string/kpi_card_item_required_daily_label"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?android:attr/textColorSecondary"
            app:layout_constraintBottom_toBottomOf="@+id/requiredDailyIcon"
            app:layout_constraintStart_toEndOf="@+id/requiredDailyIcon"
            app:layout_constraintTop_toTopOf="@+id/requiredDailyIcon" />

        <TextView
            android:id="@+id/requiredDailyValueTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?android:attr/textColorPrimary"
            android:textStyle="bold"
            app:layout_constraintBaseline_toBaselineOf="@+id/requiredDailyTextView"
            app:layout_constraintStart_toEndOf="@+id/requiredDailyTextView"
            tools:text="3,210 / day (31 days left)" />

        <!-- Removed Projected End Views -->

        <!-- Last Update Icon -->
        <ImageView
            android:id="@+id/lastUpdateIcon"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginTop="8dp"
            android:src="@android:drawable/ic_menu_recent_history"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/requiredDailyIcon"
            app:tint="?android:attr/textColorSecondary"
            android:contentDescription="@string/kpi_card_item_last_update_label" />

        <!-- Last Update Label & Value -->
        <TextView
            android:id="@+id/lastUpdateTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="@string/kpi_card_item_last_update_label"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?android:attr/textColorSecondary"
            app:layout_constraintBottom_toBottomOf="@+id/lastUpdateIcon"
            app:layout_constraintStart_toEndOf="@+id/lastUpdateIcon"
            app:layout_constraintTop_toTopOf="@+id/lastUpdateIcon" />

        <TextView
            android:id="@+id/lastUpdateValueTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?android:attr/textColorPrimary"
            android:textStyle="bold"
            app:layout_constraintBaseline_toBaselineOf="@+id/lastUpdateTextView"
            app:layout_constraintStart_toEndOf="@+id/lastUpdateTextView"
            tools:text="3 days ago" />


        <!-- Circular Progress Indicator -->
        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/progressIndicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            app:indicatorSize="60dp"
            app:trackThickness="5dp"
            app:trackCornerRadius="3dp"
            app:indicatorColor="@color/progress_color_default"
            app:trackColor="@color/progress_track_color"
            app:layout_constraintBottom_toBottomOf="@+id/currentIcon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/targetIcon"
            tools:progress="15" />

        <!-- Percentage Text -->
        <TextView
            android:id="@+id/percentageTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?attr/textAppearanceCaption"
            android:textColor="?android:attr/textColorSecondary"
            app:layout_constraintBottom_toBottomOf="@+id/progressIndicator"
            app:layout_constraintEnd_toEndOf="@+id/progressIndicator"
            app:layout_constraintStart_toStartOf="@+id/progressIndicator"
            app:layout_constraintTop_toTopOf="@+id/progressIndicator"
            tools:text="15%" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>
