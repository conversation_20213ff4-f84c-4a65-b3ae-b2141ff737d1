/ Header Record For PersistentHashMapValueStorageH Gapp/src/main/kotlin/com/example/kpitrackerapp/AdminDashboardActivity.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/KpiTrackerApplication.kt> =app/src/main/kotlin/com/example/kpitrackerapp/MainActivity.ktP Oapp/src/main/kotlin/com/example/kpitrackerapp/adapters/AdminDashboardAdapter.ktM Lapp/src/main/kotlin/com/example/kpitrackerapp/adapters/ChatMessageAdapter.ktN Mapp/src/main/kotlin/com/example/kpitrackerapp/adapters/ConversationAdapter.ktN Mapp/src/main/kotlin/com/example/kpitrackerapp/adapters/NotificationAdapter.ktL Kapp/src/main/kotlin/com/example/kpitrackerapp/adapters/UserFilterAdapter.ktJ Iapp/src/main/kotlin/com/example/kpitrackerapp/adapters/UserListAdapter.ktK Japp/src/main/kotlin/com/example/kpitrackerapp/fragments/AccountFragment.ktM Lapp/src/main/kotlin/com/example/kpitrackerapp/fragments/DashboardFragment.ktQ Papp/src/main/kotlin/com/example/kpitrackerapp/fragments/MainDashboardFragment.ktL Kapp/src/main/kotlin/com/example/kpitrackerapp/fragments/MessagesFragment.ktO Napp/src/main/kotlin/com/example/kpitrackerapp/fragments/PerformanceFragment.ktK Japp/src/main/kotlin/com/example/kpitrackerapp/models/AdminDashboardData.ktK Japp/src/main/kotlin/com/example/kpitrackerapp/models/AdminDashboardItem.ktC Bapp/src/main/kotlin/com/example/kpitrackerapp/models/ChatModels.kt< ;app/src/main/kotlin/com/example/kpitrackerapp/models/Kpi.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/models/KpiProgressEntry.ktF Eapp/src/main/kotlin/com/example/kpitrackerapp/models/OcrResultItem.ktF Eapp/src/main/kotlin/com/example/kpitrackerapp/models/SmartListItem.kt@ ?app/src/main/kotlin/com/example/kpitrackerapp/models/Subtask.kt= <app/src/main/kotlin/com/example/kpitrackerapp/models/Task.ktF Eapp/src/main/kotlin/com/example/kpitrackerapp/models/TaskAnalytics.ktE Dapp/src/main/kotlin/com/example/kpitrackerapp/models/TaskCategory.kt= <app/src/main/kotlin/com/example/kpitrackerapp/models/User.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/models/UserFilterItem.ktJ Iapp/src/main/kotlin/com/example/kpitrackerapp/models/UserKpiAssignment.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/persistence/AppDatabase.ktE Dapp/src/main/kotlin/com/example/kpitrackerapp/persistence/ChatDao.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/persistence/Converters.ktD Capp/src/main/kotlin/com/example/kpitrackerapp/persistence/KpiDao.ktQ Papp/src/main/kotlin/com/example/kpitrackerapp/persistence/KpiProgressEntryDao.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/persistence/SubtaskDao.ktM Lapp/src/main/kotlin/com/example/kpitrackerapp/persistence/TaskCategoryDao.ktE Dapp/src/main/kotlin/com/example/kpitrackerapp/persistence/TaskDao.ktE Dapp/src/main/kotlin/com/example/kpitrackerapp/persistence/UserDao.ktR Qapp/src/main/kotlin/com/example/kpitrackerapp/persistence/UserKpiAssignmentDao.ktM Lapp/src/main/kotlin/com/example/kpitrackerapp/repositories/ChatRepository.ktS Rapp/src/main/kotlin/com/example/kpitrackerapp/services/FirebaseMessagingService.ktP Oapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditAdvancedTaskActivity.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditKpiActivity.ktO Napp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditKpiOriginalActivity.ktR Qapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditProgressDialogFragment.ktM Lapp/src/main/kotlin/com/example/kpitrackerapp/ui/AutoSendSettingsActivity.ktD Capp/src/main/kotlin/com/example/kpitrackerapp/ui/ChartMarkerView.ktA @app/src/main/kotlin/com/example/kpitrackerapp/ui/ChatActivity.ktE Dapp/src/main/kotlin/com/example/kpitrackerapp/ui/ChatListActivity.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/ui/ColoredReportAdapter.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/ui/CompactReportAdapter.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/ui/CreateUserActivity.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/ui/EnhancedTaskAdapter.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExcelImportActivity.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExcelReviewActivity.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExcelReviewAdapter.ktM Lapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExpireManagementActivity.ktF Eapp/src/main/kotlin/com/example/kpitrackerapp/ui/KpiDetailActivity.ktC Bapp/src/main/kotlin/com/example/kpitrackerapp/ui/KpiListAdapter.ktL Kapp/src/main/kotlin/com/example/kpitrackerapp/ui/KpiProgressEntryAdapter.ktB Aapp/src/main/kotlin/com/example/kpitrackerapp/ui/LoginActivity.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/ui/ModernReportActivity.ktJ Iapp/src/main/kotlin/com/example/kpitrackerapp/ui/NotificationsActivity.kt@ ?app/src/main/kotlin/com/example/kpitrackerapp/ui/OcrActivity.ktF Eapp/src/main/kotlin/com/example/kpitrackerapp/ui/OcrReviewActivity.ktE Dapp/src/main/kotlin/com/example/kpitrackerapp/ui/OcrReviewAdapter.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/ui/RecentUsersAdapter.ktC Bapp/src/main/kotlin/com/example/kpitrackerapp/ui/ReportActivity.ktB Aapp/src/main/kotlin/com/example/kpitrackerapp/ui/ReportAdapter.ktO Napp/src/main/kotlin/com/example/kpitrackerapp/ui/SearchEditProgressActivity.kt@ ?app/src/main/kotlin/com/example/kpitrackerapp/ui/TaskAdapter.ktK Japp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskManagementActivity.ktQ Papp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskReminderSettingsActivity.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskReportActivity.ktF Eapp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskReportAdapter.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/ui/UnifiedReportAdapter.ktE Dapp/src/main/kotlin/com/example/kpitrackerapp/ui/UserFilterDialog.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/ui/UserKpiListActivity.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/ui/UserSummaryAdapter.ktD Capp/src/main/kotlin/com/example/kpitrackerapp/ui/UserSummaryItem.ktE Dapp/src/main/kotlin/com/example/kpitrackerapp/util/ExcelDataCache.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/util/NotificationHelper.ktJ Iapp/src/main/kotlin/com/example/kpitrackerapp/utils/AppDetectionHelper.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/utils/AutoSendScheduler.ktK Japp/src/main/kotlin/com/example/kpitrackerapp/utils/CardAnimationHelper.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/utils/CardGestureHelper.ktF Eapp/src/main/kotlin/com/example/kpitrackerapp/utils/DragDropHelper.ktN Mapp/src/main/kotlin/com/example/kpitrackerapp/utils/EisenhowerMatrixHelper.ktN Mapp/src/main/kotlin/com/example/kpitrackerapp/utils/FirebaseMessageManager.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/utils/LanguageManager.ktK Japp/src/main/kotlin/com/example/kpitrackerapp/utils/NotificationManager.ktL Kapp/src/main/kotlin/com/example/kpitrackerapp/utils/RecurringTaskManager.ktF Eapp/src/main/kotlin/com/example/kpitrackerapp/utils/SessionManager.ktD Capp/src/main/kotlin/com/example/kpitrackerapp/utils/ThemeManager.ktJ Iapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/ChatViewModel.ktQ Papp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/ChatViewModelFactory.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/viewmodels/KpiViewModel.ktP Oapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/KpiViewModelFactory.ktL Kapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/KpiWithProgress.ktJ Iapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/TaskViewModel.ktQ Papp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/TaskViewModelFactory.ktP Oapp/src/main/kotlin/com/example/kpitrackerapp/workers/AutoReportSenderWorker.ktR Qapp/src/main/kotlin/com/example/kpitrackerapp/workers/ExpiryNotificationWorker.ktM Lapp/src/main/kotlin/com/example/kpitrackerapp/workers/RecurringTaskWorker.ktL Kapp/src/main/kotlin/com/example/kpitrackerapp/workers/TaskReminderWorker.kt> =app/src/main/kotlin/com/example/kpitrackerapp/MainActivity.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/AdminDashboardActivity.kt> =app/src/main/kotlin/com/example/kpitrackerapp/MainActivity.ktP Oapp/src/main/kotlin/com/example/kpitrackerapp/adapters/AdminDashboardAdapter.ktM Lapp/src/main/kotlin/com/example/kpitrackerapp/adapters/ChatMessageAdapter.ktN Mapp/src/main/kotlin/com/example/kpitrackerapp/adapters/ConversationAdapter.ktN Mapp/src/main/kotlin/com/example/kpitrackerapp/adapters/NotificationAdapter.ktL Kapp/src/main/kotlin/com/example/kpitrackerapp/adapters/UserFilterAdapter.ktJ Iapp/src/main/kotlin/com/example/kpitrackerapp/adapters/UserListAdapter.ktQ Papp/src/main/kotlin/com/example/kpitrackerapp/fragments/MainDashboardFragment.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditKpiActivity.ktO Napp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditKpiOriginalActivity.ktR Qapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditProgressDialogFragment.ktD Capp/src/main/kotlin/com/example/kpitrackerapp/ui/ChartMarkerView.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExcelImportActivity.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExcelReviewActivity.ktF Eapp/src/main/kotlin/com/example/kpitrackerapp/ui/KpiDetailActivity.ktC Bapp/src/main/kotlin/com/example/kpitrackerapp/ui/KpiListAdapter.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/ui/ModernReportActivity.kt@ ?app/src/main/kotlin/com/example/kpitrackerapp/ui/OcrActivity.ktF Eapp/src/main/kotlin/com/example/kpitrackerapp/ui/OcrReviewActivity.ktC Bapp/src/main/kotlin/com/example/kpitrackerapp/ui/ReportActivity.ktO Napp/src/main/kotlin/com/example/kpitrackerapp/ui/SearchEditProgressActivity.kt@ ?app/src/main/kotlin/com/example/kpitrackerapp/ui/TaskAdapter.ktK Japp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskManagementActivity.ktF Eapp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskReportAdapter.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/ui/UserKpiListActivity.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/ui/UserSummaryAdapter.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/util/NotificationHelper.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/viewmodels/KpiViewModel.ktL Kapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/KpiWithProgress.ktL Kapp/src/main/kotlin/com/example/kpitrackerapp/workers/TaskReminderWorker.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/viewmodels/KpiViewModel.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/viewmodels/KpiViewModel.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/AdminDashboardActivity.kt> =app/src/main/kotlin/com/example/kpitrackerapp/MainActivity.ktP Oapp/src/main/kotlin/com/example/kpitrackerapp/adapters/AdminDashboardAdapter.ktM Lapp/src/main/kotlin/com/example/kpitrackerapp/adapters/ChatMessageAdapter.ktN Mapp/src/main/kotlin/com/example/kpitrackerapp/adapters/ConversationAdapter.ktN Mapp/src/main/kotlin/com/example/kpitrackerapp/adapters/NotificationAdapter.ktL Kapp/src/main/kotlin/com/example/kpitrackerapp/adapters/UserFilterAdapter.ktJ Iapp/src/main/kotlin/com/example/kpitrackerapp/adapters/UserListAdapter.ktO Napp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditKpiOriginalActivity.ktR Qapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditProgressDialogFragment.ktD Capp/src/main/kotlin/com/example/kpitrackerapp/ui/ChartMarkerView.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExcelReviewActivity.ktC Bapp/src/main/kotlin/com/example/kpitrackerapp/ui/KpiListAdapter.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/ui/ModernReportActivity.ktC Bapp/src/main/kotlin/com/example/kpitrackerapp/ui/ReportActivity.kt@ ?app/src/main/kotlin/com/example/kpitrackerapp/ui/TaskAdapter.ktK Japp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskManagementActivity.ktF Eapp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskReportAdapter.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/ui/UserKpiListActivity.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/viewmodels/KpiViewModel.ktP Oapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditAdvancedTaskActivity.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditKpiActivity.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/utils/AlarmClockManager.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/utils/AlarmClockManager.ktP Oapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditAdvancedTaskActivity.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditKpiActivity.ktP Oapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditAdvancedTaskActivity.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditKpiActivity.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/utils/AlarmClockManager.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/AdminDashboardActivity.kt> =app/src/main/kotlin/com/example/kpitrackerapp/MainActivity.ktP Oapp/src/main/kotlin/com/example/kpitrackerapp/adapters/AdminDashboardAdapter.ktM Lapp/src/main/kotlin/com/example/kpitrackerapp/adapters/ChatMessageAdapter.ktN Mapp/src/main/kotlin/com/example/kpitrackerapp/adapters/ConversationAdapter.ktN Mapp/src/main/kotlin/com/example/kpitrackerapp/adapters/NotificationAdapter.ktL Kapp/src/main/kotlin/com/example/kpitrackerapp/adapters/UserFilterAdapter.ktJ Iapp/src/main/kotlin/com/example/kpitrackerapp/adapters/UserListAdapter.ktP Oapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditAdvancedTaskActivity.ktO Napp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditKpiOriginalActivity.ktR Qapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditProgressDialogFragment.ktD Capp/src/main/kotlin/com/example/kpitrackerapp/ui/ChartMarkerView.ktA @app/src/main/kotlin/com/example/kpitrackerapp/ui/ChatActivity.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExcelReviewActivity.ktC Bapp/src/main/kotlin/com/example/kpitrackerapp/ui/KpiListAdapter.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/ui/ModernReportActivity.ktC Bapp/src/main/kotlin/com/example/kpitrackerapp/ui/ReportActivity.kt@ ?app/src/main/kotlin/com/example/kpitrackerapp/ui/TaskAdapter.ktK Japp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskManagementActivity.ktF Eapp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskReportAdapter.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/ui/UserKpiListActivity.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/utils/AlarmClockManager.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/AdminDashboardActivity.kt> =app/src/main/kotlin/com/example/kpitrackerapp/MainActivity.ktP Oapp/src/main/kotlin/com/example/kpitrackerapp/adapters/AdminDashboardAdapter.ktM Lapp/src/main/kotlin/com/example/kpitrackerapp/adapters/ChatMessageAdapter.ktN Mapp/src/main/kotlin/com/example/kpitrackerapp/adapters/ConversationAdapter.ktN Mapp/src/main/kotlin/com/example/kpitrackerapp/adapters/NotificationAdapter.ktL Kapp/src/main/kotlin/com/example/kpitrackerapp/adapters/UserFilterAdapter.ktJ Iapp/src/main/kotlin/com/example/kpitrackerapp/adapters/UserListAdapter.ktS Rapp/src/main/kotlin/com/example/kpitrackerapp/services/FirebaseMessagingService.ktP Oapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditAdvancedTaskActivity.ktO Napp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditKpiOriginalActivity.ktR Qapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditProgressDialogFragment.ktD Capp/src/main/kotlin/com/example/kpitrackerapp/ui/ChartMarkerView.ktA @app/src/main/kotlin/com/example/kpitrackerapp/ui/ChatActivity.ktE Dapp/src/main/kotlin/com/example/kpitrackerapp/ui/ChatListActivity.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/ui/CreateUserActivity.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExcelReviewActivity.ktF Eapp/src/main/kotlin/com/example/kpitrackerapp/ui/KpiDetailActivity.ktC Bapp/src/main/kotlin/com/example/kpitrackerapp/ui/KpiListAdapter.ktJ Iapp/src/main/kotlin/com/example/kpitrackerapp/ui/ModernAddTaskActivity.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/ui/ModernReportActivity.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/ui/RecentUsersAdapter.ktC Bapp/src/main/kotlin/com/example/kpitrackerapp/ui/ReportActivity.kt@ ?app/src/main/kotlin/com/example/kpitrackerapp/ui/TaskAdapter.ktK Japp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskManagementActivity.ktF Eapp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskReportAdapter.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/ui/UserKpiListActivity.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/ui/UserSummaryAdapter.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/util/NotificationHelper.ktK Japp/src/main/kotlin/com/example/kpitrackerapp/utils/CardAnimationHelper.ktK Japp/src/main/kotlin/com/example/kpitrackerapp/utils/NotificationManager.ktL Kapp/src/main/kotlin/com/example/kpitrackerapp/workers/TaskReminderWorker.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/AdminDashboardActivity.kt> =app/src/main/kotlin/com/example/kpitrackerapp/MainActivity.ktP Oapp/src/main/kotlin/com/example/kpitrackerapp/adapters/AdminDashboardAdapter.ktM Lapp/src/main/kotlin/com/example/kpitrackerapp/adapters/ChatMessageAdapter.ktN Mapp/src/main/kotlin/com/example/kpitrackerapp/adapters/ConversationAdapter.ktN Mapp/src/main/kotlin/com/example/kpitrackerapp/adapters/NotificationAdapter.ktO Napp/src/main/kotlin/com/example/kpitrackerapp/adapters/SearchResultsAdapter.ktL Kapp/src/main/kotlin/com/example/kpitrackerapp/adapters/UserFilterAdapter.ktJ Iapp/src/main/kotlin/com/example/kpitrackerapp/adapters/UserListAdapter.ktQ Papp/src/main/kotlin/com/example/kpitrackerapp/fragments/MainDashboardFragment.ktE Dapp/src/main/kotlin/com/example/kpitrackerapp/persistence/ChatDao.ktM Lapp/src/main/kotlin/com/example/kpitrackerapp/repositories/ChatRepository.ktS Rapp/src/main/kotlin/com/example/kpitrackerapp/services/FirebaseMessagingService.ktP Oapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditAdvancedTaskActivity.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditKpiActivity.ktO Napp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditKpiOriginalActivity.ktR Qapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditProgressDialogFragment.ktD Capp/src/main/kotlin/com/example/kpitrackerapp/ui/ChartMarkerView.ktA @app/src/main/kotlin/com/example/kpitrackerapp/ui/ChatActivity.ktE Dapp/src/main/kotlin/com/example/kpitrackerapp/ui/ChatListActivity.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/ui/ColoredReportAdapter.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/ui/CompactReportAdapter.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/ui/CreateUserActivity.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/ui/EnhancedTaskAdapter.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExcelImportActivity.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExcelReviewActivity.ktM Lapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExpireManagementActivity.ktF Eapp/src/main/kotlin/com/example/kpitrackerapp/ui/KpiDetailActivity.ktC Bapp/src/main/kotlin/com/example/kpitrackerapp/ui/KpiListAdapter.ktB Aapp/src/main/kotlin/com/example/kpitrackerapp/ui/LoginActivity.ktJ Iapp/src/main/kotlin/com/example/kpitrackerapp/ui/ModernAddTaskActivity.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/ui/ModernReportActivity.ktJ Iapp/src/main/kotlin/com/example/kpitrackerapp/ui/NotificationsActivity.kt@ ?app/src/main/kotlin/com/example/kpitrackerapp/ui/OcrActivity.ktF Eapp/src/main/kotlin/com/example/kpitrackerapp/ui/OcrReviewActivity.ktJ Iapp/src/main/kotlin/com/example/kpitrackerapp/ui/PomodoroTimerActivity.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/ui/RecentUsersAdapter.ktC Bapp/src/main/kotlin/com/example/kpitrackerapp/ui/ReportActivity.ktO Napp/src/main/kotlin/com/example/kpitrackerapp/ui/SearchEditProgressActivity.kt@ ?app/src/main/kotlin/com/example/kpitrackerapp/ui/TaskAdapter.ktK Japp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskManagementActivity.ktF Eapp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskReportAdapter.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/ui/UnifiedReportAdapter.ktH Gapp/src/main/kotlin/com/example/kpitrackerapp/ui/UserKpiListActivity.ktG Fapp/src/main/kotlin/com/example/kpitrackerapp/ui/UserSummaryAdapter.ktI Happ/src/main/kotlin/com/example/kpitrackerapp/util/NotificationHelper.ktK Japp/src/main/kotlin/com/example/kpitrackerapp/utils/CardAnimationHelper.ktF Eapp/src/main/kotlin/com/example/kpitrackerapp/utils/DragDropHelper.ktK Japp/src/main/kotlin/com/example/kpitrackerapp/utils/NotificationManager.ktJ Iapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/ChatViewModel.ktR Qapp/src/main/kotlin/com/example/kpitrackerapp/workers/ExpiryNotificationWorker.ktL Kapp/src/main/kotlin/com/example/kpitrackerapp/workers/TaskReminderWorker.kt