// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class OverallSummaryCardItemBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final LinearLayout kpiDetailsContainer;

  @NonNull
  public final MaterialCardView overallSummaryCardView;

  @NonNull
  public final View topBackgroundView;

  @NonNull
  public final LinearLayout topContentContainer;

  private OverallSummaryCardItemBinding(@NonNull MaterialCardView rootView,
      @NonNull LinearLayout kpiDetailsContainer, @NonNull MaterialCardView overallSummaryCardView,
      @NonNull View topBackgroundView, @NonNull LinearLayout topContentContainer) {
    this.rootView = rootView;
    this.kpiDetailsContainer = kpiDetailsContainer;
    this.overallSummaryCardView = overallSummaryCardView;
    this.topBackgroundView = topBackgroundView;
    this.topContentContainer = topContentContainer;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static OverallSummaryCardItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static OverallSummaryCardItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.overall_summary_card_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static OverallSummaryCardItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.kpiDetailsContainer;
      LinearLayout kpiDetailsContainer = ViewBindings.findChildViewById(rootView, id);
      if (kpiDetailsContainer == null) {
        break missingId;
      }

      MaterialCardView overallSummaryCardView = (MaterialCardView) rootView;

      id = R.id.topBackgroundView;
      View topBackgroundView = ViewBindings.findChildViewById(rootView, id);
      if (topBackgroundView == null) {
        break missingId;
      }

      id = R.id.topContentContainer;
      LinearLayout topContentContainer = ViewBindings.findChildViewById(rootView, id);
      if (topContentContainer == null) {
        break missingId;
      }

      return new OverallSummaryCardItemBinding((MaterialCardView) rootView, kpiDetailsContainer,
          overallSummaryCardView, topBackgroundView, topContentContainer);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
