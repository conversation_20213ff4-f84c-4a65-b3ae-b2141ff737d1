// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogSearchMessagesBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextInputEditText searchInput;

  @NonNull
  public final RecyclerView searchResults;

  private DialogSearchMessagesBinding(@NonNull LinearLayout rootView,
      @NonNull TextInputEditText searchInput, @NonNull RecyclerView searchResults) {
    this.rootView = rootView;
    this.searchInput = searchInput;
    this.searchResults = searchResults;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogSearchMessagesBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogSearchMessagesBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_search_messages, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogSearchMessagesBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.searchInput;
      TextInputEditText searchInput = ViewBindings.findChildViewById(rootView, id);
      if (searchInput == null) {
        break missingId;
      }

      id = R.id.searchResults;
      RecyclerView searchResults = ViewBindings.findChildViewById(rootView, id);
      if (searchResults == null) {
        break missingId;
      }

      return new DialogSearchMessagesBinding((LinearLayout) rootView, searchInput, searchResults);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
